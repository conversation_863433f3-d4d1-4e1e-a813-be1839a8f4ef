const _ = require('lodash');
const pinoHttp = require('pino-http');
const uuid = require('uuid/v4');
const config = require('../../config');

const HEADERS = [
  'origin',
  'referer',
  'user-agent',
  'x-forwarded-for',
  'x-forwarded-host',
  'access-control-request-method',
  'accept-language',
  'authorization',
  // 'cookie',
  'date',
  'via',
  'if-none-match',
  'x-requested-with',
  'x-request-id',
  'x-plugin-version',
  'x-woo-version',
  'x-wp-version',
];

const logger = pinoHttp({
  name: 'http',
  prettyPrint: config.logs.pretty,
  enabled: config.logs.enabled,
  genReqId: req => req.id,
  serializers: Object.assign({}, pinoHttp.stdSerializers, {
    error: pinoHttp.stdSerializers.err,
    req(req) {
      const result = pinoHttp.stdSerializers.req(req);
      result.headers = _.pick(result.headers, HEADERS);
      return result;
    },
  }),
});

module.exports = function (req, res, next) {
  res.id = req.id = uuid();
  logger(req, res, next);
  if(!config.disableLogRequestStart) req.log.info('request start');
};
