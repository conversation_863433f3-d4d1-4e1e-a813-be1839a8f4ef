const pino = require('pino');
const config = require('../../config');


module.exports = (name, {
  enabled = false, pretty = false,
} = { pretty: config.logs.pretty, enabled: config.logs.enabled }) => {
  // to avoid warning about memory leaks
  if(pretty) {
    process.stdout.setMaxListeners(Infinity);
  }

  const disabledLoggers = config.disabledLoggers || [];
  return pino({
    name,
    prettyPrint: config.logs.pretty,
    serializers: Object.assign({}, pino.stdSerializers, {
      error: pino.stdSerializers.err,
    }),
    enabled,
  });
};
