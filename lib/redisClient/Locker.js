module.exports = Locker;
module.exports.getLocker = redis => new Locker(redis);

function Locker(redis) {
  this.redis = redis || require('./index').getClient();
}

/**
 * @param {string} key the lock name/key
 * @param {number} [ttl=10] number of seconds to keep lock
 * @return {Promise<boolean>}
 */
Locker.prototype.lock = async function lock(key, ttl = 10) {
  const result = await this.redis.setAsync(getKey(key), 1, 'EX', ttl, 'NX');
  return !!result;
};

Locker.prototype.unlock = async function unlock(key) {
  const result = await this.redis.delAsync(getKey(key));
  return !!result;
};

function getKey(key) {
  return `locker:${key}`;
}
