const logger = require('../logger')('redis');
const redis = require('redis');
const bluebird = require('bluebird');
const slack = require('../apis/slackNotifier');

bluebird.promisifyAll(redis.RedisClient.prototype);
bluebird.promisifyAll(redis.Multi.prototype);

let client = null;

/**
 * port and host are only necessary on first call
 * @param port
 * @param host
 * @return {*}
 */
module.exports.getClient = function (port, host) {
  if(!client) client = makeClient(port, host);

  return client;
};

function makeClient(port, host) {
  const redisClient = redis.createClient(port, host, {
    retry_strategy(options) {
      let message;
      let error;
      let retry = true;
      if(options.error && options.error.code === 'ECONNREFUSED') {
        // End reconnecting on a specific error and flush all commands with
        // a individual error
        message = 'server refused the connection';
        error = options.error;
      }
      if(options.total_retry_time > 1000 * 60 * 60) {
        // End reconnecting after a specific timeout and flush all commands
        // with a individual error
        message = 'retry time exhausted';
        error = options.error;
      }
      const attempts = 3;
      if(options.attempt > attempts) {
        // End reconnecting with built in error
        message = 'failed reconnecting';
        error = new Error('too many attempts');
        logger.error({ attempts }, 'failed reconnecting');
        retry = false;
      }

      if(error) {
        slack.notifyError(error, message);
        logger.error({ err: error }, message);
      }

      if(!retry) return;
      // reconnect after
      return Math.min(1000);
    },
  });

  redisClient.on('error', (err) => {
    slack.notifyError(err, 'redis connection error');
    logger.error({ err }, 'client error');
  });

  redisClient.on('ready', () => {
    logger.info({ info: { address: redisClient.address } }, 'redis connected');
  });

  return redisClient;
}
