const logger = require('../logger')('emailer');
const config = require('../../config');
const handlebars = require('handlebars');
const nodemailer = require('nodemailer');

const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: '<EMAIL>',
    pass: 'oghrzjdcxmebbmpy',
  },
});

module.exports = {
  sendEmail(toEmail, subject, srcTemplate, params) {
    if(!config.mailer) return;

    const template = handlebars.compile(srcTemplate);
    const html = template(params);
    const opts = {
      from: 'Team ProveSource <<EMAIL>>',
      to: toEmail,
      subject,
      html,
    };

    transporter.sendMail(opts, (err, result) => {
      if(err) {
        logger.error({ err, subject, toEmail }, 'failed to send email');
      } else {
        logger.info({ result, subject, toEmail }, 'sent email successfully');
      }
    });
  },

  notifySignup(account) {
    const html = ''
			+ 'We have a new signup: <br>'
			+ '{{email}}';
    this.sendEmail('<EMAIL>', 'New Signup', html, { email: account.email });
  },

  notifyNewNotification(notification) {
    let html = 'Account {{accountId}} <br><br>';
    html += 'Created a new notification: {{name}} <br><br>';
    html += '<pre>{{json}}</pre>';
    const data = {
      accountId: notification.accountId,
      name: notification.name,
      json: JSON.stringify(notification, null, 2),
    };
    this.sendEmail('<EMAIL>', 'New Notification', html, data);
  },

};
