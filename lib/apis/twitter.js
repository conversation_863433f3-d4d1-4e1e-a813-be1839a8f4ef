const request = require('superagent');
const cheerio = require('cheerio');
const strUtil = require('./../utils/stringUtils');
const urlUtils = require('../utils/urlUtils');

module.exports = {
  getFollowers,
  getHandle,
};

function getHandle(name) {
  const nameWithoutHandle = urlUtils.encode(strUtil.getTwitterHandle(name));
  const parsedValue = urlUtils.normalizeWithPackage(nameWithoutHandle);
  const pathName = urlUtils.getPathName(parsedValue);
  if(pathName && pathName.length > 1) { // pathName has more than "/"
    return strUtil.getPathComponent(pathName, 1);
  }
  return nameWithoutHandle;
}

async function getFollowers(name) {
  const handle = getHandle(name);
  if(!handle) {
    return null;
  }
  let error = null;
  try {
    const res = await request.get(`https://api.fxtwitter.com/${handle}`)
      .set('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36');
    if(res && res.body && res.body.user) {
      return res.body.user.followers;
    }
  } catch(err) {
    error = err;
  }
  if(error) {
    throw error;
  }
  throw Error('followers not found');
}
