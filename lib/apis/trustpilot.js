const _ = require('lodash');
const request = require('superagent');
const cheerio = require('cheerio');
const { decode } = require('html-entities');

module.exports = {
  getReviews,
};

async function getReviews(options) {
  let query = 'languages=all';
  if(options.lowestRating) {
    const { lowestRating } = options;
    for(let i = lowestRating; i <= 5; i += 1) {
      query += `&stars=${i}`;
    }
  }
  const url = `https://www.trustpilot.com/review/${options.domain}?${query}`;
  // return parseReviews(url, options);
  return parseNewVersion(url, options);
}

async function parseNewVersion(url, { domain, lowestRating }) {
  const text = await getPageText(url);
  const $ = cheerio.load(text);
  const nextData = $('#__NEXT_DATA__');
  try {
    const json = JSON.parse(nextData.html());
    const { pageProps } = json.props;
    const businessName = pageProps.businessUnit.displayName;
    const { reviews } = pageProps;
    return reviews.map(r => ({
      businessName,
      domain,
      authorName: _.get(r, 'consumer.displayName'),
      profilePhotoUrl: _.get(r, 'consumer.imageUrl'),
      reviewId: r.id,
      rating: r.rating,
      time: new Date(_.get(r, 'dates.publishedDate')),
      text: r.text && decode(r.text).replace(/\n\s*\n/g, ' '),
    })).filter(r => r.rating >= lowestRating);
  } catch(err) {
    console.error('failed', err);
  }
  return [];
}

async function parseReviews(url, options) {
  let res = await request.get(url);
  if(res.redirects && res.redirects.length) {
    const lastRedirect = res.redirects[res.redirects.length - 1];
    if(!lastRedirect.includes('languages=all')) {
      res = await request.get(`${lastRedirect}?languages=all`);
    }
  }
  const reviewsBody = res.text;
  const reviewsArray = [];
  const $ = cheerio.load(reviewsBody);
  const reviewCards = $('article[class*=review]');
  const businessName = $('h1 > span[class*=displayName]').text().trim();

  reviewCards.each((idx, review) => {
    try {
      const reviewObject = parseReview($, review, options);
      if(reviewObject) {
        reviewObject.businessName = businessName;
        reviewsArray.push(reviewObject);
      }
    } catch(e) {}
  });

  return reviewsArray;
}

function parseReview($, review, options) {
  const reviewObject = {};
  reviewObject.domain = options.domain;
  let reviewScriptObject = $(review).find("script[data-initial-state='review-info']").html();

  if(reviewScriptObject) {
    reviewScriptObject = JSON.parse(reviewScriptObject);
    reviewObject.rating = reviewScriptObject.stars;
    reviewObject.authorName = reviewScriptObject.consumerName;
    reviewObject.reviewId = reviewScriptObject.reviewId;
  }

  reviewObject.profilePhotoUrl = $(review)
    .find('.review__consumer-information .consumer-information .consumer-information__picture')
    .attr('consumer-image-url');

  if(!reviewObject.authorName) {
    reviewObject.authorName = $(review).find('.review__consumer-information .consumer-information__name').text().trim();
  }

  if(!reviewObject.rating) {
    const ratingClass = $(review).find('.review-content .star-rating').attr('class');
    reviewObject.rating = getRating(ratingClass);
  }

  reviewObject.time = null;
  let timestampObj = $(review).find(".review-content-header script[data-initial-state='review-dates']").html();
  if(timestampObj) {
    timestampObj = JSON.parse(timestampObj);
    reviewObject.time = new Date(timestampObj.publishedDate);
  }
  reviewObject.text = $(review).find('.review-content__body .review-content__text').text().trim();
  if(!reviewObject.text) {
    $(review).find('.review-content__body .review-content__title').text().trim();
  }
  reviewObject.authorName = capitalize(reviewObject.authorName);

  if(options.lowestRating) {
    return reviewObject.rating >= options.lowestRating ? reviewObject : null;
  }
  return reviewObject;
}

function getRating(ratingClass) {
  if(ratingClass.includes('star-rating-5')) {
    return 5;
  } if(ratingClass.includes('star-rating-4')) {
    return 4;
  } if(ratingClass.includes('star-rating-3')) {
    return 3;
  } if(ratingClass.includes('star-rating-2')) {
    return 2;
  }
  return 1;
}

function capitalize(s) {
  if(typeof s !== 'string') return '';
  return s.charAt(0).toUpperCase() + s.slice(1);
}

async function getPageText(url) {
  let res = await request.get(url);
  if(res.redirects && res.redirects.length) {
    const lastRedirect = res.redirects[res.redirects.length - 1];
    if(!lastRedirect.includes('languages=all')) {
      res = await request.get(`${lastRedirect}?languages=all`);
    }
  }
  return res.text;
}
