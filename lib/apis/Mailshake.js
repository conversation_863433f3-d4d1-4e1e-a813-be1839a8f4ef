const superagent = require('superagent');

const HOST = 'https://api.mailshake.com/2017-04-01';

class Mailshake {
  static getClient(apiKey) {
    if(!apiKey) {
      throw new Error('Mailshake API key required');
    }
    return new Mailshake(apiKey);
  }

  constructor(apiKey) {
    this.apiKey = apiKey;
    this.request = superagent.agent().use((request) => {
      if(request.url.startsWith('/')) {
        request.url = HOST + request.url;
      } else if(!request.url.startsWith('http')) {
        request.url = `${HOST}/${request.url}`;
      }
      request.auth(this.apiKey, '');
      return request;
    });
  }

  /**
   * @param {object} params
   * @param {string|number} params.campaignId
   * @param {string} params.emails one or more emails separated by comma
   * @returns {Promise}
   */
  add({ campaignId, emails }) {
    return this.request.post('/recipients/add').send({
      campaignID: campaignId,
      listOfEmails: emails,
    });
  }
}

module.exports = Mailshake;
