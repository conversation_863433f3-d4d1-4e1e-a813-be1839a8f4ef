/**
 * MD5 the email, ex: <EMAIL> = 4fdffca28149e7fc29bc0b11f15c9ef6
 * Profile: gravatar.com/4fdffca28149e7fc29bc0b11f15c9ef6.json
 * Avatar: gravatar.com/avatar/4fdffca28149e7fc29bc0b11f15c9ef6?s=<size>&d=<default_image>
 */

/* ------------ */

const request = require('superagent');
const crypto = require('../utils/cryptoUtils');
const ErrorFactory = require('../errors/ErrorFactory');
const logger = require('../logger/index')('gravatar');

module.exports = {
  getProfile(email) {
    if(!email) return Promise.reject(ErrorFactory('email must be a valid string'));

    const hash = crypto.md5(email);
    logger.info({ email, hash }, 'fetching gravatar profile');
    return request.get(`http://gravatar.com/${hash}.json`).then((res) => {
      logger.info({ response: res.body }, 'got gravatar profile');
      const { body } = res;
      const entry = body && Array.isArray(body.entry) && body.entry.length && body.entry[0];
      if(entry) return entry;
      return null;
    }).catch((err) => {
      logger.error({ err }, 'fetch profile failed');
      return null;
    });
  },
};
