const aws = require('aws-sdk');

module.exports = {
  uploadFile,
};

/**
 * Upload a file to digitalocean space
 * @param {string} accessKey DO spaces API access key
 * @param {string} secret DO spaces secret key
 * @param {string?} endpoint
 * @param {string} bucket DO space name
 * @param {<PERSON><PERSON>er} body
 * @param {string} path the path on the space e.g. folder/file.ext
 * @param {string} contentType e.g. image/jpeg
 * @returns {Promise}
 */
function uploadFile({
  accessKey,
  secret,
  endpoint,
  bucket,
  body,
  path,
  contentType,
}) {
  const pathComps = path.split('/');
  const fileName = pathComps[pathComps.length - 1];
  let mime = contentType;
  if(!mime) {
    if(fileName.includes('.html')) {
      mime = 'text/html';
    } else if(fileName.includes('.css')) {
      mime = 'text/css';
    } else if(fileName.includes('.js')) {
      mime = 'text/javascript';
    }
  }
  return new Promise((resolve, reject) => {
    const s3 = new aws.S3({
      ...(endpoint && { endpoint: new aws.Endpoint(endpoint) }),
      accessKeyId: accessKey,
      secretAccessKey: secret,
    });
    s3.putObject({
      Body: body,
      Bucket: bucket,
      Key: path,
      ACL: 'public-read',
      ContentType: mime,
    }, (err, data) => {
      if(err) reject(err);
      else resolve({ ...data, url: `https://${bucket}.s3.amazonaws.com/${path}` });
    });
  });
}
