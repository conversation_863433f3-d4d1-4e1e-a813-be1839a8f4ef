const BigCommerceLib = require('node-bigcommerce');
const config = require('../../config');

const API_VERSION = {
  v2: 'v2',
  v3: 'v3',
};

const WEBHOOK_SCOPES = {
  orderCreated: 'store/order/created',
  storeUpdated: 'store/information/updated',
  uninstalled: 'store/app/uninstalled',
};

module.exports = function (storeHash, accessToken) {
  return new Bigcommerce(storeHash, accessToken);
};

module.exports.WEBHOOK_SCOPES = WEBHOOK_SCOPES;

function Bigcommerce(storeHash = null, accessToken = null) {
  this.bigcommerce = new BigCommerceLib({
    logLevel: 'info',
    clientId: config.bigcommerce.clientId,
    secret: config.bigcommerce.secret,
    callback: config.bigcommerce.authUrl,
    responseType: 'json',
    headers: { 'Accept-Encoding': '*' },
    apiVersion: API_VERSION.v2,
    storeHash,
    accessToken,
  });
}

Bigcommerce.prototype.authorize = function authorize(authData) {
  this.bigcommerce.apiVersion = API_VERSION.v2;
  return this.bigcommerce.authorize(authData);
};

Bigcommerce.prototype.verify = function verify(payload) {
  this.bigcommerce.apiVersion = API_VERSION.v2;
  return this.bigcommerce.verify(payload);
};

Bigcommerce.prototype.getAllScripts = function getAllScripts() {
  this.bigcommerce.apiVersion = API_VERSION.v3;
  return this.bigcommerce.get('/content/scripts');
};

Bigcommerce.prototype.setScript = function setScript(body) {
  this.bigcommerce.apiVersion = API_VERSION.v3;
  return this.bigcommerce.post('/content/scripts', body);
};

Bigcommerce.prototype.getAllWebhooks = function getAllWebhooks() {
  this.bigcommerce.apiVersion = API_VERSION.v2;
  return this.bigcommerce.get('/hooks');
};

Bigcommerce.prototype.activateWebhook = function activateWebhook(id) {
  this.bigcommerce.apiVersion = API_VERSION.v2;
  return this.bigcommerce.put(`/hooks/${id}`, { is_active: true });
};

Bigcommerce.prototype.subscribeWebhook = function subscribeWebhook(body) {
  this.bigcommerce.apiVersion = API_VERSION.v2;
  return this.bigcommerce.post('/hooks', body);
};

Bigcommerce.prototype.unsubscribeWebhook = function unsubscribeWebhook(id) {
  this.bigcommerce.apiVersion = API_VERSION.v2;
  return this.bigcommerce.delete(`/hooks/${id}`);
};

Bigcommerce.prototype.getOrderById = function getOrderById(id) {
  this.bigcommerce.apiVersion = API_VERSION.v2;
  return this.bigcommerce.get(`/orders/${id}`);
};

Bigcommerce.prototype.getOrderProducts = function getOrderProducts(orderId) {
  this.bigcommerce.apiVersion = API_VERSION.v2;
  return this.bigcommerce.get(`/orders/${orderId}/products`);
};

Bigcommerce.prototype.getStoreInfo = function getStoreInfo() {
  this.bigcommerce.apiVersion = API_VERSION.v2;
  return this.bigcommerce.get('/store');
};

Bigcommerce.prototype.getProductImages = function getProductImages(productId) {
  this.bigcommerce.apiVersion = API_VERSION.v3;
  return this.bigcommerce.get(`/catalog/products/${productId}/images`);
};

Bigcommerce.prototype.getProductVariant = function getProductVariant(productId, variantId) {
  this.bigcommerce.apiVersion = API_VERSION.v3;
  return this.bigcommerce.get(`/catalog/products/${productId}/variants/${variantId}`);
};

Bigcommerce.prototype.getProductById = function getProductById(productId) {
  this.bigcommerce.apiVersion = API_VERSION.v3;
  return this.bigcommerce.get(`/catalog/products/${productId}`);
};

Bigcommerce.prototype.getLastOrders = function getLastOrders(amount) {
  return this.bigcommerce.get(`/orders?sort=date_created:desc&limit=${amount}&is_deleted=false`);
};

Bigcommerce.prototype.getCustomer = function getCustomer(customerId) {
  this.bigcommerce.apiVersion = API_VERSION.v3;
  return this.bigcommerce.get(`/customers?id:in=${customerId}`).then(res => res.data[0]);
};
