const sendgrid = require('@sendgrid/mail');
const config = require('../../config');

module.exports.send = (apiKey, opts) => {
  if(!config.sendgrid.active) return Promise.reject(Error('sendgrid is not active'));

  sendgrid.setApiKey(apiKey);
  return sendgrid.send(opts);
};

module.exports.sendTemplate = (apiKey, {
  to,
  from,
  replyTo,
  templateId,
  data,
  category,
}) => {
  sendgrid.setApiKey(apiKey);
  return sendgrid.send({
    to,
    from,
    replyTo: replyTo || from,
    templateId,
    category,
    dynamic_template_data: data,
  });
};
