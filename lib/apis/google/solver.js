const request = require('request-promise');

const api = {
  in: 'http://2captcha.com/in.php',
  res: 'http://2captcha.com/res.php',
  key: 'YOUR_API_KEY',
};

let params = {};

function setApiKey(apiKey) {
  api.key = apiKey;
}

function cookiesToString(cookies) {
  const cookiesArray = cookies.map((cookie) => {
    if(cookie.name !== 'test_cookie') {
      return `${cookie.name}:${cookie.value}`;
    }
    return undefined;
  });
  return cookiesArray.join(';');
}

function getCaptchaParams(page) {
  /* eslint-disable */
  return page.evaluate(() => ({
    sitekey: document.querySelector('#recaptcha').dataset.sitekey,
    callback: document.querySelector('#recaptcha').dataset.callback,
    datas: document.querySelector('#recaptcha').dataset.s,
    q: document.querySelector('input[name=q]').value,
    url: document.querySelector('#recaptcha').baseURI,
  }));
  /* eslint-enable */
}

const getAnswer = captchaId => new Promise((resolve, reject) => {
  const qs = {
    key: api.key,
    json: 1,
    action: 'get',
    id: captchaId,
    taskinfo: 1,
  };
  const options = {
    uri: api.res,
    qs,
    json: true,
  };

  request(options)
    .then((res) => {
      if(res.status === 1) {
        // console.log(res)
        resolve(res);
      } else {
        reject(new Error(res.request));
      }
    })
    .catch((e) => {
      reject(e);
    });
});

const submitCaptcha = () => new Promise((resolve, reject) => {
  const qs = {
    key: api.key,
    json: 1,
    method: 'userrecaptcha',
    version: 'v2',
    googlekey: params.googlekey,
    pageurl: params.pageurl,
    datas: params.datas,
    proxy: params.proxy,
    proxytype: params.proxytype,
    cookies: params.cookies,
    userAgent: params.userAgent,
  };
  const options = {
    uri: api.in,
    qs,
    json: true,
  };

  request(options)
    .then((res) => {
      if(res.status === 1) {
        resolve(res.request);
      } else {
        const msg = `${res.request}: ${res.error_text}`;
        reject(new Error(msg));
      }
    })
    .catch((e) => {
      reject(e);
    });
});


const solveMyCaptcha = captchaId => new Promise((resolve, reject) => {
  const loop = setInterval(async () => {
    try {
      const answer = await getAnswer(captchaId);
      clearInterval(loop);
      resolve(answer);
    } catch(e) {
      // typo is in the 2captcha API response
      if(e.message !== 'CAPCHA_NOT_READY') {
        clearInterval(loop);
        reject(e);
      }
    }
  }, 5000);
});


const getToken = async (options) => {
  params = options;
  try {
    const id = await submitCaptcha();
    const cap = await solveMyCaptcha(id);
    return {
      token: cap.request,
      id,
      worker: cap.user_check,
      cookies: cap.cookies,
    };
  } catch(e) {
    throw e;
  }
};


const reportAnswer = (id, success) => new Promise((resolve, reject) => {
  const qs = {
    key: api.key,
    json: 1,
    action: success ? 'reportgood' : 'reportbad',
    id,
  };
  const options = {
    uri: api.res,
    qs,
    json: true,
  };

  // console.log(options)

  request(options)
    .then((res) => {
      if(res.status === 1) {
        resolve(res.request);
      } else {
        reject(res.request);
      }
    })
    .catch((e) => {
      reject(e);
    });
});


module.exports = {
  setApiKey,
  cookiesToString,
  getCaptchaParams,
  getToken,
  reportAnswer,
};
