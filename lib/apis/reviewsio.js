const request = require('superagent');
const querystring = require('querystring');
const { decode } = require('html-entities');
const { getInitials } = require('../utils/stringUtils');

module.exports = {
  getReviews,
};

async function getReviews(options) {
  options.store = options.store.toLowerCase();
  if(options.store.includes('reviews.co.uk') || options.store.includes('reviews.io')) {
    const comps = options.store.split('/');
    options.store = comps.pop();
  }
  const paramsToString = querystring.stringify(options);
  const baseURL = `https://api.reviews.co.uk/merchant/reviews?${paramsToString}`;
  const res = await request.get(baseURL);

  return parseReviews(res.body, options);
}

function parseReviews(reviewsBody, options) {
  const reviewsArray = [];
  const storeName = reviewsBody.store;

  reviewsBody.reviews.forEach((review, idx) => {
    const reviewObject = {};
    reviewObject.storeId = options.store;
    reviewObject.rating = review.rating;
    reviewObject.authorName = getReviewerName(review.reviewer);
    reviewObject.reviewId = review.store_review_id;
    reviewObject.initials = getInitials(reviewObject.authorName);
    reviewObject.time = new Date(review.date_created);
    reviewObject.text = review.comments;
    reviewObject.storeName = storeName;
    reviewsArray.push(reviewObject);
  });

  return reviewsArray;
}

function getReviewerName(reviewer) {
  let fullname = '';
  // eslint-disable-next-line max-len
  // decode is used twice due to a bug in reviewsio (they encode twice) resulting in &amp;quot; in text
  if(reviewer.first_name && reviewer.first_name.length > 0) {
    const firstName = decode(decode(reviewer.first_name)).replace(/['"]+/g, '');
    if(!firstName) {
      return '';
    }
    fullname += firstName;
  }
  if(reviewer.last_name && reviewer.last_name.length > 0) {
    fullname += ` ${decode(decode(reviewer.last_name)).replace(/['"]+/g, '')}`;
  }
  return fullname;
}
