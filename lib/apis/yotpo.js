const request = require('superagent');
const querystring = require('querystring');
const { getInitials } = require('../utils/stringUtils');

module.exports = {
  getReviews,
};


async function getReviews(options) {
  const res = await request.get(`https://api.yotpo.com/v1/widget/${options.token}/reviews.json`).query({
    per_page: options.per_page || 10,
    page: 1,
  });
  return parseReviews(res.body.response.reviews, options);
}

function parseReviews(reviewsBody, options) {
  const reviewsArray = [];
  if(reviewsBody.length === 0) return [];

  reviewsBody.forEach((review, idx) => {
    const reviewObject = {};
    reviewObject.yotpoAppId = options.token;
    reviewObject.reviewId = review.id;
    reviewObject.productId = review.product_id;
    reviewObject.profilePhotoUrl = getReviewPhoto(review);
    reviewObject.authorName = review.user.display_name;
    reviewObject.rating = review.score;
    reviewObject.text = review.content;
    reviewObject.time = new Date(review.created_at);
    reviewsArray.push(reviewObject);
  });

  return reviewsArray;
}

function getReviewPhoto(review) {
  if(review.user && review.user.is_social_connected === 1) {
    return review.user.social_image;
  } if(review.images_data && review.images_data.length > 0) {
    return review.images_data[0].thumb_url;
  }
  return null;
}
