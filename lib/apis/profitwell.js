/* eslint-disable no-underscore-dangle */
const request = require('superagent');

const clients = {};

/**
 * @param apiKey
 * @returns {ProfitWellClient}
 */
module.exports.getClient = function getClient(apiKey) {
  if(!apiKey || !apiKey.length) {
    throw new Error('invalid API key');
  }

  let client = clients[apiKey];
  if(!client) {
    client = new Client(apiKey);
    clients[apiKey] = client;
  }
  return client;
};

module.exports.INTERVALS = {
  month: 'month',
  year: 'year',
};

module.exports.CHURN_TYPES = {
  voluntary: 'voluntary',
  delinquent: 'delinquent', // payment issues (declined, funds, etc)
};

module.exports.STATUS = {
  active: 'active',
  churned: 'churned',
};

const ENDPOINTS = {
  subscriptions: 'https://api.profitwell.com/v2/subscriptions/',
  users: 'https://api.profitwell.com/v2/users',
  unchurn: 'https://api.profitwell.com/v2/unchurn',
  customers: 'https://api.profitwell.com/v2/customers',
};

/**
 * @class ProfitWellClient
 * @param apiKey
 * @constructor
 */
function Client(apiKey) {
  this.apiKey = apiKey;
  this.headers = {
    Authorization: this.apiKey,
    'content-type': 'application/json',
  };
}

/**
 * @memberOf ProfitWellClient#
*  @param {object} params the request parameters
 * @param {string} params.accountId the account's id
 * @param {string} params.email the account's email
 * @param {string} params.subId the account's subscription id (to identify events on it)
 * @param {string} params.planName the account's contract/plan name/id
 * @param {string|number} params.value the subscription's value (in cents)
 * @param {string} params.interval the subscription interval (month/year) use `INTERVALS` object
 * @param {number|date?} params.startDate the subscription start date
 */
Client.prototype.createSubscription = function createSubscription({
  accountId,
  email,
  subId,
  planName,
  value,
  interval,
  startDate,
}) {
  if(!accountId && !email) {
    return null;
  }

  const { INTERVALS } = module.exports;
  let planInterval = INTERVALS.month;
  if(INTERVALS[interval]) {
    planInterval = interval;
  }

  const body = {
    ...(accountId && { user_alias: accountId }),
    email,
    ...(subId && { subscription_alias: subId }),
    plan_id: planName,
    plan_interval: planInterval,
    value: Math.trunc(value),
    effective_date: getTimestamp(startDate),
  };
  return this.__makeRequest(ENDPOINTS.subscriptions, 'post').send(body);
};

/**
 * @memberOf ProfitWellClient#
 * @param subId
 * @param {object} params
 * @param {string} params.planName
 * @param {string} params.interval the subscription interval (month/year) use `INTERVALS` object
 * @param {number} params.value
 * @param {date|number?} params.startDate
 */
Client.prototype.updateSubscription = function updateSubscription(subId, params) {
  const url = `${ENDPOINTS.subscriptions + subId}/`;
  return this.__makeRequest(url, 'put').send({
    plan_id: params.planName,
    plan_interval: params.interval,
    value: Math.trunc(params.value),
    effective_date: getTimestamp(params.startDate),
  });
};

/**
 * @memberOf ProfitWellClient#
 * @param subscriptionId
 * @param {Date|number} untilDate
 * @param {string|null?} churnType use this file's CHURN_TYPES
 */
Client.prototype.churnSubscription = function churnSubscription(subscriptionId, untilDate, churnType = null) {
  const effectiveDate = getTimestamp(untilDate);
  const params = { effective_date: effectiveDate };
  if(module.exports.CHURN_TYPES[churnType]) {
    params.churn_type = churnType;
  }

  const url = `${ENDPOINTS.subscriptions + subscriptionId}/`;
  return this.__makeRequest(url, 'delete').query(params);
};

/**
 * @memberOf ProfitWellClient#
 * @param subId
 */
Client.prototype.unchurn = function unchurn(subId) {
  if(!subId) {
    return Promise.reject(new Error('subId is invalid'));
  }

  const url = `${ENDPOINTS.unchurn}/${subId}/`;
  return this.__makeRequest(url, 'put');
};

/**
 * @memberOf ProfitWellClient#
 * @param userId
 * @returns {*}
 */
Client.prototype.getSubscriptionsForUser = function getSubscriptionsForUser(userId) {
  if(!userId) {
    return Promise.reject(new Error('userId is invalid'));
  }

  const url = `${ENDPOINTS.users}/${userId}/`;
  return this.__makeRequest(url, 'get').then(res => res.body);
};

/**
 * @memberOf ProfitWellClient#
 * @param userId
 * @param subId
 * @returns {Promise<*>}
 */
Client.prototype.getSubscriptionHistory = async function getSubscriptionHistory(userId, subId) {
  if(!userId) {
    return Promise.reject(new Error('userId is invalid'));
  }

  const subscriptions = await this.getSubscriptionsForUser(userId);
  return subscriptions && subscriptions.filter(sub => sub.subscription_alias === subId);
};

/**
 * @memberOf ProfitWellClient#
 * @param {object?} options - Optional parameters for filtering and pagination
 * @param {string|Date|number?} options.startDate - Get customers updated on or after this date
 * @param {string|Date|number?} options.endDate - Get customers updated before this date
 * @param {string?} options.email - Filter by customer email
 * @param {number?} options.page - Page number (default: 1, max: 100000/per_page)
 * @param {number?} options.perPage - Customers per page (default: 250, max: 250)
 * @param {string?} options.sort - Sort direction "asc" or "desc" (default: "asc")
 * @returns {Promise<*>}
 */
Client.prototype.getCustomers = function getCustomers({
  startDate,
  endDate,
  email,
  page,
  perPage,
  sort,
} = {}) {
  const queryParams = {};
  if(startDate) {
    queryParams.start_date = startDate;
  }
  if(endDate) {
    queryParams.end_date = endDate;
  }
  if(email) {
    queryParams.email = email;
  }
  if(page) {
    queryParams.page = page;
  }
  if(perPage) {
    queryParams.per_page = Math.min(perPage, 250); // Ensure max limit
  }
  if(sort && ['asc', 'desc'].includes(sort)) {
    queryParams.direction = sort;
  }
  return this.__makeRequest(ENDPOINTS.customers, 'get')
    .query(queryParams)
    .then(res => res.body);
};

/**
 * @memberOf ProfitWellClient#
 * @param {object?} options - Optional parameters for filtering (pagination handled automatically)
 * @param {string|Date|number?} options.startDate - Get customers updated on or after this date
 * @param {string|Date|number?} options.endDate - Get customers updated before this date
 * @param {string?} options.email - Filter by customer email
 * @param {string?} options.sort - Sort direction "asc" or "desc" (default: "asc")
 * @returns {Promise<Array>} - Promise resolving to array of all customers
 */
Client.prototype.getAllCustomers = async function getAllCustomers({
  startDate,
  endDate,
  email,
  sort,
} = {}) {
  const allCustomers = [];
  let page = 1;
  while(page <= 100) {
    // eslint-disable-next-line no-await-in-loop
    const customers = await this.getCustomers({
      startDate,
      endDate,
      email,
      sort,
      page,
      perPage: 250,
    });
    if(customers && customers.length > 0) {
      allCustomers.push(...customers);
      if(customers.length < 250) {
        break;
      }
      page += 1;
    } else {
      break;
    }
  }
  return allCustomers;
};

Client.prototype.deleteCustomer = function deleteCustomer(customerId) {
  if(!customerId) {
    return null;
  }
  const url = `${ENDPOINTS.users}/${customerId}/`;
  this.__makeRequest(url, 'delete');
  return null;
};

Client.prototype.__makeRequest = function __makeRequest(url, method) {
  return request[method](url).set(this.headers);
};

function getTimestamp(value) {
  let retval = Math.trunc(Date.now() / 1000) + 100;
  if(value) {
    if(value.getTime) {
      retval = Math.trunc(value.getTime() / 1000);
    } else if(typeof value === 'number') {
      retval = Math.trunc(value / 1000);
    }
  }
  return retval;
}
