/**
 * 1. Get data entry: http://picasaweb.google.com/data/entry/api/user/<EMAIL>?alt=json
 * 2. Take 'entry.gphoto$thumbnail': https://lh3.googleusercontent.com/-ppVMSwmFJ7I/AAAAAAAAAAI/AAAAAAAAAAA/nFq_8O8ZUDc/s64-c/106902573340080381759.jpg
 * 3. Replace "s64" with "s<number>" to get the size you want
 */

/* ----- */

const request = require('superagent');
const { ErrorFactory } = require('../errors/index');
const logger = require('../logger/index')('picasa');

module.exports = {
  getProfile(email) {
    logger.warning('picasa getProfile is deprecated');
    return;

    if(!email) return Promise.reject(ErrorFactory('email must be a valid string'));

    const url = `http://picasaweb.google.com/data/entry/api/user/${email}?alt=json`;
    logger.info({ email, url }, 'fetching picasa profile');

    return request.get(url).then((res) => {
      logger.info({ response: res.body }, 'got picasa profile');
      const entry = res.body && res.body.entry;
      if(entry) return entry;
      return null;
    }).catch((err) => {
      logger.error({ err }, 'fetch profile failed');
      return null;
    });
  },
};
