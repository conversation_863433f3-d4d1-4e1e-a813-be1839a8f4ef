/* eslint-disable func-names */
const request = require('superagent');
const cheerio = require('cheerio');
const strUtil = require('./../utils/stringUtils');
const urlUtil = require('../utils/urlUtils');
const cryptoUtils = require('../utils/cryptoUtils');

require('superagent-proxy')(request);

const EVENTS = {
  purchase: 'Purchase',
  lead: 'Lead',
  registration: 'CompleteRegistration',
};

const getApiBaseUri = v => `https://graph.facebook.com/${v || 'v20.0'}`;

const getPagesApiUrl = () => `${getApiBaseUri()}/me/accounts`;

const getTokenInfoUrl = () => `${getApiBaseUri()}/debug_token`;

const getUserAccessTokenApiUrl = () => `${getApiBaseUri()}/oauth/access_token`;

const getPageAccessTokenApiUrl = pageId => `${getApiBaseUri()}/${pageId}`;

async function getRatings(pageId, options) {
  return getGraph(`${getApiBaseUri()}/${pageId}/ratings`, options);
}

/**
 * @param {string} uri
 * @param {object} options
 * @param {string} options.access_token
 * @returns {Promise<request.Response>}
 */
async function getGraph(uri, options) {
  return request
    .get(uri)
    .query(options)
    .accept('json')
    .then((res) => {
      if(res.status === 200) {
        return res.body;
      }
      throw new Error(res.body || res.body.text);
    });
}

const getPages = async (options) => {
  const uri = getPagesApiUrl();
  const result = await getGraph(uri, options);
  return result;
};

const getTokenInfo = async (options) => {
  const uri = getTokenInfoUrl();
  const result = await getGraph(uri, options);
  return result;
};

const getUserAccessToken = async (options) => {
  const uri = getUserAccessTokenApiUrl();
  const result = await getGraph(uri, options);
  return result;
};

const getPageAccessToken = async (pageId, options) => {
  const uri = getPageAccessTokenApiUrl(pageId);
  const result = await getGraph(uri, options);
  return result;
};

function getPageLikesGraph(pageId, pageToken) {
  return getGraph(`${getApiBaseUri('v20.0')}/${pageId}`, {
    access_token: pageToken, fields: 'followers_count,fan_count',
  }).then((body) => {
    if(body.followers_count > body.fan_count) {
      return body.followers_count;
    }
    return body.fan_count;
  });
}

const getUserPicture = (userId, query) => {
  if(!userId) return null;

  const uri = `${getApiBaseUri()}/${userId}/picture`;
  return getGraph(uri, query);
};

const getPageLikes = async (pageName, { proxy } = {}) => {
  if(strUtil.isEmptyString(pageName)) return null;

  let parsedName = urlUtil.encode(pageName);
  const pathName = urlUtil.getPathName(pageName);

  if(pathName) {
    parsedName = strUtil.getPathComponent(pathName, 1);
    if(!parsedName) return null;
  }

  return getWidgetLikes(parsedName, { proxy });

  // const url = `https://www.facebook.com/pg/${parsedName}/community/`;
  // const url2 = `https://www.facebook.com/${parsedName}/`;

  // let error = null;
  // const [elementLikes, likes] = await Promise.all([
  //   getElementLikes(url).catch((e) => { error = e; }),
  //   getLikes(url2).catch((e) => { error = e; }),
  // ]);
  // if(error && !elementLikes && !likes) {
  //   throw error;
  // }
  // return elementLikes || likes;
};

async function getWidgetLikes(path, { proxy } = {}) {
  const url = `https://www.facebook.com/v11.0/plugins/page.php?href=https%3A%2F%2Fwww.facebook.com%2F${path}&adapt_container_width=true&channel=https%3A%2F%2Fstaticxx.facebook.com%2Fx%2Fconnect%2Fxd_arbiter%2F%3Fversion%3D46%23cb%3Df2a4c66e30d9594%26domain%3Dcdpn.io%26origin%3Dhttps%253A%252F%252Fcdpn.io%252Ff58d156a7e43ec%26relation%3Dparent.parent&container_width=703&hide_cover=true&locale=en_US&sdk=joey&show_facepile=true&small_header=false&tabs=timeline&width=500`;
  let widgetRes;
  if(proxy) {
    widgetRes = await request.get(url).proxy(proxy);
  } else {
    widgetRes = await request.get(url);
  }
  const $ = cheerio.load(widgetRes.text);
  const likesElement = $('div').filter(function () {
    const text = $(this).text();
    return /^[0-9]/.test(text) && text.includes('likes');
  });
  if(likesElement && likesElement.text()) {
    return likesElement.text();
  }
  throw Error('likes element not found');
}

async function getElementLikes(url) {
  return request.get(url).set('Cookie', 'locale=en_US')
    .then((res) => {
      if(res.statusCode === 200) {
        const $ = cheerio.load(res.text);
        const likesElement = $('._3xom');
        if(!likesElement) return null;

        let result = likesElement.first().text();
        if(!strUtil.hasNumbers(result)) {
          result = likesElement.last().text();
          if(!strUtil.hasNumbers(result)) {
            return null;
          }
        }
        return result;
      }
      throw new Error(res.body || res.body.text);
    });
}

async function getLikes(url) {
  return request.get(url).set('Cookie', 'locale=en_US')
    .then((res) => {
      if(res.statusCode === 200) {
        const $ = cheerio.load(res.text);
        const likesText = getLikesText($);
        if(likesText) {
          const components = likesText.split(' ');
          const numberComps = components.filter(comp => strUtil.hasNumbers(comp));
          return numberComps[0];
        }
      }
      throw new Error(res.body || res.text);
    });
}

function getLikesText($) {
  let likes = $('._ikh>._4bl9').first().text();
  if(!likes) {
    likes = $('meta[property="og:description"]').attr('content');
  }
  if(likes) {
    const components = likes.split(' ');
    const likeIndex = components.indexOf('likes');
    if(likeIndex > -1) {
      likes = components[likeIndex - 1];
    }
  }
  return likes;
}

function sendEvent({
  pixelId, token, id, event, email, ip, ua, sourceUrl, customData,
}) {
  if(!Object.values(EVENTS).includes(event)) {
    throw new Error('invalid event, use EVENTS enum');
  }
  const ev = {
    event_id: id,
    event_name: event,
    event_time: Math.trunc(Date.now() / 1000),
    action_source: 'website',
    ...(sourceUrl && { event_source_url: sourceUrl }),
    user_data: {
      em: [cryptoUtils.sha256(email)],
      ...(ip && { client_ip_address: ip }),
      ...(ua && { client_user_agent: ua }),
    },
    ...(customData && { custom_data: customData }),
  };
  const url = `${getApiBaseUri('v10.0')}/${pixelId}/events?access_token=${token}`;
  return request.post(url).send({
    data: [ev],
    // test_event_code: 'TEST96761',
  });
}

module.exports = {
  getApiBaseUri,
  getPagesApiUrl,
  getUserAccessTokenApiUrl,
  getPageAccessTokenApiUrl,
  getRatings,
  getReviews: getRatings,
  getPages,
  getTokenInfo,
  getUserAccessToken,
  getPageAccessToken,
  getUserPicture,
  getPageLikes,
  getPageLikesGraph,
  sendEvent,
  EVENTS,
};
