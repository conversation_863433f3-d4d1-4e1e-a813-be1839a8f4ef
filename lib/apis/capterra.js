const uuid = require('uuid');
const cheerio = require('cheerio');
const moment = require('moment');
const superagent = require('superagent');
const strUtil = require('../utils/stringUtils');
const urlUtil = require('../utils/urlUtils');

const USER_AGENT = 'Mozilla/5.0 (Linux; Android 7.0; Moto G (4)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4695.0 Mobile Safari/537.36 Chrome-Lighthouse 622';

module.exports = {
  getReviews,
  getProductId,
};

async function getReviews(originalUrl, { lowestRating = 4, token }) {
  const url = buildCapterraUrl(originalUrl);
  const productId = getProductId(url);
  // const response = await scrapfly.get(url).set('User-Agent', USER_AGENT).then((res) => res.result.content);
  const response = await superagent.get('https://api.scrape.do').query({
    token,
    url,
    customHeaders: true,
  }).set('User-Agent', USER_AGENT)
    .then(res => res.text);
  const $ = cheerio.load(response);
  const reviews = [];
  $('[data-test-id="review-cards-container"] > div').each((index, element) => {
    const reviewId = uuid.v4();
    const authorName = $(element).find('span').filter((i, el) => $(el).hasClass('font-semibold') || $(el).css('font-weight') === 'bold').first()
      .text()
      .trim();
    const reviewerInfoBlock = $(element).find('span').filter((i, el) => $(el).text().trim() === authorName).parent();
    const reviewerInfoLines = reviewerInfoBlock.html() && reviewerInfoBlock.html().split('<br>');
    if(!reviewerInfoLines || !reviewerInfoLines.length) {
      return;
    }
    const authorTitle = reviewerInfoLines[1] ? cheerio.load(reviewerInfoLines[1]).text().trim() : '';
    const reviewTitle = $(element).find('h3').first().text()
      .replace(/["']+/g, '')
      .trim();
    const dateText = $(element).find('h3').first().nextAll('div')
      .filter((i, el) => /\w+ \d{1,2}, \d{4}/.test($(el).text()))
      .first()
      .text()
      .trim();
    const time = new Date(moment(dateText, 'MMMM D, YYYY').format('MMMM D, YYYY'));
    const rating = $(element).find('div[data-testid="Overall Rating-rating"] i[aria-label="star-full"]').length
                 || parseFloat($(element).find('div[data-testid="Overall Rating-rating"] span').first().text()) || null;
    const pros = $(element).find('span').filter((i, el) => $(el).text().trim() === 'Pros').parent()
      .find('p')
      .first()
      .text()
      .trim();
    const cons = $(element).find('span').filter((i, el) => $(el).text().trim() === 'Cons').parent()
      .find('p')
      .first()
      .text()
      .trim();
    const text = [pros, cons].filter(Boolean).join('\n');
    let profilePhotoUrl = $(element).find('img').attr('src') || '';
    if(!profilePhotoUrl) {
      profilePhotoUrl = '';
    }
    const review = {
      reviewId,
      productId,
      rating,
      reviewTitle,
      text,
      time,
      profilePhotoUrl,
      authorName,
      authorTitle,
    };
    if(rating >= lowestRating) {
      reviews.push(review);
    }
  });
  return reviews.slice(0, 30);
}

function getProductId(id) {
  if(strUtil.isEmptyString(id)) return null;
  const pathName = urlUtil.getPathName(id);
  if(pathName) {
    return strUtil.getPathComponent(pathName, 2);
  }
  return id;
}

function buildCapterraUrl(originalUrl) {
  let url = null;
  try {
    url = new URL(originalUrl);
  } catch(err) {
    // handled in the next check
  }
  const pathComps = url && url.pathname.split('/');
  // find a path component that has only numbers, that's the productId, the name comes right after usually
  const productIdx = pathComps && pathComps.findIndex(comp => /^\d+$/.test(comp));
  if(!url || !url.hostname.includes('capterra') || productIdx === -1) {
    throw new Error('Invalid Capterra URL');
  }
  const productId = pathComps[productIdx];
  const productName = pathComps[productIdx + 1];
  return `https://www.capterra.com/p/${productId}/${productName}/reviews/`;
}
