const request = require('superagent');
const config = require('../../config/index');

const ErrorFactory = require('../errors/ErrorFactory');
const logger = require('../logger/index')('mailerlite');
const constants = require('../constants/index');
const notifierService = require('../../app/common/notifier.service');

const API_KEY = config.mailerlite.apiKey;

module.exports = {
  /**
   * @param email
   * @param group The group to subscribe the email to
   * @param fields
   */
  subscribe(email, group, fields) {
    return canSendEmail().then((active) => {
      if(!active) return null;
      const API = `https://api.mailerlite.com/api/v2/groups/${group}/subscribers`;

      return makeRequest(API).send({ email, fields });
    }).then((res) => {
      if(!res) return null;
      logger.info({ status: res.statusCode, response: res.body }, 'subscribe success');
      return res;
    }).catch((err) => {
      logger.error({ err }, 'subscribe failed');
      throw err;
    });
  },

  unsubscribe(email) {
    return this.updateSubscriber(email, {}, 'unsubscribed');
  },

  updateSubscriber(email, fields, type = null) {
    const API = 'https://api.mailerlite.com/api/v2/subscribers';
    return canSendEmail().then((active) => {
      if(!active) return null;

      const url = `${API}/${email}`;
      return makeRequest(url, constants.METHODS.PUT).send({ fields, ...(type && { type }) });
    }).then((res) => {
      if(!res) return null;
      logger.info({ status: res.statusCode, response: res.body }, 'update subscriber success');
      return res;
    }).catch((err) => {
      if(shouldReportError(err)) {
        logger.error({ err }, 'update subscriber failed');
        throw err;
      }
    });
  },

  async batch(requests) {
    const active = await canSendEmail();
    if(!active || !requests || !requests.length) {
      return null;
    }
    return makeRequest('https://api.mailerlite.com/api/v2/batch', constants.METHODS.POST)
      .send({ requests }).catch((err) => {
        if(shouldReportError(err)) {
          notifierService.notifyError(err, 'batch subscriber update failed');
        }
      });
  },
};

function canSendEmail() {
  return new Promise((resolve, reject) => {
    if(!API_KEY) {
      return reject(ErrorFactory('bad api key'));
    }
    return resolve(config.mailerlite && config.mailerlite.active);
  });
}

function makeRequest(url, method) {
  const headers = { 'X-MailerLite-ApiKey': API_KEY };
  switch(method) {
  default:
  case constants.METHODS.POST:
    return request.post(url).set(headers);

  case constants.METHODS.PUT:
    return request.put(url).set(headers);
  }
}

function shouldReportError(err) {
  return err && err.response && err.response.status !== 422 && !err.message.includes('code 422');
}
