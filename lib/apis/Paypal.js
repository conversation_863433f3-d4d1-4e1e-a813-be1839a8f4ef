const url = require('url');
const superagent = require('superagent');

const SANDBOX = 'https://api-m.sandbox.paypal.com';
const PROD = 'https://api-m.paypal.com';

class Paypal {
  static verifyIPN({
    rawBody, userAgent = 'Node-IPN-Verifier', sandbox = false,
  }) {
    const verifyUrl = sandbox
      ? 'https://ipnpb.sandbox.paypal.com/cgi-bin/webscr'
      : 'https://ipnpb.paypal.com/cgi-bin/webscr';
    return superagent
      .post(verifyUrl)
      .set({ 'User-Agent': userAgent })
      .send(`cmd=_notify-validate&${rawBody}`)
      .then(verifyRes => verifyRes && verifyRes.text === 'VERIFIED');
  }

  static getClient(clientId, secret, sandbox = false) {
    if(clientId && secret) {
      return new Paypal(clientId, secret, sandbox);
    }
    throw new Error('Paypal clientId and secret required');
  }

  constructor(clientId, secret, sandbox = false) {
    this.clientId = clientId;
    this.secret = secret;
    this.sandbox = sandbox;
    this.request = superagent.agent().use((request) => {
      const prefix = this.sandbox ? SANDBOX : PROD;
      if(request.url[0] === '/' || !request.url.startsWith('https://')) {
        request.url = url.resolve(prefix, request.url);
      }
      return request;
    });
  }

  refreshAccessToken() {
    if(this.accessToken && this.expires > Date.now()) {
      return this.accessToken;
    }
    return this.request
      .post('/v1/oauth2/token')
      .auth(this.clientId, this.secret)
      .type('form')
      .send({ grant_type: 'client_credentials' })
      .then((res) => {
        const { access_token: accessToken, expires_in: expires } = res.body;
        this.accessToken = accessToken;
        this.expires = Date.now() + (expires - 100) * 1000;
        this.request.auth(accessToken, { type: 'bearer' });
        return accessToken;
      });
  }

  async getSubscription(id) {
    await this.refreshAccessToken();
    return this.request
      .get(`v1/billing/subscriptions/${id}`)
      .then(res => res.body);
  }

  async updateSubscription(id, {
    planId, setupFee, returnUrl, cancelUrl,
  }) {
    await this.refreshAccessToken();
    // add application context cancel/return URLs
    const params = {
      plan_id: planId,
      application_context: {
        return_url: returnUrl,
        cancel_url: cancelUrl,
      },
    };
    if(setupFee) {
      // doesn't work ¯\_(ツ)_/¯
      params.plan = {
        payment_preferences: {
          // auto_bill_outstanding: true,
          setup_fee: {
            currency_code: 'USD',
            value: `${setupFee}`,
          },
          setup_fee_failure_action: 'CANCEL',
        },
      };
    }
    return this.request
      .post(`/v1/billing/subscriptions/${id}/revise`)
      .send(params)
      .then(res => res.body);
  }

  async capture(subscriptionId, { note, amount }) {
    // doesnt work ZERO_OUTSTANDING_BALANCE
    await this.refreshAccessToken();
    return this.request.post(`/v1/billing/subscriptions/${subscriptionId}/capture`).send({
      note,
      capture_type: 'OUTSTANDING_BALANCE',
      amount: {
        currency_code: 'USD',
        value: `${amount}`,
      },
    }).then(res => res.body);
  }

  async addBalance(subscriptionId, amount) {
    // Doesnt work, getting error "AMOUNT_GREATER_THAN_OUTSTANDING_BALANCE"
    await this.refreshAccessToken();
    return this.request.patch(`/v1/billing/subscriptions/${subscriptionId}`).send([{
      op: 'replace',
      path: '/billing_info/outstanding_balance',
      value: {
        currency_code: 'USD',
        value: `${amount}`,
      },
    }]).then(res => res.statusCode === 204);
  }

  async verifyWebhook({ headers, body, webhookId }) {
    await this.refreshAccessToken();
    const authAlgo = headers['paypal-auth-algo'];
    const certUrl = headers['paypal-cert-url'];
    const transmissionId = headers['paypal-transmission-id'];
    const transmissionSig = headers['paypal-transmission-sig'];
    const transmissionTime = headers['paypal-transmission-time'];
    return this.request
      .post('/v1/notifications/verify-webhook-signature')
      .send({
        auth_algo: authAlgo,
        cert_url: certUrl,
        transmission_id: transmissionId,
        transmission_sig: transmissionSig,
        transmission_time: transmissionTime,
        webhook_id: webhookId,
        webhook_event: body,
      })
      .then(res => res.body.verification_status === 'SUCCESS');
  }
}

module.exports = Paypal;
