/* eslint-disable no-loop-func,no-await-in-loop */
const request = require('superagent');
const _ = require('lodash');

module.exports = {
  getReviews,
};

async function getReviews(placeId, { minRating = 4, numReviews = 100 } = {}) {
  const reviews = [];
  let page = 0;
  while(reviews.length < numReviews) {
    page += 1;
    const url = `https://api.feefo.com/api/10/reviews/all?merchant_identifier=${placeId}&page_size=100&page=${page}&sort=-created_date`;
    const rawReviews = await request.get(url).then(res => res.body && res.body.reviews);

    rawReviews.forEach((review) => {
      const rating = _.get(review, 'service.rating.rating', null) || _.get(review, 'products[0].rating.rating', null);
      if(rating && rating >= minRating) {
        reviews.push(review);
      }
    });
    // if we got less than 100 reviews, it's the last page
    if(rawReviews.length < 100) {
      break;
    }
  }

  return reviews;
}
