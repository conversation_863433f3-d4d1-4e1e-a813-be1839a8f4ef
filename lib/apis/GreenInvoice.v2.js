const request = require('superagent');

const testBaseURL = 'https://sandbox.d.greeninvoice.co.il';
const prodBaseURL = 'https://api.greeninvoice.co.il';

class GreenInvoice {
  constructor(apiKey, secret, test = false) {
    this.apiKey = apiKey;
    this.secret = secret;
    this.baseURL = test ? testBaseURL : prodBaseURL;
  }

  static getClient(apiKey, secret, test = false) {
    if(!this.clients) this.clients = {};
    if(!apiKey || !secret) {
      return null;
    }
    const clientId = `${apiKey}-${secret}`;
    const cachedClient = this.clients[clientId];
    const client = cachedClient || new GreenInvoice(apiKey, secret, test);
    if(!cachedClient) {
      this.clients[clientId] = cachedClient;
    }
    return client;
  }

  static getCardType(brand) {
    if(!brand) {
      return GreenInvoice.cardTypes.visa;
    }
    let retval = GreenInvoice.cardTypes.visa;
    const lowercase = brand.toLowerCase();
    if(lowercase.includes('visa')) {
      retval = GreenInvoice.cardTypes.visa;
    } else if(lowercase.includes('mastercard')) {
      retval = GreenInvoice.cardTypes.mastercard;
    } else if(lowercase.includes('amex') || lowercase.includes('american')) {
      retval = GreenInvoice.cardTypes.amex;
    } else if(lowercase.includes('diners')) {
      retval = GreenInvoice.cardTypes.diners;
    } else if(lowercase.includes('isra')) {
      retval = GreenInvoice.cardTypes.isracard;
    }
    return retval;
  }

  getIpnURL() {
    return `${this.baseURL}/api/v1/plugins/paypal/ipn`;
  }

  /**
   *
   * @param {object} data
   * @param {string?} data.name
   * @param {string?} data.email
   * @param {string?} data.taxId
   * @param {number?} data.page
   * @param {number?} data.pageSize
   * @returns {Promise<*>}
   */
  async searchClients(data) {
    const headers = await this.getHeaders();
    const res = await request.post(`${this.baseURL}/api/v1/clients/search`).set(headers).send(data);
    return res.body.items;
  }

  async addInvoiceCreditCard({
    client, date = new Date(),
    vat = false, currency, fullPrice, price, discount,
    description, lineDescription, cardType, cardNum, remarks = '', footer = '', contractId,
  }) {
    let { country } = client;
    if(country === 'UK') {
      country = 'GB';
    }
    const lang = vat ? 'he' : 'en';
    const vatType = vat ? GreenInvoice.vatTypes.include : GreenInvoice.vatTypes.exempt;
    const vatTypeInner = vat ? 1 : 2;
    const data = {
      type: GreenInvoice.documentTypes.invoiceReceipt,
      lang,
      description,
      currency: currency.toUpperCase(),
      vatType,
      remarks,
      footer,
      rounding: false,
      client: {
        ...client, // id, name, emails, taxId, country (VAT)
        ...(client && client.name && client.name.includes('www.') && { name: client.name.replace('www.', '') }),
        country,
        add: true,
      },
      income: [{
        description: lineDescription || description,
        quantity: 1,
        price: fullPrice || price,
        currency: currency.toUpperCase(),
        vatType: vatTypeInner,
        catalogNum: contractId,
      }],
      payment: [{
        type: GreenInvoice.paymentTypes.creditCard,
        date: date.toISOString().split('T')[0],
        price,
        currency: currency.toUpperCase(),
        cardType,
        cardNum,
        dealType: GreenInvoice.dealTypes.regular,
      }],
    };

    if(discount) {
      data.income.push({
        description: discount.name,
        quantity: 1,
        price: -discount.amount,
        vatType: vatTypeInner,
        currency: 'USD',
        catalogNum: discount.code,
      });
    }

    const url = `${this.baseURL}/api/v1/documents`;
    const headers = await this.getHeaders();
    return request
      .post(url)
      .set(headers)
      .send(data)
      .then(res => res.body);
  }

  async cancelInvoiceWithId(docId) {
    const headers = await this.getHeaders();
    const doc = await request.get(`${this.baseURL}/api/v1/documents/${docId}`).set(headers).then(res => res.body);
    if(!doc) {
      throw new Error('invoice not found');
    }
    const {
      lang, number, client, vatType, currency, remarks, income, payment,
    } = doc;
    const reference = remarks.match(/Reference number: (\d+)/)[1];
    const commonData = {
      lang,
      currency,
      vatType,
      rounding: false,
      client,
      description: `Cancel Document ${number}`,
      remarks: `Cancel Document ${number}, Reference ${reference}`,
    };
    payment.forEach((p) => {
      p.price = -p.price;
      p.date = (new Date()).toISOString().split('T')[0];
    });

    const invoice = await request.post(`${this.baseURL}/api/v1/documents`).set(headers).send({
      type: GreenInvoice.documentTypes.refundInvoice,
      ...commonData,
      income,
      linkedDocumentIds: [docId],
      linkType: 'cancel',
    }).then(res => res.body);
    const receipt = await request.post(`${this.baseURL}/api/v1/documents`).set(headers).send({
      type: GreenInvoice.documentTypes.receipt,
      ...commonData,
      payment,
      linkedDocumentIds: [invoice.id],
    }).then(res => res.body);

    return { invoice, receipt };
  }

  async cancelInvoice({
    id, num, clientId, clientName, description, vat = false, currency, price,
    cardType, cardNum, paypal = false, chargeId,
  }) {
    const lang = vat ? 'he' : 'en';
    const vatType = vat ? GreenInvoice.vatTypes.include : GreenInvoice.vatTypes.exempt;
    const vatTypeInner = vat ? 1 : 2;
    const client = { name: clientName, add: true };
    if(clientId) {
      client.id = clientId;
    }
    const commonData = {
      lang,
      currency: currency.toUpperCase(),
      vatType,
      rounding: false,
      client,
      description: `Cancel Document ${num}`,
      remarks: `Cancel Document ${num}, Reference ${chargeId}`,
      income: [{
        description,
        quantity: 1,
        price,
        currency: currency.toUpperCase(),
        vatType: vatTypeInner,
      }],
      linkedDocumentIds: [id],
      linkType: 'cancel',
    };
    const url = `${this.baseURL}/api/v1/documents`;
    const headers = await this.getHeaders();
    const invoiceRes = await request.post(url).timeout(60 * 1000).set(headers).send({
      type: GreenInvoice.documentTypes.refundInvoice,
      ...commonData,
      ...(id && { linkedDocumentIds: [id], linkType: 'cancel' }),
    })
      .then(res => res.body);

    const receiptRes = await request.post(url).timeout(60 * 1000).set(headers).send({
      type: GreenInvoice.documentTypes.receipt,
      ...commonData,
      payment: [{
        type: paypal ? GreenInvoice.paymentTypes.paypal : GreenInvoice.paymentTypes.creditCard,
        date: (new Date()).toISOString().split('T')[0],
        price: -price,
        currency: currency.toUpperCase(),
        dealType: GreenInvoice.dealTypes.regular,
        ...(!paypal && { cardType, cardNum }),
        ...(paypal && { transactionId: chargeId }),
      }],
      linkedDocumentIds: [invoiceRes.id],
      linkType: 'cancel',
    })
      .then((res => res.body));
    return { invoice: invoiceRes, receipt: receiptRes };
  }

  async addInvoicePayPal({
    client, date = new Date(), vat = false, currency,
    description, lineDescription, price, payerId, transactionId,
  }) {
    let { country } = client;
    if(country === 'UK') {
      country = 'GB';
    }
    const lang = vat ? 'he' : 'en';
    const vatType = vat ? module.exports.vatTypes.include : module.exports.vatTypes.exempt;
    const vatTypeInner = vat ? 1 : 2;
    const data = {
      type: module.exports.documentTypes.invoiceReceipt,
      lang,
      description,
      currency,
      vatType,
      remarks: `Reference number: ${transactionId}`,
      rounding: false,
      client: {
        ...client,
        ...(client && client.name && client.name.includes('www.') && { name: client.name.replace('www.', '') }),
        country,
        add: true,
      },
      income: [{
        description: lineDescription || description,
        quantity: 1,
        price,
        currency,
        vatType: vatTypeInner,
      }],
      payment: [{
        type: module.exports.paymentTypes.paypal,
        date: date.toISOString().split('T')[0],
        price,
        currency,
        dealType: module.exports.dealTypes.regular,
        transactionId,
        accountId: payerId,
      }],
    };
    const url = `${this.baseURL}/api/v1/documents`;
    const headers = await this.getHeaders();
    return request
      .post(url)
      .set(headers)
      .send(data)
      .then(res => res.body);
  }

  async getJWT() {
    if(!this.expires || this.expires < Date.now()) {
      this.jwt = null;

      const params = {
        id: this.apiKey,
        secret: this.secret,
      };
      const res = await request
        .post(`${this.baseURL}/api/v1/account/token`)
        .send(params);
      this.jwt = res.body.token;
      this.expires = Date.now() + 60 * 1000 * 30; // 30 minutes (30 minutes buffer)
    }
    return this.jwt;
  }

  async addClient(name) {
    const headers = await this.getHeaders();
    return request
      .post(`${this.baseURL}/api/v1/clients`)
      .set(headers)
      .send({ name })
      .then(res => res.body);
  }

  async makeRequest({ method, path, data }) {
    const headers = await this.getHeaders();
    if(method === 'post') {
      return request[method](`${this.baseURL}${path}`).set(headers).send(data).then(res => res.body);
    }
    return request[method](`${this.baseURL}${path}`).set(headers).then(res => res.body);
  }

  async getHeaders() {
    const jwt = await this.getJWT();
    return { Authorization: `Bearer ${jwt}` };
  }
}

GreenInvoice.testBaseURL = testBaseURL;
GreenInvoice.prodBaseURL = prodBaseURL;

GreenInvoice.documentTypes = {
  invoiceReceipt: 320,
  refundInvoice: 330,
  receipt: 400,
};

GreenInvoice.paymentTypes = {
  creditCard: 3,
  paypal: 5,
  other: 11,
};

GreenInvoice.cardTypes = {
  'N/A': 0,
  unknown: 0,
  isracard: 1,
  visa: 2,
  mastercard: 3,
  amex: 4,
  diners: 5,
};

GreenInvoice.dealTypes = {
  regular: 1,
};

GreenInvoice.vatTypes = {
  include: 0,
  exempt: 1,
};

module.exports = GreenInvoice;
