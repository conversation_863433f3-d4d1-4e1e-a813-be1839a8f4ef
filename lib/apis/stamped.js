const request = require('superagent');

async function getReviews(storeUrl, apiKey) {
  if(!storeUrl) {
    throw new Error('no store url was provided');
  }

  const query = {
    minRating: 4,
    take: 50,
    storeUrl,
  };
  const baseURL = 'https://stamped.io/api/widget/reviews';
  const [res1, res2] = await Promise.all([
    request.get(baseURL).query(query).catch(() => {}),
    request.get(baseURL).query({ ...query, apiKey }).catch(() => {}),
  ]);
  const retval = (res1 && res1.body) || (res2 && res2.body);
  if(!retval) {
    throw new Error('probably invalid store url or api key');
  }
  return retval;
}

module.exports = {
  getReviews,
};
