const superagent = require('superagent');
const qs = require('qs');

module.exports = {
  getReviews,
};

async function getReviews(apiKey, {
  rating: minRating = 4, tags = [], sources = [],
} = {}) {
  const query = qs.stringify({
    page: 1,
    size: 50,
    rating: minRating,
    status: ['public'],
    tags,
    sources,
  }, { arrayFormat: 'brackets' });
  const resReviews = await superagent
    .get(`https://api.shapo.io/testimonials?${query}`)
    .auth(apiKey, { type: 'bearer' })
    .then(res => res.body);
  return resReviews.filter((review) => {
    const { rating } = review;
    return rating && rating >= minRating;
  });
}
