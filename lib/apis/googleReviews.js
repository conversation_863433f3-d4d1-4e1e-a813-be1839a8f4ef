/* eslint-disable no-await-in-loop,no-loop-func,no-prototype-builtins,arrow-body-style */
const path = require('path');
const request = require('superagent');
const puppeteer = require('puppeteer-extra');
const stealthPlugin = require('puppeteer-extra-plugin-stealth');
const captchaSolver = require('./google/solver');
const { installMouseHelper } = require('./google/install-mouse-helper');
const sleep = require('../../lib/utils/sleep');

const NOT_NUM_REGEX = /[^0-9]+/g;
const DEFAULT_WAIT = 2000;

puppeteer.use(stealthPlugin());

module.exports = {
  getApiBaseUri,
  getReviews,
  addRecaptchaConfig,
  getScrapedReviews,
};

let captchaToken = null;

function addRecaptchaConfig({ id, token }) {
  if(captchaToken || !id || !token) {
    return false;
  }
  // puppeteer.use(recaptchaPlugin({
  //   provider: { id, token },
  //   visualFeedback: true, // colorize reCAPTCHAs (violet = detected, green = solved)
  // }));
  captchaToken = token;
  return true;
}

function getApiBaseUri() {
  return 'https://maps.googleapis.com/maps/api/place/details/json';
}

async function getReviews(options) {
  const uri = getApiBaseUri();
  const result = await request
    .get(uri)
    .query(options)
    .then((res) => {
      if(res.body.status === 'OK'
        && res.body.hasOwnProperty('result')
        && res.body.result.hasOwnProperty('name')
        && res.body.result.hasOwnProperty('reviews')) {
        return {
          name: res.body.result.name,
          reviews: res.body.result.reviews,
        };
      }

      if(res.body.status === 'ZERO_RESULTS') {
        return { reviews: [] };
      }

      // eslint-disable-next-line prefer-promise-reject-errors
      return Promise.reject({
        status: res.body.status,
        msg: (res.body.hasOwnProperty('error_message') ? res.body.error_message : 'An unknown error occurred'),
      });
    });
  return result;
}

async function getScrapedReviews(placeId, {
  executable = null,
  headless = true,
  proxy = null,
  screenshotFolder = null,
  lowestRating = 4,
  maximumReviews = 30,
  captchaApiKey,
}) {
  if(!placeId && placeId.length === 0) {
    return null;
  }

  let browser = null;
  let page = null;
  let reviews;
  let error = null;
  captchaToken = captchaApiKey;
  const opts = { slowMo: 250, headless };
  if(executable) {
    opts.executablePath = executable;
  }
  opts.args = [
    '--incognito',
    '--disable-gpu',
    // '--disable-dev-shm-usage', // on ubuntu it bloats /tmp folder
    '--no-sandbox',
    '--disable-desktop-notifications',
    '--disable-sync',
    ...(proxy ? [`--proxy-server=${proxy}`] : []),
  ];
  opts.ignoreDefaultArgs = [
    '--disable-dev-shm-usage',
  ];
  try {
    browser = await puppeteer.launch(opts);
    const [[firstPage], userAgent] = await Promise.all([
      browser.pages(),
      browser.userAgent(),
    ]);
    page = firstPage;
    await page.setViewport({
      width: 1024 + Math.trunc(Math.random() * 896),
      height: 768 + Math.trunc(Math.random() * 312),
    });
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:93.0) Gecko/20100101 Firefox/93.0');
    page.setDefaultTimeout(30000);
    if(!headless) {
      await installMouseHelper(page);
    }
    if(placeId.indexOf('g.page') > -1 || placeId.indexOf('/maps/') > -1 || placeId.indexOf('.app.') > -1) {
      let url = placeId;
      if(!url.startsWith('http')) {
        url = `https://${placeId}`;
      }

      await page.goto(url, { waitUntil: 'domcontentloaded' });
      // wait for URL to change (maps with place -> maps gibberish)
      await sleep(5000);
      if(captchaToken) {
        await setupCaptchaInterceptor(page);
      }
      await solveCaptcha(page, { userAgent, proxy });
      url = page.url();
      if(url.includes('/maps/') || url.includes('.app.')) {
        reviews = await scrapByMyBusinessLink(page, {
          url, lowestRating, maximumReviews, userAgent, proxy,
        });
      } else {
        reviews = await scrapeSearchPage(page, {
          lowestRating, maximumReviews, userAgent, proxy,
        });
      }
    } else if(placeId.search(/https?:\/\//) > -1) {
      let url = placeId;
      if(url.includes('local/writereview')) {
        url = url.replace('local/writereview', 'local/reviews');
      }
      reviews = await scrapeSearchPage(page, {
        url, lowestRating, maximumReviews, userAgent, proxy,
      });
    } else {
      const url = `http://search.google.com/local/reviews?hl=en-US&placeid=${placeId}`;
      reviews = await scrapeSearchPage(page, {
        url, lowestRating, maximumReviews, userAgent,
      });
    }
    if(screenshotFolder) {
      const filename = encodeURIComponent(`done-${Date.now()}-${placeId}.png`);
      await page.screenshot({ path: path.join(screenshotFolder, filename) }).catch(() => {});
    }
  } catch(err) {
    if(screenshotFolder) {
      const filename = encodeURIComponent(`error-${(Date.now())}-${placeId}.png`);
      if(page) {
        await page.screenshot({ path: path.join(screenshotFolder, filename) }).catch(() => {});
      }
    }
    error = err instanceof Error ? err : new Error(err);
  }
  try {
    if(page) {
      await page.close();
    }
    if(browser) {
      await browser.close();
      if(browser && browser.process() != null) {
        await browser.process().kill('SIGINT');
      }
    }
    // eslint-disable-next-line no-empty
  } catch(err) {}

  if(error) {
    throw error;
  }
  return reviews;
}

async function scrapeSearchPage(page, {
  url, lowestRating, maximumReviews, userAgent, proxy,
}) {
  await solveCaptcha(page, { userAgent, proxy });
  // page goto url
  if(url) {
    await Promise.all([
      page.goto(url, { waitUntil: 'networkidle0' }),
      page.waitForNavigation(),
    ]);
  }
  await solveCaptcha(page, { userAgent, proxy });
  // load page (english version)
  const pageUrl = page.url();
  const engUrl = pageUrl.includes('?') ? `${pageUrl}&hl=en-US` : `${pageUrl}?hl=en-US`;
  const navResponse = await page.goto(engUrl);
  if(navResponse && navResponse.status() >= 400) {
    throw Error(`page navigation error ${navResponse.status()}`);
  }
  const { hasCaptcha, solved } = await solveCaptcha(page, { userAgent, proxy });
  if(hasCaptcha && !solved) {
    throw Error('failed to solve captcha (search page)');
  }
  if(pageUrl.includes('/travel/')) {
    throw new Error('Hotel reviews page is not supported');
    // return scrapeHotelReviews(page);
  }
  await sleep(DEFAULT_WAIT);
  const [reviewList, reviewBody] = await Promise.all([
    page.$eval('.review-dialog-list', elem => elem.innerHTML).catch(() => null),
    page.$eval('.review-dialog-body', elem => elem.innerHTML).catch(() => null),
  ]);
  // Google's new SERP design makes scraping reviews difficult due to dynamic content.
  // We navigate to the Maps page instead, where reviews are easier to access.
  if(reviewBody) {
    const firstLink = await page.$eval('a[data-url*="/maps/place"]', el => el.href);
    if(firstLink) {
      return scrapByMyBusinessLink(page, {
        lowestRating, maximumReviews, userAgent, proxy, link: firstLink,
      });
    }
  }

  if(!reviewList) {
    await page.click('a[data-async-trigger*="reviewDialog"]').catch(() => {});
    await sleep(DEFAULT_WAIT);
  }
  // handle sorting (newest)
  // if reviews modal - click the "reviews" button on SERP
  const err = await selectSearchPageSortDropdown(page).catch(e => e);
  if(err) {
    await page.click('a[data-fid]').catch(() => {});
    await sleep(DEFAULT_WAIT);
    await selectSearchPageSortDropdown(page).catch(() => {});
  }

  // wait for reviews to load
  await sleep(DEFAULT_WAIT);

  // scroll to load all reviews
  const numReviewsStr = await page.$eval('g-review-stars + div > span', e => e.innerText).catch(() => '50');
  const numOnly = numReviewsStr.replace(NOT_NUM_REGEX, '');
  const totalReviews = parseInt(numOnly, 10);
  const totalReviewsNum = Math.min(totalReviews, maximumReviews);

  let reviewsCount = (await getSearchPageReviews(page, { minRating: lowestRating })).length;
  let scrolls = 0;
  while(scrolls < 50 && totalReviewsNum > reviewsCount) {
    scrolls += 1;
    await page.$eval('.review-dialog-list', (elem) => {
      elem.scrollBy(0, Math.floor(Math.random() * 1500 + 1000));
    });
    await sleep(DEFAULT_WAIT);
    const scrollDone = await page.$eval('.review-dialog-list', (elem) => {
      return elem.scrollTop + elem.offsetHeight === elem.scrollHeight;
    });
    if(scrollDone) {
      break;
    }
    reviewsCount = (await getSearchPageReviews(page, { minRating: lowestRating })).length;
  }

  return getSearchPageReviews(page, { minRating: lowestRating });
}

async function scrapByMyBusinessLink(page, {
  lowestRating, maximumReviews, userAgent, proxy, link,
}) {
  // load page (english version)
  let pageUrl = page.url();
  if(link) {
    pageUrl = link;
  }
  const engUrl = pageUrl.includes('?') ? `${pageUrl}&hl=en-US` : `${pageUrl}?hl=en-US`;
  const navResponse = await page.goto(engUrl);
  const { hasCaptcha, solved } = await solveCaptcha(page, { userAgent, proxy });

  if(navResponse && navResponse.status() >= 400) {
    throw Error(`page navigation error ${navResponse.status()}`);
  }
  if(hasCaptcha && !solved) {
    throw Error('failed to solve captcha (maps page)');
  }
  // get data from business main page
  // changing the selector to `rating.moreReviews` fixed the issues
  const reviewsSelector = 'button[jsaction*="moreReviews"]';
  await page.waitForSelector(reviewsSelector, { visible: true });
  const numReviewsStr = await page.$eval(`${reviewsSelector} span`, e => e.innerHTML);
  const onlyNumbers = numReviewsStr.replace(NOT_NUM_REGEX, '');
  const totalReviews = parseInt(onlyNumbers, 10);
  const totalReviewsNum = Math.min(totalReviews, maximumReviews);

  const businessNameRaw = await page.$eval('h1', elem => elem.textContent).catch(() => {});
  const businessName = businessNameRaw && businessNameRaw.trim();

  await page.click(reviewsSelector);
  await page.$eval(reviewsSelector, e => e.click()).catch(() => {});

  // handle sorting (newest)
  try {
    const sortSelector = 'button[aria-label*=Sort]';
    await page.waitForSelector(sortSelector, { visible: true, timeout: 5000 });
    await sleep(DEFAULT_WAIT);
    await page.click(sortSelector);
    // eslint-disable-next-line no-empty
  } catch(err) {}

  try {
    await page.evaluate(() => {
      const divs = [...document.querySelectorAll('div')];
      const newest = divs.find(elem => elem.innerHTML === 'Newest');
      if(newest) {
        newest.click();
      }
    });
    // eslint-disable-next-line no-empty
  } catch(err) {}

  await page.waitForSelector('[data-review-id]', { visible: true });
  const reviewsElement = await page.$('[data-review-id]');
  const reviewsElementBox = await reviewsElement.boundingBox();
  await page.mouse.move(reviewsElementBox.x + reviewsElementBox.width / 2,
    reviewsElementBox.y + reviewsElementBox.height / 2);
  const minRating = lowestRating;
  // scroll to load all reviews
  let reviewsCount = (await getMapsPageReviews(page, { minRating, businessName })).length;
  let scrolls = 0;
  while(scrolls < 50 && totalReviewsNum > reviewsCount) {
    scrolls += 1;
    await page.mouse.wheel({ deltaY: Math.floor(Math.random() * 1500 + 1000) });
    // await page.evaluate(() => {
    //   let element = null;
    //   const divs = [...document.querySelectorAll('div')];
    //   const scrollable = divs.find(d => d.scrollHeight > d.clientHeight);
    //   if(scrollable) {
    //     element = scrollable;
    //   }
    //   console.log('scroll element', element);
    //   if(!element) {
    //     throw new Error('failed to find scroll element');
    //   }
    //   const scrollAmount = Math.floor(Math.random() * 1500 + 1000);
    //   element.scrollBy(0, scrollAmount);
    //   return element.scrollTop + element.offsetHeight === element.scrollHeight;
    // });
    await sleep(DEFAULT_WAIT);
    const newReviewsCount = (await getMapsPageReviews(page, { minRating, businessName })).length;
    if(reviewsCount === newReviewsCount && scrolls % 5 === 0) {
      break;
    }
    reviewsCount = newReviewsCount;
  }

  // expand all review texts
  await page.evaluate(() => {
    let elems = document.querySelectorAll('.section-expand-review');
    elems.forEach(elem => elem.click());

    elems = document.querySelectorAll('button[jsaction*=expandReview]');
    elems.forEach(elem => elem.click());
  });
  await sleep(DEFAULT_WAIT);

  // scrap review data
  return getMapsPageReviews(page, { minRating: lowestRating, businessName });
}

async function scrapeHotelReviews(page) {
  // Rather difficult to parse, all classes are uglified and dynamicall generated
  // no specific attributes for reviews pages we can capture, etc
  // did manage to pull reviews but finding the hotel in SERP or maps
  // and opening the maps page and getting the share link
  // the SERP doesn't work as the reviews display is different of the regular one
  return null;
}

// region helpers

async function setupCaptchaInterceptor(page) {
  await page.setRequestInterception(true);
  page.on('request', async (r) => {
    if(r.url().includes('https://www.gstatic.com/recaptcha/')
      || r.url().includes('https://www.google.com/recaptcha/api.js')) {
      r.abort();
    } else {
      r.continue();
    }
  });
}

async function solveCaptcha(page, { userAgent, proxy }) {
  const hasCaptcha = await page.waitForSelector('#recaptcha', { timeout: 5000 }).catch(() => {});
  if(!hasCaptcha) {
    return { hasCaptcha };
  }
  captchaSolver.setApiKey(captchaToken);
  // eslint-disable-next-line no-underscore-dangle
  const client = await page.target().createCDPSession();
  const { cookies } = await client.send('Network.getAllCookies');
  const cookiesString = captchaSolver.cookiesToString(cookies);
  await sleep(5000);
  const captchaParams = await captchaSolver.getCaptchaParams(page);
  const captcha = await captchaSolver.getToken({
    googlekey: captchaParams.sitekey,
    datas: captchaParams.datas,
    pageurl: captchaParams.url,
    cookies: cookiesString,
    userAgent,
    proxy,
    proxytype: 'HTTPS',
  });
  const comps = captchaParams.url.split('#');
  comps[0] = `${comps[0]}&g-recaptcha-response=${captcha.token}`;
  const nextUrl = comps.join('#');
  const navRes = await page.goto(nextUrl, { waitUntil: 'networkidle0' });
  // navRes == null is succesful navigation to about:blank or same URL with different hash
  if(!navRes || (navRes && navRes.status && navRes.status() === 200)) {
    await captchaSolver.reportAnswer(captcha.id, true);
    return { hasCaptcha, solved: true };
  }
  await captchaSolver.reportAnswer(captcha.id, false);
  return { hasCaptcha, solved: false };
}

async function selectSearchPageSortDropdown(page) {
  const clicked = await page.click('div[data-sort-id="newestFirst"]').then(() => true).catch(() => false);
  if(clicked) {
    return;
  }

  const dropdown = await page.waitForSelector('g-dropdown-button', { visible: true, timeout: DEFAULT_WAIT }).catch(() => {});
  if(!dropdown) {
    await page.waitForSelector('a[data-async-trigger=reviewDialog]', { visible: true, timeout: DEFAULT_WAIT })
      .then(() => page.click('a[data-async-trigger=reviewDialog]'))
      .catch(() => {});
    await sleep(DEFAULT_WAIT);
  }
  await page.waitForSelector('g-dropdown-button', { visible: true, timeout: DEFAULT_WAIT }).catch(() => {});
  await page.click('g-dropdown-button');
  const idx = await page.evaluate(() => {
    const elements = Array.from(document.querySelectorAll('g-menu-item'));
    if(!elements || !elements.length) {
      return null;
    }
    return elements.findIndex(e => e.textContent.includes('Newest'));
  }).catch(() => {});
  if(idx >= 0) {
    const menuItems = await page.$$('g-menu-item');
    const newestButton = menuItems[idx];
    await newestButton.click();
  }
}

async function getSearchPageReviews(page, { minRating }) {
  // eslint-disable-next-line no-shadow
  return page.evaluate((minRating) => {
    const formatedReviews = [];
    const rawReviews = document.querySelectorAll('.gws-localreviews__google-review');

    rawReviews.forEach((review) => {
      const ratingString = review.querySelector('span[aria-label*="Rated"]').getAttribute('aria-label');
      const rawRating = ratingString && ratingString.split(' ')[1];
      const rating = parseInt(rawRating, 10);
      if(rating >= minRating) {
        const spans = [...review.querySelectorAll('span')];
        // Index 1 original text, Index 0 translated text
        let textElement = review.querySelectorAll('span[data-expandable-section]')[1];
        if(textElement && !textElement.textContent) {
          textElement = review.querySelectorAll('span[data-expandable-section]')[0];
        }
        let text = textElement && textElement.textContent;
        if(text && text !== '') {
          // In this case no need to click "More" button, it's already on page just hidden
          const index = text.indexOf('(Original)');
          if(index > -1) {
            text = text.substring(index + 10);
          }
        }

        let authorName = null;
        const authorElements = review.querySelectorAll('a[href*=contrib]');
        for(let i = 0; i < authorElements.length; i += 1) {
          const element = authorElements[i];
          authorName = element && element.innerText;
          // console.log('authorName', authorName, 'element', element);
          if(authorName
            && authorName.length
            && !authorName.includes('<')
            && !authorName.includes('review')
            && !authorName.includes('More')) {
            break;
          } else {
            authorName = null;
          }
        }
        const timeElement = spans.find(s => !s.children.length && s.textContent.includes('ago'));
        const businessElement = document.querySelector('.review-dialog-top > div > div > div');
        const photoElement = review.querySelector('a img');
        const urlElement = review.querySelector('a + div > div > a')
          || review.querySelector('a[href*=contrib]')
          || review.querySelector('a');
        formatedReviews.push({
          authorName,
          authorPhoto: photoElement && photoElement.getAttribute('src'),
          authorUrl: urlElement && urlElement.getAttribute('href'),
          rating,
          text,
          timeText: timeElement && timeElement.innerHTML,
          businessName: businessElement && businessElement.innerText,
        });
      }
    });
    return formatedReviews;
  }, minRating);
}

async function getMapsPageReviews(page, { minRating, businessName }) {
  // eslint-disable-next-line no-shadow
  return page.evaluate((minRating, businessName) => {
    // this happens in the browser's runtime scope
    const formatedReviews = [];
    let rawReviews = document.querySelectorAll('.section-review');
    if(!rawReviews || !rawReviews.length) {
      rawReviews = document.querySelectorAll('div[data-review-id][aria-label]');
    }

    const NOT_NUM_REGEX_INNER = /[^0-9+]/g;
    rawReviews.forEach((review) => {
      const moreBtn = review.querySelector('[jsaction*=expand]');
      if(moreBtn) {
        moreBtn.click();
      }
      const originalButton = review.querySelector('[jsaction*=showReviewInOriginal]');
      if(originalButton) {
        originalButton.click();
      }
      const spans = [...review.querySelectorAll('span')];
      let ratingElem = review.querySelector('.section-review-stars');
      if(!ratingElem) {
        ratingElem = review.querySelector('span[aria-label*=star]');
      }
      const ratingString = ratingElem && ratingElem.getAttribute('aria-label');
      const ratingNumString = ratingString && ratingString.replace(NOT_NUM_REGEX_INNER, '');
      let rating = parseInt(ratingNumString, 10);
      if(!rating || Number.isNaN(rating)) {
        ratingElem = spans.find(span => span.textContent && span.textContent.includes('/5'));
        if(ratingElem) {
          rating = parseInt(ratingElem.textContent.split('/')[0], 10);
        }
      }
      if(rating >= minRating) {
        let authorName = review.getAttribute('aria-label');
        if(!authorName) {
          const elem = review.querySelector('div[jsan*=title]');
          authorName = elem && elem.innerText;
        }
        let textElement = review.querySelector('.section-review-text');
        if(!textElement) {
          textElement = review.querySelector('span[jsan*=text]');
        }
        if(!textElement) {
          textElement = review.querySelector('div[id]');
        }
        let text = textElement && textElement.textContent && textElement.textContent.trim();
        if(text && text !== '') {
          const originalText = '(Original)';
          const index = text.indexOf(originalText);
          if(index > -1) {
            // Delete '(Original)/n' in the beginning of the string
            text = text.substring(index + originalText.length + 1);
          }
        }

        let photoElement = review.querySelector('img');
        let authorPhoto = null;
        if(photoElement) {
          authorPhoto = photoElement.getAttribute('src');
        } else {
          photoElement = review.querySelector('a');
          if(photoElement && photoElement.style && photoElement.style.backgroundImage) {
            authorPhoto = photoElement.style.backgroundImage.slice(4, -1).replace(/"/g, '');
          }
        }
        let timeElement = review.querySelector('.section-review-publish-date');
        if(!timeElement) {
          timeElement = review.querySelector('span[class*=date]');
        }
        if(!timeElement || timeElement.innerHTML.includes('<')) {
          timeElement = spans.find(s => !s.children.length && s.textContent.includes('ago'));
        }
        const timeText = timeElement && timeElement.textContent;
        const authorUrlElem = review.querySelector('a');
        formatedReviews.push({
          authorName,
          authorPhoto,
          authorUrl: (authorUrlElem && authorUrlElem.getAttribute('href')) || null,
          rating,
          text,
          timeText,
          businessName,
        });
      }
    });
    return formatedReviews;
  }, minRating, businessName);
}

// endregion
