const crypto = require('crypto');
const request = require('superagent');
const jwt = require('jsonwebtoken');
const _ = require('lodash');
const ErrorFactory = require('../errors/ErrorFactory');
const stringUtil = require('../../lib/utils/stringUtils');

let clientId;
let clientSecret;
let clientPublicKey;

module.exports.setAuth = (id, secret, publicKey) => {
  clientId = id;
  clientSecret = secret;
  clientPublicKey = publicKey;
};

module.exports.updateEmbedScript = (accessToken, apiKey) => {
  const url = 'https://dev.wix.com/api/v1/scripts';
  const body = {
    properties: {
      parameters: { apiKey },
    },
  };
  return request.post(url, body).set(getHeaders(accessToken));
};

module.exports.getScripts = (accessToken) => {
  const url = 'https://www.wixapis.com/apps/v1/scripts';
  return request.get(url).set(getHeaders(accessToken));
};

module.exports.decodeJwt = (token) => {
  if(!_.isString(token)) {
    return null;
  }
  try {
    const decoded = jwt.verify(token, clientPublicKey, { algorithm: 'RS256' });
    const payload = JSON.parse(decoded.data);
    payload.token = token;
    payload.data = JSON.parse(payload.data);
    return payload;
  } catch(err) {
    return null;
  }
};

module.exports.decodeSignedInstance = (instance) => {
  const [signature, data] = instance.split('.');
  const decodedSignature = signature.replace(/-/g, '+').replace(/_/g, '/');
  const binarySignature = Buffer.from(decodedSignature, 'base64').toString('binary');
  const generatedSignature = crypto.createHmac('sha256', clientSecret).update(data).digest('binary');
  if(generatedSignature === binarySignature) {
    const dataString = Buffer.from(data, 'base64').toString('utf8');
    return JSON.parse(dataString);
  }
  return null;
};


module.exports.getOrder = (accessToken, orderId) => {
  if(stringUtil.isEmptyString(orderId) || stringUtil.isEmptyString(accessToken)) {
    return Promise.reject(ErrorFactory('invalid orderId or accessToken input'));
  }
  const url = `https://www.wixapis.com/ecom/v1/orders/${orderId}`;
  return request.get(url).set(getHeaders(accessToken)).then(res => res.body.order);
};

module.exports.getPricingPlanOrder = (accessToken, orderId) => {
  if(stringUtil.isEmptyString(orderId) || stringUtil.isEmptyString(accessToken)) {
    return Promise.reject(ErrorFactory('invalid orderId or accessToken input'));
  }
  const url = `https://www.wixapis.com/pricing-plans/v2/orders/${orderId}`;
  return request.get(url).set(getHeaders(accessToken)).then(res => res.body.order);
};

module.exports.getPricingPlanOrders = (accessToken, query) => {
  const url = 'https://www.wixapis.com/pricing-plans/v2/orders';
  return request.get(url).set(getHeaders(accessToken)).query({
    limit: 50,
    orderStatuses: 'ACTIVE',
    'sorting.fieldName': 'createdDate',
    'sorting.order': 'DESC',
  }).then(res => res.body.orders);
};

module.exports.getContact = (accessToken, contactId) => {
  if(stringUtil.isEmptyString(contactId) || stringUtil.isEmptyString(accessToken)) {
    return Promise.reject(ErrorFactory('invalid contactId or accessToken input'));
  }
  const url = `https://www.wixapis.com/contacts/v4/contacts/${contactId}`;
  return request.get(url).set(getHeaders(accessToken)).then(res => res.body.contact);
};

module.exports.getProduct = (accessToken, productId) => {
  if(stringUtil.isEmptyString(productId) || stringUtil.isEmptyString(accessToken)) {
    return Promise.reject(ErrorFactory('invalid productId or accessToken input'));
  }
  const url = `https://www.wixapis.com/stores/v1/products/${productId}`;
  return request.get(url).set(getHeaders(accessToken)).then(res => res.body.product);
};

module.exports.getOrders = (accessToken, params = {
  query: { paging: { limit: 30 }, sort: '[{ "dateCreated": "desc" }]' },
}) => {
  if(stringUtil.isEmptyString(accessToken)) {
    return Promise.reject(ErrorFactory('invalid orderId or accessToken input'));
  }
  const url = 'https://www.wixapis.com/ecom/v1/orders/search';
  return request.post(url).set(getHeaders(accessToken)).send(params).then(res => res.body.orders);
};

module.exports.getProducts = (accessToken, productIds) => {
  if(!accessToken) {
    return Promise.reject(Error('invalid access token'));
  }
  const params = {};
  if(productIds && productIds.length) {
    params.query = { filter: JSON.stringify({ id: { $hasSome: productIds } }) };
  }
  const url = 'https://www.wixapis.com/stores/v1/products/query';
  return request.post(url).set(getHeaders(accessToken)).send(params)
    .then(res => res.body.products);
};

module.exports.getReviews = (accessToken, minRating = 4) => {
  if(!accessToken) {
    return Promise.reject(new Error('Invalid access token'));
  }
  const url = 'https://www.wixapis.com/reviews/v1/reviews/query';
  return request.post(url).set(getHeaders(accessToken)).send({
    query: {
      filter: {
        namespace: 'stores',
        'content.rating': { $gte: minRating },
      },
      sort: [{ fieldName: 'createdDate', order: 'DESC' }],
    },
  }).then(res => res.body.reviews);
};

module.exports.getFormSubmissions = (accessToken) => {
  if(!accessToken) {
    return Promise.reject(Error('invalid access token'));
  }
  const url = 'https://www.wixapis.com/_api/form-submission-service/v4/submissions/namespace/query';
  return request.post(url).set(getHeaders(accessToken)).send({
    query: {
      filter: {
        namespace: 'wix.form_app.form',
      },
      cursorPaging: {
        limit: 30,
      },
      sort: [{ fieldName: 'createdDate', order: 'DESC' }],
    },
    onlyYourOwn: false,
  }).then(res => res.body.submissions);
};

// used to create tokens when we don't have access/refresh tokens
module.exports.createAccessToken = async (instanceId) => {
  const res = await request.post('https://www.wixapis.com/oauth2/token').send({
    grant_type: 'client_credentials',
    client_id: clientId,
    client_secret: clientSecret,
    instance_id: instanceId,
  });
  return res.body.access_token;
};

module.exports.getTokens = (code) => {
  if(stringUtil.isEmptyString(code)) {
    return Promise.reject(ErrorFactory('invalid tokens'));
  }
  return oauthRequestMaker('authorization_code', code, null);
};

// do not use if you don't have access token, use wix.service#refreshAccessToken or createAccessToken
module.exports.refreshAccessToken = (refreshToken, accessToken) => {
  if(stringUtil.isEmptyString(refreshToken) || stringUtil.isEmptyString(accessToken)) {
    return Promise.reject(ErrorFactory('invalid tokens'));
  }
  return oauthRequestMaker('refresh_token', refreshToken, accessToken);
};

function getHeaders(accessToken) {
  return { Authorization: accessToken };
}

function oauthRequestMaker(grantType, token, accessToken) {
  if(!clientId || !clientSecret) {
    return Promise.reject(Error('forgot to call setAuth with wix appId and secret'));
  }
  const url = 'https://www.wixapis.com/oauth/access';
  const body = {
    grant_type: grantType,
    client_id: clientId,
    client_secret: clientSecret,
  };
  if(grantType === 'authorization_code') {
    body.code = token;
  } else if(grantType === 'refresh_token') {
    body.refresh_token = token;
  }
  let defaultHeader = {};
  if(accessToken) {
    defaultHeader = {
      Authorization: accessToken,
    };
  }
  return request.post(url, body).set(defaultHeader).then(res => res.body);
}

module.exports.getCheckout = (accessToken, body) => {
  if(stringUtil.isEmptyString(accessToken) || !body) {
    return Promise.reject(Error('invalid access token or bad request parameters'));
  }
  const url = 'https://www.wixapis.com/apps/v1/checkout';
  return request
    .post(url, body)
    .set({ ...getHeaders(accessToken), 'Content-Type': 'application/json' });
};

module.exports.getPurchaseHistory = (accessToken) => {
  const url = 'https://www.wixapis.com/apps/v1/checkout/history';
  return request.get(url).set(getHeaders(accessToken)).then(res => res.body.purchases);
};

module.exports.getAppInstance = (accessToken) => {
  if(stringUtil.isEmptyString(accessToken)) {
    return Promise.reject(ErrorFactory('invalid accessToken input'));
  }
  const url = 'https://www.wixapis.com/apps/v1/instance';
  return request.get(url).set(getHeaders(accessToken)).then(res => res.body);
};

module.exports.getSiteProperties = (accessToken) => {
  const url = 'https://www.wixapis.com/site-properties/v4/properties';
  return request.get(url).set(getHeaders(accessToken)).then(res => res.body && res.body.properties);
};
