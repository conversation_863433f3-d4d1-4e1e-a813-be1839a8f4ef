const Mailchimp = require('mailchimp-api-v3');
const config = require('../../config/index');
const logger = require('../logger/index')('mailchimp');

module.exports = {
  subscribe(email) {
    if(config.mailchimp.active) {
      const mailchimp = new Mailchimp(config.mailchimp.apiKey);
      mailchimp.post(`/lists/${config.mailchimp.list}/members`, {
        email_address: email,
        status: 'subscribed',
      })
        .then((results) => {
          logger.info(results, 'Subscribe user');
        })
        .catch((err) => {
          logger.info(null, 'Error subscribing user');
        });
    }
  },
};
