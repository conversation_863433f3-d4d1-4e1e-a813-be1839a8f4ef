/* eslint-disable no-underscore-dangle */
const request = require('superagent');

const clients = {};

const SANDBOX_URL = 'https://sandbox.d.greeninvoice.co.il';
const PRODUCTION_URL = 'https://api.greeninvoice.co.il';

module.exports = Client;

/**
 * @param {string} apiKey
 * @param {string} secret
 * @return {Client}
 */
module.exports.getClient = function (apiKey, secret, test = false) {
  if(!apiKey || !secret) {
    return null;
  }

  const key = `${apiKey}|${secret}`;
  if(clients[key]) {
    return clients[key];
  }

  const client = new Client(apiKey, secret, test);
  clients[key] = client;
  return client;
};

/**
 * Get card type name from card type code
 * @param {number} cardType
 * @return {string}
 */
const getCardTypeName = function getCardTypeName(cardType) {
  if(cardType === 1) {
    return 'Isracard';
  }
  if(cardType === 2) {
    return 'VISA';
  }
  if(cardType === 3) {
    return 'Mastercard';
  }
  if(cardType === 4) {
    return 'AMEX';
  }
  if(cardType === 5) {
    return 'Diners';
  }
  return 'Unknown';
};

/**
 * Get payment method name from payment type
 * @param {object} payment
 * @return {string|null}
 */
module.exports.getPaymentMethod = function getPaymentMethod(payment) {
  if(!payment || !payment[0]) {
    return null;
  }
  const paymentInfo = payment[0];
  const { type } = paymentInfo;

  if(type === 2) {
    return 'Bank Transfer';
  }
  if(type === 3) {
    const { description } = paymentInfo;
    if(description) {
      const match = description.match(/\d{4}/);
      const lastFourDigits = match ? match[0] : '';
      if(lastFourDigits !== '0000') {
        return `CC ${lastFourDigits}`;
      }
    }
    return 'Credit Card';
  }
  if(type === 5) {
    return 'PayPal';
  }
  if(type === 11) {
    return paymentInfo.name || 'Other';
  }
  return null;
};

/**
 * Get document type name from type code
 * @param {number} type
 * @return {string|null}
 */
module.exports.getDocumentType = function getDocumentType(type) {
  if(type === 300 || type === 320) {
    return 'Invoice';
  }
  if(type === 330) {
    return 'Refund';
  }
  if(type === 400) {
    return 'Receipt';
  }
  return null;
};

function Client(apiKey, secret, test = false) {
  this._apiKey = apiKey;
  this._secret = secret;
  this.baseURL = test ? SANDBOX_URL : PRODUCTION_URL;
}

/**
 * GreenInvoice unique key is a client's name.
 * When using an IPN the priority for name selection is as follows:
 * 1. company
 * 2. invoiceCompany
 * 3. invoiceFirstName invoiceLastName
 */

/**
 * @param {object} data
 * @param {string} data.name
 * @param {string} [data.email]
 * @return {Promise<*>}
 * @memberOf Client
 */
Client.prototype.getDocumentsForCustomer = async function (data, docOps) {
  const customers = await this.searchCustomers(data);
  return this.getDocumentsForCustomers(customers, docOps);
};

/**
 * @param {array} names
 * @return {Promise<void>}
 * @memberOf Client
 */
Client.prototype.getDocumentsByNames = async function getDocumentsByNames(names, page = 1) {
  const uniqueNames = [...(new Set(names))];
  const requests = uniqueNames.map(name => this.searchDocuments({
    clientName: name, fromDate: '2018-01-01', pageSize: 20, page,
  }));
  const docsForNames = await Promise.all(requests);
  if(!docsForNames) return null;

  const docs = docsForNames.reduce((accumulator, value) => {
    const exactNameDocs = value.filter((doc) => {
      const trimmedName = doc.client.name.trim();
      const exactName = names.includes(trimmedName);
      const added = accumulator.find(i => i.id === doc.id);
      return exactName && !added;
    });
    accumulator.push(...exactNameDocs);
    return accumulator;
  }, []);
  docs.sort((doc1, doc2) => {
    const date1 = new Date(doc1.creationDate || null);
    const date2 = new Date(doc2.creationDate || null);
    if(date1 > date2) return -1;
    if(date2 > date1) return 1;
    return 0;
  });
  return docs;
};

/**
 * @param customers
 * @param {object} docOps
 * @return {Promise<*>}
 * @memberOf Client
 */
Client.prototype.getDocumentsForCustomers = async function (customers, docOps) {
  const requests = customers.map((customer) => {
    const data = Object.assign({ clientId: customer.id }, docOps);
    return this.searchDocuments(data);
  });
  const docsResponses = await Promise.all(requests);
  return docsResponses.reduce((accumulator, docs) => {
    accumulator.push(...docs);
    return accumulator;
  }, []);
};

/**
 * @param {string} docId
 * @param {string[]} emails
 * @param {string} remarks
 * @return {Promise<void>}
 * @memberOf Client
 */
Client.prototype.sendDocument = async function (docId, emails, remarks) {
  const url = `${this.baseURL}/api/v1/documents/${docId}/email`;
  const jwt = await this._getJWT();
  const data = { emails };
  if(remarks) data.remarks = remarks;
  return request.post(url).set(getHeaders(jwt)).send(data);
};

Client.prototype.getDocumentSends = async function (docId) {
  const url = `${this.baseURL}/api/v1/documents/${docId}/distribution`;
  const jwt = await this._getJWT();
  return request.get(url).set(getHeaders(jwt)).then(res => res.body);
};

/**
 * @param {object} data
 * @param {string?} data.clientId
 * @param {string?} data.clientName
 * @param {string?} data.fromDate - YYYY-MM-DD
 * @param {number?} data.pageSize
 * @return {Promise<*|Array>}
 * @memberOf Client
 */
Client.prototype.searchDocuments = async function (data) {
  const jwt = await this._getJWT();
  const searchUrl = `${this.baseURL}/api/v1/documents/search`;
  const res = await request.post(searchUrl).set(getHeaders(jwt)).send(data);
  return (res.body && res.body.items) || [];
};

/**
 * @param data
 * @return {Promise<*>}
 * @memberOf Client
 */
Client.prototype.searchCustomers = async function (data) {
  const jwt = await this._getJWT();
  const res = await request.post(`${this.baseURL}/api/v1/clients/search`)
    .set(getHeaders(jwt))
    .send(data);
  return res.body.items;
};

Client.prototype.updateClient = async function updateClient(id, data) {
  const jwt = await this._getJWT();
  const headers = getHeaders(jwt);
  const res = await request.put(`${this.baseURL}/api/v1/clients/${id}`).set(headers).send(data);
  return res.body;
};

Client.prototype._getJWT = async function () {
  if(!this._expires || this._expires < Date.now()) {
    this._jwt = null;

    const params = { id: this._apiKey, secret: this._secret };
    const res = await request.post(`${this.baseURL}/api/v1/account/token`).send(params);
    this._jwt = res.body.token;
    this._expires = Date.now() + 60 * 1000 * 30; // 30 minutes (30 minutes buffer)
  }
  return this._jwt;
};

Client.prototype._getCustomer = async function (data) {
  const customers = await this.searchCustomers(data);
  return customers[0];
};

/**
 * @param {array} names
 * @return {Promise<Array>}
 * @private
 */
Client.prototype._searchCustomersByNames = async function (names) {
  const searches = names.map(
    name => this._getCustomer({ name }),
  );
  const customers = await Promise.all(searches);
  return customers.filter(customer => customer != null);
};

function getHeaders(jwt) {
  return { Authorization: `Bearer ${jwt}` };
}
