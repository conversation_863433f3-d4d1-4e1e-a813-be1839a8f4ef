/* eslint-disable no-underscore-dangle,no-tabs */

const axios = require('axios');
const xmlParser = require('fast-xml-parser');

const clients = {};

module.exports.getClient = function (apiKey, password, env) {
  if(!apiKey || !apiKey.length || !password || !password.length) {
    return null;
  }

  if(clients[apiKey]) {
    return clients[apiKey];
  }

  const client = new BlueSnap(apiKey, password, env);
  clients[apiKey] = client;
  return client;
};

function BlueSnap(apiKey, password, env) {
  this.apiKey = apiKey;
  this.password = password;
  this.env = env;
}

const HOSTS = {
  sandbox: 'https://sandbox.bluesnap.com',
  production: 'https://ws.bluesnap.com',
};

const ENDPOINTS = {
  subscriptions: '/services/2/subscriptions',
  shoppers: '/services/2/shoppers',
};

BlueSnap.prototype.getSubscription = async function (subId) {
  const url = `${this.__getUrl(ENDPOINTS.subscriptions)}/${subId}`;
  const res = await this.__makeRequest(url, 'get');
  return xmlParser.parse(res.data);
};

BlueSnap.prototype.switchContract = async function (subId, newContractId) {
  const url = `${this.__getUrl(ENDPOINTS.subscriptions)}/${subId}`;
  const data = `<subscription xmlns="http://ws.plimus.com">
					<subscription-id>${subId}</subscription-id>
					<underlying-sku-id>${newContractId}</underlying-sku-id>
				</subscription>`;
  try {
    return await this.__makeRequest(url, 'put', data);
  } catch(err) {
    throw makeError(err, 'switch contract failed', {
      subscriptionId: subId,
      contractId: newContractId,
    });
  }
};

/**
 * Add credit card to shopper
 * @param {string} shopperId the shopper id to update
 * @param {object} data the new card data
 * @param {string} data.ip the shopper's ip
 * @param {string} data.firstName the shopper's first name
 * @param {string} data.lastName name the shopper's last name
 * @param {string} data.countryCode
 * @param {string} data.stateCode
 * @param {string} data.cardNumber
 * @param {string} data.cvv
 * @param {string} data.expMonth the new card number
 * @param {string} data.expYear
 */
BlueSnap.prototype.addCard = async function (shopperId, data) {
  const url = `${this.__getUrl(ENDPOINTS.shoppers)}/${shopperId}`;
  let state = '';
  if(data.stateCode) {
    state = `<state>${data.stateCode}</state>`;
  }
  const xmlData = `<shopper xmlns="http://ws.plimus.com">
					          <web-info>
                        <ip>${data.ip}</ip>
                    </web-info>
                    <shopper-info>
                        <payment-info>
                            <credit-cards-info>
                                <credit-card-info>
                                    <billing-contact-info>
                                        <first-name>${data.firstName}</first-name>
                                        <last-name>${data.lastName}</last-name>
                                        <country>${data.countryCode}</country>
                                        ${state}
							                      </billing-contact-info>
									                  <credit-card>
										                    <card-number>${data.cardNumber}</card-number>
                                        <expiration-month>${data.expMonth}</expiration-month>
                                        <expiration-year>${data.expYear}</expiration-year>
                                        <security-code>${data.cvv}</security-code>
																		</credit-card>
                                </credit-card-info>
                          </credit-cards-info>
						            </payment-info>
					          </shopper-info>
                  </shopper>`;
  try {
    return await this.__makeRequest(url, 'put', xmlData);
  } catch(err) {
    throw makeError(err, 'failed to add credit card', { shopperId });
  }
};

BlueSnap.prototype.updateInvoice = async function (shopperId, data, ip) {
  const url = `${this.__getUrl(ENDPOINTS.shoppers)}/${shopperId}`;
  const {
    companyName, vatId, firstName, lastName, address, city, zipCode, countryCode, state, email,
  } = data;
  if(countryCode && countryCode.length !== 2) {
    throw makeError(new Error('countryCode must be a 2 letter ISO Code'), '', { countryCode });
  }
  const xmlData = ` 
  <shopper xmlns="http://ws.plimus.com">
  <web-info><ip>${ip || '*************'}</ip></web-info>
     <shopper-info>
    <store-id>549684</store-id>
          ${vatId && `<vat-code>${vatId}</vat-code>`}
        <shopper-contact-info>
          <email>${email}</email>
          ${zipCode && `<zip>${zipCode}</zip>`}
          ${city && `<city>${city}</city>`}
          ${state && `<state>${state}</state>`}
          ${vatId && `<vat-code>${vatId}</vat-code>`}
          ${address && `<address1>${address}</address1>`}
          ${firstName && `<first-name>${firstName}</first-name>`}
          ${lastName && `<last-name>${lastName}</last-name>`}
          ${countryCode && `<country>${countryCode}</country>`}
          ${vatId && companyName && `<company-name>${companyName} ${vatId}</company-name>`}
        </shopper-contact-info>
        <invoice-contacts-info>
          <invoice-contact-info>
              <default>true</default>
              <email>${email}</email>
              ${zipCode && `<zip>${zipCode}</zip>`}
              ${city && `<city>${city}</city>`}
              ${state && `<state>${state}</state>`}
              ${vatId && `<vat-code>${vatId}</vat-code>`}
              ${address && `<address1>${address}</address1>`}
              ${firstName && `<first-name>${firstName}</first-name>`}
              ${lastName && `<last-name>${lastName}</last-name>`}
              ${countryCode && `<country>${countryCode}</country>`}
              ${vatId && companyName && `<company-name>${companyName} ${vatId}</company-name>`}
              <update-existing-subscriptions>true</update-existing-subscriptions>
         </invoice-contact-info>
      </invoice-contacts-info>
    </shopper-info>
  </shopper>`;
  try {
    return await this.__makeRequest(url, 'put', xmlData);
  } catch(err) {
    throw makeError(err, 'failed to update invoice', { shopperId });
  }
};

/**
 * Change subscription payment credit card
 * @param {string} subId the subscription id
 * @param {string} fourDigits the new card data last four digits
 * @param {string} cardType VISA, MASTERCARD, etc
 */
BlueSnap.prototype.changeCard = async function (subId, fourDigits, cardType) {
  let bluesnapType = cardType;
  if(cardType.includes('america')) {
    bluesnapType = 'amex';
  } else if(cardType.includes('diners')) {
    bluesnapType = 'diners';
  } else if(cardType.includes('union')) {
    bluesnapType = 'CHINA_UNION_PAY';
  }

  const url = `${this.__getUrl(ENDPOINTS.subscriptions)}/${subId}`;
  const data = `<subscription xmlns="http://ws.plimus.com">
					<credit-card>
						<card-last-four-digits>${fourDigits}</card-last-four-digits>
						<card-type>${bluesnapType}</card-type>
					</credit-card>
				</subscription>`;
  try {
    return await this.__makeRequest(url, 'put', data);
  } catch(err) {
    throw makeError(err, 'failed to update credit card', { subscriptionId: subId });
  }
};

/**
 * Toggle auto-renew for a subscription
 * @param {string} subId - The subscription id
 * @param {boolean} enable - true to enable auto-renew, false to disable
 */

/**
 * Cancel a subscription immediately
 * @param {string} subId - The subscription id
 */
BlueSnap.prototype.cancelSubscription = async function (subId) {
  const url = `${this.__getUrl(ENDPOINTS.subscriptions)}/${subId}`;
  const data = `<subscription xmlns="http://ws.plimus.com">
		<subscription-id>${subId}</subscription-id>
    <status>C</status>
  </subscription>`;
  try {
    return await this.__makeRequest(url, 'put', data);
  } catch(err) {
    throw makeError(err, 'failed to cancel subscription', { subscriptionId: subId });
  }
};

/**
 * Reactivate a canceled subscription
 * @param {string} subId - The subscription id
 */
BlueSnap.prototype.reactivateSubscription = async function (subId) {
  const url = `${this.__getUrl(ENDPOINTS.subscriptions)}/${subId}`;
  const data = `<subscription xmlns="http://ws.plimus.com">
		<subscription-id>${subId}</subscription-id>
    <status>A</status>
  </subscription>`;
  try {
    return await this.__makeRequest(url, 'put', data);
  } catch(err) {
    throw makeError(err, 'failed to reactivate subscription', { subscriptionId: subId });
  }
};

BlueSnap.prototype.__makeRequest = function (url, method, data, headers) {
  const reqHeaders = headers || { 'content-type': 'application/xml' };
  const auth = {
    username: this.apiKey,
    password: this.password,
  };
  return axios({
    method,
    url,
    data,
    auth,
    headers: reqHeaders,
  });
};

BlueSnap.prototype.__getUrl = function (endpoint) {
  if(!endpoint) return null;

  const host = this.env === 'prod' ? HOSTS.production : HOSTS.sandbox;
  if(endpoint.startsWith('/')) return host + endpoint;
  return `${host}/${endpoint}`;
};

function makeError(err, message, data) {
  const retErr = new Error(message || err.message);
  retErr.reqData = data;
  if(err.response) {
    retErr.code = err.response.status;
    if(err.response.headers) retErr.headers = err.response.headers;
    if(err.response.data) {
      retErr.data = xmlParser.parse(err.response.data);
      if(!retErr.data) retErr.data = err.response.data;
    }
  }
  return retErr;
}
