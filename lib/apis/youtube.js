const request = require('superagent');
const strUtil = require('./../utils/stringUtils');
const urlUtil = require('../utils/urlUtils');

module.exports = {
  getSubscribers,
  getId,
};

function getId(id) {
  let parsedId = urlUtil.encode(id);
  const pathName = urlUtil.getPathName(parsedId);

  if(pathName) {
    parsedId = strUtil.getPathComponent(pathName, 2);
  }
  return parsedId;
}

async function getSubscribers(id) {
  // channel ID = 'UC' + user ID
  if(strUtil.isEmptyString(id)) return null;

  let parsedId = urlUtil.encode(id);
  let url = `https://www.youtube.com/channel/${parsedId}`;

  const pathName = urlUtil.getPathName(parsedId);

  if(pathName) {
    parsedId = strUtil.getPathComponent(pathName, 2);
    if(!parsedId) return null;
    const urlType = strUtil.getPathComponent(pathName, 1);

    switch(urlType) {
    // eslint-disable-next-line no-fallthrough
    case 'user':
    case 'c':
    case 'channel':
      url = `https://www.youtube.com/${urlType}/${parsedId}`;
      break;
    default:
      return null;
    }
  }

  return request.get(url)
    .then((res) => {
      if(res.statusCode === 200) {
        let matches = res.text.match(/subscriberCountText.*:"(.*) subscribers?/);
        let subs = matches && matches.length > 1 && matches[1];
        if(!matches || !matches.length) {
          matches = res.text.match(/metadataParts.*"(.*) subscribers"/i);
          subs = matches && matches.length > 1 && matches[1];
        }
        return strUtil.hasNumbers(subs) ? subs : null;
      }
      return Promise.reject(new Error((res.body && res.body.error) || res));
    });
}
