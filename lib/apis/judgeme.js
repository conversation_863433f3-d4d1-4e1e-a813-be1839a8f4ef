const request = require('superagent');

module.exports = {
  getReviews,
};

async function getReviews(placeId, token, { minRating = 4, numReviews = 30 } = {}) {
  const reviews = [];
  let page = 0;
  while(reviews.length < numReviews) {
    page += 1;
    const url = `http://judge.me/api/v1/reviews?shop_domain=${placeId}&api_token=${token}&per_page=10&page=${page}`;
    // eslint-disable-next-line no-await-in-loop
    const rawReviews = await request.get(url).then(res => res.body && res.body.reviews);
    rawReviews.forEach((review) => {
      const { curated, hidden } = review;
      if(curated === 'ok' && !hidden && review.rating && review.rating >= minRating) {
        reviews.push(review);
      }
    });
    // if we got less than 10 reviews, it's the last page
    if(rawReviews.length < 10) {
      break;
    }
  }
  return reviews;
}
