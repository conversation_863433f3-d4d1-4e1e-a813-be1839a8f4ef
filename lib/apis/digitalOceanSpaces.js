const aws = require('aws-sdk');
const request = require('superagent');

/**
 * Upload a file to digitalocean space
 * @param {string} accessKey DO spaces API access key
 * @param {string} secret DO spaces secret key
 * @param {string?} endpoint
 * @param {string} bucket DO space name
 * @param {Buffer} body
 * @param {string} path the path on the space e.g. folder/file.ext
 * @param {string} contentType e.g. image/jpeg
 * @returns {Promise}
 */
function uploadFile({
  accessKey, secret, endpoint = 'nyc3.digitaloceanspaces.com', bucket, body, path, contentType,
}) {
  return new Promise((resolve, reject) => {
    const s3 = new aws.S3({
      endpoint: new aws.Endpoint(endpoint),
      accessKeyId: accessKey,
      secretAccessKey: secret,
    });
    s3.putObject({
      Body: body,
      Bucket: bucket,
      Key: path,
      ACL: 'public-read',
      ContentType: contentType,
    }, (err, data) => {
      if(err) reject(err);
      else resolve(data);
    });
  });
}

function purgeCache(spaceId, apiKey, { files }) {
  const url = `https://api.digitalocean.com/v2/cdn/endpoints/${spaceId}/cache`;
  return request
    .delete(url)
    .set('Authorization', `Bearer ${apiKey}`)
    .send({ files });
}

module.exports = {
  uploadFile,
  purgeCache,
};
