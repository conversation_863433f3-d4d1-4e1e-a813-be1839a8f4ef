const request = require('superagent');
const cheerio = require('cheerio');
require('superagent-proxy')(request);
const _ = require('lodash');
const strUtil = require('./../utils/stringUtils');
const urlUtil = require('../utils/urlUtils');

module.exports = {
  getFollowers,
  getProfileId,
};

async function getFollowers(profileId, proxy, token) {
  const id = getProfileId(profileId);
  let error = null;
  let followers = null;
  const getFollowersFunctions = [
    { name: 'getFollowersPiokokSuperagent', method: () => getFollowersPiokok(id) },
    { name: 'getFollowersPiokokProxy', method: () => getFollowersPiokok(id, { proxy }) },
    { name: 'getFollowersMobileAPI', method: () => getFollowersMobileAPI(id, { token, proxy }) },
    { name: 'getFollowersPiokokScrapedo', method: () => getFollowersPiokok(id, { token }) },
  ];
  for(let i = 0; i < getFollowersFunctions.length; i += 1) {
    try {
      // eslint-disable-next-line no-await-in-loop
      followers = await getFollowersFunctions[i].method();
      if(followers) {
        return followers;
      }
    } catch(err) {
      error = err;
    }
  }
  if(error) {
    throw error;
  }
  return followers;
}

async function getFollowersPiokok(profileId, { token, proxy } = {}) {
  const url = `https://www.piokok.com/profile/${profileId}`;
  let html = null;
  if(proxy) {
    html = await request.get(url).proxy(proxy).then(res => res.text);
  } else if(token) {
    html = await request.get('https://api.scrape.do').query({ token, url }).then(res => res.text);
  } else {
    html = await request.get(url).then(res => res.text);
  }
  if(html) {
    const $ = cheerio.load(html);
    return $('div.item_followers div.num').attr('title') || null;
  }
  return null;
}

async function getFollowersMobileAPI(profileId, { token, proxy } = {}) {
  const INSTAGRAM_APP_ID = '936619743392459';
  const iosUA = 'Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_3 like Mac OS X) AppleWebKit/603.3.8 (KHTML, like Gecko) Mobile/14G60 Instagram *********.90 (iPhone9,4; iOS 10_3_3; en_US; en-US; scale=2.61; gamut=wide; 1080x1920';
  const url = `https://i.instagram.com/api/v1/users/web_profile_info/?username=${profileId}`;
  const response = await request.get('https://api.scrape.do').query({
    token,
    url,
    customHeaders: true,
  }).set({
    'User-Agent': iosUA,
    'x-ig-app-id': INSTAGRAM_APP_ID,
  }).then(res => res.text);
  const parsedResponse = JSON.parse(response);
  return _.get(parsedResponse, 'data.user.edge_followed_by.count', null);
}

function getProfileId(value) {
  const idWithoutHandle = urlUtil.encode(strUtil.getTwitterHandle(value));
  const parsedValue = urlUtil.normalizeWithPackage(idWithoutHandle);
  const pathName = urlUtil.getPathName(parsedValue);
  if(pathName && pathName.length > 1) { // pathName has more than "/"
    return strUtil.getPathComponent(pathName, 1);
  }
  return idWithoutHandle;
}
