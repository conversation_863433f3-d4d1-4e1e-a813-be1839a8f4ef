const request = require('superagent');
const querystring = require('querystring');

module.exports = {
  getReviews,
};

async function getReviews(siteId, token, { limit = 100, lowestRating = 4 }) {
  const ratings = [];
  for(let i = lowestRating; i <= 5; i += 1) {
    ratings.push(i);
  }

  const daysBack = 365;
  const date = new Date(Date.now() - 86400 * 1000 * daysBack);
  const from = date.toISOString().split('T')[0];
  const query = querystring.stringify({
    token, rating: ratings.join(','), limit, from, xml: false, test: false, sort: 'newest',
  });
  const url = `https://api.shopperapproved.com/reviews/${siteId}?${query}`;
  return request.get(url).then((res) => {
    if(res.statusCode === 200) {
      try {
        return res.body;
      } catch(err) {
        return Promise.reject(new Error(`failed to fetch data from shopper approved. ${err}`));
      }
    } else {
      return Promise.reject(new Error((res.body && res.body.error) || res));
    }
  });
}
