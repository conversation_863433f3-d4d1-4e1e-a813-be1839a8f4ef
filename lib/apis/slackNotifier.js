/* eslint-disable no-underscore-dangle */
const os = require('os');
const _ = require('lodash');
const request = require('superagent');
const config = require('../../config');
const logger = require('../logger/index')('slack-notifier');
const { errSerializer } = require('../utils');

const { active } = config.slack;
const webhook = config.slack.install;

module.exports = {
  /**
   * @param {object} text the message
   * @param {object|array?} data object with metadata of event
   * @param {object|string} [options] if string - treated as webook
   * @param {string} [options.webhook=config.slack.eventsChannels] where to POST the notification
   * @return {Promise<void>}
   */
  async notify(text, data, options) {
    if(!active) return;

    const opts = options || {};

    const attachments = [];
    if(!_.isEmpty(data)) {
      if(Array.isArray(data)) {
        for(let i = 0; i < data.length; i += 1) {
          const item = data[i];
          if(item) {
            let content = JSON.stringify(item, null, 2);
            if(item.text) {
              content = item.text;
              if(typeof content === 'object') {
                content = JSON.stringify(content, null, 2);
              }
            }
            attachments.push({
              title: item.title || item.message || `attachment #${i + 1}`,
              text: `\`\`\`${content}\`\`\``,
            });
          }
        }
      } else if(data instanceof Error) {
        const serializedErr = errSerializer(data);
        attachments.push({ text: `\`\`\`${JSON.stringify(serializedErr, null, 2)}\`\`\`` });
      } else if(data) {
        if(data.err) {
          data.err = errSerializer(data.err);
        } else if(data.error) {
          data.error = errSerializer(data.error);
        }
        data.hostname = os.hostname();
        attachments.push({ text: `\`\`\`${JSON.stringify(data, null, 2)}\`\`\`` });
      }
    }

    const payload = { text, attachments };
    try {
      let url;
      if(_.isString(opts)) url = opts;
      else url = opts.webhook || webhook;

      const res = await request.post(url).send(payload);
      logger.info({ response: res.body }, 'slack notification sent');
    } catch(err) {
      logger.error({ err }, 'slack notification failed');
    }
  },

  /**
   * @param err
   * @param msg
   * @param {object?} options
   * @param {object?} options.data
   * @param {string?} options.webhook
   * @return {Promise<void>}
   */
  async notifyError(err, msg, options) {
    const opts = options || {};
    const errWebhook = opts.webhook || config.slack.errors;
    const data = [];
    if(!opts.webhook) {
      data.push({ title: 'data', text: { ...opts } });
    } else {
      data.push({ title: 'data', text: { ...opts.data } });
    }
    const serializedError = errSerializer(err);
    if(serializedError) {
      if(serializedError.response) {
        serializedError.response = _.pick(serializedError.response, [
          'text', 'body', 'headers', 'status', 'error',
        ]);
      }
      if(serializedError._data && serializedError._data.err && serializedError._data.err.response) {
        serializedError._data.err.response = _.pick(serializedError._data.err.response, [
          'text', 'body', 'headers', 'status', 'error',
        ]);
      }
      if(serializedError._data && serializedError._data.response) {
        serializedError._data.response = _.pick(serializedError._data.response, [
          'text', 'body', 'headers', 'status', 'error',
        ]);
      }
    }
    data.push({ title: 'error', text: serializedError });
    await this.notify(msg || 'critical error occured', data, { webhook: errWebhook });
  },
};
