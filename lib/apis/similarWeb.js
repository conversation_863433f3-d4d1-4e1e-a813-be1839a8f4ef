const _ = require('lodash');
const request = require('superagent');
const parseDomain = require('parse-domain');

const keys = [
  'SiteName', 'IsSmall', 'IsSiteVerified', 'Category', 'Title', 'Description', 'Engagments',
];

/**
 * @typedef {object} SimilarWebStats
 * @property {string} SiteName
 * @property {boolean} IsSmall
 * @property {boolean} IsSiteVerified
 * @property {string} Category
 * @property {string} Title
 * @property {string} Description
 * @property {object} Engagments
 * @property {number} Engagments.Visits
 */

/**
 * @param {string} website
 * @return {Promise<SimilarWebStats>}
 */
module.exports.getStats = async function (website) {
  const domain = getDomain(website);
  if(!domain) return null;

  try {
    const url = getApiUrl(domain);
    const res = await request.get(url);
    return _.pick(res.body, keys);
  } catch(err) {
    return null;
  }
};

module.exports.isBig = async function (website) {
  try {
    const stats = await this.getStats(website);
    if(stats && stats.Engagments) {
      return !stats.IsSmall && stats.Engagments.Visits > 50000;
    }
  } catch(err) {}
  return false;
};

function getDomain(url) {
  if(!url) return null;

  const parsed = parseDomain(url);
  if(!parsed) return null;

  return `${parsed.domain}.${parsed.tld}`;
}

function getApiUrl(url) {
  return `https://data.similarweb.com/api/v1/data?domain=${url}/all`;
}
