const request = require('superagent');

module.exports = {
  refreshAccessToken,
  getAccounts,
  getLocations,
  getReviews,
};

function refreshAccessToken({ clientId, clientSecret, refreshToken }) {
  return request.post('https://oauth2.googleapis.com/token').send({
    client_id: clientId,
    client_secret: clientSecret,
    grant_type: 'refresh_token',
    refresh_token: refreshToken,
  }).then(res => res.body && res.body.access_token);
}

async function getAccounts({ accessToken }) {
  return request.get('https://mybusiness.googleapis.com/v4/accounts')
    .set(getAuthHeader(accessToken))
    .then(res => res.body && res.body.accounts);
}

async function getLocations({ accessToken, account }) {
  return request.get(`https://mybusiness.googleapis.com/v4/${account}/locations`)
    .set(getAuthHeader(accessToken))
    .then(res => res.body && res.body.locations);
}

async function getReviews({ accessToken, location, pageToken = null }) {
  let url = `https://mybusiness.googleapis.com/v4/${location}/reviews`;
  if(pageToken) {
    url += `?pageToken=${pageToken}`;
  }
  return request.get(url)
    .set(getAuthHeader(accessToken))
    .then(res => ({ reviews: res.body.reviews, nextPageToken: res.body.nextPageToken }));
}

function getAuthHeader(accessToken) {
  return { Authorization: `Bearer ${accessToken}` };
}
