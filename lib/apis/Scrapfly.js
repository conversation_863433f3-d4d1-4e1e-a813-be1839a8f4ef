const { ScrapflyClient, ScrapeConfig } = require('scrapfly-sdk');

class Scrapfly {
  constructor(scrapflyApiKey) {
    if(!scrapflyApiKey) {
      throw new Error('Scrapfly API key is required');
    }
    this.client = new ScrapflyClient({ key: scrapfly<PERSON>pi<PERSON>ey });
    this.config = {
      asp: true, // Anti-Scraping Protection bypass
      url: '',
      headers: {},
      proxy_pool: 'public_residential_pool',
    };
  }

  get(url) {
    this.config.url = url;
    return this;
  }

  set(headers = {}) {
    this.config.headers = { ...this.config.headers, ...headers };
    return this;
  }

  post(url, body) {
    this.config.url = url;
    this.config.method = 'POST';
    this.config.headers['content-type'] = 'application/json';
    this.config.data = body;
    return this;
  }
}

Scrapfly.prototype.then = function (resolve, reject) {
  return this.client.scrape(new ScrapeConfig(this.config)).then(resolve).catch(reject);
};

Scrapfly.prototype.catch = function (onRejected) {
  this.then(null, onRejected);
};

module.exports = Scrapfly;
