/* eslint-disable camelcase */
const querystring = require('querystring');
const request = require('superagent');
const _ = require('lodash');
const httpConsts = require('../constants/httpConstants');

const API_VERSION = '2024-10';

/**
 * @param {string} shop myshopify domain
 * @param {string} token shopify api token
 * @return {Shopify}
 */
module.exports = function make(shop, token) {
  if(!shop || !token) {
    return null;
  }
  return new Shopify(shop, token);
};

module.exports.topics = {
  order_create: 'orders/create',
  app_uninstall: 'app/uninstalled',
  shop_update: 'shop/update',
  // NOTE: this has no documentation
  subscription_update: 'app_subscriptions/update',
};

module.exports.getToken = function getToken(shop, apiKey, secret, code) {
  const params = { client_id: apiKey, client_secret: secret, code };
  const url = `https://${shop}/admin/oauth/access_token`;
  return request.post(url, params).then(res => res.body.access_token);
};

/**
 * DANGEROUS! this credits the merchant regardless if they have a subscription of our app or not
 * @param partnerId
 * @param appId
 * @param partnerAccessToken
 * @param shopId
 * @param amount
 * @param description
 * @returns {request.SuperAgentRequest}
 */
module.exports.sendCredits = function sendCredits({
  partnerId, appId, partnerAccessToken, shopId, amount, description,
}) {
  return request.post(`https://partners.shopify.com/${partnerId}/api/${API_VERSION}/graphql.json`).send({
    query: `mutation appCreditCreate($amount: MoneyInput!, $appId: ID!, $description: String!, $shopId: ID!) {
      appCreditCreate(amount: $amount, appId: $appId, description: $description, shopId: $shopId) {
        appCredit {
          # AppCredit fields
          amount {
            amount
            currencyCode
          }
          name
        }
        userErrors {
          field
          message
        }
      }
    }`,
    variables: {
      appId: `gid://partners/App/${appId}`,
      amount: {
        amount,
        currencyCode: 'USD',
      },
      description,
      shopId: `gid://partners/Shop/${shopId}`,
    },
  }).set({
    'x-shopify-access-token': partnerAccessToken,
  });
};


module.exports.Shopify = Shopify;

function Shopify(shop, token) {
  this.shop = shop;
  this.token = token;
  this.baseUrl = `https://${shop}`;
  // also change the version in app settings "events version"
  this.adminUrl = `${this.baseUrl}/admin/api/${API_VERSION}`;
  this.graphqlURL = `${this.baseUrl}/admin/api/${API_VERSION}/graphql.json`;
}

Shopify.prototype.removeApp = function removeApp() {
  return request.delete(`${this.adminUrl}/api_permissions/current.json`).set({
    'x-shopify-access-token': this.token,
  });
};

Shopify.prototype.addScript = function addScript(src) {
  const url = `${this.adminUrl}/script_tags.json`;
  const params = {
    script_tag: {
      src, event: 'onload', display_scope: 'online_store',
    },
  };
  return this.request(httpConsts.POST, url).send(params);
};

Shopify.prototype.getScript = function getScript(src) {
  const url = `${this.adminUrl}/script_tags.json?src=${src}`;
  return this.request(httpConsts.GET, url).then(res => _.get(res.body, 'script_tags[0]', null));
};

Shopify.prototype.getScripts = function getScripts() {
  const url = `${this.adminUrl}/script_tags.json`;
  return this.request(httpConsts.GET, url).then(res => _.get(res.body, 'script_tags', null));
};

Shopify.prototype.updateScript = function updateScript(id, { display_scope }) {
  return request.put(`${this.adminUrl}/script_tags/${id}.json`).set({
    'x-shopify-access-token': this.token,
  }).send({
    script_tag: { id, display_scope },
  }).then(res => res.body);
};

Shopify.prototype.removeScript = function removeScripts(id) {
  const url = `${this.adminUrl}/script_tags/${id}.json`;
  return this.request(httpConsts.DELETE, url);
};

Shopify.prototype.getWebhook = function (topic, webhook) {
  return this.getWebhooks(topic, webhook).then(webhooks => webhooks[0]);
};

Shopify.prototype.getWebhooks = function getWebhooks(topic, webhook) {
  const query = querystring.stringify({
    ...(topic && { topic }),
    ...(webhook && { webhook }),
  });
  const url = `${this.adminUrl}/webhooks.json?${query}`;
  return this.request(httpConsts.GET, url).then(res => _.get(res.body, 'webhooks', null));
};

Shopify.prototype.addWebhook = function addWebhook(topic, webhook) {
  const url = `${this.adminUrl}/webhooks.json`;
  const params = { webhook: { topic, address: webhook } };
  return this.request(httpConsts.POST, url).send(params);
};

Shopify.prototype.updateWebhook = function updateWebhook(id, webhook) {
  const url = `${this.adminUrl}/webhooks/${id}`;
  const params = { webhook: { id, address: webhook } };
  return this.request(httpConsts.PUT, url).send(params);
};

Shopify.prototype.getProduct = function getProduct(productId) {
  const url = `${this.adminUrl}/products/${productId}.json?fields=id,title,handle,image`;
  return this.request(httpConsts.GET, url).then(res => res.body.product);
};

Shopify.prototype.rest_getProducts = function getProducts(productIds, fields) {
  const query = querystring.stringify({
    ...{ fields: fields || 'id,title,handle,image,images' },
    ...(productIds && productIds.length && { ids: productIds }),
  });
  const url = `${this.adminUrl}/products.json?${query}`;
  return this.request(httpConsts.GET, url).then(res => res.body.products);
};

Shopify.prototype.getProducts = function getProducts(productIds = []) {
  const fields = [
    'id',
    'title',
    'handle',
    'onlineStoreUrl',
    'featuredImage { url }', // deprecated
    `featuredMedia {
      ... on MediaImage {
        image {
          originalSrc
        }
      }
    }`,
  ];
  return request.post(this.graphqlURL).set({ 'x-shopify-access-token': this.token }).send({
    query: `query GetProducts($ids: [ID!]!) {
      nodes(ids: $ids) {
        ... on Product {
          ${fields.join('\n')}
        }
      }
    }`,
    variables: {
      ids: productIds.map(id => `gid://shopify/Product/${id}`),
    },
  }).then(res => res.body.data.nodes);
};

Shopify.prototype.getProductVariants = function getProductVariants(variantIds = []) {
  return request.post(this.graphqlURL).set({ 'x-shopify-access-token': this.token }).send({
    query: `query GetMultipleVariants($ids: [ID!]!) {
      nodes(ids: $ids) {
        ... on ProductVariant {
          id
          title
          displayName
          image {
           url
          }
        }
      }
    }`,
    variables: {
      ids: variantIds.map(id => `gid://shopify/ProductVariant/${id}`),
    },
  }).then(res => res.body.data.nodes);
};

Shopify.prototype.getProductImage = function getProductImage(productId) {
  const url = `${this.adminUrl}/products/${productId}.json?fields=image`;
  return this.request(httpConsts.GET, url).then(res => _.get(res, 'body.product.image.src', null));
};

Shopify.prototype.getOrders = function getOrders(query = {}) {
  const queryStr = querystring.stringify(query);
  const url = `${this.adminUrl}/orders.json?${queryStr}`;
  return this.request(httpConsts.GET, url).then(res => _.get(res, 'body.orders', null));
};

Shopify.prototype.getShop = function getShop() {
  const url = `${this.adminUrl}/shop.json`;
  return this.request(httpConsts.GET, url).then(res => _.get(res, 'body.shop', null));
};

Shopify.prototype.getLocations = function getLocations() {
  return this.request(httpConsts.GET, `${this.adminUrl}/locations.json`).then(res => _.get(res, 'body.locations', null));
};

Shopify.prototype.getLocation = function getLocation(locId) {
  return this.request(httpConsts.GET, `${this.adminUrl}/locations/${locId}.json`).then(res => _.get(res, 'body.location', null));
};

Shopify.prototype.charge = function charge(name, price, redirect, test) {
  const url = `${this.adminUrl}/recurring_application_charges.json`;
  const data = { name, price, return_url: redirect };
  if(test) {
    data.test = true;
  }
  return this.request(httpConsts.POST, url).send({ recurring_application_charge: data }).then(res => _.get(res, 'body.recurring_application_charge', null));
};

Shopify.prototype.getCharge = function getCharge(chargeId) {
  const url = `${this.adminUrl}/recurring_application_charges/${chargeId}.json`;
  return this.request(httpConsts.GET, url).then(res => _.get(res, 'body.recurring_application_charge', null));
};

Shopify.prototype.getCharges = function getCharges() {
  const url = `${this.adminUrl}/recurring_application_charges.json`;
  return this.request(httpConsts.GET, url)
    .then(res => _.get(res, 'body.recurring_application_charges', null));
};

Shopify.prototype.cancelCharge = function cancelCharge(chargeId) {
  const url = `${this.adminUrl}/recurring_application_charges/${chargeId}.json`;
  return this.request(httpConsts.DELETE, url);
};

/**
 * @deprecated only via graphql now
 * @param amount
 * @param description
 * @returns {Promise<GetFieldType<request.Response, "body.application_credit">>}
 */
Shopify.prototype.applyCredit = function applyCredit(amount, description) {
  const url = `${this.adminUrl}/application_credits.json`;
  return request.post(url)
    .set(this.getHeaders())
    .send({ application_credit: { amount, description } })
    .then(res => _.get(res, 'body.application_credit', null));
};

Shopify.prototype.getCredits = function getCredits() {
  const url = `${this.adminUrl}/application_credits.json`;
  return request.get(url)
    .set(this.getHeaders())
    .then(res => _.get(res, 'body.application_credits', null));
};

/**
 * @param {string} method
 * @param {string} url
 */
Shopify.prototype.request = function makeRequest(method, url) {
  let req = null;
  const lowerMethod = method.toLowerCase();
  if(request[lowerMethod]) {
    req = request[lowerMethod](url);
  } else {
    req = request.get(url);
  }
  const headers = { 'x-shopify-access-token': this.token };
  return req.set(headers);
};

Shopify.prototype.getHeaders = function getHeaders() {
  return { 'x-shopify-access-token': this.token };
};
