const globalRegex = /[-+_a-z0-9]+(\.[-+_a-z0-9]+)*@[-+_a-z0-9]+(\.[-+_a-z0-9]+)*(\.[a-z]{2,15})/gi;
const emailOnlyRegex = /^[-+_a-z0-9]+(\.[-+_a-z0-9]+)*@[-+_a-z0-9]+(\.[-+_a-z0-9]+)*(\.[a-z]{2,15})$/i;

const commonEmailDomains = require('../common/commonEmailDomains');

module.exports = {
  getMatches(string) {
    if(!string || typeof string.match !== 'function') return null;

    return string.match(globalRegex);
  },

  isEmail(string) {
    if(!string || !string.length || string.indexOf('@') < 0) return false;

    return emailOnlyRegex.test(string);
  },

  getDomain(email) {
    if(!email) return null;
    const comps = email.split('@');
    return comps[1] || null;
  },

  isCommonDomain(domain) {
    return commonEmailDomains.indexOf(domain) > -1;
  },

  getMatchesInJson(json) {
    let matches = null;
    try {
      const string = JSON.stringify(json);
      matches = this.getMatches(string);
    } catch(err) {}

    return matches;
  },

  getMatchesInEncodedData(rawData) {
    let matches = null;
    try {
      const string = decodeURIComponent(rawData.toString());
      matches = this.getMatches(string);
    } catch(err) {}
    return matches;
  },
};
