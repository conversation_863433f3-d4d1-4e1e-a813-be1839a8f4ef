const toCamelCase = str => str.replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => (index == 0 ? word.toLowerCase() : word.toUpperCase())).replace(/\s+/g, '');

const changePropertyNameToCamelCase = arr => arr.map((item) => {
  const temp = {};
  Object.keys(item).forEach((k) => {
    temp[toCamelCase(k.replace(/_/g, ' '))] = item[k];
  });
  return temp;
});

module.exports = {
  toCamelCase,
  changePropertyNameToCamelCase,
};
