const _ = require('lodash');

module.exports = {
  difference(object, base) {
    return changes(object, base);
  },

  destroyCircular(obj) {
    return destroyCircular({ from: obj, seen: [] });
  },

  isValidRegex(str) {
    try {
      RegExp(str);
    } catch(err) {
      return false;
    }
    return true;
  },

  isEmpty(obj) {
    for(const key in obj) {
      if(obj.hasOwnProperty(key)) return false;
    }
    return true;
  },


  find(object, keys, opts = { ignoreCase: false, includes: false }) {
    const values = [];
    if(_.isObject(object)) {
      const entries = Object.entries(object);
      for(let i = 0; i < entries.length; i += 1) {
        const [k, v] = entries[i];
        if(_.isArray(keys)) {
          if(_.find(keys, key => compare(key, k, opts))) {
            values.push(v);
          }
        } else if(compare(keys, k, opts)) {
          values.push(v);
        }
        if(_.isObject(v)) {
          values.push(...this.find(v, keys, opts));
        }
      }
    }
    return values;
  },

  findFirst(object, keys, opts = { ignoreCase: false, includes: false }) {
    const values = this.find(object, keys, opts);
    return (values && values[0]) || null;
  },
};

function compare(listKey, dynamicKey, opts = { ignoreCase: false, includes: false }) {
  let lKey = listKey;
  let dKey = dynamicKey;
  if(opts.ignoreCase) {
    lKey = lKey && lKey.toLowerCase && lKey.toLowerCase();
    dKey = dKey && dKey.toLowerCase && dKey.toLowerCase();
  }
  if(opts.includes) {
    return dKey.includes(lKey);
  }
  return lKey === dKey;
}

function changes(object, base) {
  return _.transform(object, (result, value, key) => {
    if(!_.isEqual(value, base[key])) {
      result[key] = (_.isObject(value) && _.isObject(base[key]))
        ? changes(value, base[key])
        : value;
      if(_.isArray(value)) result[key] = _.compact(result[key]);
    }
  });
}

function destroyCircular({
  from, seen, to_,
}) {
  const to = to_ || (Array.isArray(from) ? [] : {});

  seen.push(from);

  const entries = Object.entries(from);
  for(let i = 0; i < entries.length; i += 1) {
    const [key, value] = entries[i];
    if(typeof value === 'function') {
      continue;
    }

    if(!value || typeof value !== 'object') {
      to[key] = value;
      continue;
    }

    if(!seen.includes(from[key])) {
      to[key] = destroyCircular({
        from: from[key],
        seen: seen.slice(),
      });
      continue;
    }

    to[key] = '[Circular]';
  }

  return to;
}
