const fs = require('fs-extra');
const csvParse = require('csv-parse');

module.exports = {
  async parseArray(path) {
    const retval = [];
    const parser = fs.createReadStream(path).pipe(csvParse({
      columns: true,
      relax_column_count: true,
      bom: true,
    }));
    // eslint-disable-next-line no-restricted-syntax
    for await (const row of parser) {
      retval.push(row);
    }
    return retval;
  },
};
