const cookieLib = require('cookie');

const CONSTANTS = {
  header: 'x-ps-first',
  options: ['expires', 'maxAge'],
};

function Cookie(key, value, options) {
  this.key = key;
  this.value = value;
  this.options = options;
}

function getCookie(req, key) {
  let retval = getFirstPartyCookie(req, key);
  if(!retval) retval = req.signedCookies && req.signedCookies[key];
  if(!retval) retval = req.cookies && req.cookies[key];
  return retval;
}

function setCookie(req, res, cookie) {
  const { key, value, options } = cookie;
  setFirstPartyCookie(req, cookie);
  res.cookie(key, value, options);
  const firstPartyCookie = getHeaderValue(req);
  if(firstPartyCookie) {
    res.set(CONSTANTS.header, firstPartyCookie);
  }
}

function setFirstPartyCookie(req, cookie) {
  const { key, value, options } = cookie;
  if(!req.locals || !req.locals.cookies) req.locals.cookies = {};

  const { cookies } = req.locals;
  cookies[key] = [`${key}=${value}`];
  if(options) {
    Object.entries(options).forEach(([k, v]) => {
      if(CONSTANTS.options.includes(k)) {
        if(v.toUTCString) {
          cookies[key].push(`${k}=${v.toUTCString()}`);
        } else {
          cookies[key].push(`${k}=${v}`);
        }
      }
    });
  }
}

function getFirstPartyCookie(req, key) {
  const header = req && req.headers && req.headers[CONSTANTS.header];
  if(!header) return null;

  const parsed = cookieLib.parse(header);
  return parsed && parsed[key];
}

function getHeaderValue(req) {
  if(!req.locals.cookies) return null;

  const { cookies } = req.locals;
  const joined = [];
  Object.values(cookies).forEach(cookie => joined.push(cookie.join('; ')));
  return joined.join('$');
}

module.exports = {
  CONSTANTS,
  makeCookie: (key, value, options) => new Cookie(key, value, options),
  getCookie,
  setCookie,
  getHeaderValue,
  header: CONSTANTS.header,
};
