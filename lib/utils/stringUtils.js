const _ = require('lodash');
const numeral = require('numeral');
const crypto = require('crypto');
const querystring = require('querystring');

const NUM_REGEX = /[0-9]/;

module.exports = {
  regex: {
    objectId: /^[0-9a-fA-F]{24}$/,
  },

  isObjectId(str) {
    // use mongoose.Types.ObjectId.isValid instead (checks more stuff)
    return this.regex.objectId.test(str);
  },

  getInitials(name) {
    if(!_.isString(name)) return null;

    const split = name.split(' ');
    let retval = split[0].charAt(0);
    if(split.length > 0 && split[1]) retval += split[1].charAt(0);

    retval = retval.toUpperCase();

    return retval;
  },

  capitalizeFirstLetter(str) {
    if(!str || !str.charAt) {
      return str;
    }
    return str.charAt(0).toUpperCase() + str.slice(1);
  },

  isEmptyString(str) {
    if(!str || !str.length || str === 'undefined' || str === 'null') return true;

    return false;
  },

  includes(str1, str2, { caseSensitive = false } = {}) {
    if(caseSensitive) {
      return str1.includes(str2);
    }
    if(str1.toLowerCase && str2.toLowerCase) {
      return str1.toLowerCase().includes(str2.toLowerCase());
    }
    return false;
  },

  randomString(length) {
    return crypto.randomBytes(length / 2).toString('hex');
  },

  hasNumbers(str) {
    return NUM_REGEX.test(str);
  },

  getNumber(str) {
    return str && str.replace && parseFloat(str.replace(/[^0-9.-]+/g, ''));
  },

  getTwitterHandle(name) {
    if(this.isEmptyString(name)) {
      return null;
    }
    if(name.charAt && name.charAt(0) === '@') {
      return name.substr(1);
    }
    return name;
  },

  getPathComponent(path, index) {
    if(this.isEmptyString(path)) return null;

    const pathSplit = path.split('/');

    if(pathSplit && pathSplit.length > index) {
      return this.isEmptyString(pathSplit[index]) ? null : pathSplit[index];
    }
    return null;
  },

  getQuery(URI) {
    if(this.isEmptyString(URI)) return null;

    try {
      const decoded = decodeURIComponent(URI);
      return querystring.parse(decoded);
    } catch(err) {
      return null;
    }
  },

  humanReadableNumber(value) {
    if(!value || (typeof value !== 'number')) {
      return value;
    }
    const num = numeral(value);
    const format = num.value() < 1000 ? '0' : '0.[0]a';
    const formatedNum = num.format(format);
    return formatedNum.toUpperCase();
  },

  percentage(value) {
    if(!value || (typeof value !== 'number')) {
      return value;
    }
    const num = numeral(value);
    if(num.value() >= 1) {
      return numeral(1).format('0%');
    }
    return numeral(value).format('0.0%');
  },

  safeJsonParse(str) {
    try {
      return JSON.parse(str);
    } catch(err) {
      return null;
    }
  },

  getHiddenString(str) {
    let prefix = str.charAt(0);
    let suffix = str.charAt(str.length - 1);
    if(str.length > 10) {
      prefix += str.substr(1, 2);
      suffix = str.substr(str.length - 3);
    }
    let numStars = str.length - prefix.length - suffix.length;
    if(numStars < 2) {
      numStars = 2;
    }
    return `${prefix}${'*'.repeat(numStars)}${suffix}`;
  },

  snarkdown: (function () {
    function n(n) {
      return n && n.replace(RegExp(`^${(n.match(/^(\t| )+/) || '')[0]}`, 'gm'), '');
    }

    function e(n) {
      return n && n.replace(/"/g, '&quot;');
    }

    const r = {
      _: ['<em>', '</em>'],
      __: ['<strong>', '</strong>'],
      '\n\n': ['<br />'],
      '>': ['<blockquote>', '</blockquote>'],
      '*': ['<ul>', '</ul>'],
      '#': ['<ol>', '</ol>'],
    };
    return function t(o) {
      if(!o || !o.replace) {
        return o;
      }
      function c(n) {
        const e = n.replace(/\*/g, '_').replace(/^( {2}\n\n*|\n{2,})/g, '\n\n'); const t = m[m.length - 1] === n; const
          o = r[e];
        return o ? o[1] ? (m[t ? 'pop' : 'push'](n), o[t ? 1 : 0]) : o[0] : n;
      }

      function a() {
        for(var n = '', e = m.length; e--;) n += c(m[e]);
        return n;
      }

      let l; let u; let g; let s; let p;
      const i = /(?:^```(\w*)\n([\s\S]*?)\n```$)|((?:(?:^|\n+)(?:\t|  {2,}).+)+\n*)|((?:(?:^|\n)([>*+-]|\d+\.)\s+.*)+)|(?:\!\[([^\]]*?)\]\(([^\)]+?)\))|(\[)|(\](?:\(([^\)]+?)\))?)|(?:(?:^|\n+)([^\s].*)\n(\-{3,}|={3,})(?:\n+|$))|(?:(?:^|\n+)(#{1,3})\s*(.+)(?:\n+|$))|(?:`([^`].*?)`)|( {2}\n\n*|\n{2,}|__|\*\*|[_*])/gm;
      var m = []; let f = ''; let h = 0; const
        d = {};
      for(o = o.replace(/^\n+|\n+$/g, '').replace(/^\[(.+?)\]:\s*(.+)$/gm, (n, e, r) => d[e.toLowerCase()] = r, ''); g = i.exec(o);) u = o.substring(h, g.index), h = i.lastIndex, l = g[0], u.match(/[^\\](\\\\)*\\$/) || (g[2] || g[3] ? l = `<pre class="code ${g[3] ? 'poetry' : g[1].toLowerCase()}">${n((g[2] || g[3]).replace(/^\n+|\n+$/g, ''))}</pre>` : g[5] ? ((p = g[5]).charAt(p.length - 1) === '.' && (p = '.', g[4] = g[4].replace(/^\d+/gm, '')), s = t(n(g[4].replace(/^\s*[>*+.-]/gm, ''))), p !== '>' && (p = p === '.' ? '#' : '*', s = s.replace(/^(.*)(\n|$)/gm, '<li>$1</li>')), l = r[p][0] + s + r[p][1]) : g[7] ? l = `<img src="${e(g[7])}" alt="${e(g[6])}">` : g[9] ? (f = f.replace('<a>', `<a onclick="(function(e){e.stopPropagation();})(event)" href="${e(g[10] || d[u.toLowerCase()])}">`), l = `${a()}</a>`) : g[8] ? l = '<a>' : g[11] || g[13] ? l = `<${p = `h${g[13] ? g[13].length : g[12][0] === '=' ? 1 : 2}`}>${t(g[11] || g[14])}</${p}>` : g[15] ? l = `<code>${g[15]}</code>` : g[16] && (l = c(g[16]))), f += u, f += l;
      return (f + o.substring(h) + a()).trim();
    };
  }()),
};
