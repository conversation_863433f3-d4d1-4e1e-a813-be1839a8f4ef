/** Created by natanavra on 23/02/2018 */

const crypto = require('crypto');
const EVP_BytesToKey = require('evp_bytestokey');

// const IV_LENGTH = 16;
const ALGORITHM = 'aes-256-cbc';
module.exports = {
  randomString(length) {
    return crypto.randomBytes(length / 2).toString('hex');
  },

  encrypt(string, pass) {
    const { key, iv } = getEvp(pass);
    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);
    let encrypted = cipher.update(string);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    return encrypted.toString('hex');
  },

  decrypt(encryptedText, pass) {
    const { key, iv } = getEvp(pass);
    const encryptedBuffer = Buffer.from(encryptedText, 'hex');
    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
    let decrypted = decipher.update(encryptedBuffer);
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    return decrypted.toString();
  },

  safeDecrypt(encryptedText, pass) {
    try {
      return this.decrypt(encryptedText, pass);
    } catch(e) {}
    return null;
  },

  hmacSha256(text, key) {
    return crypto.createHmac('sha256', key).update(text).digest('hex');
  },

  sha256(text, format = 'hex') {
    let digestFormat = format;
    if(digestFormat !== 'hex' && digestFormat !== 'base64') {
      digestFormat = 'hex';
    }
    return crypto.createHash('sha256').update(text).digest(digestFormat);
  },

  md5(text) {
    return crypto.createHash('md5').update(text).digest('hex');
  },

  secureMd5(text, pass) {
    const str = `${pass}-${text}`;
    return this.md5(str);
  },
};

function getEvp(pass) {
  return EVP_BytesToKey(pass, null, 32 * 8, 16);
}
