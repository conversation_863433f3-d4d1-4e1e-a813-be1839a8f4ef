const urlModule = require('url');
const normalizeUrl = require('normalize-url');
const parseDomain = require('parse-domain');
const { badSuggestions } = require('../common/domains');

const protocolWwwRe = /(https?:\/\/)?(www\.)?/;

module.exports = {
  /**
   * Normalize url by removing excess slashes
   * @param {string} url The url
   * @returns {string} A url without a '/' ending if exists
   */
  normalize(url) {
    let retval = url;
    try {
      retval = urlModule.format(urlModule.parse(retval));
      if(retval && retval.substr && retval.substr(-1) === '/') retval = retval.substr(0, retval.length - 1);
      if(retval && retval === decodeURI(retval)) retval = encodeURI(retval);
    } catch(err) {}

    if(retval) retval = retval.toLowerCase();
    return retval;
  },

  normalizeWithPackage(url, opts) {
    try {
      return normalizeUrl(url, opts);
    } catch(err) {
      return null;
    }
  },

  /**
   * Transform a url
   * @param {string} url
   * @param opts keep the part or not
   * @returns {string} a new url string without query params (if query is after #, this will not work)
   */
  transform(url, { query = false, hash = false } = {}) {
    const parsed = urlModule.parse(url.toLowerCase());
    if(!query) {
      parsed.search = '';
      parsed.query = '';
    }
    if(!hash) {
      parsed.hash = '';
    }
    return urlModule.format(parsed);
  },

  isEncoded(str) {
    return !(str === decodeURI(str));
  },

  encode(str) {
    if(!this.isEncoded(str)) return encodeURI(str);
    return str;
  },

  encodeAndLowercase(str) {
    let retval = str;
    if(!this.isEncoded(str)) retval = encodeURI(str);
    return retval.toLowerCase();
  },

  /**
   * Removes the protocol (http/https), www (not other subdomains) and query parameters
   * @param {string} url the URL to clean
   * @return {string} clean URL
   */
  clean(url) {
    if(!url || typeof url !== 'string') return url;

    let retval = '';
    const parsed = urlModule.parse(url);
    if(parsed.hostname) {
      if(parsed.hostname && parsed.hostname.startsWith('www.')) {
        retval += parsed.hostname.substring(4);
      } else {
        retval += parsed.hostname;
      }

      retval += parsed.pathname;

      const { hash } = parsed;
      if(hash && hash.startsWith('#/')) {
        if(hash.indexOf('?') > -1) retval += hash.slice(0, hash.indexOf('?'));
        else if(hash.lastIndexOf('#') > 0) retval += hash.slice(0, hash.lastIndexOf('#'));
        else retval += hash;
      }
    } else {
      // Incomplete URL
      retval = url.replace(protocolWwwRe, '');
      const qIndex = retval.indexOf('?');
      if(qIndex > -1) retval = retval.slice(0, qIndex);
    }

    if(retval && retval[retval.length - 1] === '/') retval = retval.substring(0, retval.length - 1);

    try {
      if(retval && retval === decodeURI(retval)) retval = encodeURI(retval);
    } catch(err) {}

    if(retval) retval = retval.toLowerCase();

    return retval;
  },

  getDomain(url, allowLocal, { includeSubdomain = true } = {}) {
    const opts = allowLocal && { customTlds: /localhost/ };
    const parsed = parseDomain(url, opts);
    if(parsed) {
      const comps = [];
      if(includeSubdomain && parsed.subdomain) comps.push(parsed.subdomain);
      if(parsed.domain) comps.push(parsed.domain);
      if(parsed.tld) comps.push(parsed.tld);
      return comps.join('.');
    }
    return null;
  },

  /**
  *Get url as string end returns path name
  * @param url
  * @returns {string|null} path name
  */
  getPathName(url) {
    let parsed;
    try {
      parsed = new urlModule.URL(url);
    } catch(err) {
      return null;
    }
    if(!parsed || !parsed.pathname) return null;

    return parsed.pathname;
  },

  validUrlSuggestion(url) {
    return !badSuggestions.some(suggest => url.includes(suggest));
  },
};
