const { proxies, platforms } = require('../common/domains');

module.exports.isProxyDomain = function (host) {
  for(let i = 0; i < proxies.length; i++) {
    if(host.includes(proxies[i])) return true;
  }
  return false;
};

module.exports.isPlatformDomain = function (host) {
  for(let i = 0; i < platforms.length; i += 1) {
    if(host && host.includes(platforms[i])) {
      return true;
    }
  }
  return false;
};
