const fs = require('fs').promises;
const path = require('path');

module.exports = {
  getFiles,
};

async function getFiles(dirPath, { recursive = true }) {
  const allFiles = await fs.readdir(dirPath);
  const files = [];
  const promises = [];
  for(let i = 0; i < allFiles.length; i += 1) {
    const filePath = path.join(dirPath, allFiles[i]);
    promises.push(fs.stat(filePath).then((stat) => {
      if(stat.isFile()) {
        files.push(filePath);
      } else if(recursive) {
        return getFiles(filePath, { recursive }).then(paths => files.push(...paths));
      }
      return null;
    }));
  }
  await Promise.all(promises);
  return files;
}
