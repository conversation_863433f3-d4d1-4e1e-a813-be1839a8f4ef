const serializer = require('serialize-error');

module.exports = serializeError;

function serializeError(err) {
  return serializer.serializeError(err);
  // const obj = {
  //   type: err.constructor.name,
  //   message: err.message,
  //   stack: err.stack,
  // };
  //
  // // eslint-disable-next-line no-restricted-syntax
  // for(const key in err) {
  //   if(obj[key] === undefined) {
  //     obj[key] = err[key];
  //   }
  // }
  //
  // if(obj.data) {
  //   if(obj.data.err) {
  //     obj.data.err = serializeError(obj.data.err);
  //   }
  //   if(obj.data.error) {
  //     obj.data.error = serializeError(obj.data.error);
  //   }
  // }
  //
  // return obj;
}
