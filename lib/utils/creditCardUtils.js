const creditCardInfo = require('credit-card-type');

module.exports.getVendor = function (cardNumber) {
  if(!cardNumber || !cardNumber.length) return null;

  const cardInfo = creditCardInfo(cardNumber);
  return (cardInfo && cardInfo.length && cardInfo[0].type) || null;
};

module.exports.getFourDigits = function (cardNumber) {
  if(!cardNumber || !cardNumber.substr) return null;

  const noWhitespace = cardNumber.replace(/\s/g, '');
  return noWhitespace.substr(-4);
};
