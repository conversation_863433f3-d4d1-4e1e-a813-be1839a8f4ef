const _ = require('lodash');
const castPath = require('lodash/_castPath');
const toKey = require('lodash/_toKey');

function geti(object, path, defaultValue) {
  if(!object) return defaultValue === undefined ? null : defaultValue;

  const paths = castPath(path, object);
  const { length } = paths;
  let index = 0;

  let iterator = object;
  while(iterator != null && index < length) {
    const key = (toKey(paths[index])).toLowerCase();
    iterator = findLowercaseKey(iterator, key);
    index += 1;
  }
  return (index && index === length) ? iterator : defaultValue;
}

function findLowercaseKey(value, key) {
  return Object.keys(value).reduce((a, k) => {
    if(a !== undefined) {
      return a;
    }
    if(_.toLower(k) === key) {
      return value[k];
    }
  }, undefined);
}

module.exports = {
  geti,
};
