const _ = require('lodash');
const config = require('../../config');
const logger = require('../logger')('triggers');

const slack = require('../apis/slackNotifier');
const emailAutomationService = require('../../app/common/email.automation.service');
const billingMailer = require('../../app/billing/mailer');
const mailer = require('../../app/common/mailer.service');
const tracker = require('../../app/events/track');

const Notification = require('../../app/notifications/models/Notification');
const Account = require('../../app/account/models/Account');
const Bill = require('../../app/billing/Bill');
const similarWeb = require('../apis/similarWeb');
const emailUtils = require('../utils/emailUtils');
const domainUtils = require('../utils/domainUtils');
const Feed = require('../../app/account/models/Feed');
const { SUBSCRIPTION_SOURCE } = require('../../app/billing/constants');
const analyticsService = require('../../app/common/analytics.service');

const REMARKETING_TYPES = {
  subscription: 'subscription',
  wizard: 'wizard',
  planLimit: 'planLimit',
};

const REMARKETING_MAILER_LITE_FIELDS = {
  subscription: 'saw_subscription_remarketing',
  wizard: 'saw_wizard_remarketing',
  planLimit: 'plan_limit_remarketing',
};


module.exports = {
  REMARKETING_TYPES,
  REMARKETING_MAILER_LITE_FIELDS,

  presignup(account) {
    const group = config.mailerlite.groups.pre_signup;
    emailAutomationService.createSubscription(account.email, { group });
    const data = _.pick(account, ['_id', 'email', 'source', 'origin', 'query', 'location']);
    slack.notify(`Pre Signup ${account.email}`, data, { webhook: config.slack.leads });
  },

  /**
  * @param account The newly created user account
  * @param {object?} opts
  * @param {string?} opts.platform The signup source of the signup (Google | Email)
  * @param {string?} opts.shopDomain shopify domain
  * @param {string?} opts.ip The user's IP (sent to webhook)
  */
  async signup(account, opts) {
    if(!opts) opts = {};

    const source = opts.platform || 'Email';
    let message = `Signup (${source}) ${account.email}`;
    const referrer = _.get(account, 'affiliate.referrer', null);
    if(referrer) message += ` referred by ${referrer}`;

    const fields = {
      signup_type: source,
      plan: 'free',
    };
    const name = account.getName();
    if(name) fields.name = name;

    const { location } = account;
    if(location) {
      if(location.country) fields.country = location.country;
      if(location.city) fields.city = location.city;
    }

    if(opts.shopDomain) {
      fields.shopify = 'true';
      fields.shopify_uninstalled = null;
      fields.domain = opts.shopDomain;
      fields.installed_code = new Date();
      fields.created_notification = new Date();
    }

    const data = _.pick(account, ['_id', 'email', 'source', 'origin', 'query', 'location']);

    const domain = opts.domain || emailUtils.getDomain(account.email);
    if(!emailUtils.isCommonDomain(domain) && !domainUtils.isPlatformDomain(domain)) {
      const swStats = await similarWeb.getStats(domain);
      if(swStats) {
        const visits = swStats.Engagments && swStats.Engagments.Visits;
        if(!swStats.IsSmall && visits > 50000) message = `🔴 ${message}`;
      }
      data.similarWeb = swStats;
    }

    slack.notify(message, data, { webhook: config.slack.leads });
    emailAutomationService.createSubscription(account.email, { fields });

    const webhook = 'f2e15b1d5569a2bd9974906491992b781cf8a807c03a97f96d7b5c0b238cef3f';
    const yosiAccountId = '5a9fda10ed46370b2d767cba';
    tracker.trackWebhook(webhook, { email: account.email, ip: opts.ip, accountId: yosiAccountId });
    Feed.saveFeed(yosiAccountId, 'Webhook Event (Internal)', { email: account.email });
  },

  async installedScript(account, domain, options) {
    const opts = options || {};
    let data = opts.data || null;
    const { email } = account;
    let message = `ProveSource installed by ${email} in ${domain}`;

    if(!domainUtils.isPlatformDomain(domain)) {
      const stats = await similarWeb.getStats(domain);
      if(stats) {
        data = { similarWeb: stats };
        const visits = stats.Engagments && stats.Engagments.Visits;
        if(!stats.IsSmall && visits > 50000) message = `🔴 ${message}`;
      }
    }

    Feed.saveFeed(account.id, `ProveSource code installed on ${domain}`);
    slack.notify(message, data, { webhook: config.slack.install });
    analyticsService.trackInstalledCode(account.id, { domain });
    const fields = {};
    if(!opts.installed) {
      fields.installed_code = new Date();
    }

    if(opts.shopify) {
      fields.shopify = 'true';
      fields.shopify_uninstalled = null;
      fields.domain = domain;
      fields.created_notification = new Date();
    }
    emailAutomationService.updateSubscription(email, fields);
  },

  async notificationCreated(notification, account) {
    try {
      const options = { webhook: config.slack.notifications };
      if(account && account.stats && account.stats.createdNotifications === 1) {
        const fields = { created_notification: new Date() };
        emailAutomationService.updateSubscription(account.email, fields);
      }
      analyticsService.trackNotificationCreated(account.id, { type: notification.type });
    } catch(err) {
      logger.error({ err }, 'failed to populate notification');
    }
  },

  async notificationUpdated(email) {
    try {
      const fields = { updated_notification: new Date() };
      emailAutomationService.updateSubscription(email, fields);
    } catch(err) {
      logger.error({ err }, 'failed to populate notification');
    }
  },

  async onboardingComplete(account, domain, data) {
    if(!account) return;
    if(!data) data = {};

    const { email } = account;
    let message = `onboarding complete ${email}: ${domain}`;
    const date = new Date();
    const fields = {
      onboarding_website: domain,
      onboarding_complete: 'true',
      onboarding_complete_date: date,
      created_notification: date,
    };

    if(!domainUtils.isPlatformDomain(domain)) {
      const stats = await similarWeb.getStats(domain);
      if(stats) {
        data.similarWeb = stats;
        const visits = stats.Engagments && stats.Engagments.Visits;
        if(!stats.IsSmall && visits > 50000) message = `🔴 ${message}`;
      }
    }
    emailAutomationService.updateSubscription(email, fields);
    slack.notify(message, data, config.slack.onboardingComplete);
    analyticsService.trackOnboardingComplete(account.id);
  },

  subscriptionStateChanged(account, ipnName, previousSub, data) {
    const { email } = account;
    const subscription = account.subscription || {};
    const { contractName, source } = subscription;
    const previousPlan = _.get(previousSub, 'plan', null);
    const previousIPN = _.get(previousSub, 'recentIPN', null);
    const previousIsActive = previousIPN === 'CHARGE' || previousIPN === 'RECURRING';
    let message = '';
    if((previousIsActive && ipnName === 'CHARGE') || (ipnName === 'RECURRING' && previousPlan !== subscription.plan)) {
      message = 'POSSIBLE DOUBLE CHARGE? ';
    }
    let identifier = email;
    if(account.kind === 'SubAccount') {
      identifier += ` (SubAccount - ${account.name})`;
    }
    message += `${source} - ${identifier}: ${contractName} (${ipnName})`;

    let extraData = {};
    if(data && !_.isEmpty(data)) {
      extraData = _(data)
        .pick(['failureReason', 'coupon', 'couponCode', 'cancelReason'])
        .omitBy(_.isEmpty).value();
    }

    if(ipnName === 'CONTRACT_CHANGE') {
      extraData.newContract = _.get(data, 'newContractName');
      extraData.oldContract = _.get(data, 'contractName');
    }

    const shop = account.getSubscriptionShop && account.getSubscriptionShop();
    if(shop) {
      extraData.shop = shop.myshopify_domain;
    }

    if(ipnName === 'CHARGE' || ipnName === 'CONTRACT_CHANGE') {
      slack.notify(message, extraData, { webhook: config.slack.charges });
    } else if(ipnName && ipnName.includes('CANCELLATION')) {
      if(extraData && !extraData.cancelReason) {
        if(subscription.source === SUBSCRIPTION_SOURCE.shopify) {
          extraData.cancelReason = 'shopify';
        } else if(subscription.source === SUBSCRIPTION_SOURCE.wix) {
          extraData.cancelReason = 'wix';
        } else if(subscription.source === SUBSCRIPTION_SOURCE.paypal) {
          extraData.cancelReason = 'paypal';
        } else if(subscription.source === SUBSCRIPTION_SOURCE.stripe) {
          extraData.cancelReason = data.cancellation_details;
        } else {
          extraData.cancelReason = 'charge failure?';
        }
      }

      if(_.get(account, 'configuration.emailOpt.ipns', true)) {
        const lastSent = _.get(account, 'stats.sentEmails.cancellation');
        if(!lastSent || lastSent < Date.now() - 86400 * 1000 * 7) {
          const toEmail = account.getEmails();
          const { untilDate } = subscription;
          const { apiKey: sgApiKey } = config.sendgrid;
          const accountName = account.name || null;
          // 30-120 minutes (seconds x 60)
          const delaySeconds = parseInt(Math.random() * (120 - 30 + 1) + 30, 10) * 60;
          Promise.all([
            billingMailer.sendCancellation(sgApiKey, toEmail, untilDate, accountName),
            mailer.sendWhatWeDidWrong({ to: toEmail, delaySeconds, accountName }),
          ]).catch(() => {});
          _.set(account, 'stats.sentEmails.cancellation', Date.now());
        }
      }
      slack.notify(message, extraData, { webhook: config.slack.cancellations });
    }
    slack.notify(message, extraData || data, { webhook: config.slack.ipns });

    emailAutomationService.updateSubscription(email, { plan: contractName, recent_ipn: ipnName });
  },

  remarketingSent(email, type) {
    let webhook = config.slack.install;
    if(type === REMARKETING_TYPES.planLimit) webhook = config.slack.planLimit;
    slack.notify(`Remarketing ${type} for ${email}`, null, { webhook });

    const field = REMARKETING_MAILER_LITE_FIELDS[type];
    emailAutomationService.updateSubscription(email, { [field]: new Date() });
  },

  /**
    *
    * @param {string} accountId
    * @param {object} opts
    * @param {Date} opts.expires
    * @param {Number} opts.daysleft
    * @param {Number} opts.limit
    */
  async planLimitReached(accountId, opts) {
    try {
      const [account, bill, viewedNotification, numNotifications] = await Promise.all([
        Account.findOneAndUpdate({ _id: accountId }, { 'stats.planLimitReached': Date.now() }),
        Bill.findOne({ accountId }).sort({ _id: -1 }).skip(1).limit(1),
        Notification.findOne({ lastView: { $gte: Date.now() - 86400 * 1000 } }),
        Notification.count({ accountId }),
      ]);

      const data = _.pick(opts, ['daysleft', 'limit', 'expires']);
      data.notification_count = numNotifications;
      data.missed_text = '';
      if(bill) {
        const limit = bill.limit || opts.limit;
        const diff = bill.total - limit;
        const sameLimit = limit === account.getPlanLimit();
        if(diff > 0 && sameLimit) data.missed_text = `. Last month ${diff} visitors missed your social proof`;
      }

      const { email } = account;
      let message = `plan limit reached by ${email}`;
      if(!viewedNotification) {
        message += ' (analytics - no impressions)';
        data.zero_impressions = 'true';
      } else {
        data.zero_impressions = 'false';
      }

      if(data.daysleft === 0) {
        message += ' (0 days not sending email)';
      } else {
        emailAutomationService.createSubscription(email, {
          group: config.mailerlite.groups.plan_limit_reached,
          fields: data,
        });
      }
      analyticsService.trackPlanLimitReached(accountId, data);

      slack.notify(message, data, { webhook: config.slack.planLimit });
    } catch(err) {
      logger.error({ err }, 'planLimitReached trigger failed');
      slack.notifyError(err, 'planLimitReached trigger failed', { webhook: config.slack.errors });
    }
  },

  async reached90Percent(accountId, limit) {
    try {
      const account = await Account.findOneAndUpdate({ _id: accountId }, {
        'stats.planLimit90Reached': Date.now(),
      }).select('email name');
      await slack.notify(`${account.email} 90% plan limit reached`,
        { limit },
        { webhook: config.slack.planLimit });
    } catch(err) {
      const msg = 'failed to send 90% reached email';
      logger.error({ err }, msg);
      slack.notifyError(err, msg);
    }
  },
};
