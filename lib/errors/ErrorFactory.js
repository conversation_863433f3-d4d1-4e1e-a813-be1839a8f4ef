
class ServerError extends Error {
  constructor(message, code) {
    super(message);
    this.name = this.constructor.name;
	    this.code = code || 500;
	    Error.captureStackTrace(this, ServerError);
  }
}

/**
 * Create a ServerError object
 * @param message The error's message
 * @param {number} [code=400] The error's code, can be omitted
 * @param {object|function} [data] Additional internal data for logs, can be omitted
 * @param {object} [context=ErrorFactory] The object to ignore/delete from the stack trace, can be omitted
 * @returns {ServerError} Error object with specified message and code
 * @constructor
 */
function ErrorFactory(message, code, data, context = ErrorFactory) {
  const defaultCode = 400;
  if(typeof code !== 'number') data = code;
  if(typeof data === 'function') {
    context = data;
    data = null;
  }

  if(!code || isNaN(code)) code = defaultCode;

  const error = new ServerError(message, code);
  if(data) error._data = data;
  Error.captureStackTrace(error, context || ErrorFactory);
  return error;
}

ErrorFactory.Error = ServerError;
ErrorFactory.AccountNotFound = function (accountId) {
  return ErrorFactory('account not found', 400, { accountId }, this.AccountNotFound);
};

ErrorFactory.ProcessingError = function (code, data) {
  let _code = code; let
    _data = data;
  if(typeof code === 'object') {
    _data = code;
    _code = null;
  }
  return ErrorFactory(`failed processing request ${data.reason ? `(${data.reason})` : ''}`, _code || 500, _data, this.ProcessingError);
};

module.exports = ErrorFactory;
