const ErrorFactory = require('./ErrorFactory');

module.exports = {
  methodNotAllowed(method) {
	    return ErrorFactory(`method '${method}' not allowed`, 405, this.methodNotAllowed);
  },
  endpointNotFound(path) {
    return ErrorFactory(`endpoint not found ${path}`, 404, this.endpointNotFound);
  },
  processingError() {
    return ErrorFactory('failed processing request', 500, this.processingError);
  },
};
