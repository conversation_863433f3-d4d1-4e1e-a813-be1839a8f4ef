/* eslint-disable no-use-before-define */
const _ = require('lodash');
const maxmind = require('maxmind');
const geolite2 = require('geolite2');

const loader = (async () => maxmind.open(geolite2.paths.city))();

/**
 * @param {string} ip
 * @return {{country, countryCode, state, stateCode, city} | null} location result
 */
async function getLocationFromIP(ip) {
  if(!ip) return null;

  const db = await loader;
  if(!db || !db.get) return null;
  return formatResult(db.get(ip));
}

function formatResult(location) {
  const country = _.get(location, 'country.names.en', null);
  const countryCode = _.get(location, 'country.iso_code', null);
  const city = _.get(location, 'city.names.en', null);
  if(!city && !country && !countryCode) return null;

  const result = { country, countryCode, city };

  const state = _.get(location, 'subdivisions[0].names.en', null);
  const stateCode = _.get(location, 'subdivisions[0].iso_code', null);
  if(state) Object.assign(result, { state, stateCode });

  return result;
}

module.exports = { geoIP: getLocationFromIP, getLocationFromIP };
