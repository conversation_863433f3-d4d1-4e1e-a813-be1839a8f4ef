const mongoose = require('mongoose');

const { ReadPreference } = mongoose.mongo;
const logger = require('../logger')('database');
const slack = require('../apis/slackNotifier');

module.exports.load = function load(url, readPref = 'primary') {
  let readPreference = ReadPreference.PRIMARY_PREFERRED;
  if(readPref === 'secondary') {
    readPreference = ReadPreference.SECONDARY_PREFERRED;
  }
  const promise = mongoose.connect(url, {
    autoReconnect: true,
    reconnectTries: 60,
    reconnectInterval: 10000,
    readPreference,
    // useNewUrlParser: true,
    // useUnifiedTopology: true,
    // useCreateIndex: true,
  });

  const db = mongoose.connection;
  db.on('error', (err) => {
    slack.notifyError(err, 'mongo connection error');
    logger.error({ err, critical: true }, 'mongo connection error');
  });

  db.on('disconnected', () => {
    const err = new Error('database disconnected');
    slack.notifyError(err, 'mongo disconnected');
    logger.error({ err, critical: true }, 'mongo disconnected');
  });

  db.on('reconnectFailed', () => {
    const err = new Error('unable to reconnect to database');
    slack.notifyError(err, 'mongo reconnect failed');
    logger.error({ err, critical: true }, 'mongo reconnect failed');
  });

  db.on('reconnected', () => {
    logger.info('mongo reconnected');
  });

  db.on('connected', () => {
    logger.info('mongo connected');
  });

  db.once('open', () => {
    logger.info({ url }, 'mongo connection open');
  });

  return promise;
};
