/* eslint-disable no-console */
const process = require('process');

let listenCalled = false;

module.exports.listen = (server = null, notifier, logger) => {
  if(listenCalled) {
    // this function can only be called once per process
    return;
  }
  listenCalled = true;
  process.on('exit', (code) => {
    if(server) {
      server.close();
    }

    if(code > 0) {
      logger.error({ code }, 'process exit non 0 code');
      notifier.notifyError(null, 'process exit with non 0 code', { code });
    } else {
      logger.warn('process exit with code 0');
    }
  });

  process.on('unhandledRejection', (err, p) => {
    logger.error({ err }, 'unhandled promise rejection');
    notifier.notifyError(err, 'unhandled promise rejection');
  });

  process.on('uncaughtException', (err) => {
    logger.error({ err }, 'uncaught exception, shut down');
    if(err && err.code) {
      if(err.code === 'EACCES') {
        logger.error('bind requires elevated privileges');
      } if(err.code === 'EADDRINUSE') {
        logger.error('bind is already in use');
      }
    }
    notifier.notifyError(err, 'uncaught exception');
    return process.exit(1);
  });

  process.on('SIGINT', () => {
    logger.info('server received kill signal, graceful shut down');
    return process.exit(0);
  });

  process.on('warning', (warning) => {
    logger.warn({ warning }, 'process warning');
    notifier.notifyError(warning, 'process warning');
  });

  logger.info('process listeners active');
};
