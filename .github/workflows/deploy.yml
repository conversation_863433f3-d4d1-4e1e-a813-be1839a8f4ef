# This is a basic workflow to help you get started with Actions

name: deploy production

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the develop branch
on:
  workflow_dispatch:
#   push:
#     branches: 
#     - master

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  deploy:
    # The type of runner that the job will run on
    runs-on: ubuntu-18.04

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      - name: create ssh files
        run: |
          echo creating ssh files
          SSHPATH="$HOME/.ssh"
          mkdir -p "$SSHPATH"
          touch "$SSHPATH/known_hosts"
          
          echo "${{ secrets.API_SERVER_SSH_KEY }}" > "$SSHPATH/deploy_key.pem"
          chmod 700 "$SSHPATH"
          chmod 600 "$SSHPATH/known_hosts"
          chmod 600 "$SSHPATH/deploy_key.pem"

      - name: deploy
        run: |
          SSHPATH="$HOME/.ssh"
          echo deploying to server
          ssh ec2-user@********** -o StrictHostKeyChecking=no -i $SSHPATH/deploy_key.pem -t "chmod +x ./update-server.sh && ./update-server.sh 6.33.0"
          
      - name: cleanup
        run: |
          echo removing ssh file
          rm -rf "$SSHPATH/deploy_key.pem"
          
