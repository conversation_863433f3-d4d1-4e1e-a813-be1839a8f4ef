const express = require('express');
const _ = require('lodash');
const routeMiddlewareLoader = require('./routerMiddlewares');

const ROOT_ROUTES = {
    '/': require('../app/routes'),
    '/account': require('../app/account/routes'),
    '/notifications': require('../app/notifications/routes'),
    '/events': require('../app/events/routes'),
    '/webhooks': require('../app/webhooks/routes'),
    '/leads': require('../app/leads/routes'),
    '/shopify': require('../app/shopify/routes'),
    '/script': require('../app/script/routes'),
    '/wix' : require('../app/wix/routes'),
    '/zapier' : require('../app/zapier/routes'),
    '/bigcommerce' : require('../app/bigcommerce/routes'),
};

module.exports = (function() {
    let routers = {};
    for(let rootPath in ROOT_ROUTES) {
        let subRoutes = ROOT_ROUTES[rootPath];
        let router = express.Router();
        for(let routePath in subRoutes) {
            let route = subRoutes[routePath];

            let error = null;
            const handles = _.compact([route.handle, route.handleGET, route.handlePOST, route.handleDELETE, route.handlePUT]);
            const handlesError = validateHandles(handles);
            if(handlesError) error = handlesError;
            else if(!route.config) error = 'no route.config';
            else if(!Array.isArray(route.config.methods)) error = 'route.config.methods (http methods) must be an array';
            else if(!route.config.noAuth && !route.config.authType) error = "route.config must have 'authType' or 'noAuth'";
            else if(!route.schema) error = 'no route.schema';

            if(error) {
                error += ` for '${rootPath}${routePath}'`;
                throw new Error(error);
            }

            routeMiddlewareLoader(router, routePath, route);
        }
        routers[rootPath] = router;
    }

    return routers;
})();

function validateHandles(handles) {
    let error = null;
    for(let i = 0 ; i < handles.length ; i ++) {
        const handle = handles[i];
	    if(typeof handle !== 'function') error = 'no handle OR handleGET or handlePOST';
	    else if(handle.length !== 3) error = 'route handle must be a middleware (req, res, next)';

	    if(error) break;
    }
    return error;
}
