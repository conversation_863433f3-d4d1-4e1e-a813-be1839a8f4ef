const os = require('os');
const cookieSession = require('cookie-session');
const compression = require('compression');

const cookieParser = require('cookie-parser');
const logger = require('../lib/logger')('middlewareLoader');
const config = require('../config');
const responseHandler = require('./responseHandler');
const errorHandler = require('./errorHandler');
const httpLogger = require('../lib/logger/httpLogger');
const headersHandler = require('./headersHandler');

module.exports.load = function (app) {
  logger.info('loading middlewares');

  app.use(compression({ threshold: '100B' }));
  app.use(cookieSession(config.cookie));
  app.use(cookieParser(config.cookie.secret));
  app.use(httpLogger);
  app.use(headersHandler);
  app.use((req, res, next) => {
    res.set('x-ps-host', os.hostname());
    req.locals = {};
    next();
  });

  const routers = require('./routers');
  for(const route in routers) {
    app.use(route, routers[route]);
  }

  app.get('/', (req, res, next) => {
    req.locals.handled = true;
    res.body = { success: true };
    next();
  });

  // Keep last
  app.use(responseHandler);
  app.use(errorHandler);
};
