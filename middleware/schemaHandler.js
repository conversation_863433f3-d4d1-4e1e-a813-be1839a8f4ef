const { validate } = require('jsonschema');
const objectUtils = require('../lib/utils/objectUtils');
const ErrorFactory = require('../lib/errors/ErrorFactory');

module.exports = function (req, res, next) {
  let { schema } = req.locals;
  if(schema.hasOwnProperty(req.method)) schema = schema[req.method];

  let params = {};
  const canHaveBody = ['DELETE', 'POST', 'PUT'].includes(req.method);
  if(canHaveBody && !objectUtils.isEmpty(req.body)) {
    params = req.body;
  } else {
    params = req.query;
  }

  const result = validate(params, schema);
  if(result.valid) next();
  else next(ErrorFactory(result.errors, { reqParams: params }));
};
