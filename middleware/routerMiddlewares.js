const bodyParser = require('body-parser');
const { ObjectId } = require('mongoose').Types;
const config = require('../config');
const methodHandler = require('./methodHandler');
const authHandler = require('./authHandler');
const schemaHandler = require('./schemaHandler');
const botHandler = require('./botHandler');
const localsHandler = require('./localsHandler');

module.exports = function (router, path, route) {
  router.use(path, (req, res, next) => {
    req.locals.handled = false;
    req.locals.path = path;
    req.locals.config = route.config;
    req.locals.schema = route.schema;
    next();
  });
  router.use(path, localsHandler);

  if(route.config.checkBot) {
    router.all(path, botHandler);
  }

  let verify = null;
  if(route.config.keepRawBody) {
    verify = function (req, res, body, encoding) {
      req.rawBody = body;
    };
  }
  router.use(path, bodyParser.json({ type: 'application/json', limit: route.config.maxPayload || '200kb', verify }));
  router.use(path, bodyParser.urlencoded({
    type: '*/x-www-form-urlencoded', limit: '100kb', verify, extended: true,
  }));
  router.use(path, bodyParser.text({ type: 'text/*', verify }));
  router.use(path, (req, res, next) => {
    const json = req.is('json');
    const urlencoded = req.is('urlencoded');
    const text = req.is('text');
    if(urlencoded) {
      const keys = req.body && Object.keys(req.body);
      if(keys.length === 1 && req.body[keys[0]] === '') {
        attemptBodyJsonParse(req, req.rawBody && req.rawBody.toString());
      }
    } else if(text) {
      if(req.body && (req.body.includes('{') || req.body.includes('['))) {
        attemptBodyJsonParse(req, req.rawBody && req.rawBody.toString());
      }
    } else if(!json) {
      attemptBodyJsonParse(req, req.rawBody && req.rawBody.toString());
    }
    next();
  });

  router.all(path, methodHandler);
  router.all(path, authHandler);
  router.all(path, schemaHandler);

  if(route.handle) router.all(path, handler(route.handle));
  if(route.handleGET) router.get(path, handler(route.handleGET));
  if(route.handlePOST) router.post(path, handler(route.handlePOST));
  if(route.handleDELETE) router.delete(path, handler(route.handleDELETE));
  if(route.handlePUT) router.put(path, handler(route.handlePUT));
  if(route.handleHEAD) router.head(path, handler(route.handleHEAD));
};

function attemptBodyJsonParse(req, toParse) {
  try {
    req.body = JSON.parse(toParse);
  } catch(err) {}
}

/**
 * This is an endpoint function wrapper - if it fails/throws, we catch it here and next(err)
 * @param {function} fn - the endpoint handler
 * @return {Function} express middleware function (req, res, next)
 */
function handler(fn) {
  return async (req, res, next) => {
    try {
      req.locals.handled = true;
      await fn(req, res, next);
    } catch(err) {
      next(err);
    }
  };
}
