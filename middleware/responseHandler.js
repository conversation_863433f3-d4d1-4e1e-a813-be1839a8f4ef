
const Errors = require('../lib/errors/Errors');
const config = require('../config');
const { cookieUtils } = require('../lib/utils');

module.exports = function responseHandler(req, res, next) {
  // requests that affect database take 20-50ms to update test db
  if(config.env !== 'test') {
    if(!renderResponse(req, res)) {
      next(Errors.endpointNotFound(req.originalUrl));
    }
  } else {
    setTimeout(() => {
      if(!renderResponse(req, res)) {
        next(Errors.endpointNotFound(req.originalUrl));
      }
    }, 50);
  }
};

function renderResponse(req, res) {
  if(req.route || (req.locals && req.locals.handled)) {
    const firstPartyHeader = cookieUtils.getHeaderValue(req);
    if(firstPartyHeader) {
      res.set(cookieUtils.header, firstPartyHeader);
    }
    res.send(res.body || { message: 'no results' });
    return true;
  }
  return false;
}
