
module.exports = function (req, res, next) {
  const origin = req.headers.origin || req.headers.host;
  req.remoteAddress = req.headers['x-forwarded-for']
    || req.headers['x-real-ip']
    || req.connection.remoteAddress
    || req.ip;
  if(req.remoteAddress) {
    req.remoteAddress = req.remoteAddress.split(',')[0];
  }

  res.header('Access-Control-Allow-Origin', origin);
  res.header('Access-Control-Max-Age', 7200);
  res.header('Access-Control-Allow-Credentials', true);
  res.header('Access-Control-Allow-Methods', 'GET, PUT, POST, DELETE, HEAD, OPTIONS');
  res.header('Access-Control-Expose-Headers', 'x-ps-first');
  if(req.headers['access-control-request-headers']) {
    res.header('Access-Control-Allow-Headers', req.headers['access-control-request-headers']);
  }
  res.header('X-Request-ID', req.id);
  next();
};
