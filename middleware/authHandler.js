const _ = require('lodash');

const jwt = require('jsonwebtoken');
const ErrorFactory = require('../lib/errors/ErrorFactory');
const config = require('../config');
const authTypes = require('./authTypes');

module.exports = function (req, res, next) {
  let success = false;

  if(req.locals.config.noAuth || req.locals.config.authType === authTypes.noAuth) {
    success = true;
  }
  const anyAuth = authAny(req);
  const { authType } = req.locals.config;
  if(_.isString(authType)) {
    success = authWithType(req, res, authType);
  } else if(_.isArray(authType)) {
    success = authWithArray(req, res, authType);
  } else if(_.isObject(authType)) {
    const method = req.method.toUpperCase();
    const authMethod = authType[method];
    if(_.isArray(authMethod)) success = authWithArray(req, res, authMethod);
    else success = authWithType(req, res, authMethod);
  }

  if(!success) {
    if(anyAuth) return next(ErrorFactory('request forbidden', 403));
    return next(ErrorFactory('request not authorized', 401));
  }
  return next();
};

module.exports.decodeJwt = decodeJwt;

function authAny(req) {
  return consoleAuth(req) || apiAuth(req);
}

function authWithArray(req, res, availableAuthTypes) {
  let result = false;
  for(let i = 0; i < availableAuthTypes.length; i++) {
    result = authWithType(req, res, availableAuthTypes[i]);
    if(result) break;
  }
  return result;
}

function authWithType(req, res, authType) {
  switch(authType) {
  case authTypes.console:
    return consoleAuth(req);
  case authTypes.api:
    return apiAuth(req);
  case authTypes.noAuth: {
    const data = decodeJwt(req.headers.authorization);
    if(data) {
      req.jwtData = data;
    }
    return true;
  }
  case authTypes.backoffice:
    return backofficeAuth(req, res);
  }
}

function consoleAuth(req) {
  return !!req.session.accountId;
}

function apiAuth(req) {
  if(!req.headers.authorization) return false;

  const data = decodeJwt(req.headers.authorization);
  if(data) {
    req.jwtData = data;
    return true;
  }
  req.log.error('jwt verify failed');
  return false;
}

function decodeJwt(authHeader) {
  if(!authHeader || !authHeader.length) {
    return null;
  }
  const comps = authHeader.split(' ');
  if(comps[0].toLowerCase() === 'bearer') {
    const token = comps[1];
    try {
      const data = jwt.verify(token, config.jwt.api);
      data.apiKey = token;
      return data;
    } catch(err) {}
  }
  return null;
}

function backofficeAuth(req, res) {
  const { id, role } = req.locals.backoffice || {};
  const { permissions } = req.locals.config;
  if(!id) {
    res.clearCookie('ps_backoffice', config.getCookieConfig());
    throw ErrorFactory('request not authorized', 401);
  }
  if(permissions && !permissions.includes(role)) {
    throw ErrorFactory('request forbidden', 403);
  }
  return true;
  // return !!(req.locals.backoffice && req.locals.backoffice.id);
  // return headers.authorization === config.admin.secret
  //   || body.secret === config.admin.secret
  //   || false;
}
