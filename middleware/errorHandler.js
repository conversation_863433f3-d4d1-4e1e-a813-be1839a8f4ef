const _ = require('lodash');
const logger = require('../lib/logger')('errorHandler');
const notifierService = require('../app/common/notifier.service');

function getNumber(errCode) {
  const num = _.toNumber(errCode);
  if(_.isFinite(num)) {
    return num;
  }
  return null;
}

module.exports = function errorHandler(err, req, res, next) {
  res.err = err;
  if(req.headersSent) {
    return next(err);
  }

  if(!res.statusCode || res.statusCode === 200) {
    const errCode = getNumber(err.code) || getNumber(err.status) || getNumber(err.statusCode);
    if(!errCode || errCode < 100 || errCode >= 600) {
      res.status(500);
    } else {
      res.status(errCode);
    }
  }

  if(res.statusCode >= 500) {
    const { url } = req;
    const headers = _.omit(req.headers, ['cookie']);
    const data = {
      locals: _.omit(req.locals, ['schema']),
      session: req.session,
      url,
      headers,
      query: req.query,
    };
    data.body = req.body;
    if(Buffer.isBuffer(data.body)) {
      data.body = data.body.toString();
    }
    if(_.isObject(data.body)) {
      data.body = _.omitBy(data.body, (value, key) => {
        const lowerKey = key.toLowerCase();
        return (lowerKey.includes('pass')
          || lowerKey.includes('password')
          || lowerKey.includes('card')
          || lowerKey.includes('secret'));
      });
    }
    notifierService.notifyError(err, err.message || 'server error', data);
  }

  if(!req.log || req.log.level === 'silent') {
    logger.error({ req, err }, 'response error');
  }

  const body = { error: err.message };
  if(err.referenceCode) body.referenceCode = err.referenceCode;
  return res.send(body);
};
