const Errors = require('../lib/errors/Errors');

module.exports = function (req, res, next) {
  const allowedMethods = (req.locals && req.locals.config && req.locals.config.methods) || null;
  const method = req.method.toUpperCase();
  if(method === 'OPTIONS') {
    const { origin, referer } = req.headers;
    if((origin && origin.includes('tunnelto.dev'))
      || (referer && referer.includes('tunnelto.dev'))) {
      return res.send({ success: true }).end();
    }
    return res.status(200).end();
  } if(allowedMethods && allowedMethods.indexOf(method) === -1) {
    next(Errors.methodNotAllowed(method));
  } else {
    next();
  }
};
