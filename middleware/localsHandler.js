const jwt = require('jsonwebtoken');
const { ObjectId } = require('mongoose').Types;
const config = require('../config');
const BackofficeUser = require('../app/backoffice/BackofficeUser');
const Account = require('../app/account/models/Account');

module.exports = async function adminHandler(req, res, next) {
  const adminCookie = req.cookies.ps_admin || '';
  const comps = adminCookie.split('.');
  const isValidPass = comps.length > 1 && comps[1] === config.admin.pass;
  const isValidId = ObjectId.isValid(comps[0]);
  req.locals.accountId = req.session && req.session.accountId;
  req.locals.email = req.session.email;
  req.locals.ps_admin = false;
  if(isValidPass && isValidId) {
    req.locals.accountId = comps[0];
    req.locals.ps_admin = true;
  }
  req.locals.debug = req.cookies.ps_debug === 'true';

  try {
    if(req.baseUrl.includes('/backoffice') && req.cookies.ps_backoffice) {
      const { id } = jwt.verify(req.cookies.ps_backoffice, config.admin.secret);
      const user = await BackofficeUser.findOne({ _id: id });
      if(user) {
        req.locals.backoffice = { id, role: user.role };
      }
    }
  } catch(err) {}

  return next();
};
