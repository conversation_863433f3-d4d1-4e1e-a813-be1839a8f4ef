const PORT = process.env.PORT || 3001;
const express = require('express');
const processListeners = require('./processListeners');

const { version } = require('./package.json');
const config = require('./config');
const logger = require('./lib/logger')('server.js');
const slack = require('./lib/apis/slackNotifier');
const databaseLoader = require('./lib/mongooseLoader');
const cachegooseLoader = require('./lib/cachegooseLoader');
const pubsub = require('./cron/pubsub');
const router = require('./cron/router');

require('./lib/redisClient').getClient(config.redis.port, config.redis.host);

databaseLoader.load(config.mongodb.url, config.mongodb.readPref).then(() => {
  logger.info(config.mongodb, 'db connected');
});
cachegooseLoader('redis', config.redis.port, config.redis.host);
pubsub.setupListeners();


const app = express().use(router);
const server = app.listen(PORT);
logger.info({ version, PORT, env: process.env.NODE_ENV || 'development' }, 'server up');
processListeners.listen(server, slack, logger);
