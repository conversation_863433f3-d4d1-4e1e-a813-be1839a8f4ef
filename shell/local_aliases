#!/bin/sh

alias cd-ps="cd ~/Projects/ProofSource"
alias cd-ps-server="cd ~/Projects/ProofSource/server"
alias cd-ps-scripts="cd ~/Projects/ProofSource/scripts"
alias cd-ps-console="cd ~/Projects/ProofSource/console"
alias cd-ps-js-sdk="cd ~/Projects/ProofSource/js-sdk"
alias ps-customer="node ~/Projects/ProofSource/scripts/getCustomerData.js"
alias ps-account="node ~/Projects/ProofSource/scripts/accounts/findSubscription.js"
alias ps-notification="node ~/Projects/ProofSource/scripts/getNotificationData.js"
alias ps-set-whitelabel="node ~/Projects/ProofSource/scripts/setWhitelabel.js"
alias ps-reset-limit="node ~/Projects/ProofSource/scripts/resetPlanLimit.js"
alias ps-find-accounts="node ~/Projects/ProofSource/scripts/findAccounts.js"
alias ps-affiliate="node ~/Projects/ProofSource/scripts/accounts/findAffiliate.js"
alias ps-updateCard-email="node ~/Projects/ProofSource/scripts/emails/updateCard.js"

