#!/bin/sh

packageVersion=$(node -e "console.log(require('./package.json').version)")
version=${1:-${packageVersion}}

. ./shell/.envrc

if [ ! -f "${IDENTITY_FILE}" ]; then
  echo "ssh key not found ${IDENTITY_FILE}"
  exit -1;
else
  echo "ssh key found"
fi

echo "updating servers with $version"
echo "*************************** git merge, tag and push..."
git checkout master
git merge develop --no-ff --no-edit
git tag ${version}
git push github ${version}
git push github master
git push github develop
git checkout develop

COUNTER=0
for server in "${UBUNTU_API[@]}"
do
  echo "******************************* updating server ${server}"
  #copy config file (untracked in git)
  scp -i "${IDENTITY_FILE}" -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null config/prodConfig.js shell/.envrc shell/copy-to-server/update-server.sh ubuntu@${server}:~
  #deploy version
  ssh ubuntu@${server} -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -i "${IDENTITY_FILE}" -t "chmod +x ./update-server.sh && ./update-server.sh ${version}"
  COUNTER=$((COUNTER+1))
  echo "========= updated ${COUNTER} servers ========="
  sleep 10 # deploy make server unreachable for about 10 seconds
done

for server in "${CRON_SERVERS[@]}"
do
  echo "******************************* updating cron server ${server}"
  #copy config file (untracked in git)
  scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -i "${IDENTITY_FILE}" config/prodConfig.js shell/.envrc shell/copy-to-cron-server/update-server.sh ubuntu@${server}:~
  #deploy version
  ssh ubuntu@${server} -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -i "${IDENTITY_FILE}" -t "chmod +x ./update-server.sh && ./update-server.sh ${version}"
  COUNTER=$((COUNTER+1))
  echo "========= updated ${COUNTER} servers ========="
  sleep 10 # deploy make server unreachable for about 10 seconds
done
