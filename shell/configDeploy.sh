. ./shell/.envrc

for server in "${UBUNTU_API[@]}"
do
  echo "********** pushing configuration to api server ${server}"
  scp -i "${IDENTITY_FILE}" -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ConnectTimeout=5 config/prodConfig.js ubuntu@${server}:server/config/prodConfig.js
  ssh -i "${IDENTITY_FILE}" -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ConnectTimeout=5 ubuntu@${server} -t "pm2 reload all"
  sleep 10
done

for server in "${CRON_SERVERS[@]}"
do
  echo "********** pushing configuration to cron server ${server}"
  scp -i "${IDENTITY_FILE}" -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ConnectTimeout=5 config/prodConfig.js ubuntu@${server}:server/config/prodConfig.js
  ssh -i "${IDENTITY_FILE}" -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ConnectTimeout=5 ubuntu@${server} -t "pm2 reload all"
  sleep 10
done
