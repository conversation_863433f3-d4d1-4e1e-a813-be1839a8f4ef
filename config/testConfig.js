const cookieDomain = '';

module.exports = {
  env: 'test',
  isProduction() {
    return this.env === 'prod';
  },
  apiDomain: 'localhost',
  apiUrl: 'http://localhost:3000',
  mongodb: {
    url: 'mongodb://127.0.0.1:27017/proofsrc-test',
    readPref: 'secondary',
  },
  dbUrl: 'mongodb://127.0.0.1:27017/proofsrc-test',
  redis: {
    port: 6379,
    host: '127.0.0.1',
  },
  consoleUrl: 'http://localhost:3001/app',
  thankYou: 'http://test.com/thank-you.html',
  disabledLoggers: [],
  disableLogRequestStart: true,
  getSocialProfiles: false,
  bluesnap: {
    tokenUrl: 'https://sandbox.bluesnap.com/services/2/payment-fields-tokens',
    apiKey: 'API_152137882877668248147',
    password: 'BlueSand123',
  },
  sendgrid: {
    active: false,
    apiKey: 'NOT AVAILABLE',
  },
  scrapfly: {
    apiKey: 'scp-test-0227cfe73a5a4887be3a583c632a81b7',
  },
  digitalOcean: {
    spaces: {
      accessKey: '',
      secret: '',
      bucket: 'cdn-provesrc',
      endpoint: 'nyc3.cdn.digitaloceanspaces.com',
    },
  },
  greenInvoice: {
    apiKey: '01937bd1-95bc-4402-a585-31a88b32469b',
    secret: 'jnfRGyKFRcSbOzwjvxDfiA',
  },
  mailerlite: {
    active: true,
    groups: {
      customers: '********',
      pre_signup: '9930480',
      plan_limit_reached: '********',
      plan_selected: '********',
    },
    apiKey: 'f55ad7caffe1424a68e8bed53b6b8cbd', // Natan Mailerlite account
  },
  segment: {
    context: {
      integrations: {
        name: 'provesource',
        version: '1.0.0',
      },
    },
  },
  shopify: {
    apiKey: 'd6d05c42f519de8ac68ccccc82eac6f4',
    secret: '70b8caff6e94b72bbad35a1b47d1393f',
    redirect: 'http://localhost:3000/shopify/setup',
    scope: 'read_products,read_orders,write_script_tags',
    confirm: 'http://localhost:3000/shopify/confirm',
  },
  slack: {
    active: false,
    install: '*****************************************************************************',
    notifications: '*****************************************************************************',
    leads: '*****************************************************************************',
    ipns: '*****************************************************************************',
    charges: '*****************************************************************************',
    affiliates: '*****************************************************************************',
    errors: '*****************************************************************************',
    planLimit: '*****************************************************************************',
    shopify: '*****************************************************************************',
    preview: '*****************************************************************************',
    onboardingComplete: '*****************************************************************************',
    warnings: '*****************************************************************************',
    psAds: '*****************************************************************************',
    serverMessages: '',
  },
  jwt: {
    api: 'CCd9aY44Z*w!d@=n',
  },
  cryptoKeys: {
    webhook: 'EMbCoIi5JaT1fSijvNjUQwPLSy6OSmOI',
    mailto: 'DVOb0MmihHvFDKT9tGQzZTeQ7hp26ZS3',
    passReset: 'F1VWWLURJcOBSA4Vxs0R0Ua9CZGJbm2s',
    cloudflare: 'XKIkkRR2c4W5HCq52vQ019M5E3elViBO6KsshLM__tA=',
    activate: 'gbALE4oAMD5MaaA4jzfxpJnolCSVDk7C',
    changeEmail: 'YyLd4neAU&CPy$ubB%9Gv!hG',
    wixInstallEmail: 'G9DVb0ihzMm3tFhK9FDGTTeZQ7p26ZS',
  },
  googleAuth: {
    clientId: '************-h2uq0oj0ip4aqv6jftqmb48ll72kdntl.apps.googleusercontent.com',
    clientSecret: 'n0Ba1dQfMjsU38M8u82xByk8',
    callback: 'http://localhost:3000/account/googleCallback',
  },
  cookies: {
    generic: {
      domain: '',
      httpOnly: false,
      signed: false,
    },
    ps_login: {
      domain: '',
      expires: new Date('2030-01-01'),
      httpOnly: false,
      secure: false,
      signed: true,
    },
  },
  cookie: {
    name: 'ps_session',
    secret: 'super!SpecialStrong13Pass',
    domain: '',
    maxAge: 60 * 60 * 24 * 100 * 1000, // 100 days,
    httpOnly: false,
    secure: false,
  },
  psuidCookie: {
    domain: '',
    expires: new Date('2030-01-01'),
    httpOnly: true,
    secure: false,
    signed: true,
  },
  psacctsCookie: {
    domain: '',
    httpOnly: true,
    secure: false,
    signed: true,
  },
  getCookieConfig(domain, expires, maxAge, httpOnly, secure, signed) {
    return {
      domain: domain || cookieDomain,
      maxAge: maxAge || ********,
      httpOnly: httpOnly || false,
      secure: secure || false,
      signed: signed || false,
    };
  },
  googlePlaces: {
    key: '',
  },
  wix: {
    appId: 'appppppsID',
    secret: 'secret',
    redirectUrl: 'localhost:3000/wix/auth',
    publicKey: '-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyBWAazyP9VtAM+Cp+OeLHKsX07/SlBlttxBtXdCJZLWa1Ruj8l1GU4Luz8uvG9OghZI94aNtYLlu3PFUBNI2adAQHRPwUeQK1zrWzo22Sx98k9mo5JCtRc8szXC0Q2z9Cn7rfAdP/B/U5WrUfRrXBO0xKIbEBZiw0S9FPdmnXdFA8n6p2NbqkTLCrR4VTT4ynz5prrTZ+46ep2HjBzzI0DyOs8f7EbH5A0I/FOkNIeEOkiEQfJuWbxcvHeT4QdgtcfctD3D+XkFGUDYnmnpFNY6mpsZkKrdHEooDk+Mi84mABAHIAqdFXfnyij2x5BG7/Uc2XNVu+aRvHsq6iWg/UQIDAQAB\n-----END PUBLIC KEY-----',
  },
  zapier: {
    secret: '654321',
  },
};
