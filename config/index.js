/* eslint-disable global-require */

const _ = require('lodash');
const template = require('./template');

/**
 * @callback getCookieConfig
 * @param {string?} domain
 * @param {date?} expires
 * @param {timestamp} maxAge
 * @param {boolean} httpOnly
 * @param {boolean} secure
 * @param {boolean} signed
 */

/**
 * @typdef {object} AppConfig
 * @property {string} AppConfig.env - current environment, either `prod`, `dev` or `test`
 * @property {function} AppConfig.isProduction - check if current environment is production
 * @property {object} AppConfig.logs
 * @property {boolean} AppConfig.logs.pretty
 * @property {boolean} AppConfig.logs.enabled
 *
 * @property {string} AppConfig.apiUrl
 * @property {string} AppConfig.consoleUrl
 * @property {string} AppConfig.scriptUrl
 * @property {string} AppConfig.thankYou
 *
 * @property {object} AppConfig.cryptoKeys - crypto keys
 * @property {object} AppConfig.cryptoKeys.webhook
 * @property {object} AppConfig.cryptoKeys.mailto
 * @property {object} AppConfig.cryptoKeys.passReset
 * @property {object} AppConfig.cryptoKeys.cloudflare
 * @property {object} AppConfig.cryptoKeys.activate
 * @property {object} AppConfig.cryptoKeys.changeEmail
 * @property {object} AppConfig.cryptoKeys.wixInstallEmail
 *
 * @property {string} AppConfig.dbUrl - mongodb connection url (#deprecated, use mongodb.url)
 * @property {object} AppConfig.mongodb - mongodb configuration
 * @property {string} AppConfig.mongodb.url - mongodb connection url
 * @property {string} AppConfig.mongodb.readPref - read preference (`none`, `secondary`, `primary`)

 * @property {object} AppConfig.cron
 * @property {boolean} AppConfig.cron.schedule should schedule cron tasks
 * @property {number} AppConfig.cron.port
 * @property {function} AppConfig.cron.baseUrl
 * *
 * @property {object} AppConfig.redis - redis configuration
 * @property {number} AppConfig.redis.port - redis server port
 * @property {string} AppConfig.redis.host - redis server host
 *
 * @property {object} AppConfig.sentry
 * @property {boolean} AppConfig.sentry.active
 * @property {string} AppConfig.sentry.dsn
 *
 * @property {object} AppConfig.admin
 * @property {string} AppConfig.admin.pass
 * @property {string} AppConfig.admin.secret
 *
 * @property {object} AppConfig.facebook
 * @property {string} AppConfig.facebook.clientId
 * @property {string} AppConfig.facebook.clientSecret
 * @property {string} AppConfig.facebook.accessToken
 *
 * @property {object} AppConfig.apm
 * @property {boolean} AppConfig.apm.active
 * @property {string} AppConfig.apm.serviceName
 * @property {string} AppConfig.apm.secretToken
 * @property {string} AppConfig.apm.serverUrl
 *
 * @property {object} AppConfig.spm - sematext montior
 * @property {boolean} AppConfig.spm.active
 *
 * @property {object} AppConfig.stripe
 * @property {string} AppConfig.stripe.publicKey
 * @property {string} AppConfig.stripe.secretKey
 * @property {string} AppConfig.stripe.webhookSecret
 * @property {string} AppConfig.stripe.taxId
 * @property {string} AppConfig.stripe.thankYou
 *
 * @property {object} AppConfig.slack - slack configuration
 * @property {boolean} AppConfig.slack.active - whetherslack alerts should be sent or not
 * @property {string} AppConfig.slack.processWarnings
 * @property {string} AppConfig.slack.chargeFailed
 * @property {string} AppConfig.slack.serverMessages
 * @property {string} AppConfig.slack.uninstall
 * @property {string} AppConfig.slack.cancellations
 * @property {string} AppConfig.slack.shopifySubscriptions
 * @property {string} AppConfig.slack.shopify
 * @property {string} AppConfig.slack.wp
 * @property {string} AppConfig.slack.wix
 * @property {string} AppConfig.slack.bigcommerce
 * @property {string} AppConfig.slack.thinkific
 * @property {string} AppConfig.slack.paypal
 * @property {string} AppConfig.slack.secret
 * @property {string} AppConfig.slack.ipns
 * @property {string} AppConfig.slack.leads
 * @property {string} AppConfig.slack.stripe
 *
 * @property {object} AppConfig.sendgrid - SendGrid configuration
 * @property {boolean} AppConfig.sendgrid.active - should send emails
 * @property {string} AppConfig.sendgrid.apiKey - api token
 *
 * @property {object} AppConfig.twilio
 * @property {string} AppConfig.twilio.sid
 * @property {string} AppConfig.twilio.auth
 * @property {string} AppConfig.twilio.number
 *
 * @property {object} AppConfig.digitalOcean - DigitalOcean configurations
 * @property {object} AppConfig.digitalOcean.spaces - DigitalOcean Spaces Configurations
 * @property {string} AppConfig.digitalOcean.spaces.accessKey - DigitalOcean Spaces API Access Key
 * @property {string} AppConfig.digitalOcean.spaces.secret - DigitalOcean Spaces API Secret Key
 * @property {string} AppConfig.digitalOcean.spaces.bucket - DigitalOcean Spaces main bucket
 * @property {string} AppConfig.digitalOcean.spaces.endpoint - DigitalOcean Spaces endpoint / host
 * @property {string} AppConfig.digitalOcean.spaces.apiKey - DigitalOcean spaces API Key
 * @property {string} AppConfig.digitalOcean.spaces.id - DigitalOcean space id
 *
 * @property {object} AppConfig.jwt
 * @property {string} AppConfig.jwt.api - api jwt signing secret
 *
 * @property {object} AppConfig.zapier
 * @property {string} AppConfig.zapier.secret
 *
 * @property {object} AppConfig.googleAuth
 * @property {string} AppConfig.googleAuth.clientId
 * @property {string} AppConfig.googleAuth.clientSecret
 * @property {string} AppConfig.googleAuth.callback
 * @property {string} AppConfig.googleAuth.gmbCallback
 *
 * @property {object} AppConfig.googlePlaces
 * @property {string} AppConfig.googlePlaces.executable
 * @property {string} AppConfig.googlePlaces.captchaToken
 *
 * @property {object} AppConfig.shopify
 * @property {string} AppConfig.shopify.partnerAccessToken
 * @property {string} AppConfig.shopify.apiKey
 * @property {string} AppConfig.shopify.secret
 * @property {string} AppConfig.shopify.redirect - dashobard url for redirect after install
 * @property {string} AppConfig.shopify.confirm - url that confirms charge
 * @property {string} AppConfig.shopify.scriptUrl
 * @property {object} AppConfig.shopify.webhooks
 * @property {string} AppConfig.shopify.webhooks.uninstall
 * @property {string} AppConfig.shopify.webhooks.update
 * @property {string} AppConfig.shopify.webhooks.subscriptionUpdate
 * @property {string} AppConfig.shopify.webhooks.track
 *
 * @property {object} AppConfig.thinkific
 * @property {string} AppConfig.thinkific.key
 * @property {string} AppConfig.thinkific.secret
 *
 * @property {object} AppConfig.wix
 * @property {string} AppConfig.wix.appId
 * @property {string} AppConfig.wix.secret
 * @property {string} AppConfig.wix.publicKey
 * @property {string} AppConfig.wix.thankYou
 * @property {string} AppConfig.wix.redirectUrl
 *
 * @property {object} AppConfig.profitWell
 * @property {string} AppConfig.profitWell.apiKey
 *
 * @property {object} AppConfig.mailerlite
 * @property {boolean} AppConfig.mailerlite.active
 * @property {string} AppConfig.mailerlite.apiKey
 * @property {object} AppConfig.mailerlite.groups
 * @property {string} AppConfig.mailerlite.groups.all
 *
 * @property {object} AppConfig.mailshake
 * @property {string} AppConfig.mailshake._apiKey_ mailshake subscription is inactive
 *
 * @property {object} AppConfig.paypal
 * @property {string} AppConfig.paypal.clientId
 * @property {string} AppConfig.paypal.secret
 * @property {string} AppConfig.paypal.merchantId
 * @property {string} AppConfig.paypal.webhookId
 * @property {boolean} AppConfig.paypal.sandbox
 *
 * @property {object} AppConfig.greenInvoice
 * @property {boolean} AppConfig.greenInvoice.testMode
 * @property {string} AppConfig.greenInvoice.apiKey
 * @property {string} AppConfig.greenInvoice.secret
 * @property {string} AppConfig.greenInvoice.purpleApikey
 * @property {string} AppConfig.greenInvoice.purpleSecret
 *
 * @property {object} AppConfig.bigcommerce
 * @property {string} AppConfig.bigcommerce.clientId
 * @property {string} AppConfig.bigcommerce.secret
 * @property {string} AppConfig.bigcommerce.authUrl
 * @property {string} AppConfig.bigcommerce.scriptSrc
 * @property {object} AppConfig.bigcommerce.webhooks
 * @property {string} AppConfig.bigcommerce.webhooks.appUninstall
 * @property {string} AppConfig.bigcommerce.webhooks.storeUpdate
 * @property {string} AppConfig.bigcommerce.webhooks.orderCreate
 *
 * @property {string} AppConfig.scrapfly
 * @property {string} AppConfig.scrapfly.apiKey
 * @property {string} AppConfig.scrape_do
 * @property {string} AppConfig.scrape_do.token
 *
 * @property {object} AppConfig.instagram
 * @property {array<{username, password}>} AppConfig.instagram.users
 *
 * @property {array<String>} AppConfig.proxies
 *
 * @property {boolean} AppConfig.getSocialProfiles
 * @property {getCookieConfig} AppConfig.getCookieConfig
 *
 * @property {object} AppConfig.cookies
 * @property {object} AppConfig.cookies.generic
 * @property {object} AppConfig.cookies.ps_signup
 * @property {object} AppConfig.cookies.ps_login
 *
 * @property {object} AppConfig.mixpanel
 * @property {string} AppConfig.mixpanel.token
 * @property {string} AppConfig.mixpanel.secret
 */


/**
 * @type {AppConfig}
 */
module.exports = (() => {
  let config;
  switch(process.env.NODE_ENV) {
    default:
    case 'development':
      config = require('./devConfig');
      break;
    case 'production':
      config = require('./prodConfig');
      break;
    case 'test':
      config = require('./testConfig');
      break;
  }
  if(process.env.TYPE === 'cron') {
    _.merge(config, require('./cronConfig'));
  }
  return _.merge(template, config);
})();
