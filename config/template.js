module.exports = {
  env: '',
  logs: {
    pretty: true,
    enabled: true,
    cron: true,
  },
  apiDomain: '',
  apiUrl: '',
  mongodb: {
    url: '',
    readPref: '',
  },
  dbUrl: '',
  redis: {
    port: 1,
    host: '',
  },
  cron: {
    schedule: false,
    port: 3001,
    baseUrl: () => `http://localhost:${module.exports.cron.port}`,
  },
  admin: {
    pass: 'bassword',
    secret: 'shits',
  },
  consoleUrl: '',
  thankYou: '',
  disabledLoggers: [''],
  getSocialProfiles: true,
  bluesnap: {
    tokenUrl: '',
    apiKey: '',
    password: '',
  },
  profitWell: {
    apiKey: '5E8B6F666494C4D0D1461E4F7C38582D',
  },
  paypal: {
    clientId: 'test',
    secret: 'test',
    merchantId: 'test',
    webhookId: 'test',
    sandbox: true,
  },
  sendgrid: {
    active: false,
    apiKey: '',
  },
  twilio: {
    sid: '',
    auth: '',
    number: '',
  },
  greenInvoice: {
    apiKey: '',
    secret: '',
    purpleApiKey: '',
    purpleSecret: '',
  },
  facebook: {
    clientId: '',
    clientSecret: '',
    accessToken: '',
  },
  digitalOcean: {
    spaces: {
      accessKey: '',
      secret: '',
      bucket: '',
      endpoint: '',
    },
  },
  zapier: {
    secret: '',
  },
  mailerlite: {
    active: true,
    groups: {
      customers: '',
      pre_signup: '',
      plan_limit_reached: '',
      plan_selected: '',
      all: '',
    },
    apiKey: '',
  },
  shopify: {
    apiKey: '',
    secret: '',
    redirect: '',
    scope: '',
    confirm: '',
    scriptUrl: '',
    webhooks: {
      uninstall: '',
      update: '',
      track: '',
    },
  },
  thinkific: {
    key: '',
    secret: '',
  },
  segment: {
    context: {
      integrations: {
        name: '',
        version: '',
      },
    },
  },
  proxies: [],
  wix: {
    appId: '',
    secret: '',
    redirectUrl: '',
  },
  slack: {
    active: false,
    install: '',
    notifications: '',
    leads: '',
    ipns: '',
    charges: '',
    cancellations: '',
    affiliates: '',
    errors: '',
    planLimit: '',
    shopify: '',
    preview: '',
    onboardingComplete: '',
    warnings: '',
    uninstall: '',
    processWarnings: '',
    shopifySubscriptions: '',
    wp: '',
    thinkific: '*******************************************************************************',
  },
  jwt: {
    api: '',
  },
  cryptoKeys: {
    webhook: '',
    mailto: '',
    passReset: '',
    cloudflare: '',
    activate: '',
    changeEmail: '',
  },
  googlePlaces: {
    executable: null,
    captchaToken: '',
  },
  googleAuth: {
    clientId: '',
    clientSecret: '',
    callback: '',
    gmbCallback: '',
  },
  getCookieConfig(domain, expires, maxAge, httpOnly, secure, signed) {
    return Object.assign({
      domain: '',
      maxAge: 86400000,
      httpOnly: false,
      secure: false,
      signed: false,
    }, {
      domain, expires, maxAge, httpOnly, secure, signed,
    });
  },
  cookies: {
    generic: {
      domain: '',
      httpOnly: false,
      signed: false,
    },
    ps_login: {
      domain: '',
      expires: new Date('2030-01-01'),
      httpOnly: false,
      secure: false,
      signed: true,
    },
    ps_signup: {
      domain: '',
      httpOnly: false,
      secure: false,
      signed: false,
    },
  },
  cookie: {
    name: '',
    secret: '',
    domain: '',
    expires: new Date('2030-01-01'),
    httpOnly: false,
    secure: false,
  },
  psuidCookie: {
    domain: '',
    expires: new Date('2030-01-01'),
    httpOnly: true,
    secure: false,
    signed: true,
  },
  psacctsCookie: {
    domain: '',
    httpOnly: true,
    secure: false,
    signed: true,
  },
};
