{"name": "provesrc-server", "version": "6.69.11", "description": "The backend for provesrc.com", "main": "server.js", "scripts": {"test": "NODE_ENV=test nyc mocha test/*/*.test.js", "lint": "eslint . --fix"}, "repository": {"type": "git", "url": "git+ssh://*****************/configo/server.git"}, "author": "", "license": "ISC", "homepage": "https://bitbucket.org/configo/server#readme", "dependencies": {"@sendgrid/mail": "^6.3.1", "@slack/web-api": "^7.7.0", "airtable": "0.7.1", "async": "2.6.0", "aws-sdk": "^2.493.0", "axios": "^0.18.0", "bcryptjs": "2.4.3", "bluebird": "3.5.1", "body-parser": "1.18.2", "cachegoose": "^8.0.0", "chance": "^1.0.16", "cheerio": "1.0.0-rc.3", "chrono-node": "^1.4.6", "compression": "^1.7.3", "consolidate": "^0.15.1", "cookie": "^0.4.1", "cookie-parser": "^1.4.3", "cookie-session": "^1.4.0", "credit-card-type": "^8.1.0", "cron": "^1.7.1", "crypto": "1.0.1", "csv-parse": "^4.12.0", "edit-json-file": "^1.4.1", "evp_bytestokey": "^1.0.3", "express": "^4.16.2", "fast-xml-parser": "^3.12.12", "fs-extra": "^9.0.1", "geolite2": "^3.6.2", "geolite2-redist": "^3.1.0", "google-auth-library": "1.2.1", "googleapis": "131.0.0", "handlebars": "^4.7.8", "html-entities": "^2.3.2", "html-validator": "^6.0.1", "i18n-iso-countries": "^7.6.0", "instagram-web-api": "^2.2.0", "ip-matching": "^2.1.2", "is-url": "^1.2.4", "isbot": "^5.1.13", "json2csv": "^4.5.4", "jsonschema": "1.2.2", "jsonwebtoken": "8.1.1", "lodash": "4.17.5", "lru-cache": "^7.0.0", "mailchecker": "3.0.36", "mailchimp-api-v3": "1.8.0", "maxmind": "^4.3.24", "mixpanel": "^0.18.0", "moment": "^2.22.1", "mongoose": "5.0.16", "node-bigcommerce": "^4.1.0", "nodemailer": "^4.6.4", "normalize-url": "^5.3.0", "numeral": "^2.0.6", "parse-domain": "^2.1.6", "pino": "^5.12.6", "pino-http": "^4.2.0", "puppeteer": "^23.11.1", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-recaptcha": "^3.6.8", "puppeteer-extra-plugin-stealth": "^2.4.14", "puppeteer-real-browser": "1.4.0", "qs": "^6.13.1", "rate-limiter-flexible": "^2.3.6", "redis": "^2.8.0", "request-promise": "^4.2.6", "sanitize-html": "^2.11.0", "scrapfly-sdk": "^0.6.8", "serialize-error": "^7.0.1", "spm-agent-nodejs": "^3.0.1", "stripe": "^17.5.0", "superagent": "^3.8.3", "superagent-proxy": "^2.1.0", "tough-cookie-filestore2": "^1.0.0", "twilio": "^3.49.4", "uuid": "3.2.1"}, "devDependencies": {"chai": "4.1.2", "chai-as-promised": "^7.1.1", "chai-datetime": "^1.5.0", "chai-http": "^4.0.0", "cookie-signature": "^1.1.0", "debug": "^4.1.1", "eslint": "^5.16.0", "eslint-config-airbnb-base": "^13.1.0", "eslint-plugin-chai-friendly": "^0.4.1", "eslint-plugin-import": "^2.17.3", "mocha": "5.0.0", "mock-session": "0.0.5", "mongodb-memory-server-global": "^6.2.3", "nock": "^10.0.6", "nyc": "^14.0.0", "pino-pretty": "^3.1.0", "sinon": "^7.3.2", "sinon-chai": "^3.3.0", "sinon-mongoose": "^2.3.0", "superagent-mock": "^3.7.0"}, "bit": {"env": {}, "componentsDefaultDirectory": "components/{name}", "packageManager": "npm"}, "geolite2": {"account-id": "192791", "license-key": "****************************************", "selected-dbs": ["City"]}}