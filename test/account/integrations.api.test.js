const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const httpUtils = require('../httpUtils');
const testUtils = require('../testUtils');
const constants = require('../constants');

const integrations = require('../../app/account/integrations');

describe('/accounts/integrations API', () => {
  let server;
  before(async () => {
    server = await require('../../server');
    if(!server) throw new Error('server not loaded');
  });

  afterEach(async () => {
    sandbox.restore();
  });

  describe('PUT', () => {
    it('should call updateIntegrations', async () => {
      const data = {
        segment: { writeKey: '12345' },
        stripe: { apiKey: '***********' },
      };
      const stub = sandbox.stub(integrations, 'updateIntegrations').resolves(data);
      const res = await httpUtils.consoleRequest(server, '/account/integrations', httpUtils.PUT).send(data);
      expect(res).to.have.status(200);
      expect(stub).to.have.been.calledWith(constants.accountId, data);
    });

    it('should return error 500 if update failed unexpectedly', async () => {
      sandbox.stub(integrations, 'updateIntegrations').throws(new Error('failed failed'));
      const res = await httpUtils.consoleRequest(server, '/account/integrations', httpUtils.PUT).send({});
      expect(res).to.have.status(500);
    });
  });

  describe('GET', () => {
    it('should return account.integrations', async () => {
      const data = { jon: 'wick' };
      sandbox.stub(integrations, 'getIntegrations').resolves(data);

      const res = await httpUtils.consoleRequest(server, '/account/integrations', httpUtils.GET);

      expect(res).to.have.status(200);
      expect(res.body).to.be.eql(data);
    });
  });
});
