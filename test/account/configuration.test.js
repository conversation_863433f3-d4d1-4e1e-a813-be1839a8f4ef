const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const chaiHttp = require('chai-http');

chai.use(chaiHttp);
const sinon = require('sinon');
const testUtils = require('../testUtils');
const redis = require('../../lib/redisClient').getClient();
const cookie = require('cookie');
const cookieSignature = require('cookie-signature');

const serverPromise = require('../../server');
const constants = require('../constants');
const config = require('../../config');

const ENDPOINT = '/account/configuration';
const botList = require('../../lib/bots/botList');

const Account = require('../../app/account/models/Account');
const AccountFactory = require('../factories/AccountFactory');
const NotificationFactory = require('../factories/NotificationFactory');

const { Notification } = NotificationFactory;
const Bill = require('../../app/billing/Bill');
const BillF = require('../factories/BillFactory');
const signature = require('cookie-signature');

describe('/account/configuration', () => {
  let server;
  const sandbox = sinon.createSandbox();
  before(async () => {
    server = await serverPromise;
  });

  let request;
  beforeEach(async () => {
    request = chai.request(server);
    sandbox.stub(Account.prototype, 'save').resolves();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('GET', () => {
    const email = '<EMAIL>';

    beforeEach(async () => {
      await Promise.all([
        Account.remove(),
        Bill.remove(),
        redis.flushallAsync(),
      ]);
    });

    it('should return empty remarketing when no cookie', async () => {
      sandbox.stub(Account, 'findOne').resolves(AccountFactory.default());
      const res = await httpUtils.configConsoleGET(server);
      expect(res).to.have.status(200);
      expect(res.body.remarketing).to.be.undefined;
    });

    it('should not return whitelabel', async () => {
      const account = AccountFactory.inactiveSubscription('<EMAIL>').model;
      account.configuration = { whitelabel: true };
      sandbox.stub(Account, 'findOne').resolves(account);

      let res = await httpUtils.configConsoleGET(server);
      expect(res.body.whitelabel).to.not.exist;

      res = await httpUtils.configApiGET(server, 'provesrc.com');
      expect(res.body.whitelabel).to.not.exist;
    });

    it('should return empty remarketing when cookie not same accountId', async () => {
      const account = AccountFactory.inactiveSubscription(email).model;
      const agent = httpUtils.Agent(server, account.apiKey);

      sandbox.stub(Account, 'findOne').resolves(account);

      const res = await agent.accountConfiguration(null, { apiKey: constants.apiKey });
      expect(res).to.have.status(200);
      expect(res.body.remarketing).be.null;
    });

    it('should return remarketing=null when subscription=true and notifications > 0', async () => {
      const account = AccountFactory.monthlySubscription(email).model;
      const url = 'http://provesrc.com';
      const notification = NotificationFactory.PageVisits(account._id, url, url);
      sandbox.stub(Account, 'findOne').resolves(account);
      sandbox.stub(Notification, 'findOne').resolves(notification);

      const agent = httpUtils.Agent(server, account.apiKey);

      const res = await agent.accountConfiguration(null, { apiKey: account.apiKey });
      expect(res).to.have.status(200);
      expect(res.body.remarketing).to.be.null;
    });

    it('should return remarketing {preview=true, wizard=true} when {subscription=true, cookie=accountId}', async () => {
      const account = AccountFactory.monthlySubscription(email).model;
      sandbox.stub(Account, 'findOne').resolves(account);

      const agent = httpUtils.Agent(server, account.apiKey);
      const headers = { cookie: httpUtils.makeCookie('ps_login', account.id, config.cookie.secret) };
      const res = await agent.accountConfiguration(null, { apiKey: account.apiKey, host: 'test.com', headers });
      expect(res.body.remarketing.wizard).to.be.true;

      const dbAccount = await Account.findOne({ _id: account._id });
      const diff = Date.now() - dbAccount.remarketing.wizard;
      expect(diff).to.be.below(1000).above(0);
    });

    it('should return an error if no such account', (done) => {
      const account = new Account({ email: '<EMAIL>', password: 'fakePass' });
      const apiKey = account.addApiKey();
      request.get(ENDPOINT).set('authorization', `bearer ${apiKey}`).end((err, res) => {
        expect(res).to.have.status(400);
        expect(res.body).to.have.property('error');
        done();
      });
    });

    it('should contain properties', async () => {
      const account = AccountFactory.default();
      account.configuration = {
        branding: {
          active: true, text: '123', link: 'https://natanavra.ninja', color: '#AABBCC',
        },
        trackers: { googleAnalytics: true, mixpanel: true, amplitude: true },
      };
      sandbox.stub(Account, 'findOne').resolves(account);

      const res = await httpUtils.configApiGET(server, 'provesrc.com', { apiKey: account.apiKey, url: 'https://provesrc.com' });
      expect(res.body).to.contain.keys(['affiliateLink', 'affiliateId']);
      expect(res.body.affiliateId).to.have.lengthOf(8);
      expect(res.body.branding).to.be.deep.equal(account.configuration.branding.toObject());
      expect(res.body.trackers.googleAnalytics).to.be.true;
      expect(res.body.trackers.mixpanel).to.be.true;
      expect(res.body.trackers.amplitude).to.be.true;
      expect(res.body.whitelabel).to.not.exist;
      expect(res.body.shouldSendGoals).to.be.false;
    });

    describe('bill related', () => {
      it('should increment user count', async () => {
        const account = AccountFactory.default();
        const bill = BillF.makeBill(account.id, account.getBillingCycleDate());
        const { total } = bill;

        sandbox.stub(Account, 'findOne').resolves(account);
        sandbox.stub(Bill, 'findOne').returns({ sort: () => bill });
        sandbox.stub(Bill, 'update').returns({ exec: () => bill.total++ });
        const incSpy = sandbox.spy(Bill, 'incrementTotal');

        const res = await httpUtils.configApiGET(server, 'test.com');
        expect(res).to.have.status(200);
        expect(res).to.have.cookie(`ps${constants.accountId}`);

        expect(incSpy).to.have.been.called;
        expect(bill.total).to.be.equal(total + 1);
      });

      it('should not increment user count (psAccount cookie)', async () => {
        const account = AccountFactory.default();
        const bill = BillF.makeBill(account.id, account.getBillingCycleDate());

        sandbox.stub(Account, 'findOne').resolves(account);
        sandbox.stub(Bill, 'findOne').returns({ sort: () => bill });
        const incSpy = sandbox.spy(Bill, 'incrementTotal');

        const value = `false|${bill.date.getTime()}`;
        const signed = signature.sign(value, config.cookie.secret);
        const cookie = `ps${constants.accountId}=s:${signed}`;

        const res = await httpUtils.configApiGET(server, 'provesrc.com', { headers: { cookie } });
        expect(res).to.have.status(200);
        expect(res).to.not.have.cookie(`ps${constants.accountId}`);

        expect(incSpy).to.not.be.called;
      });

      it('should not increment user count (bot)', async () => {
        const account = AccountFactory.default();
        const bill = BillF.makeBill(account.id, account.getBillingCycleDate());

        sandbox.stub(Account, 'findOne').resolves(account);
        sandbox.stub(Bill, 'findOne').returns({ sort: () => bill });
        const spy = sandbox.stub(Bill, 'incrementTotal');

        const res = await httpUtils.accountConfiguration(server, { headers: { 'user-agent': botList[5] } });
        expect(res).to.have.status(200);

        expect(spy).to.have.not.been.called;
      });

      it('should update cookie', async () => {
        const account = AccountFactory.default();
        const bill = BillF.makeBill(account.id, account.getBillingCycleDate());

        sandbox.stub(Account, 'findOne').resolves(account);
        sandbox.stub(Bill, 'findOne').returns({ sort: () => bill });
        const spy = sandbox.stub(Bill, 'incrementTotal');

        const value = `false|${Date.now()}`;
        const signed = signature.sign(value, config.cookie.secret);
        const cookie = `ps${constants.accountId}=s:${signed}`;

        const res = await httpUtils.configApiGET(server, 'test.com', { headers: { cookie } });
        expect(res).to.have.status(200);
        expect(res).to.have.cookie(`ps${constants.accountId}`);

        expect(spy).to.have.been.called;
      });
    });

    it('should save the domain on the account document', async () => {
      const account = AccountFactory.monthlySubscription(email).model;
      sandbox.stub(Account, 'findOne').resolves(account);

      const domain = 'coolwebsite.com';
      await httpUtils.accountConfiguration(server, { apiKey: account.apiKey, host: domain });

      expect(account.hosts).to.include(domain);
      const diff = Date.now() - account.installed;
      expect(diff).to.be.below(1000);
    });

    it('should not save cloudflare domain if not cloudflare_preview', async () => {
      const account = AccountFactory.monthlySubscription(email).model;
      sandbox.stub(Account, 'findOne').resolves(account);

      const domain = '1jn0hqk2p2_s_mssg_me.p.cloudflare.works';
      await httpUtils.accountConfiguration(server, { apiKey: account.apiKey, host: domain });

      expect(account.hosts).to.not.include(domain);
    });

    it('should set the psuid from redis if exists (no cookie)', async () => {
      sandbox.stub(Account, 'findOne').resolves(AccountFactory.default());
      const psuid = '12345';
      const headers = { 'x-ps-uid': psuid };
      const res1 = await httpUtils.accountConfiguration(server, { host: 'provesrc.com', headers });
      const res2 = await httpUtils.accountConfiguration(server, { host: 'provesrc.com', headers });

      const c1 = cookie.parse(res1.headers['set-cookie'][0]);
      const uid1 = c1.psuid.substring(2, c1.psuid.lastIndexOf('.'));
      const c2 = cookie.parse(res2.headers['set-cookie'][0]);
      const uid2 = c2.psuid.substring(2, c2.psuid.lastIndexOf('.'));

      expect(uid1).to.be.equal(uid2);
    });

    it('should say shouldSendGoals=true', async () => {
      const acc = AccountFactory.default();
      acc.goals = [{ name: 'Goal 1', url: '/#/signup' }];
      sandbox.stub(Account, 'findOne').resolves(acc);
      const res = await httpUtils.configApiGET(server, null, {
			  url: 'aHR0cHM6Ly9jb25zb2xlLnByb3Zlc3JjLmNvbS8jL3NpZ251cA==',
      });
      expect(res.body.shouldSendGoals).to.be.true;
    });

    it('should have GANonInteraction=true', async () => {
      const acc = AccountFactory.default();
      acc.configuration.GANonInteraction = true;
      sandbox.stub(Account, 'findOne').resolves(acc);
      const res = await httpUtils.configApiGET(server, null, {
        url: 'aHR0cHM6Ly9jb25zb2xlLnByb3Zlc3JjLmNvbS8jL3NpZ251cA==',
      });
      expect(res.body.GANonInteraction).to.be.true;
    });
  });

  describe('POST', () => {
    const email = '<EMAIL>';
    beforeEach(async () => {
      await Account.remove();
    });

    it('should accept properties', async () => {
      const account = AccountFactory.default();
      sandbox.stub(Account, 'findOne').resolves(account);

      const params = {
        loop: true,
        randomize: true,
        affiliateLink: true,
        branding: {
          active: true,
          text: 'natanavra',
          color: '#AABBCC',
          link: 'https://natanavra.ninja',
        },
        trackers: {
          googleAnalytics: true,
          mixpanel: false,
        },
      };
      const endpoint = constants.ACCOUNT.CONFIGURATION;
      const agent = httpUtils.Agent(server, account.apiKey);
      const res = await agent.consoleRequest(server, endpoint, httpUtils.POST).send(params);
      expect(res).to.have.status(200);

      expect(account.configuration.loop).to.be.true;
      expect(account.configuration.randomize).to.be.true;
      expect(account.configuration.affiliateLink).to.be.true;
      expect(account.configuration.branding.toObject()).to.be.deep.equal(params.branding);
      expect(account.configuration.trackers.googleAnalytics).to.be.true;
      expect(account.configuration.trackers.mixpanel).to.be.false;
    });

    it('should not allow to POST configuration with api key', async () => {
      const res = await request.post(ENDPOINT).set('authorization', constants.authorizationHeader).send({ firstShowDelay: 1 });
      expect(res).to.have.status(403);
    });

    it('should set configuration on POST', async () => {
      const account = AccountFactory.inactiveSubscription(email).model;
      sandbox.stub(Account, 'findOne').resolves(account);
      const agent = httpUtils.Agent(server, account.apiKey);

      const firstShowDelay = 2;
      const res = await agent.configPOST(server, { firstShowDelay });
      expect(res).to.have.status(200);
      expect(res.body.firstShowDelay).to.be.equal(firstShowDelay);
      expect(account.configuration.firstShowDelay).to.be.equal(firstShowDelay);
    });

    describe('whitelabel', () => {
      it('should not let user change whitelabel if not eligible', async () => {
        const email = '<EMAIL>';
        const account = AccountFactory.inactiveSubscription(email).model;
        sandbox.stub(Account, 'findOne').resolves(account);

        const agent = httpUtils.Agent(server, account.apiKey);
        const res = await agent.configPOST(server, { whitelabel: true });
        expect(res).to.have.status(200);
        expect(res.body.whitelabel).to.not.exist;

        expect(account.configuration.whitelabel).to.be.undefined;
      });

      it('should let user change whitelabel if >= growth', async () => {
        const email = '<EMAIL>';
        const account = AccountFactory.monthlySubscription(email).model;
        account.subscription.plan = 'growth';
        sandbox.stub(Account, 'findOne').resolves(account);

        const agent = httpUtils.Agent(server, account.apiKey);
        const res = await agent.configPOST(server, { whitelabel: true });
        expect(res).to.have.status(200);
        expect(res.body.whitelabel).to.be.true;

        expect(account.configuration.whitelabel).to.be.true;
      });

      it('should not user change whitelabel if == starter', async () => {
        const email = '<EMAIL>';
        const account = AccountFactory.monthlySubscription(email).model;
        account.subscription.plan = 'starter';
        sandbox.stub(Account, 'findOne').resolves(account);

        const agent = httpUtils.Agent(server, account.apiKey);
        const res = await agent.configPOST(server, { whitelabel: true });
        expect(res).to.have.status(200);
        expect(res.body.whitelabel).to.not.exist;
        expect(account.configuration.whitelabel).to.be.undefined;
      });

      it('should let user change whitelabel if custom branding', async () => {
        const email = '<EMAIL>';
        const account = AccountFactory.monthlySubscription(email).model;
        account.subscription.contractId = '3246771';
        sandbox.stub(Account, 'findOne').resolves(account);

        const agent = httpUtils.Agent(server, account.apiKey);
        const res = await agent.configPOST(server, { whitelabel: true });
        expect(res).to.have.status(200);
        expect(res.body.whitelabel).to.be.true;

        expect(account.configuration.whitelabel).to.be.true;
      });
    });
  });
});
