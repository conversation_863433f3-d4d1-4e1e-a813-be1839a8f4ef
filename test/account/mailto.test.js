const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');
const urlModule = require('url');

const { URLSearchParams } = urlModule;
const config = require('../../config');
const cryptoUtils = require('../../lib/utils/cryptoUtils');

describe('/account/mailto', () => {
  let server;
  before(() => serverP.then((app) => {
    if(!app) throw new Error('server not loaded');
    server = app;
  }));

  it('should return a link with accountId encrypted in token', () => httpUtils.consoleRequest(server, constants.ACCOUNT.MAIL_TO, httpUtils.GET).then((res) => {
    expect(res).to.have.status(200);
    expect(res.body).to.have.property('link');

    const url = urlModule.parse(res.body.link);
    const params = new URLSearchParams(url.query);
    expect(params.get('apiKey')).to.equal(constants.apiKey);

    const encToken = params.get('token');
    const token = cryptoUtils.decrypt(encToken, config.cryptoKeys.mailto);
    expect(token).to.exist;
    expect(token).to.equal(constants.accountId);
  }));
});
