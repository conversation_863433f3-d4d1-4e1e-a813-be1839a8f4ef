const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const httpUtils = require('../httpUtils');

require('sinon-mongoose');

const constants = require('../constants');
const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;
const NotificationFactory = require('../factories/NotificationFactory');

const { Notification } = NotificationFactory;
const notifConsts = NotificationFactory.notificationConstants;
const triggers = require('../../lib/triggers');

describe('/account/onboarding', () => {
  let server;
  before(async () => {
    server = await require('../../server');
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    sandbox.stub(Account.prototype, 'save').resolves();
    await Notification.remove();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('GET /account/onboarding', () => {
    it('should return step=1 for just signed up users', async () => {
      const account = AccountFactory.free('<EMAIL>');
      sandbox.mock(Account).expects('findOne').chain('cache').chain('exec')
        .resolves(account);

      const res = await httpUtils.getOnboarding(server);
      expect(res.body.step).to.be.equal(1);
    });

    it('should return step=1 if user never installed but has notification', async () => {
      const account = AccountFactory.free('<EMAIL>');
      account.onboarding.created = Date.now();
      sandbox.mock(Account).expects('findOne').chain('cache').chain('exec')
        .resolves(account);
      sandbox.stub(Notification, 'findOne').resolves({ name: 'notification' });

      const res = await httpUtils.getOnboarding(server);
      expect(res.body.step).to.be.equal(1);
    });

    it('should return step=2 when user passed onboarding.installed', async () => {
      const account = AccountFactory.free('<EMAIL>');
      account.onboarding = { installed: new Date('2018-12-01') };
      account.installed = new Date('2018-12-01');
      sandbox.mock(Account).expects('findOne').chain('cache').chain('exec')
        .resolves(account);

      const res = await httpUtils.getOnboarding(server);
      expect(res.body.step).to.be.equal(2);
    });

    it('should return step=3 when user passed onboarding.created', async () => {
      const account = AccountFactory.free('<EMAIL>');
      account.onboarding = { installed: new Date('2018-12-01'), created: new Date('2018-12-02') };
      sandbox.mock(Account).expects('findOne').chain('cache').chain('exec')
        .resolves(account);

      const res = await httpUtils.getOnboarding(server);
      expect(res.body.step).to.be.equal(3);
    });

    it('should return step=2 when user `installed` exists', async () => {
      const account = AccountFactory.free('<EMAIL>');
      account.installed = new Date('2018-12-01');
      sandbox.mock(Account).expects('findOne').chain('cache').chain('exec')
        .resolves(account);

      const res = await httpUtils.getOnboarding(server);
      expect(res.body.step).to.be.equal(2);
    });
  });

  describe('POST /account/onboarding', () => {
    afterEach(async () => {
      await Notification.remove();
    });

    it('should fail if website is invalid', async () => {
      const website = 'https://natanavra';
      const refer = 'natans';
      const message = 'booked a natan';

      const account = AccountFactory.free('<EMAIL>');
      const agent = httpUtils.Agent(server, account.apiKey);
      const res = await agent.postOnboarding(server, { website, refer, message });
      expect(res).to.have.status(400);
      expect(res.body.error).to.be.equal('website address is invalid');
    });

    it('should create stream and page visits', async () => {
      const website = 'natanavra.com';
      const refer = 'natans';
      const message = 'booked a natan';
      const localization = 'de';
      const image = 'https://provesrc.com/some-image.png';

      const account = AccountFactory.free('<EMAIL>');
      sandbox.stub(Account, 'findOne').resolves(account);
      const updateMock = sandbox.mock(Account).expects('updateOne').chain('exec');
      const onboardingTrigger = sandbox.stub(triggers, 'onboardingComplete');

      const agent = httpUtils.Agent(server, account.apiKey);
      await agent.postOnboarding(server, {
        website, refer, message, localization, image,
      });

      expect(onboardingTrigger).to.have.been.calledWith(account, website);
      expect(updateMock).to.have.been.called;
      const accountId = account._id;
      const [stream, visits] = await Promise.all([
        Notification.findOne({ accountId, type: notifConsts.notificationTypes.stream }),
        Notification.findOne({ accountId, type: notifConsts.notificationTypes.pageVisits }),
      ]);
      expect(stream.message).to.be.equal(message);
      expect(stream.refer).to.be.equal(refer);
      expect(stream.urlTypes.track).to.be.equal(notifConsts.URL_TYPES.contains);
      expect(stream.trackURL[0]).to.be.equal('natanavra.com');
      expect(stream.urlTypes.display).to.be.equal(notifConsts.URL_TYPES.all);
      expect(stream.webhookId).to.not.be.empty;
      expect(stream.localization).to.be.equal(localization);
      expect(stream.image).to.not.be.equal(image);

      expect(visits.refer).to.be.equal(refer);
      expect(visits.urlTypes.track).to.be.equal(notifConsts.URL_TYPES.contains);
      expect(visits.trackURL[0]).to.be.equal('natanavra.com');
      expect(visits.urlTypes.display).to.be.equal(notifConsts.URL_TYPES.all);
      expect(visits.localization).to.be.equal(localization);
      expect(visits.image).to.be.equal(image);
    });

    it('should not create notifications if user already passed', async () => {
      const website = 'https://natanavra.com';
      const refer = 'natans';
      const message = 'booked a natan';

      const account = AccountFactory.free('<EMAIL>');
      account.onboarding.created = new Date('2018-01-01');
      sandbox.stub(Account, 'findOne').resolves(account);
      const updateMock = sandbox.mock(Account).expects('updateOne').chain('exec');

      const agent = httpUtils.Agent(server, account.apiKey);
      await agent.postOnboarding(server, { website, refer, message });

      expect(updateMock).to.have.not.been.called;
      const count = await Notification.count({ accountId: account._id });
      expect(count).to.be.equal(0);
    });
  });

  describe('identify install', () => {
    it('should set onboarding.installed on configuration call', async () => {
      const account = AccountFactory.free('<EMAIL>');
      sandbox.stub(Account, 'findOne').resolves(account);
      const agent = httpUtils.Agent(server, account.apiKey);
      await agent.configApiGET(null, 'http://natan.ninja');

      expect(account.onboarding.installed).to.exist;
    });
  });
});
