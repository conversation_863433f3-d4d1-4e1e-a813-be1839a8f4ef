const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');
const urlModule = require('url');

describe('/account/install', () => {
  let server;
  before(() => serverP.then((app) => {
    if(!app) throw new Error('server not loaded');
    server = app;
  }));

  it('should return an error when token is bad', () => {
    const url = `${constants.ACCOUNT.INSTALL}?apiKey=${constants.apiKey}&token=shit`;
    return httpUtils.noAuthRequest(server, url).catch(() => {});
  });

  it('should return an html page', () => httpUtils.consoleRequest(server, constants.ACCOUNT.MAIL_TO)
    .then(res => res.body.link)
    .then((link) => {
      const url = urlModule.parse(link);
      return httpUtils.noAuthRequest(server, url.path);
    })
    .then((res) => {
      expect(res).to.be.html;
    }));
});
