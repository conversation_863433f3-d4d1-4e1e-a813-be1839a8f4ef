require('../../lib/mongooseLoader');

const chai = require('chai');

const { expect } = chai;
const constants = require('../constants');

const Account = require('../../app/account/models/Account');
const AccountFactory = require('../factories/AccountFactory');
const {
  PLANS, PERIODS, FREE_CONTRACT, LIMITS, WHITELABEL_CONTRACTS,
} = require('../../app/account/plansEnum');

describe('Model - Account', () => {
  const EMAIL = '<EMAIL>';

  beforeEach(async () => {
    await Account.remove({});
  });

  describe('getters', () => {
    it('should get google name', async () => {
      const firstName = 'Natan';
      const lastName = 'Abramov';
      const account = new Account({ email: EMAIL, password: '1234', google: { name: `${firstName} ${lastName}` } });
      const name = account.getName();
      expect(name).to.be.equal(firstName);
    });

    it('should get shopify name', async () => {
      const firstName = 'Natan';
      const lastName = 'Abramov';
      const account = new Account({
        email: EMAIL,
        password: '1234',
        shopify: [{ id: 1, shop_owner: `${firstName} ${lastName}` }],
      });
      const name = account.getName();
      expect(name).to.be.equal(firstName);
    });

    it('should return all invoice names', async () => {
      const name = 'heloist laight';
      const companyName = 'ZippGo';
      const acc = new Account({
        subscription: {
          invoiceNames: [name],
          invoiceCompanies: [companyName],
        },
      });
      expect(acc.getInvoiceNames()).to.include(name);
      expect(acc.getInvoiceNames()).to.include(companyName);
    });

    it('should get all emails for account (unique)', async () => {
      const [email1, email2, email3] = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
      const acc = new Account({
        email: email1,
        emails: [email1, email2, email3, email1],
      });
      const emails = acc.getEmails();
      expect(emails.length).to.be.equal(3);
      expect(emails).to.include.members([email1, email2, email3]);
    });

    it('should get unique emails', async () => {
      const [email1, email2] = ['<EMAIL>', '<EMAIL>'];
      const acc = new Account({
        email: email1,
      });
      acc.emails = [email2];
      const emails = acc.getEmails();
      expect(emails.length).to.be.equal(1);
      expect(emails).to.be.eql([email1]);
    });
  });

  describe('whitelabel', () => {
    it('should say whitelabel eligible for non-starter plans', async () => {
      const account = AccountFactory.monthlySubscription('<EMAIL>', '12345').model;
      account.subscription.plan = 'monster';
      expect(account.canUseWhitelabel()).to.be.true;
    });

    it('should say whitelabel eligible for custom branding starter plans', async () => {
      const account = AccountFactory.monthlySubscription('<EMAIL>', '12345').model;
      account.subscription.contractId = WHITELABEL_CONTRACTS[2];
      expect(account.canUseWhitelabel()).to.be.true;
    });

    it('should say whitelabel NOT eligible for starter plan', async () => {
      const account = AccountFactory.monthlySubscription('<EMAIL>', '12345').model;
      account.subscription.contractName = 'Starter Yearly/Monthly Plan';
      expect(account.canUseWhitelabel()).to.be.false;
    });

    it('should say whitelabel NOT eligible for free plan', async () => {
      const account = AccountFactory.free('<EMAIL>');
      expect(account.canUseWhitelabel()).to.be.false;
    });

    it('should say whitelabel eligible for lifetime', async () => {
      const account = AccountFactory.monthlySubscription('<EMAIL>').model;
      account.subscription.plan = PLANS.LIFETIME_39_ADDON;
      expect(account.canUseWhitelabel()).to.be.true;
    });

    it('should say whitelabel eligible if cancelled ', async () => {
      const account = new Account({
        email: '<EMAIL>',
        password: '1234',
        subscription: {
          recentIPN: 'CANCELLATION',
          untilDate: new Date(),
          plan: 'growth',
        },
      });
      expect(account.canUseWhitelabel()).to.be.true;
    });
  });

  it('should not set the password on save', async () => {
    const password = 'zaq12345';
    const exAccount = await new Account({
      email: EMAIL,
      hashedPassword: false,
      password,
      configuration: { whitelabel: true },
    }).save();

    const account = await Account.findOne({ _id: exAccount._id });
    const result = await new Promise((resolve, reject) => {
      account.comparePassword(password, (err, result) => {
			  if(err) reject(err);
			  else resolve(result);
      });
    });

    expect(result).to.be.true;
  });

  describe('current bill date', () => {
    it('should return the bill date based on createdAt', async () => {
      const account = AccountFactory.free('<EMAIL>');
      account.createdAt = new Date('2010-01-01');
      const result = account.getBillingCycleDate();

      const expected = new Date();
      expected.setUTCDate(1);
      expected.normalizeTo12Am();
      expect(result.getTime()).to.be.equal(expected.getTime());
    });

    it('should return the bill date based on transactionDate', async () => {
      const account = AccountFactory.monthlySubscription('<EMAIL>').model;
      account.subscription.transactionDate = new Date('2010-01-05');
      const result = account.getBillingCycleDate();

      const expected = new Date();
      if(expected.getUTCDate() < 5) expected.setUTCMonth(expected.getUTCMonth() - 1);
      expected.setUTCDate(5);
      expected.normalizeTo12Am();
      expect(result.getTime()).to.be.equal(expected.getTime());
    });
  });

  describe('free subscription', () => {
    it('should return a free subscription when no subscription', async () => {
      const email = '<EMAIL>';
      const account = AccountFactory.free(email);
      const subscription = account.subscriptionInfo();
      expect(subscription.plan).to.be.equal(PLANS.FREE);
      expect(subscription.period).to.be.equal(PERIODS.MONTHLY);
      expect(subscription.contractName).to.be.equal(FREE_CONTRACT.name);
      expect(subscription.contractId).to.be.equal(FREE_CONTRACT.id);
      expect(subscription.created).to.be.equal(account.createdAt);
    });

    it('should return a free subscription when subscription is cancelled / bad', async () => {
      const email = '<EMAIL>';
      const account = AccountFactory.inactiveSubscription(email).model;
      const subscription = account.subscriptionInfo();
      expect(subscription.plan).to.be.equal(PLANS.FREE);
      expect(subscription.period).to.be.equal(PERIODS.MONTHLY);
      expect(subscription.contractName).to.be.equal(FREE_CONTRACT.name);
      expect(subscription.contractId).to.be.equal(FREE_CONTRACT.id);
      expect(subscription.created).to.be.equal(account.createdAt);
    });

    it('should return the correct limit', async () => {
      const account = AccountFactory.free(EMAIL);
      const limit = account.getPlanLimit();
      expect(limit).to.be.equal(LIMITS[FREE_CONTRACT.plan]);
    });
  });

  describe('getPlanLimit', () => {
    it('should return custom limit if exists', async () => {
      const customLimit = 5531;
      const account = AccountFactory.free(EMAIL);
      account.customLimit = customLimit;
      expect(account.getPlanLimit()).to.be.equal(customLimit);
    });

    it('should return plan limit if > custom limit', async () => {
      const customLimit = 5531;
      const account = AccountFactory.monthlySubscription('<EMAIL>', '123456', { model: true });
      account.customLimit = customLimit;

      const limit = account.getPlanLimit();
      expect(limit).to.be.equal(LIMITS[account.subscription.plan]);
    });

    it('should not add additionalLimit if free', async () => {
      const additional = 71237;
      const account = AccountFactory.free('<EMAIL>');
      account.additionalLimit = additional;

      const limit = account.getPlanLimit();
      expect(limit).to.be.equal(LIMITS[PLANS.FREE]);
    });

    it('should add additionalLimit if paying account', async () => {
      const additional = 71237;
      const account = AccountFactory.monthlySubscription('<EMAIL>', '123456', { model: true });
      account.additionalLimit = additional;

      const limit = account.getPlanLimit();
      const expectedLimit = additional + LIMITS[account.subscription.plan];
      expect(limit).to.be.equal(expectedLimit);
    });
  });

  describe('goals', () => {
    it('should check goal url matches', async () => {
      const acc = new Account({ email: '<EMAIL>', password: '12345' });
      acc.goals.push({ name: 'goal 1', url: 'thanks' });
      acc.goals.push({ name: 'goal 2', url: 'complete' });
      acc.goals.push({ name: 'goal 3', url: 'purchase' });
      acc.goals.push({ name: 'goal 4', url: 'thank-you.ht' });

      expect(acc.isGoalUrl('https://provesrc.com/thank-you.html')).to.be.true;
      expect(acc.isGoalUrl('https://provesrc.com/purchased')).to.be.true;
      expect(acc.isGoalUrl('https://provesrc.com/thanks/now')).to.be.true;
      expect(acc.isGoalUrl('https://provesrc.com/register-complete-yesterday')).to.be.true;
      expect(acc.isGoalUrl('https://provesrc.com/#onboarding-complete')).to.be.true;

      expect(acc.isGoalUrl('https://provesrc.com/about')).to.be.false;
      expect(acc.isGoalUrl('https://provesrc.com/price')).to.be.false;
      expect(acc.isGoalUrl('https://provesrc.com/#hallocomp')).to.be.false;
      expect(acc.isGoalUrl('https://provesrc.com/almostPurcha')).to.be.false;
      expect(acc.isGoalUrl('https://provesrc.com/about/this/order/notCompl')).to.be.false;
    });

    it('should support codeTrack property', async () => {
      const acc = new Account({ email: '<EMAIL>', password: '12345' });
      acc.goals.push({ name: 'goal 1', codeTrack: true });
      expect(acc.goals[0].codeTrack).to.be.true;
    });
  });

  describe('integrations', () => {
    it('should allow segment.writeKey', async () => {
      const key = '1234';
      const acc = new Account();
      acc.integrations.segment.writeKey = key;
      expect(acc.integrations.segment.writeKey).to.be.equal(key);
    });

    it('should allow stripe.apiKey', async () => {
      const key = '1234';
      const acc = new Account();
      acc.integrations.stripe.apiKey = key;
      expect(acc.integrations.stripe.apiKey).to.be.equal(key);
    });
  });

  describe('Transactions', () => {
    it('should find transaction based on custom id', async () => {
      const id = '12345';
      const acc = new Account({ email: '<EMAIL>', password: '12345' });
      acc.transactions.push({
        _id: id,
        ipn: 'CHARGE',
        currency: 'USD',
        invoiceAmount: '19',
        invoiceAmountUSD: 19,
        contractName: 'Starter Monthly Plan',
      });
      const payment = acc.transactions.id(id);
      expect(payment).to.exist;
      expect(payment.id).to.be.equal(id);
      expect(payment.invoiceAmount).to.be.equal(19);
    });

    it('should add payment correctly', async () => {
      const acc = new Account({ email: '<EMAIL>', password: '12345' });
      acc.transactions.push({
        _id: '12345',
        ipn: 'RECURRING',
        currency: 'RON',
        amount: '453.29',
        amountUSD: '19.00',
        plan: 'Starter Monthly Plan',
      });
      expect(acc.transactions[0].amountUSD).to.be.equal(19);
    });
  });
});
