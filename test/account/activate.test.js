const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const url = require('url');
const querystring = require('querystring');
const httpUtils = require('../httpUtils');
const constants = require('../constants');

const config = require('../../config');
const cryptoUtils = require('../../lib/utils/cryptoUtils');
const activate = require('../../app/account/login/activate');
const AccountF = require('../factories/AccountFactory');

const { Account } = AccountF;

describe('/account/activate', () => {
  let server;
  before(async () => {
    server = await require('../../server');
    if(!server) throw new Error('server not loaded');

    await Account.remove();
  });

  afterEach(async () => {
    sandbox.restore();
  });

  it('should return an activation link', async () => {
    const account = { _id: 'adosijw1oidm1' };
    const link = activate.getActivationLink(account);
    expect(link).to.contain('/account/activate?token=');
    const parsed = url.parse(link);
    const query = querystring.parse(parsed.query);
    expect(query.token).to.not.be.empty;
    const tokenValue = cryptoUtils.decrypt(query.token, config.cryptoKeys.activate);
    const accountId = tokenValue.split('|')[0];
    expect(accountId).to.be.equal(account._id);
  });

  it('should fail with bad link', async () => {
    const res = await httpUtils.noAuthRequest(server, '/account/activate', httpUtils.GET).query({ token: '********' });
    expect(res).to.have.status(401);
    expect(res.body.error).to.be.equal('this link is invalid or expired');
  });

  it('should redirect and set ps_signup if not active', async () => {
    const account = AccountF.free('<EMAIL>');
    account.active = false;
    sandbox.stub(Account, 'findOneAndUpdate').resolves(account);

    const token = activate._getToken(account);
    const res = await httpUtils.noAuthRequest(server, '/account/activate').query({ token }).redirects(0);
    expect(res).to.have.cookie('ps_signup');
    expect(res).to.redirect;
  });
});
