const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();

const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;

describe('/account/login', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should set ps_login cookie', async () => {
    const params = { email: constants.email, hashedPassword: false, password: constants.password };
    const account = new Account(params);
    account.active = true;
    sandbox.stub(Account.prototype, 'save').resolves();
    sandbox.stub(Account, 'findOne').yields(null, account);

    const res = await httpUtils.consoleRequest(server, constants.ACCOUNT.LOGIN, httpUtils.POST).send(params);
    expect(res).to.cookie('ps_login');
    let contains = false;
    const cookieHeaders = res.headers['set-cookie'];
    for(let i = 0; i < cookieHeaders.length; i++) {
      const cookie = cookieHeaders[i];
      contains = cookie.indexOf(account._id) > -1;
      if(contains) break;
    }
    expect(contains).to.be.true;
  });

  it('should return 403 if account not activated', async () => {
    const email = '<EMAIL>';
    const password = '12345';
    const account = AccountFactory.free(email, password);
    account.active = false;
    sandbox.stub(Account, 'findOne').yields(null, account);

    const res = await httpUtils
      .consoleRequest(server, constants.ACCOUNT.LOGIN, httpUtils.POST)
      .send({ email, password });
    expect(res).to.have.status(403);
    expect(res.body.error).to.contain('Your account has not been activated, please check your email');
  });
});
