const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const httpUtils = require('../httpUtils');
const testUtils = require('../testUtils');
const changeEmail = require('../../app/account/changeEmail');

const constants = require('../constants');

const ENDPOINT = '/account/change-email';

describe('/account/change-email', () => {
  let server;
  before(async () => {
    server = await require('../../server');
    if(!server) throw new Error('server not loaded');
  });

  afterEach(async () => {
    sandbox.restore();
  });

  describe('PUT', () => {
    it('should find the endpoint + allow method', async () => {
      sandbox.stub(changeEmail, 'sendConfirmationEmail').resolves();
      const res = await httpUtils.consoleRequest(server, ENDPOINT, 'PUT')
        .send({ email: '<EMAIL>' });
      expect(res).to.have.status(200);
    });
  });

  describe('GET', () => {
    it('should redirect user on success', async () => {
      sandbox.stub(changeEmail, 'changeEmail').resolves();
      const res = await httpUtils.noAuthRequest(server, `${ENDPOINT}?token=123`, 'GET').redirects(0);
      expect(res).to.redirect;
    });

    it('should and update session if exists on successful change', async () => {
      sandbox.stub(changeEmail, 'changeEmail').resolves('<EMAIL>');
      const res = await httpUtils.consoleRequest(server, `${ENDPOINT}?token=123`, 'GET').redirects(0);
      expect(res.headers['set-cookie'][0]).to.include('ps_session');
      expect(res).to.redirect;
    });
  });
});
