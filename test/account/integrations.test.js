const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();

const integrations = require('../../app/account/integrations');

const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;

describe('/account/integrations controller', () => {
  afterEach(() => {
    sandbox.restore();
  });

  describe('updateIntegrations', () => {
    it('should get account', async () => {
      sandbox.stub(Account.prototype, 'save').resolves();
      const findStub = sandbox.stub(Account, 'findOne').resolves(AccountFactory.default());
      const accountId = '12345';
      const data = { dummy: 'data' };
      await integrations.updateIntegrations(accountId, data);
      expect(findStub).to.have.been.calledWith({ _id: accountId });
    });

    it('should set integrations on account and save', async () => {
      const account = AccountFactory.default();
      sandbox.stub(Account, 'findOne').resolves(account);
      const saveStub = sandbox.stub(Account.prototype, 'save').resolves();
      const data = {
        segment: { writeKey: '12345' },
        stripe: { apiKey: '********' },
      };

      const result = await integrations.updateIntegrations(account.id, data);

      expect(saveStub).to.have.been.called;
      expect(account.integrations.toObject()).to.be.eql(data);
      expect(result).to.be.eql(account.integrations);
    });
  });

  describe('getIntegrations', () => {
    it('should get account and return integrations', async () => {
      const account = AccountFactory.default();
      account.integrations = { segment: { writeKey: '12345' } };
      const findStub = sandbox.stub(Account, 'findOne').resolves(account);

      const result = await integrations.getIntegrations(account.id);

      expect(findStub).to.have.been.calledWith({ _id: account.id });
      expect(result).to.be.eql(account.integrations);
    });
  });
});
