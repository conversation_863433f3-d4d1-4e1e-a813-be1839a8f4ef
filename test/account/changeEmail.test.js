const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const config = require('../../config');

const constants = require('../constants');
const changeEmail = require('../../app/account/changeEmail');

const cryptoUtils = require('../../lib/utils/cryptoUtils');
const mailer = require('../../app/account/mailer');
const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;

describe('account.changeEmail', () => {
  let accountFind;
  beforeEach(async () => {
    accountFind = sandbox.stub(Account, 'findOne');
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('sendConfirmationEmail', () => {
    let mailerStub;
    beforeEach(async () => {
      mailerStub = sandbox.stub(mailer, 'sendChangeEmailConfirmation').resolves();
    });

    it('should send a confirmation email', async () => {
      const email = '<EMAIL>';
      const newEmail = '<EMAIL>';
      accountFind.resolves();

      await changeEmail.sendConfirmationEmail(email, newEmail);

      expect(mailerStub).to.have.been.calledWith(
        sinon.match.string,
        email,
        newEmail,
        sinon.match('change-email?token='),
      );
    });

    it('should fail if new email already exists', async () => {
      const email = '<EMAIL>';
      const newEmail = '<EMAIL>';
      accountFind.resolves(AccountFactory.free(newEmail));

      await expect(changeEmail.sendConfirmationEmail(email, newEmail)).to.be.rejected;
    });
  });

  describe('changeEmail', () => {
    it('should fail if token is bad', async () => {
      const token = '1234';
      await expect(changeEmail.changeEmail(token)).to.be.rejectedWith('invalid');
    });

    it('should fail if token is expired', async () => {
      const yesterday = Date.now() - 86400 * 1000;
      const token = getToken(yesterday, '<EMAIL>', '<EMAIL>');
      await expect(changeEmail.changeEmail(token)).to.be.rejectedWith('expired');
    });

    it('should fail if email exists', async () => {
      accountFind.resolves({});
      const token = getToken(Date.now(), '<EMAIL>', '<EMAIL>');
      await expect(changeEmail.changeEmail(token)).to.be.rejectedWith('email');
    });

    it('should update the email for the account', async () => {
      const oldEmail = '<EMAIL>';
      const newEmail = '<EMAIL>';
      const updateStub = sandbox.stub(Account, 'updateOne').resolves({ nModified: 1 });
      const token = getToken(Date.now(), oldEmail, newEmail);

      const output = await changeEmail.changeEmail(token);

      expect(updateStub).to.have.been.calledWith(
        sinon.match({ email: oldEmail }),
        sinon.match(val => val.$set.email === newEmail
						&& val.$addToSet.emails.$each.includes(oldEmail)
						&& val.$addToSet.emails.$each.includes(newEmail)),
      );
      expect(output).to.be.equal(newEmail);
    });

    function getToken(timestamp, email, newEmail) {
      const data = { timestamp, email, newEmail };
      return cryptoUtils.encrypt(JSON.stringify(data), config.cryptoKeys.changeEmail);
    }
  });
});
