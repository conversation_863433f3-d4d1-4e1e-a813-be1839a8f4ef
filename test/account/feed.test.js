const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');

const NotificationFactory = require('../factories/NotificationFactory');
const Feed = require('../../app/account/models/Feed');
const utils = require('../testUtils');

describe('/account/feed', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => Feed.remove());

  it('should create a feed for webhook', async () => {
    const { accountId } = constants;
    const notification = NotificationFactory.ConversionWebhook({ accountId });
    await notification.save();
    const email = '<EMAIL>';
    await httpUtils.trackWebhook(server, notification.webhookId, email);

    const [feed, res] = await Promise.all([
      Feed.findOne({ accountId }),
      httpUtils.consoleRequest(server, constants.ACCOUNT.FEED, httpUtils.GET),
    ]);
    expect(feed.data.webhookId).to.be.equal(notification.webhookId);
    expect(feed.data.email).to.be.equal(email);
    expect(feed.data.notification).to.be.equal(notification.name);

    const feedItem = res.body.feed[0];
    expect(feedItem.data.webhookId).to.be.equal(notification.webhookId);
    expect(feedItem.data.email).to.be.equal(email);
    expect(feedItem.data.source).to.not.be.empty;
    expect(feedItem.timestamp).to.satisfy(timestamp => timestamp > 0 && timestamp - Date.now() < 0);
  });

  it('should identify Zapier source', async () => {
    const { accountId } = constants;
    const notif = NotificationFactory.Stream({ autoTrack: false });
    await notif.save();

    const email = '<EMAIL>';
    const { webhookId } = notif;
    const ua = 'Zapier';
    await httpUtils.trackWebhookData(server, webhookId, { email }, { 'user-agent': ua });

    const feed = await Feed.findOne({ accountId });
    expect(feed.data.source).to.be.equal(ua);
    expect(feed.message).to.include(ua);
  });

  it('should create a feed for form events', async () => {
    const { accountId } = constants;
    const url = 'http://provesrc.com/form';
    const email = '<EMAIL>';
    await httpUtils.trackForm(server, url, email);

    const [feed, res] = await Promise.all([
      Feed.findOne({ accountId }),
      httpUtils.consoleRequest(server, constants.ACCOUNT.FEED, httpUtils.GET),
    ]);

    expect(feed.data.url).to.be.equal(url);
    expect(feed.data.email).to.be.equal(email);

    const feedItem = res.body.feed[0];
    expect(feedItem.data.url).to.be.equal(url);
    expect(feedItem.data.email).to.be.equal(email);
    expect(feedItem.timestamp).to.satisfy(timestamp => timestamp > 0 && timestamp - Date.now() < 0);
  });
});
