const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const querystring = require('querystring');
const constants = require('../constants');
const serverP = require('../../server');
const StreamEvent = require('../../app/events/models/StreamEvent');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();

const Account = require('../../app/account/models/Account');
const mailer = require('../../app/account/mailer');
const cryptoUtils = require('../../lib/utils/cryptoUtils');
const maxmind = require('../../lib/maxmind');

describe('/account/signup', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  const email = '<EMAIL>';
  beforeEach(async () => {
    await Promise.all([StreamEvent.remove(), Account.remove({ email })]);
  });

  afterEach(() => {
    sandbox.restore();
  });


  it('should save properties on signup', async () => {
    sandbox.stub(mailer, 'sendActivation').resolves();
    const sourceAffiliate = cryptoUtils.randomString(8);
    const cookieKey = 'ps_aff';
    const ip = '*************';
    const loc = await maxmind.geoIP(ip);
    const query = 'utm_medium%3Dtester%26utm_content%3Dlal';
    const gacid = '**********.*************';
    const headers = { cookie: `${cookieKey}=${sourceAffiliate}; ps_query_params=${query}; ps_gacid=${gacid}`, 'x-real-ip': ip };
    const params = { email, password: '123' };
    const res = await httpUtils.noAuthRequest(server, '/account/signup', httpUtils.POST).set(headers).send(params);
    expect(res).to.have.status(200);
    expect(res).to.not.have.cookie('ps_signup');

    const account = await Account.findOne({ email });
    expect(account.active).to.be.false;
    expect(account.affiliate.referrer).to.be.equal(sourceAffiliate);
    expect(account.affiliate.id).to.have.lengthOf(8);
    expect(account.ip).to.be.equal(ip);
    expect(account.location.toObject()).to.be.deep.equal(loc);
    expect(account.apiKey).to.not.be.null;

    const qObj = querystring.parse(decodeURIComponent(query));
    expect(account.query).to.be.deep.equal(qObj);
    expect(account.gaClientId).to.be.equal(gacid);
    expect(account.emails).to.include(email);
  });

  it('should save if came from ltd page', async () => {
    sandbox.stub(mailer, 'sendActivation').resolves();
    const headers = { cookie: 'ps_deal=true' };
    const params = { email, password: '123' };
    const res = await httpUtils.noAuthRequest(server, '/account/signup', httpUtils.POST).set(headers).send(params);
    expect(res).to.have.status(200);

    const account = await Account.findOne({ email });
    expect(account.ltd).to.be.true;
  });
});
