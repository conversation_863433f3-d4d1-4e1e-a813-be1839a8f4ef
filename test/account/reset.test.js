const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');
const Account = require('../../app/account/models/Account');
const forgot = require('../../app/account/login/forgot').testExports;

describe('/account/reset', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  const email = '<EMAIL>';
  beforeEach(async () => {
    await Account.remove({ email });
  });

  afterEach(async () => {
    await Account.remove({ email });
  });

  it('should change password to account', async () => {
    const password = 'zaq12345';
    const account = new Account({ email, hashedPassword: false, password });
    await account.save();

    const token = forgot.generateToken(account._id);
    const newPassword = '123ret';

    const params = { token, password: newPassword };
    const res = await httpUtils.noAuthRequest(server, '/account/reset', httpUtils.POST).send(params);
    expect(res).to.have.status(200);
    expect(res.body).to.have.property('success', true);

    const dbAccount = await Account.findOne({ email });
    expect(await dbAccount.comparePassword(newPassword)).to.be.true;
  });

  it('should fail with bad token', async () => {
    const password = 'zaq12345';
    const account = new Account({ email, password });
    await account.save();

    const token = 'tokeneenneena';
    const newPassword = '123ret';

    try {
      const params = { token, password: newPassword };
      const res = await httpUtils.noAuthRequest(server, '/account/reset', httpUtils.POST).send(params);
    } catch(err) {
      expect(err.response.body.error).to.be.equal('bad token');
    }
  });

  it('should fail when expires', async () => {
    const password = 'zaq12345';
    const account = new Account({ email, password });
    await account.save();

    const token = forgot.generateToken(account._id, Date.now() - 32 * 60 * 1000);
    const newPassword = '123ret';

    try {
      const params = { token, password: newPassword };
      await httpUtils.noAuthRequest(server, '/account/reset', httpUtils.POST).send(params);
    } catch(err) {
      expect(err).to.exist;
      expect(err.response.body).to.have.property('error', 'token expired');
    }
  });
});
