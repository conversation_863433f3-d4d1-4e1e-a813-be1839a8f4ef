const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const httpUtils = require('../httpUtils');

const constants = require('../constants');

const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;

// tests are very slow due to bluesnap API
xdescribe('/account/updateCard', () => {
  let server;
  before(async () => {
    server = await require('../../server');
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    sandbox.stub(Account.prototype, 'save').resolves();
  });

  afterEach(async () => {
    sandbox.restore();
  });

  it('should update card and subscription', async () => {
    const account = AccountFactory.monthlySubscription('<EMAIL>', '12345', { model: true });
    account.subscription.subscriptionId = '********';
    account.subscription.bluesnapId = '********';
    sandbox.stub(Account, 'findOne').resolves(account);
    const data = {
      cardNumber: '5425 2334 3010 99 03',
      firstName: 'John',
      lastName: 'Doe',
      cvv: '111',
      expiry: '04 / 2023',
    };
    const headers = { 'x-real-ip': '*************' };
    const res = await httpUtils.consoleRequest(server, '/account/updateCard', 'POST').set(headers).send(data);
    expect(res).to.have.status(200);
  }).timeout(5000);

  it('should accept 2 digit exp year', async () => {
    const account = AccountFactory.monthlySubscription('<EMAIL>', '12345', { model: true });
    account.subscription.subscriptionId = '********';
    account.subscription.bluesnapId = '********';
    sandbox.stub(Account, 'findOne').resolves(account);
    const data = {
      cardNumber: '5425 2334 3010 99 03',
      firstName: 'John',
      lastName: 'Doe',
      cvv: '111',
      expiry: '04 / 23',
    };
    const headers = { 'x-real-ip': '*************' };
    const res = await httpUtils.consoleRequest(server, '/account/updateCard', 'POST').set(headers).send(data);
    expect(res).to.have.status(200);
  }).timeout(5000);

  it('should fail to update card', async () => {
    const account = AccountFactory.monthlySubscription('<EMAIL>', '12345', { model: true });
    account.subscription.subscriptionId = '********';
    account.subscription.bluesnapId = '********';
    sandbox.stub(Account, 'findOne').resolves(account);
    const data = {
      cardNumber: '5425 2334 3010 99',
      firstName: 'John',
      lastName: 'Doe',
      cvv: '111',
      expiry: '04 / 2023',
    };
    const headers = { 'x-real-ip': '*************' };
    const res = await httpUtils.consoleRequest(server, '/account/updateCard', 'POST').set(headers).send(data);
    expect(res).to.have.status(500);
  }).timeout(5000);
});
