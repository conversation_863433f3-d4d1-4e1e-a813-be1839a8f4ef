const chai = require('chai');
const sinon = require('sinon');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const slack = require('../../lib/apis/slackNotifier');
const serverP = require('../../server');
const config = require('../../config');
const { Account } = require('../factories/AccountFactory');

describe('/account/affiliate/payout', () => {
  const sandbox = sinon.createSandbox();
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should accept request and update affiliate data', async () => {
    const email = '<EMAIL>';
    const balance = 50;
    const paid = 25;
    const account = new Account({ email, affiliate: { balance, paid } });
    sandbox.stub(Account, 'findOne').resolves(account);
    sandbox.stub(account, 'save').resolves();
    const slackStub = sandbox.stub(slack, 'notify');

    const res = await httpUtils.consoleRequest(server, '/account/affiliate/payout', httpUtils.POST).send({ email });
    expect(res).to.have.status(200);

    expect(slackStub).to.have.been.calledWith(
      `payout requested by ${email} to paypal ${email} balance: ${balance}`,
      sinon.match.any,
      { webhook: config.slack.affiliates },
    );
    expect(account.affiliate.balance).to.be.equal(0);
    expect(account.affiliate.paid).to.be.equal(paid + balance);
    expect(account.affiliate.paypal).to.be.equal(email);
    const payout = account.affiliate.payouts[0];
    expect(payout).to.exist;
  });

  it('should not accept request if balance less than $25', async () => {
    const email = '<EMAIL>';
    sandbox.stub(Account, 'findOne').resolves(new Account({ email, affiliate: { balance: 12 } }));

    const res = await httpUtils.consoleRequest(server, '/account/affiliate/payout', httpUtils.POST).send({ email });
    expect(res).to.have.status(400);
  });
});
