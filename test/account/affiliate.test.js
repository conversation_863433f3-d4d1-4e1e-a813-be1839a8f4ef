const httpUtils = require('../httpUtils');
const chai = require('chai');

const { expect } = chai;
const sinon = require('sinon');

const sandbox = sinon.createSandbox();

const constants = require('../constants');
const serverP = require('../../server');
const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;

describe('/account/affiliate', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should return affiliate info', async () => {
    sandbox.stub(Account, 'findOne').resolves(AccountFactory.default());
    const res = await httpUtils.consoleRequest(server, '/account/affiliate', httpUtils.GET);
    expect(res).to.have.status(200);
    expect(res.body).to.contain.keys(['id', 'balance', 'mrr', 'paid', 'referrer', 'signups', 'affiliateLink', 'paypal']);
  });

  it('should include affiliate commission', async () => {
    const commissionRate = 0.5;
    const account = {
      affiliate: {
        toObject() {
          return { commissionRate };
        },
      },
      getAffiliateId() {
        return '1234';
      },
    };
    sandbox.stub(Account, 'findOne').resolves(account);
    const res = await httpUtils.consoleRequest(server, '/account/affiliate', httpUtils.GET);
    expect(res).to.have.status(200);
    expect(res.body.commissionRate).to.be.equal(commissionRate);
  });
});
