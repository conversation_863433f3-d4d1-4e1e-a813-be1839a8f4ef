const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const httpUtils = require('../httpUtils');

const constants = require('../constants');
const AccountF = require('../factories/AccountFactory');

xdescribe('/account/upgrade (#slow)', () => {
  let server;
  before(async () => {
    server = await require('../../server');
    if(!server) throw new Error('server not loaded');
  });

  afterEach(async () => {
    sandbox.restore();
  });

  it('should upgrade account subscription', async () => {
    const data = { contractId: '2396489' };
    sandbox.stub(AccountF.Account, 'findOne').resolves({ subscription: { subscriptionId: '********' } });
    const res = await httpUtils.consoleRequest(server, '/account/upgrade', httpUtils.POST).send(data);
    expect(res).to.have.status(200);
    expect(res.body.message).to.be.equal('success');
  });

  it('should fail if no account found', async () => {
    const fakeAccount = AccountF.free('<EMAIL>');
    const agent = httpUtils.Agent(server, fakeAccount.apiKey);
    const res = await agent.consoleRequest(server, '/account/upgrade', httpUtils.POST);
    expect(res).to.have.status(400);
  });
});
