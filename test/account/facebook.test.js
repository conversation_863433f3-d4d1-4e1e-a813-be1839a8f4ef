const { expect } = require('chai');
const sinon = require('sinon');
const httpUtils = require('../httpUtils');
const testUtils = require('../testUtils');
const app = require('../../server');

const facebookLib = require('../../lib/apis/facebook');
const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;

describe('/account/facebook', () => {
  afterEach(async () => {
    sinon.restore();
  });

  describe('facebook config', () => {
    it('should add facebook page to account', async () => {
      const account = AccountFactory.default();
      sinon.stub(Account, 'findOne').resolves(account);
      sinon.stub(account, 'save').resolves();
      sinon.stub(facebookLib, 'getUserAccessToken').resolves({ access_token: 'userToken' });
      sinon.stub(facebookLib, 'getPageAccessToken').resolves({ access_token: 'pageToken' });
      const data = {
        pageId: 'pageId',
        pageName: 'pageName',
        accessToken: 'accessToken',
      };

      const res = await httpUtils
        .consoleRequest(app, '/account/facebook', httpUtils.POST)
        .send(data);
      expect(res).to.have.status(200);

      expect(account.facebook.length).to.be.equal(1);
      expect(account.facebook[0].pageId).to.be.equal(data.pageId);
    });

    it('should update existing facebook page on account', async () => {
      const account = AccountFactory.default();
      account.facebook.push({
        pageId: 'pageId',
        pageName: 'pageName',
        userToken: 'userToken',
        pageToken: 'pageToken',
      });
      const pageToken = 'newPageToken';
      const userToken = 'newUserToken';
      sinon.stub(Account, 'findOne').resolves(account);
      sinon.stub(account, 'save').resolves();
      sinon.stub(facebookLib, 'getUserAccessToken').resolves({ access_token: userToken });
      sinon.stub(facebookLib, 'getPageAccessToken').resolves({ access_token: pageToken });

      const res = await httpUtils
        .consoleRequest(app, '/account/facebook', httpUtils.POST)
        .send({ pageId: 'pageId', pageName: 'pageName', accessToken: 'accessToken' });
      expect(res).to.have.status(200);

      expect(account.facebook.length).to.be.equal(1);
      expect(account.facebook[0].userToken).to.be.equal(userToken);
      expect(account.facebook[0].pageToken).to.be.equal(pageToken);
    });
  });
});
