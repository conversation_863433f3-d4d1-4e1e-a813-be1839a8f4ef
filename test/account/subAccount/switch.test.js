const chai = require('chai');
const sinon = require('sinon');
const chaiHttp = require('chai-http');

chai.use(chaiHttp);

const server = require('../../../server');
const httpUtils = require('../../httpUtils');
const Account = require('../../../app/account/models/Account');
const AccountFactory = require('../../factories/AccountFactory');
const config = require('../../../config');

const { expect } = chai;

describe('switch', () => {
  let app;
  before(async () => {
    app = await server;
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should redirect to console with new session', async () => {
    const id = 123456;
    const accountFind = sinon.mock(Account).expects('findById').chain('lean').resolves(AccountFactory.default());

    const res = await httpUtils.consoleRequest(app, `/account/sub/switch?id=${id}`, 'GET').redirects(0);

    expect(res).to.redirectTo(config.consoleUrl);
  });
});
