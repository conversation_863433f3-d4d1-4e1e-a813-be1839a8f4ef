const chai = require('chai');

const { expect } = chai;
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const Errors = require('../../lib/errors/Errors');

describe('Lib - ErrorFactory', () => {
  it('should have all properties', () => {
    const expectations = {
      message: 'message',
      code: 444,
      data: { info: '123' },
    };
    const error = ErrorFactory(expectations.message, expectations.code, expectations.data);
    expect(error.message).to.be.equal(expectations.message);
    expect(error.code).to.be.equal(expectations.code);
    expect(error._data).to.be.eql(expectations.data);
  });

  it('should set data when code is omitted', () => {
    const expectations = {
      message: 'message',
      data: { info: '123' },
    };
    const error = ErrorFactory(expectations.message, expectations.data);
    expect(error.message).to.be.equal(expectations.message);
    expect(error._data).to.be.eql(expectations.data);
  });

  it('should handle context', () => {
    const method = 'GET';
    const expectations = {
      message: `method '${method}' not allowed`,
      code: 405,
      context: Errors.methodNotAllowed,
    };
    const error = Errors.methodNotAllowed(method);
    expect(error.message).to.be.equal(expectations.message);
    expect(error.code).to.be.equal(expectations.code);
    expect(error.stack).to.not.have.string(expectations.context.name);
  });
});
