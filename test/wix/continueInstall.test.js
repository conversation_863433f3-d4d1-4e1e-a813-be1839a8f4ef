const chai = require('chai');
const sinon = require('sinon');
const httpUtils = require('../httpUtils');
const cryptoUtils = require('../../lib/utils/cryptoUtils');
const config = require('../../config');
const wixService = require('../../app/wix/wix.service');
const accountService = require('../../app/account/account.service');
const Account = require('../../app/account/models/Account');
const constants = require('../constants');
const app = require('../../server');

const { expect } = chai;

describe('wix/continue-install', () => {
  afterEach(() => {
    sinon.restore();
  });
  describe('bad token', () => {
    it('should throw an error if token is invalid', async () => {
      const token = 'blabla';
      const res = await httpUtils.noAuthRequest(app, `/wix/continue-install?token=${token}`, 'GET');
      expect(res).to.have.status(400);
      expect(res.body.error).to.include('invalid token');
    });
  });
  describe('wix site connected', () => {
    it('should let user know site is connected to a different account', async () => {
      const instanceId = '12345';
      const { accountId, accountId2, email } = constants;
      const site = {
        siteName: 'somethingSite', instanceId, noConnectedAccount: true, ownerEmail: email,
      };
      sinon.stub(wixService, 'getConnectedAccount').resolves({ id: accountId2 });
      sinon.stub(wixService, 'updateSite').resolves(site);
      const token = cryptoUtils.encrypt(JSON.stringify({ instanceId }), config.cryptoKeys.wixInstallEmail);
      const cookie = httpUtils.makeSession(accountId);
      const loggedInRes = await httpUtils.consoleRequest(app, `/wix/continue-install?token=${token}`, 'GET').set({ cookie });
      const noAuthRes = await httpUtils.noAuthRequest(app, `/wix/continue-install?token=${token}`, 'GET');

      expect(loggedInRes).to.have.status(200);
      expect(loggedInRes.text).to.include(`Wix site <strong>${site.siteName}</strong> is already connected to another account`);
      expect(noAuthRes).to.have.status(200);
      expect(noAuthRes.text).to.include(`Wix site <strong>${site.siteName}</strong> is already connected`);
    });
    it('should let user know site is connected to their account', async () => {
      const instanceId = '12345';
      const siteName = 'somethingSite';
      const { accountId, email } = constants;
      sinon.stub(wixService, 'getConnectedAccount').resolves({ id: accountId });
      sinon.stub(wixService, 'updateSite').resolves({
        siteName, instanceId, noConnectedAccount: true, ownerEmail: email,
      });
      const token = cryptoUtils.encrypt(JSON.stringify({ instanceId }), config.cryptoKeys.wixInstallEmail);
      const cookie = httpUtils.makeSession(accountId);
      const res = await httpUtils.consoleRequest(app, `/wix/continue-install?token=${token}`, 'GET').set({ cookie });
      expect(res).to.have.status(200);
      expect(res.text).to.include(`Wix site <strong>${siteName}</strong> is already connected to your account`);
    });
  });
  describe('wix site not connected', () => {
    it('should take user to login/signup if no account OR account not eligible to install (too many sites)', async () => {
      const instanceId = '12345';
      const { accountId, email, apiKey } = constants;
      // account with installed wix
      const account = new Account({
        accountId, apiKey, email, wix: [{ instanceId }],
      });
      sinon.stub(wixService, 'updateSite').resolves({
        siteName: 'somethingSite', instanceId, noConnectedAccount: true, ownerEmail: email,
      });
      sinon.stub(accountService, 'getAccount').resolves(account);
      const token = cryptoUtils.encrypt(JSON.stringify(instanceId), config.cryptoKeys.wixInstallEmail);
      const cookie = httpUtils.makeSession(accountId);
      const authRes = await httpUtils.consoleRequest(app, `/wix/continue-install?token=${token}`, 'GET').set({ cookie });
      const noAuthRes = await httpUtils.consoleRequest(app, `/wix/continue-install?token=${token}`, 'GET');

      expect(authRes).to.have.status(200);
      expect(authRes.text).to.include('Signup Required');
      expect(noAuthRes).to.have.status(200);
      expect(authRes.text).to.include('Signup Required');
    });
    it('should install the wix site on the account if eligible', async () => {
      const instanceId = '12345';
      const siteName = 'somethingSite';
      const orders = 10;
      const forms = 12;
      const subscriptions = 15;
      const url = 'https://wixapp.something.com';
      const { accountId, apiKey, email } = constants;
      sinon.stub(wixService, 'updateSite').resolves({
        siteName, instanceId, noConnectedAccount: true, ownerEmail: email, url,
      });
      sinon.stub(wixService, 'integrateSite').resolves({ orders, forms, subscriptions });
      const account = new Account({ accountId, apiKey, email });
      sinon.stub(accountService, 'getAccount').resolves(account);

      const token = cryptoUtils.encrypt(JSON.stringify(instanceId), config.cryptoKeys.wixInstallEmail);
      const cookie = httpUtils.makeSession(accountId);
      const res = await httpUtils.consoleRequest(app, `/wix/continue-install?token=${token}`, 'GET').set({ cookie }).redirects(0);
      const redirect = res.headers.location;
      expect(redirect).to.include('/#/integration-complete');
      expect(redirect).to.include(`website=${encodeURIComponent(url)}`);
    });
    it('should not install on account with wixSite.ownerEmail if not verified', async () => {
      const instanceId = '12345';
      const siteName = 'somethingSite';
      const { accountId, apiKey, email } = constants;
      sinon.stub(wixService, 'updateSite').resolves({
        siteName, instanceId, noConnectedAccount: true, ownerEmail: email, ownerInfo: { email, emailStatus: 'NOT_VERIFIED' },
      });
      sinon.stub(wixService, 'integrateSite').resolves(true);
      const account = new Account({ accountId, apiKey, email });
      sinon.stub(accountService, 'getAccountByEmail').resolves(account);

      const token = cryptoUtils.encrypt(JSON.stringify(instanceId), config.cryptoKeys.wixInstallEmail);
      const res = await httpUtils.noAuthRequest(app, `/wix/continue-install?token=${token}`, 'GET').redirects(0);
      expect(res.text).to.include('Signup Required');
    });
    it('should install and login to wixSite.ownerEmail if verified', async () => {
      const instanceId = '12345';
      const siteName = 'somethingSite';
      const { accountId, apiKey, email } = constants;
      sinon.stub(wixService, 'updateSite').resolves({
        siteName, instanceId, noConnectedAccount: true, ownerEmail: email, ownerInfo: { email, emailStatus: 'VERIFIED' },
      });
      sinon.stub(wixService, 'integrateSite').resolves(true);
      const account = new Account({ accountId, apiKey, email });
      sinon.stub(accountService, 'getAccountByEmail').resolves(account);

      const token = cryptoUtils.encrypt(JSON.stringify(instanceId), config.cryptoKeys.wixInstallEmail);
      const res = await httpUtils.noAuthRequest(app, `/wix/continue-install?token=${token}`, 'GET').redirects(0);
      expect(res).to.redirect;
    });
  });
});
