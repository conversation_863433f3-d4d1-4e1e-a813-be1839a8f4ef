module.exports.GET_ORDER = {
  order: {
    id: '9e8a206f-4fef-4c8b-aca6-d2fa60c98aa4',
    number: 10682,
    dateCreated: '2019-11-20T23:12:54.731Z',
    buyerInfo: {
      id: '6712046a-5cdb-42c7-a766-94aec08c898f',
      type: 'MEMBER',
      identityType: 'MEMBER',
      firstName: 'Ivanushka',
      lastName: 'Trulala',
      phone: '+972 *********',
      email: '<EMAIL>',
    },
    currency: 'USD',
    weightUnit: 'LB',
    totals: {
      subtotal: '26.67',
      shipping: '8.0',
      tax: '1.33',
      discount: '0.0',
      total: '36.0',
      weight: '0.0',
      quantity: 1,
    },
    billingInfo: {
      paymentMethod: 'Fake Payment Method',
      paymentGatewayTransactionId: '411c672c-693a-4c24-88bb-8e5903ba2c88',
      address: {
        fullName: {
          firstName: '<PERSON>ush<PERSON>',
          lastName: 'Trulala',
        },
        country: 'IL',
        city: 'Tel Aviv',
        zipCode: '12323',
        phone: '+972 *********',
        email: '<EMAIL>',
        addressLine1: 'Street Str, 15',
      },
      paidDate: '2019-11-20T23:12:55.150Z',
    },
    shippingInfo: {
      deliveryOption: 'Express shipping',
      estimatedDeliveryTime: '1 day',
      shipmentDetails: {
        address: {
          fullName: {
            firstName: 'Ivanushka',
            lastName: 'Trulala',
          },
          country: 'IL',
          city: 'Tel Aviv',
          zipCode: '12323',
          phone: '+972 *********',
          email: '<EMAIL>',
          addressLine1: 'Street Str, 15',
        },
        discount: '0.0',
        tax: '0.0',
      },
    },
    read: false,
    archived: false,
    paymentStatus: 'PAID',
    fulfillmentStatus: 'NOT_FULFILLED',
    lineItems: [{
      index: 1,
      quantity: 1,
      price: '28.0',
      name: 'Shop the Trends Plus Size Womens Top',
      translatedName: 'Shop the Trends Plus Size Womens Top',
      productId: 'd7a77e5d-1774-4cb8-a3bb-8cf6e09f57a8',
      totalPrice: '28.0',
      lineItemType: 'PHYSICAL',
      options: [
        {
          option: 'Size',
          selection: '1XL',
        },
        {
          option: 'Color',
          selection: 'Navy White',
        },
      ],
      customTextFields: [],
      mediaItem: {
        mediaType: 'IMAGE',
        url: 'https://static.wixstatic.com/media/bfa274_219377d5ee164e7fb058cfe02441bad7~mv2.jpg/v1/fit/w_1200,h_1800,q_90/file.jpg',
        width: 1200,
        height: 1800,
        mediaId: 'bfa274_219377d5ee164e7fb058cfe02441bad7~mv2.jpg',
      },
      sku: 't4350x-womens-plus-top-1xl',
      variantId: '00000000-0000-0020-0005-964d944567d7',
      fulfillerId: 'f4ef9627-612a-4b63-b8bd-edb7831dcc92',
      discount: '0.0',
      tax: '1.33',
      taxIncludedInPrice: true,
    }],
    activities: [
      {
        type: 'ORDER_PLACED',
        timestamp: '2019-11-20T23:12:54.731Z',
      },
      {
        type: 'ORDER_PAID',
        timestamp: '2019-11-20T23:12:55.150Z',
      },
    ],
    fulfillments: [],
    cartId: 'ee32c513-5f54-405b-9e0c-2af7e3dfd909',
    buyerLanguage: 'en',
    channelInfo: { type: 'WEB' },
    lastUpdated: '2019-11-20T23:12:55.279Z',
  },
};

module.exports.GET_PRODUCT = {
  product: {
    id: 'a60fef92-ee29-070f-a7ed-9bbc3cc1c2f4',
    name: 'T-shirt',
    slug: 't-shirt-1',
    visible: false,
    productType: 'physical',
    description: 'nice summer t-shirt',
    stock: {
      trackInventory: false,
      inStock: true,
    },
    price: {
      currency: 'ILS',
      price: 10.5,
      discountedPrice: 9.5,
      formatted: {
        price: '10.50 ₪',
        discountedPrice: '9.50 ₪',
      },
    },
    priceData: {
      currency: 'ILS',
      price: 10.5,
      discountedPrice: 9.5,
      formatted: {
        price: '10.50 ₪',
        discountedPrice: '9.50 ₪',
      },
    },
    additionalInfoSections: [
    ],
    ribbons: [
    ],
    media: {
      items: [
      ],
    },
    customTextFields: [
    ],
    manageVariants: true,
    productOptions: [
      {
        optionType: 'drop_down',
        name: 'Size',
        choices: [
          {
            value: 'S',
            description: 'S',
            inStock: true,
            visible: true,
          },
          {
            value: 'L',
            description: 'L',
            inStock: true,
            visible: true,
          },
        ],
      },
    ],
    productPageUrl: {
      base: 'https://www.itsjusttooeasy123.com/',
      path: '/product-page/t-shirt-1',
    },
    numericId: '1567588455405000',
    inventoryItemId: '1d71b222-912f-bf67-5a1a-30b4263b084a',
    discount: {
      type: 'AMOUNT',
      value: 1.0,
    },
    collectionIds: [
    ],
  },
};
