const chai = require('chai');
const sinon = require('sinon');
const httpUtils = require('../httpUtils');
const app = require('../../server');
const wixLib = require('../../lib/apis/wix');
const WixData = require('../../app/wix/models/WixData');

const { expect } = chai;

describe('wix/app-installed', () => {
  afterEach(() => {
    sinon.restore();
  });
  it('should successfully save data in wixData DB  ', async () => {
    // setup
    const token = '123.123.123';
    const stab = sinon.stub(wixLib, 'decodeJwt').returns({
      eventType: '123',
      instanceId: '12345',
      data: '123333',
    });
    sinon.stub(WixData.prototype, 'save').resolves({});
    // action
    const res = await httpUtils.noAuthRequest(app, '/wix/app-installed', 'POST')
      .set('content-type', 'text/plain')
      .send(token);
    // validation
    expect(res).to.have.status(200);
    expect(stab).to.have.been.calledWith('123.123.123');
  });
  it('should return error if bad token (null)', async () => {
    // setup
    const token = '123.123.123';
    const stab = sinon.stub(wixLib, 'decodeJwt').returns(null);
    const save = sinon.stub(WixData.prototype, 'save');
    // action
    const res = await httpUtils.noAuthRequest(app, '/wix/app-installed', 'POST')
      .set('content-type', 'text/plain')
      .send(token);
    // validation
    expect(res).to.have.status(400);
    expect(stab).to.have.been.calledWith('123.123.123');
  });
});
