const chai = require('chai');
const sinon = require('sinon');
const wixLib = require('../../lib/apis/wix');
const Account = require('../../app/account/models/Account');
const service = require('../../app/wix/wix.service');
const accountService = require('../../app/account/account.service');

const { expect } = chai;

describe('wix.service', () => {
  describe('wix.service.integrateApp', () => {
    afterEach(() => {
      sinon.restore();
    });

    function stubs() {
      const getToken = sinon.stub(wixLib, 'getToken');
      const updateEmbedScript = sinon.stub(wixLib, 'updateEmbedScript');
      const saveOrder = sinon.stub(service, 'saveOrder');
      const getOrders = sinon.stub(wixLib, 'getOrders');
      const getAppInstance = sinon.stub(wixLib, 'getAppInstance');
      const hasWixNotification = sinon.stub(accountService, 'hasWixNotification');
      return {
        getToken, updateEmbedScript, saveOrder, getOrders, hasWixNotification, getAppInstance,
      };
    }

    it('should return null if input is malformed', async () => {
    // account validation
      await expect(service.integrateAppWithAccount({}, '123', '123'))
        .to.eventually.be.rejectedWith('account must not be null');
      await expect(service.integrateAppWithAccount(null, '123', '123'))
        .to.eventually.be.rejectedWith('account must not be null');
      await expect(service.integrateAppWithAccount(undefined, '123', '123'))
        .to.eventually.be.rejectedWith('account must not be null');
      await expect(service.integrateAppWithAccount('', '123', '123'))
        .to.eventually.be.rejectedWith('account must not be null');
      await expect(service.integrateAppWithAccount(123, '123', '123'))
        .to.eventually.be.rejectedWith('account must not be null');
      await expect(service.integrateAppWithAccount('123', '123', '123'))
        .to.eventually.be.rejectedWith('account must not be null');
      await expect(service.integrateAppWithAccount(true, '123', '123'))
        .to.eventually.be.rejectedWith('account must not be null');

      // instanceId validation
      await expect(service.integrateAppWithAccount({ data: 123 }, '', '123'))
        .to.eventually.be.rejectedWith('instanceId should not be an empty string');
      await expect(service.integrateAppWithAccount({ data: 123 }, null, '123'))
        .to.eventually.be.rejectedWith('instanceId should not be an empty string');
      await expect(service.integrateAppWithAccount({ data: 123 }, undefined, '123'))
        .to.eventually.be.rejectedWith('instanceId should not be an empty string');
      await expect(service.integrateAppWithAccount({ data: 123 }, 132, '123'))
        .to.eventually.be.rejectedWith('instanceId should not be an empty string');
      await expect(service.integrateAppWithAccount({ data: 123 }, true, '123'))
        .to.eventually.be.rejectedWith('instanceId should not be an empty string');

      // token validation
      await expect(service.integrateAppWithAccount({ data: 123 }, '123', ''))
        .to.eventually.be.rejectedWith('token should not be an empty string');
      await expect(service.integrateAppWithAccount({ data: 123 }, '123', null))
        .to.eventually.be.rejectedWith('token should not be an empty string');
      await expect(service.integrateAppWithAccount({ data: 123 }, '123', undefined))
        .to.eventually.be.rejectedWith('token should not be an empty string');
      await expect(service.integrateAppWithAccount({ data: 123 }, '123', 123))
        .to.eventually.be.rejectedWith('token should not be an empty string');
      await expect(service.integrateAppWithAccount({ data: 123 }, '123', true))
        .to.eventually.be.rejectedWith('token should not be an empty string');
    });

    it('should return error if failed to get tokens from wix', async () => {
      const { getToken } = stubs();
      getToken.rejects({});

      await expect(service.integrateAppWithAccount({ data: 123 }, '321', '12345'))
        .to.eventually.be.rejectedWith('get tokens from wix');
    });

    it('should throw error if update embed script failed', async () => {
      const { getToken, updateEmbedScript } = stubs();
      getToken.resolves({});
      const accountInput = new Account({
        wix: [],
      });

      updateEmbedScript.rejects({});

      const promise = service.integrateAppWithAccount(accountInput, '321', '12345');
      await expect(promise).to.eventually.be.rejectedWith('install the script on wix');
    });

    it('should successfully return updated refresh and access tokens.', async () => {
      const {
        getToken, updateEmbedScript, saveOrder, getOrders, hasWixNotification, getAppInstance,
      } = stubs();
      const token1 = '123';
      const token2 = '321';
      const instanceId = '123456';
      getToken.resolves({
        refresh_token: token1,
        access_token: token2,
      });
      updateEmbedScript.resolves({});
      const accountInput = new Account({
        removedWix: [],
        wix: [],
      });
      getOrders.resolves({ item: 1 });
      saveOrder.returns({
        then(saveResults) {
          saveResults({ saveResults: 1, orders: { id: 1 } });
        },
      });
      hasWixNotification.resolves(true);
      getAppInstance.resolves({ site: { installedWixApps: ['store'] } });

      const res = await service.integrateAppWithAccount(accountInput, instanceId, token1);
      expect(res).to.not.be.empty;
      expect(res.refreshToken).to.be.equal(token1);
      expect(res.accessToken).to.be.equal(token2);
    });
  });

  describe('wix.service.getCheckoutUrl', () => {
    afterEach(() => {
      sinon.restore();
    });

    function stubs() {
      const refreshAccessToken = sinon.stub(wixLib, 'refreshAccessToken');
      const getCheckoutUrl = sinon.stub(wixLib, 'getCheckoutUrl');
      return { refreshAccessToken, getCheckoutUrl };
    }

    it('should response with error if tokens not passed input validation', async () => {
      await Promise.all([
        expect(service.getCheckoutUrl({})).to.eventually.be.rejectedWith('tokens'),
        expect(service.getCheckoutUrl({ refresh: '123' })).to.eventually.be.rejectedWith('tokens'),
        expect(service.getCheckoutUrl({ access: '123' })).to.eventually.be.rejectedWith('tokens'),

        expect(service.getCheckoutUrl({ accessToken: '123', refreshToken: '123' }))
          .to.eventually.be.rejectedWith('plan'),
        expect(service.getCheckoutUrl({ accessToken: '123', refreshToken: '123', plan: null }))
          .to.eventually.be.rejectedWith('plan'),

        expect(service.getCheckoutUrl({ accessToken: '123', refreshToken: '123', plan: 'starter' }))
          .to.eventually.be.rejectedWith('period'),
        expect(service.getCheckoutUrl({
          accessToken: '123', refreshToken: '123', period: null, plan: 'starter',
        })).to.eventually.be.rejectedWith('period'),
      ]);
    });

    it('should response with error if failed to refresh wix tokens', async () => {
      const params = {
        accessToken: '123',
        refreshToken: '123',
        plan: 'starter',
        period: 'monthly',
      };

      const { refreshAccessToken } = stubs();
      await refreshAccessToken.rejects({});

      await expect(service.getCheckoutUrl(params)).to.eventually.be.rejectedWith('failed to refresh wix tokens');
    });

    it('should response with error if failed to get the checkout url from wix', async () => {
      const params = {
        accessToken: '123',
        refreshToken: '123',
        plan: 'starter',
        period: 'monthly',
      };
      const newTokens = {
        access_token: 'OAuth2.0 123 NEW',
        refresh_token: 'OAuth2.0 321 NEW',
      };

      const { refreshAccessToken, getCheckoutUrl } = stubs();
      refreshAccessToken.resolves({
        access_token: 'OAuth2.0 123 NEW',
        refresh_token: 'OAuth2.0 321 NEW',
      });
      await getCheckoutUrl.rejects(new Error('failed'));

      await expect(service.getCheckoutUrl(params))
        .to.eventually.be.rejectedWith('failed to get checkout url from wix');
    });

    it('should response successfully with checkout url', async () => {
      const params = {
        accessToken: 'OAuth2.0 123',
        refreshToken: 'OAuth2.0 321',
        plan: 'starter',
        period: 'monthly',
      };
      const newTokens = {
        access_token: 'OAuth2.0 123 NEW',
        refresh_token: 'OAuth2.0 321 NEW',
      };
      const response = { checkoutUrl: 'some.com/thanks' };
      const { refreshAccessToken, getCheckoutUrl } = stubs();
      refreshAccessToken.resolves(newTokens);
      getCheckoutUrl.resolves(response);

      const res = await service.getCheckoutUrl(params);
      expect(res).to.not.be.empty;
      expect(res.checkoutUrl).to.be.eql(response);
      expect(res.accessToken).to.be.eql(newTokens.access_token);
      expect(res.refreshToken).to.be.eql(newTokens.refresh_token);
    });
  });

  describe('wix.service.getAppBilling', () => {
    afterEach(() => {
      sinon.restore();
    });

    function stubs() {
      const refreshAccessToken = sinon.stub(wixLib, 'refreshAccessToken');
      const getAppInstance = sinon.stub(wixLib, 'getAppInstance');
      return { refreshAccessToken, getAppInstance };
    }

    it('should response with error if tokens not passed input validation', async () => {
      await Promise.all([
        expect(service.getBillingInfo({})).to.eventually.be.rejectedWith('tokens'),
        expect(service.getBillingInfo({ refreshToken: '1234' })).to.eventually.be.rejectedWith('tokens'),
        expect(service.getBillingInfo({ accessToken: '1234' })).to.eventually.be.rejectedWith('tokens'),
      ]);
    });

    it('should response with error if failed to refresh wix tokens', async () => {
      const { refreshAccessToken } = stubs();
      refreshAccessToken.rejects(new Error('failed'));

      await expect(service.getBillingInfo({ accessToken: '1234', refreshToken: '123' }))
        .to.eventually.be.rejectedWith('failed to refresh wix tokens');
    });

    it('should response with error if failed to get the app instance from wix', async () => {
      const newTokens = {
        access_token: 'OAuth2.0 123 NEW',
        refresh_token: 'OAuth2.0 321 NEW',
      };

      const { refreshAccessToken, getAppInstance } = stubs();
      refreshAccessToken.resolves(newTokens);
      getAppInstance.rejects(new Error('failed'));

      await expect(service.getBillingInfo({
        accessToken: 'OAuth2.0 123',
        refreshToken: 'OAuth2.0 321',
      })).to.eventually.be.rejectedWith('failed to get app instance from wix');
    });

    it('should response successfully with billing data', async () => {
      const newTokens = {
        access_token: 'OAuth2.0 123 NEW',
        refresh_token: 'OAuth2.0 321 NEW',
      };
      const appInstance = {
        instance: {
          billing: {
            some: 'data',
          },
        },
      };

      const { refreshAccessToken, getAppInstance } = stubs();
      refreshAccessToken.resolves(newTokens);
      getAppInstance.resolves(appInstance);

      const res = await service.getBillingInfo({
        accessToken: 'OAuth2.0 123',
        refreshToken: 'OAuth2.0 321',
      });
      expect(res).to.not.be.empty;
      expect(res.billing).to.be.eql(appInstance.instance.billing);
      expect(res.accessToken).to.be.eql(newTokens.access_token);
      expect(res.refreshToken).to.be.eql(newTokens.refresh_token);
    });
  });
});
