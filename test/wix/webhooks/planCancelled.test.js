const chai = require('chai');
const sinon = require('sinon');
const mongoose = require('mongoose');

const wixLib = require('../../../lib/apis/wix');
const WixData = require('../../../app/wix/models/WixData');
const Account = require('../../../app/account/models/Account');
const subscriptionService = require('../../../app/billing/subscription.service');
const httpUtils = require('../../httpUtils');
const app = require('../../../server');

const { expect } = chai;

describe('wix/planCancelled', () => {
  afterEach(() => {
    sinon.restore();
  });
  function stubsFunc() {
    const getJwtData = sinon.stub(wixLib, 'decodeJwt');
    const saveWixData = sinon.stub(WixData.prototype, 'save').resolves({});
    const accountFindOne = sinon.stub(mongoose.model('Account'), 'findOne');
    const cancelSubscription = sinon.stub(subscriptionService, 'cancelSubscription');
    const accountSave = sinon.stub(Account.prototype, 'save');
    return {
      getJwtData, saveWixData, accountFindOne, accountSave, cancelSubscription,
    };
  }

  it('should return error if bad token', async () => {
    const { getJwtData } = stubsFunc();
    const token = '123.123.123';

    getJwtData.returns(null);

    const res = await httpUtils.noAuthRequest(app, '/wix/plan-cancelled', 'POST')
      .set('content-type', 'text/plain')
      .send(token);

    expect(res).to.have.status(400);
    expect(res.error).with.property('text', '{"error":"malformed jwt payload from webhook"}');
    expect(getJwtData).to.have.been.calledWith('123.123.123');
  });

  it('should return error if account not found in DB using instanceId', async () => {
    const { getJwtData, saveWixData, accountFindOne } = stubsFunc();
    const token = '123.123.123';
    const instance = '12345';

    getJwtData.returns({
      eventType: '123',
      instanceId: instance,
    });
    accountFindOne.resolves(null);

    const res = await httpUtils.noAuthRequest(app, '/wix/plan-cancelled', 'POST')
      .set('content-type', 'text/plain')
      .send(token);

    expect(res).to.have.status(400);
    expect(res.error).with.property('text', '{"error":"failed to find account using instanceId"}');
  });

  it('should return error if failed to save changed updated account', async () => {
    const {
      getJwtData, saveWixData, accountFindOne, accountSave, cancelSubscription,
    } = stubsFunc();

    const token = '123.123.123';
    const instance = '12345';

    const recentIPN = 'CANCELLATION';
    const untilDate = Date.now();
    getJwtData.returns({
      eventType: '123',
      instanceId: instance,
    });

    const accountObj = new Account({
      subscription: {},
    });
    accountFindOne.resolves(accountObj);
    cancelSubscription.returns({ recentIPN, untilDate });
    await accountSave.rejects({});

    const res = await httpUtils.noAuthRequest(app, '/wix/plan-cancelled', 'POST')
      .set('content-type', 'text/plain')
      .send(token);

    expect(res).to.have.status(500);
    expect(res.body.error).to.be.eql('failed to save account');
  });

  it('should return status 200 and save updated account(with canceled subscription)', async () => {
    const {
      getJwtData, saveWixData, accountFindOne, accountSave, cancelSubscription,
    } = stubsFunc();

    const token = '123.123.123';
    const instance = '12345';
    const recentIPN = 'CANCELLATION';
    const untilDate = new Date(0);

    getJwtData.returns({
      eventType: '123',
      instanceId: instance,
    });
    const accountObj = new Account({
      subscription: {},
    });

    accountFindOne.resolves(accountObj);
    cancelSubscription.returns({ recentIPN, untilDate });
    accountSave.resolves({});

    const res = await httpUtils.noAuthRequest(app, '/wix/plan-cancelled', 'POST')
      .set('content-type', 'text/plain')
      .send(token);

    expect(res).to.have.status(200);
    expect(accountObj.subscription).to.not.be.empty;
    expect(accountObj.subscription.recentIPN).to.eql(recentIPN);
    expect(accountObj.subscription.untilDate).to.eql(untilDate);
  });
});
