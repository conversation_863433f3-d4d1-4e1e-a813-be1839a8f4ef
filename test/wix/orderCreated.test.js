const chai = require('chai');
const sinon = require('sinon');
const mongoose = require('mongoose');
const httpUtils = require('../httpUtils');
const app = require('../../server');
const wixLib = require('../../lib/apis/wix');
const Account = require('../../app/account/models/Account');
const WixEvent = require('../../app/wix/models/WixEvent');

const { expect } = chai;
const wixData = require('../../app/wix/models/WixData');

describe('wix/order-created', () => {
  afterEach(() => {
    sinon.restore();
  });
  function stubsFunc() {
    const getJwtData = sinon.stub(wixLib, 'decodeJwt');
    const save = sinon.stub(wixData.prototype, 'save').resolves({});
    const accountFindOne = sinon.stub(mongoose.model('Account'), 'findOne');
    const refreshTokens = sinon.stub(wixLib, 'refreshAccessToken');
    const accountSave = sinon.stub(Account.prototype, 'save');
    const wixOrder = sinon.stub(wixLib, 'getOrder');
    const wixProduct = sinon.stub(wixLib, 'getProduct');
    const wixEventSave = sinon.stub(WixEvent.prototype, 'save');
    return {
      getJwtData,
      save,
      accountFindOne,
      refreshTokens,
      accountSave,
      wixOrder,
      wixProduct,
      wixEventSave,
    };
  }

  it('should return error if bad token (null) is sent in webhook', async () => {
    // setup
    const { getJwtData } = stubsFunc();
    getJwtData.returns(null);
    const token = '123.123.123';
    // action
    const res = await httpUtils.noAuthRequest(app, '/wix/order-created', 'POST')
      .set('content-type', 'text/plain')
      .send(token);
    // validation
    expect(res).to.have.status(400);
    expect(res.error).with.property('text', '{"error":"malformed jwt payload from webhook"}');
    expect(getJwtData).to.have.been.calledWith('123.123.123');
  });

  it('should response with error if not founded Account object in DB using instanceId', async () => {
    const token = '123.123.123';
    const instance = '123456';
    const { getJwtData, accountFindOne } = stubsFunc();
    getJwtData.returns({
      eventType: '123',
      instanceId: instance,
      data: {},
    });
    accountFindOne.resolves(null);
    const res = await httpUtils.noAuthRequest(app, '/wix/order-created', 'POST')
      .set('content-type', 'text/plain')
      .send(token);
    expect(res).to.have.status(400);
    expect(res.error).with.property('text', '{"error":"failed to find account using instanceId"}');
    expect(accountFindOne).to.have.been.calledWith({ 'wix.instanceId': instance });
  });

  it('should response with error if failed to refresh tokens', async () => {
    const token = '123.123.123';
    const instance = '123456';
    const {
      getJwtData,
      accountFindOne,
      refreshTokens,
    } = stubsFunc();
    getJwtData.returns({
      eventType: '123',
      instanceId: instance,
      data: {},
    });
    const wix = {
      refreshToken: 'OAUTH2.4567Hgd4Q0ns',
      accessToken: 'OAUTH2.wabUHPX',
      instanceId: instance,
    };
    const varAccount = new Account({
      wix: [wix],
      getWixSite() { return wix; },
    });
    accountFindOne.resolves(varAccount);
    await refreshTokens.rejects(new Error('bad refresh token request'));
    // refreshTokens.throws(new Error("bad refresh token request"));
    const res = await httpUtils.noAuthRequest(app, '/wix/order-created', 'POST')
      .set('content-type', 'text/plain')
      .send(token);
    expect(res).to.have.status(500);
    expect(res.error).with.property('text', '{"error":"bad refresh token request"}');
    expect(refreshTokens).to.have.been.calledWith(wix.refreshToken, wix.accessToken);
  });

  it('should response with error if orderId not found in wix', async () => {
    const token = '123.123.123';
    const instance = '123456';
    const orderId = '123456';
    const {
      getJwtData,
      accountFindOne,
      refreshTokens,
      accountSave,
      wixOrder,
      wixEventSave,
    } = stubsFunc();
    getJwtData.returns({
      eventType: '123',
      instanceId: instance,
      data: {
        orderId,
        buyerInfo: {
          email: '<EMAIL>',
        },
      },
    });
    const wix = {
      refreshToken: 'OAUTH2.3210ns',
      accessToken: 'OAUTH2.FO6_OhbLA',
      instanceId: instance,
    };
    const varAccount = new Account({
      wix: [wix],
      getWixSite() { return wix; },
    });
    accountFindOne.resolves(varAccount);
    refreshTokens.resolves({
      refresh_token: '123.123.123',
      access_token: '123.123.123',
    });
    wixEventSave.resolves({});
    accountSave.resolves({});
    await wixOrder.rejects(new Error('bad get order request'));
    const res = await httpUtils.noAuthRequest(app, '/wix/order-created', 'POST')
      .set('content-type', 'text/plain')
      .send(token);
    expect(res).to.have.status(200);
  });

  it('should response with error if productId not found in wix', async () => {
    const token = '123.123.123';
    const instance = '123456';
    const productId = '12345';
    const orderId = '********';
    const {
      getJwtData,
      accountFindOne,
      refreshTokens,
      accountSave,
      wixOrder,
      wixProduct,
      wixEventSave,
    } = stubsFunc();
    getJwtData.returns({
      eventType: '123',
      instanceId: instance,
      data: {
        orderId,
        buyerInfo: {
          email: '<EMAIL>',
        },
      },
    });
    const wix = {
      refreshToken: 'OAUTH2.745ghjfgh.dHgd4Q0ns',
      accessToken: 'OAUTH2.meKs6_OhbLA',
      instanceId: instance,
    };
    const varAccount = new Account({
      wix: [wix],
      getWixSite() { return wix; },
    });
    accountFindOne.resolves(varAccount);
    refreshTokens.resolves({
      refresh_token: '123.123.123',
      access_token: '123.123.123',
    });
    accountSave.resolves({});
    wixOrder.resolves({
      lineItems: [
        { productId },
        { productId: '123' },
      ],
      totals: {
        total: 12,
      },
    });
    wixEventSave.resolves({});
    await wixProduct.rejects('bad get product request');
    const res = await httpUtils.noAuthRequest(app, '/wix/order-created', 'POST')
      .set('content-type', 'text/plain')
      .send(token);
    expect(res).to.have.status(200);
  });

  it('should response with error if wix event save is failed', async () => {
    const token = '123.123.123';
    const instance = '123456';
    const productId = '12345';
    const {
      getJwtData,
      accountFindOne,
      refreshTokens,
      accountSave,
      wixOrder,
      wixProduct,
      wixEventSave,
    } = stubsFunc();
    getJwtData.returns({
      eventType: '123',
      instanceId: instance,
      data: {
        orderId: '*********',
        buyerInfo: {
          email: '<EMAIL>',
        },
        dateCreated: Date.now(),
      },
    });
    const wix = {
      refreshToken: 'OAUTH2.fdgh0PzdHgd4Q0ns',
      accessToken: 'OAUTH2.euTUFOQU.P27GBWN3M3uYx5bQ0NngmKmeKs6_OhbLA',
      instanceId: instance,
    };
    const varAccount = new Account({
      wix: [wix],
      getWixSite() { return wix; },
    });
    accountFindOne.resolves(varAccount);
    refreshTokens.resolves({
      refresh_token: '123.123.123',
      access_token: '123.123.123',
    });
    accountSave.resolves({});
    wixOrder.resolves({
      lineItems: [
        {
          productId,
          quantity: 10,
        },
        {
          productId: '123',
          quantity: 3,
        },
      ],
      totals: {
        total: 12,
      },
      currency: 'NIS',
    });
    wixProduct.resolves({
      id: '123',
      name: 'test name',
      priceData: {
        price: 1,
      },
      productPageUrl: {
        base: 'https://example.org/',
        path: '/product',
      },
      media: {
        mainMedia: {
          image: {
            url: 'google.com/pic/1',
          },
        },
      },
    });
    await wixEventSave.rejects(new Error('failed to save order created wixEvent'));
    const res = await httpUtils.noAuthRequest(app, '/wix/order-created', 'POST')
      .set('content-type', 'text/plain')
      .send(token);
    expect(res).to.have.status(500);
    expect(res.error).with.property('text', '{"error":"failed to save order created wixEvent"}');
  });

  it('should successfully save data in wix Event DB', async () => {
    const token = '123.123.123';
    const instance = '123456';
    const productId = '12345';
    const {
      getJwtData,
      accountFindOne,
      refreshTokens,
      accountSave,
      wixOrder,
      wixProduct,
      wixEventSave,
    } = stubsFunc();
    getJwtData.returns({
      eventType: '123',
      instanceId: instance,
      data: {
        orderId: '123rwedfsdvf',
        buyerInfo: {
          email: '<EMAIL>',
        },
        dateCreated: Date.now(),
      },
    });
    const wix = {
      refreshToken: 'OAUTH2.eyJraWQi0ns',
      accessToken: 'OAUTH2.eyTUFOXKsNkYeKs6_OhbLA',
      instanceId: instance,
    };
    const varAccount = new Account({
      wix: [wix],
      getWixSite() { return wix; },
    });
    accountFindOne.resolves(varAccount);
    refreshTokens.resolves({
      refresh_token: '123.123.123',
      access_token: '123.123.123',
    });
    accountSave.resolves({});
    wixOrder.resolves({
      lineItems: [
        {
          productId,
          quantity: 10,
        },
        {
          productId: '123',
          quantity: 3,
        },
      ],
      totals: {
        total: 12,
      },
      currency: 'NIS',
    });
    wixProduct.resolves({
      id: '123',
      name: 'test name',
      priceData: {
        price: 1,
      },
      productPageUrl: {
        base: 'https://example.org/',
        path: '/product',
      },
      media: {
        mainMedia: {
          image: {
            url: 'google.com/pic/1',
          },
        },
      },
    });
    wixEventSave.resolves({});
    const res = await httpUtils.noAuthRequest(app, '/wix/order-created', 'POST')
      .set('content-type', 'text/plain')
      .send(token);
    expect(res).to.have.status(200);
  });
});
