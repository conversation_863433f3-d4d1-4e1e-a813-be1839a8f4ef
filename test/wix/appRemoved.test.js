const chai = require('chai');
const chaiAsPromised = require('chai-as-promised');
const sinon = require('sinon');
const mongoose = require('mongoose');
const httpUtils = require('../httpUtils');
const app = require('../../server');
const wixLib = require('../../lib/apis/wix');

const { expect } = chai;
const Account = require('../../app/account/models/Account');
const WixData = require('../../app/wix/models/WixData');


describe('wix/app-removed', () => {
  afterEach(() => {
    sinon.restore();
  });
  function stubsFunc() {
    const getJwtData = sinon.stub(wixLib, 'decodeJwt');
    const saveWixData = sinon.stub(WixData.prototype, 'save').resolves({});
    const accountFindOne = sinon.stub(mongoose.model('Account'), 'findOne');
    const accountSave = sinon.stub(Account.prototype, 'save');
    return {
      getJwtData, saveWixData, accountFindOne, accountSave,
    };
  }
  it('should return error if bad token (null)', async () => {
    // setup
    const { getJwtData, saveWixData } = stubsFunc();
    const token = '123.123.123';
    getJwtData.returns(null);
    // action
    const res = await httpUtils.noAuthRequest(app, '/wix/app-removed', 'POST')
      .set('content-type', 'text/plain')
      .send(token);
    // validation
    expect(res).to.have.status(400);
    expect(res.error).with.property('text', '{"error":"malformed jwt payload from webhook"}');
    expect(getJwtData).to.have.been.calledWith('123.123.123');
  });

  it('Should response with error if not founded Account model in DB using instanceId', async () => {
    // setup
    const { getJwtData, saveWixData, accountFindOne } = stubsFunc();
    const token = '123.123.123';
    const instance = '12345';
    getJwtData.returns({
      eventType: '123',
      instanceId: instance,
    });
    accountFindOne.resolves(null);
    // action
    const res = await httpUtils.noAuthRequest(app, '/wix/app-removed', 'POST')
      .set('content-type', 'text/plain')
      .send(token);
    // validation
    expect(res).to.have.status(400);
    expect(res.error).with.property('text', '{"error":"failed to find account using instanceId"}');
  });

  it('should response with error if removedWix model failed to saved in DB', async () => {
    // setup
    const {
      getJwtData, saveWixData, accountFindOne, accountSave,
    } = stubsFunc();
    const token = '123.123.123';
    const instance = '12345';
    const wix = {
      refreshToken: 'OAUTH2.eyJraWQiOiJkZ0x3cjNRMCIsImFsZyI6IkhTMjU2In0.******************************************************************************************************************************.Jb3trqj0Kr_naH8Xew7x8w3ytncoOua0PzdHgd4Q0ns',
      accessToken: 'OAUTH2.eyJraWQiOiJLaUp3NXZpeSIsImFsZyI6IlJTMjU2In0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.P27jXJaopXLBcSMsP_bCujk-AdjQpOKLiwaqghPYW7vrBI1j9DXGO37blPmhm4pMji6ClClG8Hxp8X53ZOnRvBdCGrDjvWhSqt7zIWQoDcc3UjoSr_iCFCRDUoz9nZbN2aINiair5fYfzB5lftxyc855234OxPj5RTccwicN6zpDKcN40WDaxMQRwWL3kI_GyQttFBsOrAhGBWNAGEz6kLEFXKsNkYfBRsIxYXiFKwfEWztHlKM50ySNVo9L4hU-wabUHPX-EmQsmGCg52yCHzqv4xTrhMzHE_uHroAIrgFvxQOBfIjGVuY9eJ9g3M3uYx5bQ0NngmKmeKs6_OhbLA',
      instanceId: instance,
    };
    getJwtData.returns({
      eventType: '123',
      instanceId: instance,
    });
    const accountObj = new Account({
      wix: [wix],
      getWixSite() { return wix; },
      removedWix: {
        wix: [wix],
        uninstalledDate: Date.now(),
      },
    });
    accountFindOne.resolves(accountObj);
    await accountSave.rejects(new Error('failed to save account data in DB'));
    // action
    const res = await httpUtils.noAuthRequest(app, '/wix/app-removed', 'POST')
      .set('content-type', 'text/plain')
      .send(token);
    // validation
    expect(res).to.have.status(500);
    expect(res.error).with.property('text', '{"error":"failed to save account data in DB"}');
  });

  it('should successfully save the removedWix model', async () => {
    // setup
    const {
      getJwtData, saveWixData, accountFindOne, accountSave,
    } = stubsFunc();
    const token = '123.123.123';
    const instance = '12345';
    const wix = {
      refreshToken: 'OAUTH2.eyJraWQiOiJkZ0x3cjNRMCIsImFsZyI6IkhTMjU2In0.******************************************************************************************************************************.Jb3trqj0Kr_naH8Xew7x8w3ytncoOua0PzdHgd4Q0ns',
      accessToken: 'OAUTH2.eyJraWQiOiJLaUp3NXZpeSIsImFsZyI6IlJTMjU2In0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.P27jXJaopXLBcSMsP_bCujk-AdjQpOKLiwaqghPYW7vrBI1j9DXGO37blPmhm4pMji6ClClG8Hxp8X53ZOnRvBdCGrDjvWhSqt7zIWQoDcc3UjoSr_iCFCRDUoz9nZbN2aINiair5fYfzB5lftxyc855234OxPj5RTccwicN6zpDKcN40WDaxMQRwWL3kI_GyQttFBsOrAhGBWNAGEz6kLEFXKsNkYfBRsIxYXiFKwfEWztHlKM50ySNVo9L4hU-wabUHPX-EmQsmGCg52yCHzqv4xTrhMzHE_uHroAIrgFvxQOBfIjGVuY9eJ9g3M3uYx5bQ0NngmKmeKs6_OhbLA',
      instanceId: instance,
    };
    getJwtData.returns({
      eventType: '123',
      instanceId: instance,
    });
    const accountObj = new Account({
      wix: [wix],
      getWixSite() { return wix; },
      removedWix: {
        wix: [wix],
        uninstalledDate: Date.now(),
      },
    });
    accountFindOne.resolves(accountObj);
    // action
    const res = await httpUtils.noAuthRequest(app, '/wix/app-removed', 'POST')
      .set('content-type', 'text/plain')
      .send(token);
    // validation
    expect(res).to.have.status(200);
  });
});
