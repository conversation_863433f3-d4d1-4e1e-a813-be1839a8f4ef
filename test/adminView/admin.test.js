const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const testUtils = require('../testUtils');

const NFactory = require('../factories/NotificationFactory');

const { Notification } = NFactory;

describe('dashboard admin view', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  afterEach(async () => {
    sandbox.restore();
  });

  it('should return notifications based on ps_admin cookie', async () => {
    const accId = testUtils.ObjectId().toString();
    const notifSpy = sandbox.spy(Notification, 'find');

    const session = httpUtils.makeSession(constants.accountId);
    const headers = { cookie: `${session};ps_admin=${accId}.bassword` };
    const res = await httpUtils.consoleRequest(server, '/notifications/list', httpUtils.GET).set(headers);
    expect(res).to.have.status(200);

    expect(notifSpy).to.have.been.calledWith(sinon.match.has('accountId', accId));
  });
});
