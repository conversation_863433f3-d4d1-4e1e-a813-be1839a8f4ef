/** Created by natanavra on 27/02/2018 */

const jwt = require('jsonwebtoken');
const { ObjectId } = require('mongoose').Types;
const config = require('../config');

const constants = {
  apiKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************.MpCRM_K4GJkUExCeBQsOwL5IxDT3bg1cHU0m4cqMeGw',
  cookieHeader: (
    'ps_session=********************************************************************************************************************************************************************************************************************************************************************************************************************************; path=/; domain=localhost; Expires=Tue, 19 Jan 2038 03:14:07 GMT;'
		+ 'ps_session.sig=0fWygIhtP_uqK8JlQqkTkP0CnJU; path=/; domain=localhost; Expires=Tue, 19 Jan 2038 03:14:07 GMT;'
  ),
  subscription: {
    created: new Date(),
    plan: 'lifetime-39-addon',
    contractId: '3452234',
    untilDate: new Date('2020'),
    transactionDate: Date.now() + ********,
    card: {
      firstName: 'Natan',
      lastName: 'Abramov',
      fourDigits: '1234',
      expDate: '01/2040',
    },
    recentIPN: 'CHARGE',
    invoiceEmail: '<EMAIL>',
    invoiceCompanies: ['Jojo mojo'],
  },
  NOTIFICATIONS: {
    GET: '/notifications/get',
    CREATE: '/notifications/create',
    LIST: '/notifications/list',
    STATE: '/notifications/state',
    DELETE: '/notifications/delete',
    ANALYTICS: '/notifications/analytics',
  },
  WEBHOOKS: {
    TRACK: '/webhooks/track',
  },
  EVENTS: {
    TRACK: '/events/track',
    TRACK_FORM: '/events/trackForm',
  },
  ACCOUNT: {
    MAIL_TO: '/account/mailto',
    INSTALL: '/account/install',
    LOGIN: '/account/login',
    SIGNUP: '/account/signup',
    CONFIGURATION: '/account/configuration',
    FEED: '/account/feed',
  },
  BILLING: {
    IPN: '/billing/ipn',
  },
};
constants.authorizationHeader = `bearer ${constants.apiKey}`;
constants.email = '<EMAIL>';
constants.affiliateId = '********';
constants.limit = 50000;
constants.accountId = (() => jwt.decode(constants.apiKey).accountId)();
constants.password = 'test123';
constants.accountId2 = new ObjectId();
constants.apiKey2 = jwt.sign({ accountId: constants.accountId2 }, config.jwt.api);

module.exports = constants;
