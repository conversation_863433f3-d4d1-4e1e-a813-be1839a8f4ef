const chai = require('chai');
const sinon = require('sinon');
const app = require('../../server');

const sandbox = sinon.createSandbox();
const { expect } = chai;

describe('request.options', () => {
  afterEach(async () => {
    sandbox.restore();
  });

  it('should respond with 200', async () => {
    const res = await chai.request(app).options('/abcda');
    expect(res).to.have.status(200);
    expect(res.body).to.be.empty;
  });
});
