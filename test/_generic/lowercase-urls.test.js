const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();

const urlUtils = require('../../lib/utils/urlUtils');

const NotifFactory = require('../factories/NotificationFactory');
const Notification = require('../../app/notifications/models/Notification');
const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;

const Event = require('../../app/events/models/Event');

describe('lowercase urls', () => {
  let server;
  before(() => serverP.then((app) => {
    if(!app) throw new Error('server not loaded');
    server = app;
  }));

  beforeEach(() => Promise.all([Notification.remove({}), Event.remove({})]));

  afterEach(() => {
    sandbox.restore();
  });

  describe('/notifications/create', () => {
    it('should lowercase urls on create', () => {
      const params = NotifFactory.params.pageVisits();
      for(let i = 0; i < params.trackURL.length; i++) params.trackURL[i] = params.trackURL[i].toUpperCase();
      for(let i = 0; i < params.trackURL.length; i++) params.displayURLs[i] = params.displayURLs[i].toUpperCase();

      return httpUtils.consoleRequest(server, constants.NOTIFICATIONS.CREATE, httpUtils.POST)
        .send(params)
        .then((res) => {
          expect(res).to.have.status(200);
          return Notification.findOne({ accountId: constants.accountId, name: params.name });
        })
        .then((notif) => {
          expect(notif).to.exist;
          checkUrls(notif);
        });
    });

    it('should lowercase urls on update', () => {
      const params = NotifFactory.params.pageVisits();
      return httpUtils.consoleRequest(server, constants.NOTIFICATIONS.CREATE, httpUtils.POST)
        .send(params)
        .then((res) => {
          expect(res).to.have.status(200);
          return Notification.findOne({ accountId: constants.accountId, name: params.name });
        })
        .then((notification) => {
          params.trackURL.push('https://ProveSrc.com/ReTaRD');
          params.displayURLs.push('https://ProveSrc.com/DissssplaY');
          params._id = notification._id;
          return httpUtils.consoleRequest(server, constants.NOTIFICATIONS.CREATE, httpUtils.POST).send(params);
        })
        .then((res) => {
          expect(res).to.have.status(200);
          return Notification.findOne({ accountId: constants.accountId, name: params.name });
        })
        .then((notification) => {
          checkUrls(notification);
        });
    });

    it('should lowercase clean URLs', async () => {
      const params = NotifFactory.PageVisitsParams();
      params.urlTypes = { trackAbs: false, displayAbs: false };
      const url = 'events.genndi.com/registerBox/169105139238448751/9291b15581?asdakd=asdkadk';
      params.displayURLs = [url];
      params.trackURL = [url];

      const res = await httpUtils.createNotification(server, params);
      expect(res).to.have.status(200);

      const cleanUrlLower = 'events.genndi.com/registerBox/169105139238448751/9291b15581'.toLowerCase();
      const notification = await Notification.findOne({ name: params.name });
      expect(notification.urlTypes.trackAbs).to.be.false;
      expect(notification.urlTypes.displayAbs).to.be.false;
      expect(notification.trackURL[0]).to.be.equal(cleanUrlLower);
      expect(notification.displayURLs[0]).to.be.equal(cleanUrlLower);
    });

    function checkUrls(notification) {
      for(let i = 0; i < notification.trackURL.length; i++) {
        const url = notification.trackURL[i];
        expect(url).to.equal(url.toLowerCase());
      }

      for(let i = 0; i < notification.displayURLs.length; i++) {
        const url = notification.displayURLs[i];
        expect(url).to.equal(url.toLowerCase());
      }
    }
  });

  describe('/notifications/get', () => {
    it('should lowercase url in event saved', () => {
      sandbox.stub(Account, 'findOne').resolves(AccountFactory.default());

      const params = NotifFactory.params.pageVisits();
      const uppercaseUrl = params.trackURL[0].toUpperCase();
      const lowercaseUrl = uppercaseUrl.toLowerCase();
      return httpUtils.consoleRequest(server, constants.NOTIFICATIONS.CREATE, httpUtils.POST).send(params).then((res) => {
        expect(res).to.have.status(200);
        return httpUtils.apiRequest(server, constants.NOTIFICATIONS.GET, httpUtils.POST).send({ url: uppercaseUrl, unique: true });
      }).then((res) => {
        expect(res).to.have.status(200);
        return Event.findOne({ url: lowercaseUrl });
      })
        .then((event) => {
          expect(event).to.exist;
          expect(event.counts).to.not.eql({});
        });
    });

    it('should lowercase url in notification search', () => {
      sandbox.stub(Account, 'findOne').resolves(AccountFactory.default());

      const params = NotifFactory.params.pageVisits();
      const uppercaseUrl = params.trackURL[0].toUpperCase();

      return httpUtils.consoleRequest(server, constants.NOTIFICATIONS.CREATE, httpUtils.POST).send(params).then((res) => {
        expect(res).to.have.status(200);
        return httpUtils.apiRequest(server, constants.NOTIFICATIONS.GET, httpUtils.POST).send({ url: uppercaseUrl, unique: true });
      }).then((res) => {
        expect(res).to.have.status(200);
        const displayUrl = params.displayURLs[0].toUpperCase();
        return httpUtils.apiRequest(server, constants.NOTIFICATIONS.GET, httpUtils.POST).send({ url: displayUrl, unique: false });
      })
        .then((res) => {
          expect(res.body).to.not.be.empty;
        });
    });
  });

  describe('/events APIs', () => {
    describe('/events/track', () => {
      it('should save event and parsed parts in lowercase', () => {
        const stupidCaseUrl = 'http://www.autoDS.com/LandingPage?Key=vaLUe';
        return httpUtils.apiRequest(server, constants.EVENTS.TRACK, httpUtils.POST).send({ url: stupidCaseUrl, unique: true }).then((res) => {
          expect(res).to.have.status(200);

          return Event.findOne({ url: stupidCaseUrl.toLowerCase() });
        }).then((event) => {
          expect(event).to.exist;

          const { host, pathname, query } = event;
          expect(host).to.equal(host.toLowerCase());
          expect(pathname).to.equal(pathname.toLowerCase());
          expect(query).to.equal(query.toLowerCase());
        });
      });
    });

    describe('/events/trackForm', () => {
      it('should save event and parsed parts in lowercase', () => {
        const stupidCaseUrl = 'http://www.autoDS.com/SignupForm?ref=Bulbul';
        return httpUtils.apiRequest(server, constants.EVENTS.TRACK_FORM, httpUtils.POST).send({ url: stupidCaseUrl, email: '<EMAIL>' }).then((res) => {
          expect(res).to.have.status(200);

          return Event.findOne({ url: stupidCaseUrl.toLowerCase() });
        }).then((event) => {
          expect(event).to.exist;

          const { host, pathname, query } = event;
          expect(host).to.equal(host.toLowerCase());
          expect(pathname).to.equal(pathname.toLowerCase());
          expect(query).to.equal(query.toLowerCase());
        });
      });
    });
  });
});
