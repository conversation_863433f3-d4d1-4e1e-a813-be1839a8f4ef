const chai = require('chai');
const sinon = require('sinon');
const moment = require('moment');

const { expect } = chai;
const sandbox = sinon.createSandbox();

const config = require('../../config');
const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;
const GreenInvoice = require('../../lib/apis/greeninvoice');

const sendInvoices = require('../../cron/jobs/greenInvoiceJobs');

describe('cron - sendInvoices', () => {
  afterEach(async () => {
    sandbox.restore();
  });

  it('should get accounts', async () => {
    const stub = sandbox.stub(Account, 'find').resolves([]);
    sendInvoices.getAccounts();

    const yesterday = moment.utc().subtract(1, 'day').startOf('day').toDate();
    const today = moment.utc().startOf('day').toDate();
    const query = {
      'subscription.recentIPN': { $in: ['RECURRING', 'CHARGE'] },
      'subscription.transactionDate': { $gte: yesterday, $lte: today },
      'configuration.sendInvoices': true,
    };
    expect(stub).to.have.been.calledWith(sinon.match(query));
  });

  it('should get last document', async () => {
    const acc = AccountFactory.default();
    sandbox.stub(acc, 'getInvoiceNames').returns(['HAPPYCAR Gmbh']);
    const stub = sandbox.stub(GreenInvoice.prototype, 'searchDocuments').resolves();

    const name = acc.getInvoiceNames()[0];
    sendInvoices.getLatestDocument(name);

    expect(stub).to.have.been.calledWith(sinon.match({
      clientName: name,
      pageSize: 1,
    }));
  });

  it('should send document for each account', async () => {
    const acc1 = AccountFactory.default();
    const acc2 = AccountFactory.default();
    acc2.subscription.invoiceEmail = '<EMAIL>';
    const doc1 = { id: '1' };
    // const doc2 = {id: '2'};

    const getAccounts = sandbox.stub(Account, 'find').resolves([acc1, acc2]);
    const searchDocuments = sandbox.stub(GreenInvoice.prototype, 'searchDocuments').resolves([doc1]);
    const sendDocs = sandbox.stub(GreenInvoice.prototype, 'sendDocument').resolves();

    await sendInvoices.sendInvoices();

    expect(getAccounts).to.have.been.calledOnce;
    expect(searchDocuments).to.have.been.calledTwice;
    expect(sendDocs).to.have.been.calledTwice;
  });
});
