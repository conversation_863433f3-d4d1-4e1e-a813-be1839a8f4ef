const chai = require('chai');
const chaiAsPromised = require('chai-as-promised');
const sinon = require('sinon');
const mongoose = require('mongoose');
const task = require('../../cron/jobs/yotpoReviews');

chai.use(chaiAsPromised);

const { expect } = chai;

describe('cron/reviews/yotpo', () => {
  describe('yotpoReviewsTask', () => {
    it('fetches and saves reviews', async () => {
      const stub = sinon.stub(mongoose.Model, 'insertMany').resolves([]);
      await task.yotpoReviewsTask([{ placeId: 'FBtb3QzWyatQY0OYjMcwfZEFvJ4a6CVkIZLgHEf0', accountId: '5a9fdd328e67579d65812a17' }]);
      expect(stub).to.be.calledOnce;
      expect(stub.firstCall.args[0]).to.be.an('array', 'args not array');
      expect(stub.firstCall.args[0]).to.have.lengthOf(15, 'args not expected length');
      expect(stub.firstCall.args[0][0]).to.be.an('Object', 'args 0 expected to be object');
      expect(stub.firstCall.args[0][0]).to.have.keys([
        'yotpoAppId', 'reviewId',
        'productId', 'profilePhotoUrl', 'authorName',
        'rating', 'text', 'time', 'accountId',
      ]);
      stub.restore();
    }).timeout(5000);
  });
});
