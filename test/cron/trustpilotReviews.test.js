const chai = require('chai');
const chaiAsPromised = require('chai-as-promised');
const sinon = require('sinon');
const mongoose = require('mongoose');
const task = require('../../cron/jobs/trustpilotReviews');
const lib = require('../../lib/apis/trustpilot');

chai.use(chaiAsPromised);

const { expect } = chai;

describe('cron/reviews/trustpilot', () => {
  describe('trustpilotReviewsTask', () => {
    it('checks function exists', () => {
      expect(task.trustpilotReviewsTask).to.be.an('function', 'trustpilotReviewsTask not function');
    });

    it('fetches and saves reviews', async () => {
      const stub = sinon.stub(mongoose.Model, 'insertMany');
      await task.trustpilotReviewsTask([{ placeId: 'provesrc.com', accountId: '5a9fdd328e67579d65812a17' }]);
      expect(stub).to.be.calledOnce;
      expect(stub.firstCall.args[0]).to.be.an('array', 'args not array');
      expect(stub.firstCall.args[0]).to.have.lengthOf(10, 'args not expected length');
      expect(stub.firstCall.args[0][0]).to.be.an('Object', 'args 0 expected to be object');
      expect(stub.firstCall.args[0][0]).to.have.keys([
        'authorName', 'domain',
        'profilePhotoUrl', 'rating', 'time',
        'text', 'placeId', 'accountId', 'reviewId', 'businessName',
      ]);
      stub.restore();
    });
  });
});
