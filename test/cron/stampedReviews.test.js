const chai = require('chai');
const chaiAsPromised = require('chai-as-promised');
const sinon = require('sinon');
const mongoose = require('mongoose');
const task = require('../../cron/jobs/stampedReviews');

chai.use(chaiAsPromised);

const { expect } = chai;

describe('cron/reviews/stamped', () => {
  describe('stampedReviewsTask', () => {
    it('should fetch and save reviews with store url and api key', async () => {
      const stub = sinon.stub(mongoose.Model, 'insertMany').resolves([]);
      await task.stampedReviewsTask([{ placeId: 'seguidores.com.br', pageToken: 'pubkey-0hNp68x545x7D87hU31FtpV760P604', accountId: '5a9fdd328e67579d65812a17' }]);
      expect(stub).to.be.calledOnce;
      expect(stub.firstCall.args[0]).to.be.an('array', 'args not array');
      expect(stub.firstCall.args[0]).to.have.lengthOf(15, 'args not expected length');
      expect(stub.firstCall.args[0][0]).to.be.an('Object', 'args 0 expected to be object');
      expect(stub.firstCall.args[0][0]).to.have.keys([
        'storeUrl', 'reviewId',
        'productId', 'profilePhotoUrl', 'authorName',
        'rating', 'text', 'time', 'accountId',
      ]);
      stub.restore();
    });

    it('should fetch and save reviews with shopify store url only', async () => {
      const stub = sinon.stub(mongoose.Model, 'insertMany').resolves([]);
      await task.stampedReviewsTask([{ placeId: 'myblankii.myshopify.com', accountId: '5a9fdd328e67579d65812a17' }]);
      expect(stub).to.be.calledOnce;
      expect(stub.firstCall.args[0]).to.be.an('array', 'args not array');
      expect(stub.firstCall.args[0]).to.have.lengthOf(15, 'args not expected length');
      expect(stub.firstCall.args[0][0]).to.be.an('Object', 'args 0 expected to be object');
      expect(stub.firstCall.args[0][0]).to.have.keys([
        'storeUrl', 'reviewId',
        'productId', 'profilePhotoUrl', 'authorName',
        'rating', 'text', 'time', 'accountId',
      ]);
      stub.restore();
    });
  });
});
