const chai = require('chai');
const chaiAsPromised = require('chai-as-promised');
const sinon = require('sinon');
const mongoose = require('mongoose');
const task = require('../../cron/jobs/reviewsioReviews');

chai.use(chaiAsPromised);

const { expect } = chai;

describe('cron/reviews/reviews.io', () => {
  describe('reviewsioReviewsTask', () => {
    it('fetches and saves reviews', async () => {
      const stub = sinon.stub(mongoose.Model, 'insertMany').resolves([]);
      await task.reviewsioReviewsTask([{ placeId: 'virtalent', accountId: '5a9fdd328e67579d65812a17' }]);
      expect(stub).to.be.calledOnce;
      expect(stub.firstCall.args[0]).to.be.an('array', 'args not array');
      expect(stub.firstCall.args[0]).to.have.lengthOf(10, 'args not expected length');
      expect(stub.firstCall.args[0][0]).to.be.an('Object', 'args 0 expected to be object');
      expect(stub.firstCall.args[0][0]).to.have.keys([
        'storeId', 'rating',
        'authorName', 'reviewId', 'initials',
        'time', 'text', 'storeName', 'accountId',
      ]);
      stub.restore();
    });
  });
});
