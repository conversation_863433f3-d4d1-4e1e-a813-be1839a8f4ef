require('../../server');
const chai = require('chai');
const request = require('superagent');
const config = require('../mocking/tasks/facebookReviews');
const chaiAsPromised = require('chai-as-promised');
const superagentMock = require('superagent-mock')(request, config);
const sinon = require('sinon');
const mongoose = require('mongoose');
const task = require('../../cron/jobs/facebookReviews');
const lib = require('../../lib/apis/facebook');
const utils = require('../../lib/utils/cryptoUtils');
const FacebookReview = require('../../app/reviews/models/FacebookReview');

chai.use(chaiAsPromised);

const { expect } = chai;

describe('cron/tasks/facebookReviews', () => {
  after(() => {
    superagentMock.unset();
  });

  afterEach(async () => {
    sinon.restore();
  });

  describe('reformatResults', () => {
    it('returns an array of results reformatted as expected', async () => {
      const data = [
        { created_time: '2019-06-05T08:11:17+0000', recommendation_type: 'positive', review_text: 'This is test post 1. Please ignore this.' },
        { created_time: '2019-06-02T08:09:17+0000', recommendation_type: 'positive', review_text: 'This is test post 2. Please ignore this.' },
      ];
      const results = await task.reformatResults(data, { placeId: '***************', accountId: '5d08c07265ce8a5a33732804' });
      expect(results).to.be.an('array', 'results not array');
      expect(results).to.have.lengthOf(2, 'results not expected length');
      expect(results[0]).to.be.an('object', 'first item in array not expected object');
      const keys = ['time', 'rating', 'text', 'accountId', 'pageId', 'reviewId', 'authorId', 'authorName', 'businessName'];
      expect(results[0]).to.have.all.keys(keys);
      expect(results[1]).to.have.all.keys(keys);
      expect(results[0].pageId).to.equal('***************', 'pageId not as expected');
      expect(results[1].pageId).to.equal('***************', 'pageId not as expected');
      expect(results[0].accountId).to.equal('5d08c07265ce8a5a33732804', 'accountid not as expected');
      expect(results[1].accountId).to.equal('5d08c07265ce8a5a33732804', 'accountid not as expected');
    });
  });

  describe('facebookReviewsTask', () => {
    it('fetches and saves reviews', async () => {
      const stub = sinon.stub(mongoose.Model, 'insertMany').resolves();
      await task.facebookReviewsTask([{
        accountId: '5d08c07265ce8a5a33732804',
        placeId: '***************',
        pageName: 'Test',
        pageToken: 'N3SMsizs6xKTpRX1Xx5VDfyIzpJmFVjKgMe201EfI8Hc1M87gCzEqQ5HEmrGooQBhUYe1BqzHW2tK5FrNmSNLsQxN1QaYqDZ1n09JRKDCifA0zDaCxQZ2ihaCFW91sTn2MCq2nJzS378giA7l6llUq0sHMxVFjCMlukJv37yIT6dmOtbZrcxCxCy4hFvXwuyQzRrXblWz',
      }]);
      expect(stub).to.be.calledOnce;
      expect(stub.firstCall.args[0]).to.be.an('array', 'args not array');
      expect(stub.firstCall.args[0]).to.have.lengthOf(2, 'args not expected length');
      expect(stub.firstCall.args[0][0]).to.be.an('Object', 'args 0 expected to be object');
      expect(stub.firstCall.args[0][0]).to.include.keys(
        'time', 'rating', 'text', 'accountId', 'pageId', 'identifier',
      );
      stub.restore();
    });

    it('fetches and there are zero results', async () => {
      const stub = sinon.stub(mongoose.Model, 'insertMany');
      await task.facebookReviewsTask([{
        accountId: '5d08c07265ce8a5a33732804',
        placeId: '***************',
        pageName: 'Test',
        pageToken: 'N3SMsizs6xKTpRX1Xx5VDfyIzpJmFVjKgMe201EfI8Hc1M87gCzEqQ5HEmrGooQBhUYe1BqzHW2tK5FrNmSNLsQxN1QaYqDZ1n09JRKDCifA0zDaCxQZ2ihaCFW91sTn2MCq2nJzS378giA7l6llUq0sHMxVFjCMlukJv37yIT6dmOtbZrcxCxCy4hFvXwuyQzRrXblWz',
      }]);
      expect(stub).not.to.be.called;
      stub.restore();
    });

    it('fetches and status is not 200', async () => {
      const stub = sinon.stub(mongoose.Model, 'insertMany');
      await task.facebookReviewsTask([{
        accountId: '5d08c07265ce8a5a33732804',
        placeId: '***************',
        pageName: 'Test',
        pageToken: 'N3SMsizs6xKTpRX1Xx5VDfyIzpJmFVjKgMe201EfI8Hc1M87gCzEqQ5HEmrGooQBhUYe1BqzHW2tK5FrNmSNLsQxN1QaYqDZ1n09JRKDCifA0zDaCxQZ2ihaCFW91sTn2MCq2nJzS378giA7l6llUq0sHMxVFjCMlukJv37yIT6dmOtbZrcxCxCy4hFvXwuyQzRrXblWz',
      }]);
      expect(stub).not.to.be.called;
      stub.restore();
    });

    it('attempts to fetch and an error occurs on getReviews', async () => {
      const stubGetReviews = sinon.stub(lib, 'getRatings').throws(Error);
      const stubInsertMany = sinon.stub(mongoose.Model, 'insertMany');
      await task.facebookReviewsTask([{
        accountId: '5d08c07265ce8a5a33732804',
        placeId: '***************',
        pageToken: 'N3SMsizs6xKTpRX1Xx5VDfyIzpJmFVjKgMe201EfI8Hc1M87gCzEqQ5HEmrGooQBhUYe1BqzHW2tK5FrNmSNLsQxN1QaYqDZ1n09JRKDCifA0zDaCxQZ2ihaCFW91sTn2MCq2nJzS378giA7l6llUq0sHMxVFjCMlukJv37yIT6dmOtbZrcxCxCy4hFvXwuyQzRrXblWz',
      }]);
      expect(stubInsertMany).not.to.be.called;
      stubGetReviews.restore();
      stubInsertMany.restore();
    });
  });

  describe('reformatResults integration test', () => {
    beforeEach(async () => {
      await FacebookReview.remove();
    });

    it('should return null for reviews older than latest in db', async () => {
      const accountId = '5dfeca3a1a086f52d9396e4d';
      const pageId = '***************';
      const notification = { accountId, placeId: pageId };
      const fbOldReview = {
        review_text: 'old review',
        recommendation_type: 'positive',
        created_time: '2019-01-01',
        reviewer: {
          name: 'Neli',
          id: '****************',
        },
      };
      const fbNewReview = {
        review_text: 'new review',
        recommendation_type: 'positive',
        created_time: '2019-12-01',
        reviewer: {
          name: 'Neli Ciucaș2',
          id: '****************',
        },
      };
      // add an old review to db
      await (new FacebookReview({
        pageId,
        accountId,
        text: fbOldReview.review_text,
        rating: 5,
        authorName: fbOldReview.reviewer.name,
        authorId: fbOldReview.reviewer.id,
        time: fbOldReview.created_time,
        reviewId: '1234',
      })).save();

      const reviews = [fbOldReview, fbNewReview];
      const results = await task.reformatResults(reviews, notification);

      expect(results.length).to.be.equal(1);
      expect(results[0].text).to.be.equal(fbNewReview.review_text);
    });
  });
});
