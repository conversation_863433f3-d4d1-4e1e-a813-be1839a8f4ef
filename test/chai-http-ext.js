/* eslint no-param-reassign: ["error", { "props": false }] */

const mockSession = require('mock-session');
const config = require('../config');

// eslint-disable-next-line no-unused-vars
module.exports = (chai, _) => {
  if(!chai.request) return;

  chai.request.Test.prototype.session = function (accountId) {
    const { name, secret } = config.cookie;
    return this.set('cookie', mockSession(name, secret, { accountId }));
  };

  // const oldSet = chai.request.Test.prototype.set;
  // chai.request.Test.prototype.set = function(key, value) {
  //
  // }
};
