module.exports = [{
  pattern: 'https://maps.googleapis.com/maps/api/place/details/json(.*)',
  fixtures(match, params, headers, context) {
    if(match[1] === '') {
      return {
        body: {
          error_message: 'You must use an API key to authenticate each request to Google Maps Platform APIs. For additional information, please refer to http://g.co/dev/maps-no-account',
          html_attributions: [],
          status: 'REQUEST_DENIED',
        },
      };
    }
    if(match[1] === '?key=1234') {
      return {
        body: {
          error_message: 'The provided API key is invalid.',
          html_attributions: [],
          status: 'REQUEST_DENIED',
        },
      };
    }

    if(match[1] === '?key=5678') {
      return {
        body: {
          error_message: 'Missing the placeid or reference parameter.',
          html_attributions: [],
          status: 'INVALID_REQUEST',
        },
      };
    }

    if(match[1] === '?key=5678&placeid=A') {
      return {
        body: {
          status: 'UNKNOWN_ERROR',
        },
      };
    }

    if(match[1] === '?key=5678&placeid=B') {
      return {
        body: {
          status: 'ZERO_RESULTS',
        },
      };
    }

    if(match[1] === '?key=5678&placeid=C') {
      return {
        body: {
          status: 'OVER_QUERY_LIMIT',
        },
      };
    }

    if(match[1] === '?key=5678&placeid=D') {
      return {
        body: {
          status: 'REQUEST_DENIED',
        },
      };
    }

    if(match[1] === '?key=5678&placeid=E') {
      return {
        body: {
          status: 'INVALID_REQUEST',
        },
      };
    }

    if(match[1] === '?key=5678&placeid=F') {
      return {
        body: {
          status: 'NOT_FOUND',
        },
      };
    }

    if(match[1] === '?key=5678&&placeid=G') {
      return {
        body: {
          status: 'OK',
        },
      };
    }

    if(match[1] === '?key=5678&&placeid=H') {
      return {
        body: {
          status: 'OK',
          result: {},
        },
      };
    }

    if(match[1] === '?key=5678&placeid=ABCD') {
      return {
        body: {
          html_attributions: [],
          result: {
            name: 'Airport',
            reviews: [
              {
                author_name: 'Andrew Boyles',
                author_url: 'https://www.google.com/maps/contrib/104383385062561191985/reviews',
                language: 'en',
                profile_photo_url: 'https://lh4.googleusercontent.com/-shrNzrMuizc/AAAAAAAAAAI/AAAAAAAADFs/rdctbr5GV_g/s128-c0x00000000-cc-rp-mo-ba4/photo.jpg',
                rating: 5,
                relative_time_description: '4 weeks ago',
                text: 'Great airport fantastic service and a great building.',
                time: 1553607149,
              },
              {
                author_name: 'Dan Kane',
                author_url: 'https://www.google.com/maps/contrib/103590752051960221561/reviews',
                language: 'en',
                profile_photo_url: 'https://lh3.googleusercontent.com/-GSoTSQG9tbA/AAAAAAAAAAI/AAAAAAAAAAA/ACHi3reniHPOfuLtUjt4oee1eO3Xig9y5g/s128-c0x00000000-cc-rp-mo-ba3/photo.jpg',
                rating: 5,
                relative_time_description: 'a month ago',
                text: 'Lovely looking building',
                time: 1550933193,
              },
              {
                author_name: 'Johnny Groats',
                author_url: 'https://www.google.com/maps/contrib/108598583746241052517/reviews',
                language: 'en',
                profile_photo_url: 'https://lh3.googleusercontent.com/-r2Ffm3iXmI8/AAAAAAAAAAI/AAAAAAAAApw/OOr2J8nMAoY/s128-c0x00000000-cc-rp-mo-ba8/photo.jpg',
                rating: 3,
                relative_time_description: '8 months ago',
                text: 'Not bad for doing all the flying about type stuff',
                time: 1533674132,
              },
              {
                author_name: 'Bob Marshall',
                author_url: 'https://www.google.com/maps/contrib/108599244266153839306/reviews',
                language: 'en',
                profile_photo_url: 'https://lh6.googleusercontent.com/-5xyI3FCY1T4/AAAAAAAAAAI/AAAAAAAAAAA/ACHi3rc7L7Yao2fBVY9HRwQVan5V4_Jcvg/s128-c0x00000000-cc-rp-mo/photo.jpg',
                rating: 1,
                relative_time_description: '7 months ago',
                text: 'I see you have banned me from posting on your Facebook page because my comments exposed the glaring ineptitude of your operations. Fraud and misrepresentation and false advertising.',
                time: 1536076492,
              },
              {
                author_name: 'sohel isat',
                author_url: 'https://www.google.com/maps/contrib/105793988402788371070/reviews',
                language: 'en',
                profile_photo_url: 'https://lh4.googleusercontent.com/-wA13WXuMATQ/AAAAAAAAAAI/AAAAAAAABRg/W1nrJYzbg7s/s128-c0x00000000-cc-rp-mo-ba5/photo.jpg',
                rating: 5,
                relative_time_description: 'a month ago',
                text: 'Hmm',
                time: 1551415522,
              },
            ],
          },
          status: 'OK',
        },
      };
    }
  },
  callback(match, data) {
    return data;
  },
}];
