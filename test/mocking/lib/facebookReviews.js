module.exports = [{
  pattern: 'https://graph.facebook.com/v3.3/(.*)/ratings',
  fixtures(match, params, headers, context) {
    if(match[1] === '740002622392017') {
      return {
        status: 200,
        body: {
          data: [
            {
              created_time: '2019-06-05T08:11:17+0000',
              recommendation_type: 'positive',
              review_text: 'This is test post 1. Please ignore this.',
            },
            {
              created_time: '2019-06-02T08:09:17+0000',
              recommendation_type: 'negative',
              review_text: 'This is test post 2. Please ignore this.',
            },
            {
              created_time: '2019-02-02T08:09:17+0000',
              recommendation_type: 'positive',
              review_text: 'This is test post 3. Please ignore this.',
            },
            {
              created_time: '2019-01-02T08:09:17+0000',
              recommendation_type: 'positive',
              review_text: 'This is test post 4. Please ignore this.',
            },
            {
              created_time: '2019-01-01T03:09:17+0000',
              recommendation_type: 'positive',
              review_text: 'This is test post 5. Please ignore this.',
            },
          ],
          paging: [],
        },
      };
    }

    if(match[1] === '***************') {
      return {
        status: 400,
        body: {
          error: {
            message: 'An error message',
            type: 'AnErrorType',
            code: 200,
            fbtrace_id: 'TR4C3',
          },
        },
      };
    }

    if(match[1] === '***************') {
      return {
        status: 200,
        body: {},
      };
    }
  },
  callback(match, data) {
    return data;
  },
},
{
  pattern: 'https://graph.facebook.com/v3.3/me/accounts(.*)',
  fixtures(match, params, headers, context) {
    if(match[1] === '?access_token=IgDhFVbzkXsoFx0tomtP6hC119Ks6o36xLoFX0wTvkLvfzlJfKROLlRZDohe6DEE9Mtjtl3X1KKW5n3rsUeWUGCKY4pTMzLQ7UsgLmNBkLuV5bAYugF2A9uwotC5aW14YYqCyG17aG1nZFyAMzURSduzsPfoowjHMTdBnqAA9cutGrsPszDq4wMmMCjRagpIFK1mPUXUe') {
      return {
        status: 200,
        body: {
          data: [{
            access_token: 'TokenHere',
            category: 'E-commerce Website',
            category_list: [{
              id: '****************',
              name: 'E-commerce Website',
            }],
            name: 'A Website',
            id: '***************',
            tasks: [
              'ANALYZE',
              'ADVERTISE',
              'MODERATE',
              'CREATE_CONTENT',
              'MANAGE',
            ],
          }],
          paging: {
            cursors: {
              before: 'NDU0Njg3NTA4NjI4NzYy',
              after: 'NDU0Njg3NTA4NjI4NzYy',
            },
          },
        },
      };
    }

    if(match[1] === '?access_token=cTr4OWJqlDbC7dYMuCns0hiPfZB3ayu24UNgeclYMNoDiJSMyDfrybOcbxI1iPYrwhVS2qnSgdH65EtECoFc75d7d2Q5O5acVQr10TRaHYqZQD5s5mW7HSLU9Wn4LFfMca2TvYM3tzhf1HwunJmoY5BBzRsVm1AgthkfJtclIKEwIb5xEUvkOg0GfqtT2epnmoW5GEXhP') {
      return {
        status: 400,
        body: {
          data: [],
          paging: [],
        },
      };
    }

    if(match[1] === '?access_token=LTE7Ojiz9k3kfKjnQ7RodtVgQANtDu3HXfY8wWQqYAKrvO7i3XCKRBM8y1l7AcyXv3VW52yTgr9y0qdh8KV6xSCgaBJzEGVbFyHYrYpeV1TO0TKooVHcz58ImKa5KgCdU6bZdoZea95bGtbSNYfMm0XYBQncDf47QJFYREQ4yOtrYdsb86ndKSWrm7oE3cpeCVKQnVf0R') {
      return {
        status: 200,
        body: {
          paging: [],
        },
      };
    }
  },
  callback(match, data) {
    return data;
  },
},
{
  pattern: 'https://graph.facebook.com/v3.3/debug_token(.*)',
  fixtures(match, params, headers, context) {
    if(match[1] === '?access_token=uAb3MXKioQdquB2eKyVmz5F0kT8G4IZfVUIB5gSi9vImP10rQnLOOg3jiXCNg6yxLEOn8qFF5H5bjHxiKOUzcrTyVfHMmJ3WZDI0aGx9bNNwuKmHqlir4KArRLMQjp3lYUDKpAkq91dUKHOxSs3kCTYcOoLusgwPdHhfvF4Ly2ZNIuEMu5Yncg8VnnynXgxjmKQlhnsrg&input_token=EgCfit02uTr37D4Qpjbd5YDTJlh0pBx64FMSUWdeQwxz5trobQuuqaxYeLf43stKfo8gaB0p0QZOjFYvtrnYctX1oONNy5vngLJ7xEnhqfBetwRU7wf9LsOmvMFgT7if4IxIdQ7X0zvMCXOJbKfsHT4wgKGshtUUPfm4o0myoUgWFM1rr82avMEroD2F37ijOhMzxBL8b') {
      return {
        status: 200,
        body: {
          data: {
            app_id: '1234',
            type: 'USER',
            application: 'DemoTest',
            data_access_expires_at: 1567791388,
            expires_at: 1560020400,
            is_valid: true,
            scopes: [
              'manage_pages',
              'pages_show_list',
              'publish_pages',
              'public_profile',
            ],
            user_id: 'ABCD',
          },
        },
      };
    }
    if(match[1] === '?access_token=LTE7Ojiz9k3kfKjnQ7RodtVgQANtDu3HXfY8wWQqYAKrvO7i3XCKRBM8y1l7AcyXv3VW52yTgr9y0qdh8KV6xSCgaBJzEGVbFyHYrYpeV1TO0TKooVHcz58ImKa5KgCdU6bZdoZea95bGtbSNYfMm0XYBQncDf47QJFYREQ4yOtrYdsb86ndKSWrm7oE3cpeCVKQnVf0R&input_token=sdaQZqklie7yifecMxhTFMVMtdLQkGUK9NnYsuy16GyJv4oDkpD64BvEwngVuIaD4Lp200yRrJIET0PWbi5OmAkpUaQyTknWJm3D8NoB0eYq1q8fLRVDHrFUF9WWwvUjJvZ3K3A2dBphOVOtVVirA2Ef5SjLrJxk9FNQlZXR0g86g8fUDqGKlUnpuOJUn79l7WEMJjp8a') {
      return {
        status: 400,
        body: {
          data: {},
        },
      };
    }
    if(match[1] === '?access_token=WZPbWgAYYo060q0dB20FhPdq54DnSgVn9dXOmDl30RVuP0HxzqmjH6TctnL8ypoRrlHMgKlJu5IJviAps4x7SmTOjh4Fb9M3jC9HG8SV6nh5l50XEYnewjS9x9b8pYFW2uo3WrglrBkgcDLW43dfC6scgAJoeZYS8qkUeO31lMC4BZg6ZslS9Tp3bcCgj5dqE7XpLsRZq&input_token=frNzDHP3dGBsZxenKfimoTEtIDNivBX6AHI24MfALPWkOFllUBAC7nhRETCvGvAmIYqwP1AOH9rrpYtlNnu2SjQ2XaKilAYPdWghIJFc7iMZX24bKBIYhx4IBQl6Kr0G1nRzNX0gbpHht0uu6VcxtvSaWhK0fcIzzqeaBtI1XBfFbRhgAFvpqaIwkuHsZxk0ezlCWSwfMF') {
      return {
        status: 200,
        body: {},
      };
    }
  },
  callback(match, data) {
    return data;
  },
},
{
  pattern: 'https://graph.facebook.com/v3.3/oauth/access_token(.*)',
  fixtures(match, params, headers, context) {
    if(match[1] === '?grant_type=fb_exchange_token&client_id=183740247262197&client_secret=501614648436422&fb_exchange_token=zEakht3c7c2lifPcjOTAoxB10RFRCteR4Vcz9INWBLezOZYReMww9cRE3mBTHSdzqWnzqRUQzOOXYgQfxBONkxziL6bSSKvnIYMBQnqs7vR4HC3spfQOzcVUCbreVgOlB0uYbEiPxPxwEtxuoJ1YnS7VEda0Kr1Hp0jjh1cCz2ZhhRDUkLjgULPBTp0bIFzRqTBJfUSmY') {
      return {
        status: 200,
        body: {
          access_token: 'GxRagVPLUWldHcuKadnsuJKFywfTXd238IXNgHiQA2KxaY9BV1FRcV4Fkl3NG4ZjUsCMqOTNXJ6ch9YfEPxsKrnPrNQiSASsjTjfcmasSzvtvzUOf4cgRTRvlUoVkyzUZaAhkxk1MBBqsiJ1tsiPpWddjuKTmNizECAa4oq1BwmJTWoG8U3N0Xp8Mka5LYS9MVSRJs8j7',
          token_type: 'bearer',
          expires_in: 51834488,
        },
      };
    }

    if(match[1] === '?grant_type=fb_exchange_token&client_id=183740247262197&client_secret=501614648436422&&fb_exchange_token=WE55N1gHAUmf9PPFFEf1cEDfLL1vlZFaYugZKx9oXPmG9JK3TeGwVwgaGGftE41fWKg3K7oBRPBcvjyxKCMEVJePBYojQHpXb9gntWdkHujzR0TuHrL3iUDC61zVEr3iLJZfCpcXVgnYBHk45FcV3dQHUwDEA8ZUIpbf3SBkhvYIxtI4GgKMHhvvXKHDgFVjbd2SCbTig') {
      return {
        status: 400,
        body: {},
      };
    }

    if(match[1] === '?grant_type=fb_exchange_token&client_id=183740247262197&client_secret=501614648436422&fb_exchange_token=ekvfsv9caItJedSu3icNWdwExPzxwqCxLIUG9NKBlNK7yU4bB82V9HcD5T308oUK7dYdKtyUpVTYfV9AXM8OH97iAtTUVlmvBLJNAzfEese5yFpNpEPvmN13c6gYyFb5zfbGgy8g3ybk7Njzx1lY9js0ldGFSEevQ9qq265Q9tpJR0bmgYzvzfZzVC5E0D8uZ3s0wWtgz') {
      return {
        status: 200,
        body: {},
      };
    }
  },
  callback(match, data) {
    return data;
  },
},
{
  pattern: 'https://graph.facebook.com/v3.3/(.*)',
  fixtures(match, params, headers, context) {
    if(match[1] === '913640925564999?access_token=ekvfsv9caItJedSu3icNWdwExPzxwqCxLIUG9NKBlNK7yU4bB82V9HcD5T308oUK7dYdKtyUpVTYfV9AXM8OH97iAtTUVlmvBLJNAzfEese5yFpNpEPvmN13c6gYyFb5zfbGgy8g3ybk7Njzx1lY9js0ldGFSEevQ9qq265Q9tpJR0bmgYzvzfZzVC5E0D8uZ3s0wWtgz&fields=access_token') {
      return {
        status: 200,
        body: {
          access_token: 'FJeDgYe8D0zMi6NR05ASCLEKmbq1eh7L5P0gTqPyBqtoQ3EnkkvZxsvselFvmhrqke4Qu4iOStGnOI0TvKKSleP9zPfngwkyBjuAGOALZppVS7dMV9mLpAnuDdthEe2gqrg1fbRASnICwIB3xY7HLywpKTi3Glg9DmWYEcxVh3cpmZvO77ggQfnPHo69fyxRNPJ03aSCk',
          id: '12345',
        },
      };
    }
    if(match[1] === '544993869929785?access_token=Jjp6dDuAbm2HcaaMb4RvaUvEhYlw4kkcOlRYpf7gYIwV2D95kCrEJnGPTOc4n3G6vjZnT0yfFT0CXaPZPrVEixDWilo2akZFxPOacorcNeyn0tUJf8p65BjD5L6HGoG7dspvdwDoap3pqmFIXKsJMTFzdfVSe5rvPf8gTvvhN2ijwoTo9np9jt7WmYOFlWYw3KmQBVjxa&fields=access_token') {
      return {
        status: 400,
        body: {},
      };
    }
    if(match[1] === '663231235548314?access_token=U3s0j4JfGrsanIRSlvheWT4lduqM3lZSKTPqmgOZBPDZsaBOtxvDbTheELMvjagEfTujCJ7Tqp0RY6RRaOQP2KxZA6FeVQZfhlCrpY34mEPQU5dbHwxqJf5oviEJ8sTDomdBPNk2UroMXx7GNq9d2FV13LZbRK8dXpyD3EWRzeZLIyeRgk4ovBfcWjlcF234VBRp18R6W&fields=access_token') {
      return {
        status: 200,
        body: {},
      };
    }
  },
  callback(match, data) {
    return data;
  },
}];
