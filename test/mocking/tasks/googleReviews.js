module.exports = [{
  pattern: 'https://maps.googleapis.com/maps/api/place/details/json(.*)',
  fixtures(match, params, headers, context) {
    if(match[1] === '?placeid=A&fields=name%2Creview&key=B') {
      return {
        body: {
          html_attributions: [],
          result: {
            name: 'Airport',
            reviews: [
              {
                author_name: '<PERSON>',
                author_url: 'https://www.google.com/maps/contrib/104383385062561191985/reviews',
                language: 'en',
                profile_photo_url: 'https://lh4.googleusercontent.com/-shrNzrMuizc/AAAAAAAAAAI/AAAAAAAADFs/rdctbr5GV_g/s128-c0x00000000-cc-rp-mo-ba4/photo.jpg',
                rating: 5,
                relative_time_description: '4 weeks ago',
                text: 'Great airport fantastic service and a great building.',
                time: 1553607149,
              },
              {
                author_name: '<PERSON>',
                author_url: 'https://www.google.com/maps/contrib/103590752051960221561/reviews',
                language: 'en',
                profile_photo_url: 'https://lh3.googleusercontent.com/-GSoTSQG9tbA/AAAAAAAAAAI/AAAAAAAAAAA/ACHi3reniHPOfuLtUjt4oee1eO3Xig9y5g/s128-c0x00000000-cc-rp-mo-ba3/photo.jpg',
                rating: 5,
                relative_time_description: 'a month ago',
                text: 'Lovely looking building',
                time: 1550933193,
              },
              {
                author_name: 'Johnny Groats',
                author_url: 'https://www.google.com/maps/contrib/108598583746241052517/reviews',
                language: 'en',
                profile_photo_url: 'https://lh3.googleusercontent.com/-r2Ffm3iXmI8/AAAAAAAAAAI/AAAAAAAAApw/OOr2J8nMAoY/s128-c0x00000000-cc-rp-mo-ba8/photo.jpg',
                rating: 3,
                relative_time_description: '8 months ago',
                text: 'Not bad for doing all the flying about type stuff',
                time: 1533674132,
              },
              {
                author_name: 'Bob Marshall',
                author_url: 'https://www.google.com/maps/contrib/108599244266153839306/reviews',
                language: 'en',
                profile_photo_url: 'https://lh6.googleusercontent.com/-5xyI3FCY1T4/AAAAAAAAAAI/AAAAAAAAAAA/ACHi3rc7L7Yao2fBVY9HRwQVan5V4_Jcvg/s128-c0x00000000-cc-rp-mo/photo.jpg',
                rating: 1,
                relative_time_description: '7 months ago',
                text: 'I see you have banned me from posting on your Facebook page because my comments exposed the glaring ineptitude of your operations. Fraud and misrepresentation and false advertising.',
                time: 1536076492,
              },
              {
                author_name: 'sohel isat',
                author_url: 'https://www.google.com/maps/contrib/105793988402788371070/reviews',
                language: 'en',
                profile_photo_url: 'https://lh4.googleusercontent.com/-wA13WXuMATQ/AAAAAAAAAAI/AAAAAAAABRg/W1nrJYzbg7s/s128-c0x00000000-cc-rp-mo-ba5/photo.jpg',
                rating: 5,
                relative_time_description: 'a month ago',
                text: 'Hmm',
                time: 1551415522,
              },
            ],
          },
          status: 'OK',
        },
      };
    }

    if(match[1] === '?placeid=B&key=B') {
      return {
        body: {
          status: 'ZERO_RESULTS',
        },
      };
    }

    if(match[1] === '?placeid=C&key=B') {
      return {
        body: {
          status: 'NOT OK',
        },
      };
    }
  },
  callback(match, data) {
    return data;
  },
}];
