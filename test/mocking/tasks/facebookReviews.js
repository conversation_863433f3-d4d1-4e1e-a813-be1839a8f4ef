module.exports = [{
  pattern: 'https://graph.facebook.com/v3.3/(.*)/ratings',
  fixtures(match, params, headers, context) {
    if(match[1] === '867342492039522') {
      return {
        status: 200,
        body: {
          data: [
            {
              created_time: '2019-06-05T08:11:17+0000',
              recommendation_type: 'positive',
              review_text: 'This is test post 1. Please ignore this.',
              reviewer: {
                id: '2543327322378723',
              },
            },
            {
              created_time: '2019-06-02T08:09:17+0000',
              recommendation_type: 'positive',
              review_text: 'This is test post 2. Please ignore this.',
            },
            {
              created_time: '2019-06-02T08:09:17+0000',
              recommendation_type: 'negative',
              review_text: 'This is test post 3. Please ignore this.',
            },
          ],
        },
      };
    }
    if(match[1] === '501614648436422') {
      return {
        status: 200,
        body: {
          data: [],
        },
      };
    }
    if(match[1] === '147344673226393') {
      return {
        status: 400,
        body: {
          data: [],
        },
      };
    }
  },
  callback(match, data) {
    return data;
  },
}];
