const chai = require('chai');

const { expect } = chai;
const sinon = require('sinon');
const httpUtils = require('../httpUtils');
const constants = require('../constants');
const serverP = require('../../server');
const config = require('../../config');

const utils = require('../testUtils');
const dateUtils = require('../../lib/utils/dateUtils');
const urlUtils = require('../../lib/utils/urlUtils');
const crypto = require('../../lib/utils/cryptoUtils');

const FormEvent = require('../../app/events/models/FormEvent');
const CleanFormEvent = require('../../app/events/models/CleanFormEvent');
const FormStreamEvent = require('../../app/events/models/FormStreamEvent');
const CleanFormStreamEvent = require('../../app/events/models/CleanFormStreamEvent');
const AnalyticsEvent = require('../../app/notifications/models/AnalyticsEvent');
const User = require('../../app/users/User');

const ENDPOINT = '/events/trackForm';

describe('/events/trackFrom', () => {
  let server;
  before(async () => {
    server = await serverP;
  });

  const sandbox = sinon.createSandbox();
  beforeEach(async () => {
    sandbox.restore();
    await Promise.all([
      FormEvent.remove({}),
      CleanFormEvent.remove(),
      FormStreamEvent.remove(),
      CleanFormStreamEvent.remove(),
      AnalyticsEvent.remove(),
      User.remove(),
    ]);
  });

  it('should create a FormEvent per call and include uid', () => {
    const url = 'https://provesrc.com/form/';
    const url2 = 'https://provesrc.com/form';
    const email = '<EMAIL>';
    return httpUtils.apiRequest(server, ENDPOINT, httpUtils.METHODS.POST).send({ url, email }).then((res) => {
      expect(res).to.have.status(200);
      expect(res.body).to.have.property('message', 'success');

      const { accountId } = constants;
      return FormEvent.findOne({ accountId, url: url2, day: dateUtils.todayNormalized12am() });
    }).then((result) => {
      expect(result).to.exist;
    });
  });

  xit('should track stream, name and fetch picasa and gravatar profiles', async () => {
    sandbox.stub(config, 'getSocialProfiles').value(true);
    const { accountId } = constants;
    const url = 'https://provesrc.com/contact-us';
    const email = '<EMAIL>';
    const firstName = 'Natan';
    const lastName = 'Abramov';
    const timestamp = Date.now() - 5000;
    const opts = {
      url, email, timestamp, ip: '***************', firstName, lastName,
    };
    await httpUtils.trackFormOpts(server, opts);

    await utils.sleep(2000);

    const event = await FormStreamEvent.findOne({ accountId, email });
    expect(event.date.getTime()).to.equal(timestamp);
    expect(event.ip).to.exist;
    expect(event.location.country).to.exist;
    expect(event.picasa).to.exist;
    expect(event.picasa.author).to.exist;
    expect(event.picasa.author[0].name.$t).to.be.equal('Natan Abramov');
    expect(event.gravatar.hash).to.be.equal(crypto.md5(email));
    expect(event.firstName).to.be.equal(firstName);
    expect(event.lastName).to.be.equal(lastName);
  });

  it('should not track form twice (based on submissionId)', async () => {
    const { accountId } = constants;
    const submissionId = crypto.randomString(8);
    const url = 'https://provesrc.com';
    const email = '<EMAIL>';
    await httpUtils.trackFormData(server, {}, { url, email, submissionId });
    await httpUtils.trackFormData(server, {}, { url, email, submissionId });

    const events = await FormStreamEvent.find({ accountId });
    expect(events[0].submissionId).to.be.equal(submissionId);
    expect(events.length).to.be.equal(1);
  });

  xit('should add user uid to FormStreamEvent', async () => {
    const agent = httpUtils.Agent(server);
    const res = await agent.notificationsGet(server, { url: 'https://provesrc.com' });
    expect(res).to.have.cookie('psuid');
    const cookie = agent.agent.jar.getCookie('psuid', { path: '/' });
    const psuid = cookie.value.substring(4, cookie.value.indexOf('.'));

    const email = '<EMAIL>';
    const formUrl = 'https://provesrc.com/contact';
    await agent.trackForm(server, formUrl, email);
    await utils.sleep(200);
    const event = await FormStreamEvent.findOne({ accountId: constants.accountId });
    expect(event.user_uid).to.be.equal(psuid);

    const user = await User.findOne({ uid: psuid });
    expect(user.events[0].email).to.be.equal(email);
    expect(user.events[0].source).to.be.equal(formUrl);
  });

  it('should track conversion analytics', async () => {
    const email = '<EMAIL>';
    const url = 'https://provesrc.com/signup';
    const viewed = true;
    const hovered = true;
    const clicked = true;
    const res = await httpUtils.trackFormData(server, null, {
      email, url, viewed, clicked, hovered,
    });
    expect(res).to.have.status(200);

    const event = await AnalyticsEvent.findOne({ accountId: constants.accountId });
    expect(event.total.conversions).to.be.equal(1);
    expect(event.total.viewConversions).to.be.equal(1);
    expect(event.total.hoverConversions).to.be.equal(1);
    expect(event.total.clickConversions).to.be.equal(1);
  });

  it('should track view-through conversion only', async () => {
    const email = '<EMAIL>';
    const url = 'https://provesrc.com/signup';
    const viewed = true;
    const hovered = false;
    const clicked = false;
    const res = await httpUtils.trackFormData(server, null, {
      email, url, viewed, clicked, hovered,
    });
    expect(res).to.have.status(200);

    const event = await AnalyticsEvent.findOne({ accountId: constants.accountId });
    expect(event.total.conversions).to.be.equal(1);
    expect(event.total.viewConversions).to.be.equal(1);
    expect(event.total.hoverConversions).to.not.be.equal(1);
    expect(event.total.clickConversions).to.not.be.equal(1);
  });

  it('should track conversions only', async () => {
    const email = '<EMAIL>';
    const url = 'https://provesrc.com/signup';
    const viewed = false;
    const hovered = false;
    const clicked = false;
    const res = await httpUtils.trackFormData(server, null, {
      email, url, viewed, clicked, hovered,
    });
    expect(res).to.have.status(200);

    const event = await AnalyticsEvent.findOne({ accountId: constants.accountId });
    expect(event.total.conversions).to.be.equal(1);
    expect(event.total.viewConversions).to.not.be.equal(1);
    expect(event.total.hoverConversions).to.not.be.equal(1);
    expect(event.total.clickConversions).to.not.be.equal(1);
  });

  it('should store a clean version of the count and stream event', async () => {
    const url = 'https://provesrc.com/form?utm_source=facebook';
    const email = '<EMAIL>';
    const res = await httpUtils.trackFormData(server, null, { url, email });
    expect(res).to.have.status(200);

    await utils.sleep(50);

    const [countEvent, streamEvent] = await Promise.all([
      CleanFormEvent.findOne(),
      CleanFormStreamEvent.findOne(),
    ]);
    const cleanUrl = urlUtils.clean(url);
    expect(countEvent.url).to.be.equal(cleanUrl);
    expect(streamEvent.url).to.be.equal(cleanUrl);
    expect(streamEvent.email).to.be.equal(email);
    expect(streamEvent.shortUrl).to.be.equal(cleanUrl.substr(0, 900));
  });
});
