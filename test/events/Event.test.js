const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const utils = require('../testUtils');

const constants = require('../constants');
const WebhookEvent = require('../../app/events/models/WebhookEvent');
const Event = require('../../app/events/models/Event');

describe('Event - Model', () => {
  afterEach(() => {
    sandbox.restore();
  });

  it('should return correct date key for subclass WebhookEvent', async () => {
    expect(Event.getDateKey()).to.be.equal('date');
    expect(WebhookEvent.getDateKey()).to.be.equal('day');
  });
});
