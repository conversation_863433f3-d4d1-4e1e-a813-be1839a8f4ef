const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();

const constants = require('../constants');
const EventFactory = require('../factories/EventFactory');

describe('Model - Magento2Event', () => {
  afterEach(() => {
    sandbox.restore();
  });

  it('should return product', async () => {
    const event = EventFactory.Magento2Event();
    expect(event.getProduct()).to.exist;
  });
});
