const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const constants = require('../constants');
const serverP = require('../../server');
const WebsiteEvent = require('../../app/events/models/WebsiteEvent');
const CleanWebsiteEvent = require('../../app/events/models/CleanWebsiteEvent');
const testUtils = require('../testUtils');
const urlModule = require('url');
const urlUtils = require('../../lib/utils/urlUtils');
const chance = require('chance').Chance();

const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;
const EVENT_CONSTANTS = require('../../app/events/constants');

describe('/events/track', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    sandbox.stub(Account, 'findOne').resolves(AccountFactory.default());
    await Promise.all([
      WebsiteEvent.remove(),
      CleanWebsiteEvent.remove(),
    ]);
  });

  afterEach(async () => {
    await Promise.all([
      WebsiteEvent.remove(),
      CleanWebsiteEvent.remove(),
    ]);
    sandbox.restore();
  });

  it('should encode URLs', async () => {
    const url = 'https://קלאבמד.co.il/קורסים';
    await httpUtils.eventTrack(server, { url, unique: true });

    const event = await WebsiteEvent.findOne();
    const encoded = encodeURI(urlModule.format(urlModule.parse(url))).toLowerCase();
    expect(event.url).to.be.equal(encoded);
  });

  it('should store an event with a "clean URL"', async () => {
    const url = 'https://www.provesrc.com/about?utm_source=facebook';
    const clean = urlUtils.clean(url);
    await httpUtils.eventTrack(server, { url, unique: true });
    const event = await CleanWebsiteEvent.findOne();
    expect(event.url).to.be.equal(clean);
  });

  it('should store short url version', async () => {
    const url = chance.url({
      domain: chance.domain(),
      path: chance.string({ length: 1500, pool: 'abcdefghijklmnopqrstuvwxyz1234567890' }),
    });
    const shortClean = urlUtils.clean(url).substr(0, EVENT_CONSTANTS.maxUrlLength);
    const shortNormalized = urlUtils.normalize(url).substr(0, EVENT_CONSTANTS.maxUrlLength);
    await httpUtils.eventTrack(server, { url, unique: true });

    const [cEvent, event] = await Promise.all([
      CleanWebsiteEvent.findOne(),
      WebsiteEvent.findOne(),
    ]);
    expect(cEvent.shortUrl).to.be.equal(shortClean);
    expect(event.shortUrl).to.be.equal(shortNormalized);
  });
});
