const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();

const constants = require('../constants');
const EventFactory = require('../factories/EventFactory');

describe('Model - WooEvent', () => {
  afterEach(() => {
    sandbox.restore();
  });

  it('should return a product', async () => {
    const event = EventFactory.WooEvent();
    expect(event.getProduct()).to.exist;
  });
});
