const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();

const constants = require('../constants');
const EventFactory = require('../factories/EventFactory');

describe('Model - WebhookStreamEvent', () => {
  afterEach(() => {
    sandbox.restore();
  });

  it('should return product', async () => {
    const products = [{ id: 1, name: 'product' }];
    const event = EventFactory.WebhookStreamEvent({ products });
    const product = event.getProduct();
    expect(product).to.exist;
    expect(product.name).to.be.equal(products[0].name);
  });

  it('should return product 2', async () => {
    const data = {
      _id: '5cb8621e5a9ee01ef21b3ebb',
      location: {
        city: 'Heywood',
        country: 'United Kingdom',
        countryCode: 'GB',
        state: 'England',
        stateCode: 'ENG',
      },
      __t: 'WebhookStreamEvent',
      ip: '*************',
      products: [
        {
          id: '1830',
          name: '2018 KS2 SATs Maths Paper 1...',
          link: 'https://www.sats-papers.co.uk/ks2-sats-papers/?proveSource=true',
          image: 'https://www.sats-papers.co.uk/assets/images/thumbnails/ks2-mathematics-2018-paper-1.jpg',
        },
        {
          id: '1829',
          name: '2018 KS2 SATs Maths Marking Scheme (Answ...',
          link: 'https://www.sats-papers.co.uk/ks2-sats-papers/?proveSource=true',
          image: 'https://www.sats-papers.co.uk/assets/images/thumbnails/ks2-mathematics-2018-marking-scheme.jpg',
        },
      ],
      email: '<EMAIL>',
      firstName: null,
      lastName: null,
      date: '2019-04-18T11:39:27.000Z',
      accountId: '5c86469eeb26286eff676ee1',
      webhookId: '844af46a39ef53a73c504ec00ab56989',
      gravatar: null,
      __v: 0,
    };

    const event = new EventFactory.models.WebhookStreamEvent(data);
    const product = event.getProduct();
    expect(product.name).to.be.equal(data.products[0].name);
  });
});
