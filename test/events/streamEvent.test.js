const _ = require('lodash');
const chai = require('chai');

const { expect } = chai;
const StreamEvent = require('../../app/events/models/StreamEvent');
const EventFactory = require('../factories/EventFactory');

describe('Model - StreamEvent', () => {
  it('should return name from properties', async () => {
    const firstName = 'Natan';
    const lastName = 'Abramov';
    const picasa = {
      gphoto$nickname: { $t: '<PERSON>' },
      author: [{
        name: { $t: '<PERSON>' },
      }],
    };
    const gravatar = {
      name: { givenName: '<PERSON>' },
    };

    const event = new StreamEvent({
      date: new Date(),
      email: '<EMAIL>',
      firstName,
      lastName,
      picasa,
      gravatar,
    });

    expect(event.getName().name).to.be.equal(firstName);
  });

  it('should return name from picasa', async () => {
    const firstName = '<PERSON>';
    const lastName = 'Smith';
    const fullName = [firstName, lastName].join(' ');
    const picasa = {
      gphoto$nickname: { $t: fullName },
      author: [{
        name: { $t: fullName },
      }],
    };

    const event = new StreamEvent({
      date: new Date(),
      email: '<EMAIL>',
      picasa,
    });

    const data = event.getName();
    expect(data.name).to.be.equal(firstName);
    expect(data.initials).to.be.equal(firstName.charAt(0) + lastName.charAt(0));
  });

  it('should return name form gravatar', () => {
    const firstName = 'John';
    const gravatar = { name: { givenName: firstName } };
    const event = new StreamEvent({
      date: new Date(),
      email: '<EMAIL>',
      picasa: {},
      gravatar,
    });
    expect(event.getName().name).to.be.equal(firstName);
  });

  it('should return empty response in products is empty array  ', async () => {
    const products = [{}];
    const event = EventFactory.ShopifyEvent({ products });
    expect(event.getProduct()).to.not.exist;
  });

  it('should return the most expensive product without filtering', async () => {
    const products = [
      {
        id: 1, quantity: 1, price: 10, name: 'p1', link: 'p1.html', image: 'p1.jpg',
      },
      {
        id: 2, quantity: 1, price: 3, name: 'p2', link: 'p2.html', image: 'p2.jpg',
      },
      {
        id: 3, quantity: 1, price: 300, name: 'p3', link: 'p3.html', image: 'p3.jpg',
      },
      {
        id: 4, quantity: 1, price: 100, name: 'p4', link: 'p4.html', image: 'p4.jpg',
      },
    ];
    const expProd = _.pick(_.maxBy(products, 'price'), ['name', 'image', 'link']);
    const event = EventFactory.ShopifyEvent({ products });
    expect(event.getProduct()).to.be.eql(expProd);
  });

  it('should return the most expensive product after filtering', async () => {
    const products = [
      {
        id: 1, quantity: 1, price: 10, name: 'p1', link: 'p1.html', image: 'p1.jpg',
      },
      {
        id: 2, quantity: 1, price: 3, name: 'p2', link: 'p2.html', image: 'p2.jpg',
      },
      {
        id: 3, quantity: 1, price: 300, name: 'p3', link: 'p3.html', image: 'p3.jpg',
      },
      {
        id: 4, quantity: 1, price: 100, name: 'p4', link: 'p4.html', image: 'p4.jpg',
      },
    ];
    const filter1 = {
      include: true,
      values: ['p3', 'p4'],
    };
    const filter2 = {
      include: false,
      values: ['p1', 'p3'],
    };
    const expProd1 = _.pick(products[2], ['name', 'image', 'link']);
    const expProd2 = _.pick(products[3], ['name', 'image', 'link']);
    const event = EventFactory.ShopifyEvent({ products });
    expect(event.getProduct(filter1)).to.be.eql(expProd1);
    expect(event.getProduct(filter2)).to.be.eql(expProd2);
  });

  describe('getLocation', () => {
    it('should not return country if same as city', async () => {
      const city = 'Singapore';
      const event = new StreamEvent({
        location: {
          city,
          country: city,
          countryCode: 'SG',
        },
      });
      const loc = event.getLocation();
      expect(loc).to.not.have.any.keys('country');
      expect(loc.city).to.be.equal(city);
    });

    it('should return state if US', async () => {
      const event = new StreamEvent({
        location: {
          city: 'New York',
          country: 'United States',
          countryCode: 'US',
          stateCode: 'NY',
          state: 'New-York',
        },
      });
      const loc = event.getLocation();
      expect(loc).to.have.include.keys('state', 'stateCode');
    });

    it('should city, country, countryCode if not in US', async () => {
      const event = new StreamEvent({
        location: {
          city: 'Beersheba',
          country: 'Israel',
          countryCode: 'IL',
          stateCode: 'HD',
          state: 'HaDarom',
        },
      });
      const loc = event.getLocation();
      expect(loc).to.not.include.keys('state', 'stateCode');
    });
  });

  describe('getMapIcon', () => {
    it('should return hashed map icon url (lowercase normalize and ignore state)', async () => {
      const event = new StreamEvent({
        location: {
          country: 'PoLand',
          state: 'some fake state',
          city: 'cynkoW',
        },
      });
      const host = 'cdn-provesrc.nyc3.cdn.digitaloceanspaces.com';
      const hash = '01b270ff1426dc404eab7622914ae927';
      expect(event.getMapIcon()).to.be.equal(`https://${host}/maps/${hash}-poland,cynkow.png`);
    });

    it('should include state if United States', async () => {
      const event = new StreamEvent({
        location: {
          country: 'United States',
          state: 'New York',
          city: 'New York',
        },
      });
      const host = 'cdn-provesrc.nyc3.cdn.digitaloceanspaces.com';
      const hash = 'f3869c640efc799824059c7a8ab7d3c3';
      expect(event.getMapIcon()).to.be.equal(`https://${host}/maps/${hash}-united%20states,new%20york,new%20york.png`);
    });
  });
});
