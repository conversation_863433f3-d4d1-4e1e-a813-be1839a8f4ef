const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const httpUtils = require('../httpUtils');
const testUtils = require('../testUtils');

const constants = require('../constants');

const NFactory = require('../factories/NotificationFactory');
const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;

const Feed = require('../../app/account/models/Feed');
const StreamEvent = require('../../app/events/models/StreamEvent');
const WebhookStreamEvent = require('../../app/events/models/WebhookStreamEvent');
const CleanFormStreamEvent = require('../../app/events/models/CleanFormStreamEvent');
const WPEvent = require('../../app/events/models/WPEvent');
const WooEvent = require('../../app/events/models/WooEvent').model;
const Magento2Event = require('../../app/events/models/Magento2Event');
const ShopifyEvent = require('../../app/events/models/ShopifyEvent');

describe('DELETE /events', () => {
  let server;
  before(async () => {
    server = await require('../../server');
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    await Promise.all([
      Feed.remove(),
      StreamEvent.remove(),
      WebhookStreamEvent.remove(),
      CleanFormStreamEvent.remove(),
      WPEvent.remove(),
      WooEvent.remove(),
      Magento2Event.remove(),
      ShopifyEvent.remove(),
    ]);
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should remove leads from collections: feed, streamEvents, cleanFormStreamEvents', async () => {
    const email = '<EMAIL>';
    const url = 'https://provesrc.com/test';
    await httpUtils.trackForm(server, url, email);

    const res = await httpUtils.consoleRequest(server, '/events/delete', httpUtils.DELETE).send({ email });
    expect(res).to.have.status(200);

    const feed = await Feed.findOne();
    expect(feed).to.not.exist;

    const streamEvent = await StreamEvent.findOne();
    expect(streamEvent).to.not.exist;

    const cleanEvent = await CleanFormStreamEvent.findOne();
    expect(cleanEvent).to.not.exist;
  });

  it('should remove webhook lead', async () => {
    const notif = NFactory.Stream({ autoTrack: false });
    await notif.save();
    const email = '<EMAIL>';
    const { webhookId } = notif;

    let res = await httpUtils.trackWebhookData(server, webhookId, { email });
    expect(res).to.have.status(200);

    res = await httpUtils.consoleRequest(server, `/events/delete?email=${email}`, httpUtils.DELETE);
    expect(res).to.have.status(200);
    const wsEvent = await WebhookStreamEvent.findOne();
    expect(wsEvent).to.not.exist;
  });

  it('should delete wordpress events', async () => {
    const email = '<EMAIL>';
    await httpUtils.trackWordpress(server, { email, siteUrl: 'https://blog.provesrc.com' });

    const res = await httpUtils.consoleRequest(server, `/events/delete?email=${email}`, httpUtils.DELETE);
    expect(res).to.have.status(200);

    const wpEvent = await WPEvent.findOne();
    expect(wpEvent).to.not.exist;
  });

  it('should delete woocommerce events', async () => {
    const email = '<EMAIL>';
    const product = {
      id: 1, name: 'prod', quantity: 1, link: 'https://shop.provesrc.com/prod', price: 5,
    };
    const data = {
      email, firstName: 'test', lastName: 'testo', products: [product], currency: 'USD', total: 123, siteUrl: 'https://shop.provesrc.com',
    };
    let res = await httpUtils.trackWooCommerce(server, data);
    expect(res).to.have.status(200);

    res = await httpUtils.consoleRequest(server, `/events/delete?email=${email}`, httpUtils.DELETE);
    expect(res).to.have.status(200);

    const wooEvent = await WooEvent.findOne();
    expect(wooEvent).to.not.exist;
  });

  it('should delete magento 2 events', async () => {
    const email = '<EMAIL>';
    const product = {
      id: '1', name: 'prod', quantity: 1, link: 'https://shop.provesrc.com/prod', price: 5,
    };
    const data = {
      email, firstName: 'test', lastName: 'testo', products: [product], currency: 'USD', total: 123, siteUrl: 'https://shop.provesrc.com',
    };
    let res = await httpUtils.trackMagento2(server, data);
    expect(res).to.have.status(200);

    res = await httpUtils.consoleRequest(server, `/events/delete?email=${email}`, httpUtils.DELETE);
    expect(res).to.have.status(200);

    const mag2Event = await Magento2Event.findOne();
    expect(mag2Event).to.not.exist;
  });

  it('should delete shopify events', async () => {
    const shop = 'myshop.com';
    const account = AccountFactory.shopify('<EMAIL>', shop, shop);
    sandbox.stub(Account, 'findOne').resolves(account);

    const agent = httpUtils.Agent(server, account.apiKey);

    const orderData = require('../webhooks/shopify.data');
    let res = await agent.trackShopify(server, shop, orderData);
    expect(res).to.have.status(200);

    res = await agent.consoleRequest(server, `/events/delete?email=${orderData.email}`, httpUtils.DELETE);
    expect(res).to.have.status(200);

    const shopifyEvent = await ShopifyEvent.findOne();
    expect(shopifyEvent).to.not.exist;
  });
});
