const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const config = require('../../config');

const slack = require('../../lib/apis/slackNotifier');
const mailerlite = require('../../lib/apis/mailerlite');
const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;

describe('/leads/upgrade', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  afterEach(async () => {
    sandbox.restore();
  });

  it('should not allow unauthenticated requests', async () => {
    try {
      await httpUtils.noAuthRequest(server, '/leads/upgrade', httpUtils.GET);
      expect.fail();
    } catch(err) {}
  });

  it('should send a slack message about country selection', async () => {
    const notify = sandbox.stub(slack, 'notify');

    const agent = httpUtils.Agent(server);
    const { email, password } = constants;
    await agent.login(server, { email, password });

    const country = 'US';
    await agent.consoleRequest(server, `/leads/upgrade?country=${country}`, httpUtils.GET);
    expect(notify).to.have.been.called;
  });

  it('should send a slack message / mailerlite register about plan selection', async () => {
    const account = AccountFactory.default();
    sandbox.stub(Account, 'findOne').resolves(account);
    const notify = sandbox.stub(slack, 'notify');
    const subscribe = sandbox.stub(mailerlite, 'subscribe');

    const agent = httpUtils.Agent(server, account.apiKey);

    const plan = 'starter';
    await agent.consoleRequest(server, `/leads/upgrade?plan=${plan}`, httpUtils.GET);
    expect(notify).to.have.been.called;
    expect(subscribe).to.have.been.calledWith(account.email, { group: config.mailerlite.groups.plan_selected });
  });
});
