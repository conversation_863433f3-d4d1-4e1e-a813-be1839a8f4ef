const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');
const PlansEnum = require('../../app/account/plansEnum');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();

const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;
const BillFactory = require('../factories/BillFactory');

const { Bill } = BillFactory;

describe('/billing/info', () => {
  let server;
  before(() => serverP.then(app => server = app));

  beforeEach(async () => {
    await Bill.remove();
  });

  afterEach(async () => {
    sandbox.restore();
    await Bill.remove();
  });

  it('should return subscription info', async () => {
    const account = AccountFactory.default();
    const bill = BillFactory.makeBill(constants.accountId, account.getBillingCycleDate());
    sandbox.stub(Account, 'findOne').resolves(account);
    sandbox.stub(Bill, 'findOne').resolves(bill);

    return httpUtils.consoleRequest(server, '/billing/info', httpUtils.METHODS.GET).then((res) => {
      expect(res).to.have.status(200);
      expect(res.body).to.have.property('subscription');
      const { subscription } = res.body;
      expect(subscription).to.have.property('card');
      expect(subscription).to.have.property('created');
      expect(subscription.recentIPN).to.not.be.empty;

      const { plan } = constants.subscription;
      expect(res.body).to.have.property('visitorsLimit', PlansEnum.LIMITS[plan]);
      expect(res.body.visitorsMonth).to.be.equal(bill.total);
    });
  });

  it('should return a free subscription', async () => {
    const account = AccountFactory.free('<EMAIL>');
    const bill = BillFactory.makeBill(account._id, account.getBillingCycleDate());
    sandbox.stub(Account, 'findOne').resolves(account);
    sandbox.stub(Bill, 'findOne').resolves(bill);

    const agent = httpUtils.Agent(server, account.apiKey);
    const res = await agent.consoleRequest(server, '/billing/info', httpUtils.METHODS.GET);

    expect(res).to.have.status(200);
    expect(res.body).to.have.property('subscription');
    const { subscription } = res.body;
    expect(subscription).to.have.property('created');

    const limit = PlansEnum.LIMITS[PlansEnum.PLANS.FREE];
    expect(res.body).to.have.property('visitorsLimit', limit);
    expect(res.body.visitorsMonth).to.be.equal(bill.total);
    expect(res.body.shopify).to.be.false;
    expect(res.body.subscription.untilDate).to.not.be.null;
  });

  it('should return shopify & shopifyStaff true', async () => {
    const account = AccountFactory.shopify('<EMAIL>');
    account.shopify[0].plan_name = 'staff_business';
    sandbox.stub(Account, 'findOne').resolves(account);
    const agent = httpUtils.Agent(server, account.apiKey);
    const res = await agent.consoleRequest(server, '/billing/info', httpUtils.METHODS.GET);
    expect(res.body.shopify).to.be.true;
    expect(res.body.shopifyStaff).to.be.true;
  });

  it('should return ltd info', async () => {
    const account = AccountFactory.free('<EMAIL>');
    sandbox.stub(Account, 'findOne').resolves(account);
    account.ltd = true;
    const agent = httpUtils.Agent(server, account.apiKey);
    const res = await agent.consoleRequest(server, '/billing/info', httpUtils.METHODS.GET);
    expect(res.body.ltd).to.be.true;
  });
});
