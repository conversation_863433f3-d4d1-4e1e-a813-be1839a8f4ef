const chai = require('chai');

const { expect } = chai;
const sinon = require('sinon');
const constants = require('../constants');

const Bill = require('../../app/billing/Bill');
const dateUtils = require('../../lib/utils/dateUtils');

describe('Model - Bill', () => {
  const sandbox = sinon.createSandbox();

  afterEach(() => {
    sandbox.restore();
  });

  it('should return the visitors counts', async () => {
    const total = 50;
    const bill = mockBill(total);
    sandbox.stub(Bill, 'findOne').resolves(bill);
    const numVisitors = await Bill.getVisitorCountThisMonth(constants.accountId);
    expect(numVisitors).to.be.equal(total);
  });

  it('should return 0 when no current bill', async () => {
    sandbox.stub(Bill, 'findOne').resolves(null);
    const visitors = await Bill.getVisitorCountThisMonth(constants.accountId, Date.now());
    expect(visitors).to.be.equal(0);
  });

  it('should update with increment', async () => {
    const { accountId } = constants;
    const date = dateUtils.dateByAddingMonths(new Date(), -1);
    const update = sandbox.stub(Bill, 'update').returns({ exec: () => null });

    await Bill.incrementTotal(accountId, constants.limit, date);

    const findQuery = { accountId, date };
    const dayOfMonth = dateUtils.dayOfMonth();
    const $setOnInsert = { limit: constants.limit };
    const updateQuery = { $inc: { [`days.${dayOfMonth}`]: 1, total: 1 }, $setOnInsert };
    expect(update).to.have.been.calledWith(findQuery, updateQuery, { upsert: true });
  });

  function mockBill(total) {
    const { accountId } = constants;
    const date = Date.now();
    return new Bill({
      accountId, date, total, days: { 23: 50 },
    });
  }
});
