require('../../lib/mongooseLoader');
const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');
const utils = require('../testUtils');
const stringUtils = require('../../lib/utils/stringUtils');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();

const Account = require('../../app/account/models/Account');
const AccountFactory = require('../factories/AccountFactory');
const IPN = require('../../app/billing/bluesnap/IPNModel');
const PlansEnum = require('../../app/account/plansEnum');
const profitWell = require('../../app/billing/profitwell.controller');

describe('/billing/ipn', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  let account;
  beforeEach(async () => {
    account = AccountFactory.default();
    sandbox.stub(Account, 'findOne').resolves(account);
    sandbox.stub(Account.prototype, 'save').resolves();
    await IPN.remove();
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should set a far untilDate when lifetime deal', async () => {
    const ipnData = Object.assign({}, require('./ipnExData').LIFETIME);
    ipnData.merchantTransactionId = account.id;
    await httpUtils.noAuthRequest(server, constants.BILLING.IPN, httpUtils.POST).type('form').send(ipnData);

    expect(account.subscription).to.exist;
    const { untilDate } = account.subscription;
    expect(untilDate.getFullYear()).to.be.at.least(2020);
  });

  it('should set lifetime deal addon', async () => {
    const ipnData = Object.assign({}, require('./ipnExData').LIFETIME_PAYPAL);
    ipnData.merchantTransactionId = account.id;
    ipnData.contractId = '3452702';
    await httpUtils.noAuthRequest(server, constants.BILLING.IPN, httpUtils.POST).type('form').send(ipnData);

    expect(account.getPlanLimit()).to.be.equal(PlansEnum.LIMITS[PlansEnum.PLANS.LIFETIME_39_ADDON]);
    expect(account.subscription.untilDate.getFullYear()).to.be.equal(2038);
    expect(account.configuration.whitelabel).to.be.true;
  });

  describe('properties', () => {
    it('should store properties', async () => {
      const ipnData = Object.assign({}, require('./ipnExData').LIFETIME_PAYPAL);
      ipnData.merchantTransactionId = account.id;
      await httpUtils.noAuthRequest(server, constants.BILLING.IPN, httpUtils.POST).type('form').send(ipnData);

      const ipn = await IPN.findOne();

      expect(ipn.accountId.toString()).to.be.equal(constants.accountId);
      expect(ipn.email).to.be.equal(ipnData.email);
      expect(ipn.type).to.be.equal(ipnData.transactionType);
    });

    it('should store company names in unique array', async () => {
      const ipnData = Object.assign({}, require('./ipnExData').withCompany);
      ipnData.merchantTransactionId = account.id;
      await httpUtils.noAuthRequest(server, constants.BILLING.IPN, httpUtils.POST).type('form').send(ipnData);

      expect(account.subscription.invoiceCompanies).to.include(ipnData.company);
      expect(account.subscription.invoiceCompanies).to.include(ipnData.invoiceCompany);
    });

    it('should store full names in unique array', async () => {
      const ipnData = Object.assign({}, require('./ipnExData').firstLast);
      ipnData.merchantTransactionId = account.id;
      await httpUtils.noAuthRequest(server, constants.BILLING.IPN, httpUtils.POST).type('form').send(ipnData);

      const name = `${ipnData.invoiceFirstName} ${ipnData.invoiceLastName}`;
      expect(account.subscription.invoiceNames).to.include(name);
    });

    it('should store emails', async () => {
      const ipnData = Object.assign({}, require('./ipnExData').firstLast);
      ipnData.merchantTransactionId = account.id;
      await httpUtils.noAuthRequest(server, constants.BILLING.IPN, httpUtils.POST).type('form').send(ipnData);

      expect(account.emails).to.include(ipnData.email);
      expect(account.emails).to.include(ipnData.invoiceEmail);
    });
  });

  describe('negative IPNs', () => {
    it('should add 14 days to untilDate if subscription charge failed', async () => {
      const ipnData = Object.assign({}, require('./ipnExData').SUBSCRIPTION_CHARGE_FAILURE);
      ipnData.merchantTransactionId = account.id;

      await httpUtils.noAuthRequest(server, constants.BILLING.IPN, httpUtils.POST).type('form').send(ipnData);

      const untilDate = new Date(ipnData.untilDate);
      expect(account.subscription.untilDate - untilDate).to.be.lessThan(86400 * 1000 * 14 + 1);
    });
  });

  describe('subscription value', () => {
    it('should increment payment metrics', async () => {
      const ipnData = Object.assign({}, require('./ipnExData').RECURRING);
      ipnData.merchantTransactionId = account.id;
      const amount = parseInt(ipnData.invoiceAmount, 10);
      const prevCltv = 100;
      const prevPayments = 10;
      account.metrics.cltv = prevCltv;
      account.metrics.nPayments = prevPayments;

      const res = await httpUtils.noAuthRequest(server, constants.BILLING.IPN, httpUtils.POST).type('form').send(ipnData);
      expect(res).to.have.status(200);

      expect(account.metrics.cltv).to.be.equal(prevCltv + amount);
      expect(account.metrics.mrr).to.be.equal(amount);
      expect(account.transactions[0].amount).to.be.equal(amount);
    });

    it('should support negative ipns (decrease)', async () => {
      const ipnData = Object.assign({}, require('./ipnExData').CANCELLATION_REFUND);
      const amount = parseInt(ipnData.invoiceAmount, 10);
      ipnData.merchantTransactionId = account.id;
      const pCltv = 167;
      const pPayments = 5;
      account.metrics.cltv = pCltv;
      account.metrics.nPayments = pPayments;

      await httpUtils.noAuthRequest(server, constants.BILLING.IPN, httpUtils.POST).type('form').send(ipnData);

      expect(account.transactions[0].amount).to.be.equal(amount);
    });

    it('should calculate mrr for yearly contracts', async () => {
      const ipnData = Object.assign({}, require('./ipnExData').RECURRING);
      ipnData.contractId = '3434682';
      ipnData.merchantTransactionId = account.id;
      ipnData.invoiceAmount = '492';
      const amount = parseInt(ipnData.invoiceAmount, 10);

      await httpUtils.noAuthRequest(server, constants.BILLING.IPN, httpUtils.POST).type('form').send(ipnData);

      expect(account.metrics.mrr).to.be.equal(amount / 12);
    });
  });

  describe('affiliate', () => {
    it('should increment affiliate balance and mrr', async () => {
      const [initialBalance, initialMrr] = [32, 7];
      account.affiliate.balance = initialBalance;
      account.affiliate.mrr = initialMrr;

      const newAccount = AccountFactory.free('<EMAIL>', '1234');
      newAccount.affiliate.referrer = account.affiliate.id;

      Account.findOne.restore();
      const findStub = sandbox.stub(Account, 'findOne');
      findStub.withArgs({ _id: sinon.match.any }).resolves(newAccount);
      findStub.withArgs({ 'affiliate.id': sinon.match.any }).resolves(account);

      const ipnData = Object.assign({}, require('./ipnExData').RECURRING);
      ipnData.merchantTransactionId = newAccount.id;
      ipnData.transactionType = 'CHARGE';
      const commission = parseInt(ipnData.invoiceAmount, 10) * 0.2;
      await httpUtils.noAuthRequest(server, constants.BILLING.IPN, httpUtils.POST).type('form').send(ipnData);

      await utils.sleep(200);

      expect(account.getBalance()).to.be.equal(commission + initialBalance);
      expect(account.getMRR()).to.be.equal(commission + initialMrr);
      expect(account.affiliate.customers).to.be.equal(1);
    });

    it('should decrease affiliate balance on refund', async () => {
      const account = AccountFactory.default();
      const [initialBalance, initialMrr, initialCustomers] = [32, 7, 12];
      Object.assign(account.affiliate, { balance: initialBalance, mrr: initialMrr, customers: initialCustomers });
      const newAccount = AccountFactory.free('<EMAIL>', '1234');
      newAccount.affiliate.referrer = account.affiliate.id;

      Account.findOne.restore();
      const findStub = sandbox.stub(Account, 'findOne');
      findStub.withArgs({ _id: sinon.match.any }).resolves(newAccount);
      findStub.withArgs({ 'affiliate.id': sinon.match.any }).resolves(account);

      const ipnData = Object.assign({}, require('./ipnExData').CANCELLATION_REFUND);
      ipnData.merchantTransactionId = newAccount.id;
      const commission = parseInt(ipnData.invoiceAmount, 10) * 0.2;
      await httpUtils.noAuthRequest(server, constants.BILLING.IPN, httpUtils.POST).type('form').send(ipnData);

      await utils.sleep(200);

      expect(account.getBalance()).to.be.equal(initialBalance + commission);
      expect(account.getMRR()).to.be.equal(initialMrr - commission);
      expect(account.affiliate.customers).to.be.equal(initialCustomers - 1);
    });


    it('should use commissionRate', async () => {
      const [initialBalance, initialMrr, initialCustomers] = [32, 7, 12];
      const commissionRate = 0.5;
      const account = AccountFactory.default();
      Object.assign(account.affiliate, { balance: initialBalance, mrr: initialMrr, customers: initialCustomers });
      account.affiliate.commissionRate = commissionRate;
      const newAccount = AccountFactory.free('<EMAIL>', '1234');
      newAccount.affiliate.referrer = account.affiliate.id;

      Account.findOne.restore();
      const findStub = sandbox.stub(Account, 'findOne');
      findStub.withArgs({ _id: sinon.match.any }).resolves(newAccount);
      findStub.withArgs({ 'affiliate.id': sinon.match.any }).resolves(account);

      const ipnData = Object.assign({}, require('./ipnExData').RECURRING);
      ipnData.merchantTransactionId = newAccount.id;
      ipnData.transactionType = 'CHARGE';
      const commission = parseInt(ipnData.invoiceAmount, 10) * commissionRate;
      await httpUtils.noAuthRequest(server, constants.BILLING.IPN, httpUtils.POST).type('form').send(ipnData);

      await utils.sleep(200);

      expect(account.getBalance()).to.be.equal(initialBalance + commission);
      expect(account.getMRR()).to.be.equal(initialMrr + commission);
    });

    it('should decrease MRR on cancellation', async () => {
      const account = AccountFactory.default();
      const [initialBalance, initialMrr, initialCustomers] = [32, 7, 12];
      Object.assign(account.affiliate, { balance: initialBalance, mrr: initialMrr, customers: initialCustomers });
      const newAccount = AccountFactory.free('<EMAIL>', '1234');
      newAccount.affiliate.referrer = account.affiliate.id;

      Account.findOne.restore();
      const findStub = sandbox.stub(Account, 'findOne');
      findStub.withArgs({ _id: sinon.match.any }).resolves(newAccount);
      findStub.withArgs({ 'affiliate.id': sinon.match.any }).resolves(account);

      const ipnData = Object.assign({}, require('./ipnExData').CANCELLATION);
      ipnData.merchantTransactionId = newAccount.id;
      const commission = parseInt(ipnData.invoiceAmount, 10) * 0.2;
      await httpUtils.noAuthRequest(server, constants.BILLING.IPN, httpUtils.POST).type('form').send(ipnData);

      await utils.sleep(200);

      expect(account.getMRR()).to.be.equal(initialMrr - commission);
    });
  });

  describe('profitwell reporting', () => {
    afterEach(() => {
      sinon.restore();
    });

    function stubs() {
      const updateSub = sinon.stub(profitWell, 'updateSubscription');
      const churnSub = sinon.stub(profitWell, 'churnSubscription');
      return { updateSub, churnSub };
    }

    it('should update subscriber in profitwell', async () => {
      const ipnData = Object.assign({}, require('./ipnExData').firstLast);
      const value = (stringUtils.getNumber(ipnData.invoiceAmountUSD)) * 100;
      const contract = PlansEnum.CONTRACT_DETAILS[ipnData.contractId];
      const params = {
        email: account.email,
        planName: ipnData.contractName,
        value,
        interval: contract.period.slice(0, -2), // 'month' versus 'monthly' issue
      };

      const { updateSub } = stubs();
      await updateSub.resolves({});

      const res = await httpUtils.noAuthRequest(server, constants.BILLING.IPN, httpUtils.POST).type('form').send(ipnData);

      expect(res).to.not.be.empty;
      expect(res).to.have.status(200);
      expect(updateSub).to.have.been.called;
      expect(updateSub).to.have.been.calledWith(account.id, params);
    });

    it('should churn subscriber in profitwell', async () => {
      const ipnData = Object.assign({}, require('./ipnExData').CANCELLATION);
      const churnDate = new Date(ipnData.untilDate).getTime();

      const { churnSub } = stubs();
      await churnSub.resolves({});

      const res = await httpUtils.noAuthRequest(server, constants.BILLING.IPN, httpUtils.POST).type('form').send(ipnData);

      expect(res).to.not.be.empty;
      expect(res).to.have.status(200);
      expect(churnSub).to.have.been.called;
      expect(churnSub).to.have.been.calledWith(account.id, null, churnDate);
    });
  });
});
