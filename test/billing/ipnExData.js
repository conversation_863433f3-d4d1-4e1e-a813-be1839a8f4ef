module.exports.CANCELLATION_REFUND = {
  lastName: 'Cuevas',
  country: 'MX',
  zipCode: '62120',
  extension: '',
  personalId: '',
  address2: '',
  city: '',
  address1: '',
  homePhone: '',
  brazilianId: '',
  title: '',
  accountId: '********',
  firstName: 'Fabiola',
  mobilePhone: '',
  faxNumber: '',
  company: '',
  workPhone: '',
  state: '',
  email: '<EMAIL>',
  username: '1533656775327299399856991505911',
  invoiceCountry: 'MX',
  invoiceZipCode: '62120',
  invoiceFirstName: 'Fabiola',
  invoiceFaxNumber: '',
  invoiceState: '',
  invoiceAddress2: '',
  invoiceEmail: '<EMAIL>',
  invoiceLastName: 'Cuevas',
  invoiceAddress1: '',
  invoiceCompany: '',
  invoiceCity: '',
  invoiceExtension: '',
  invoiceMobilePhone: '',
  invoiceWorkPhone: '',
  invoiceTitle: '',
  shippingCity: '',
  shippingLastName: 'Cuevas',
  shippingCountry: 'MX',
  shippingZipCode: '62120',
  shippingMethod: '',
  shippingFirstName: 'Fabiola',
  shippingState: '',
  shippingAddress2: '',
  shippingAddress1: '',
  taxAmountUSD: '0.00',
  taxRate: '',
  taxChargeAmount: '0.00',
  vatId: '',
  authKey: '',
  paymentMethod: 'CC',
  paymentType: 'CC',
  bluesnapNode: '1',
  plimusNode: '1',
  transactionType: 'CANCELLATION_REFUND',
  untilDate: '09/07/2018 08:46 AM',
  invoiceURL: 'https://www.bluesnap.com/jsp/show_invoice.jsp?ref=CB0CF0214668A3BE3282A1D6BBE83FFA',
  shopperOrderUrl: 'https://shoppers.bluesnap.com/jsp/order_locator_info.jsp?refId=CB0CF0214668A3BE3282A1D6BBE83FFA&acd=626EFF8B196F0A96',
  originalRequestUrl: 'https://console.provesrc.com/',
  shopperAdminUrl: 'https://shoppers.bluesnap.com/jsp/order_locator_info.jsp?refId=CB0CF0214668A3BE3282A1D6BBE83FFA&acd=626EFF8B196F0A96',
  reversalRefNum: '*********',
  purchaseDate: '08/07/2018 08:46 AM',
  dpanLastFourDigits: '',
  cardCategory: 'ELECTRON',
  invoiceAmount: '-19.00',
  language: 'SPANISH',
  creditCardType: 'VISA',
  invoiceAmountUSD: '-19.00',
  templateId: '20801',
  invoiceLocalCurrency: 'USD',
  productName: 'ProveSource',
  creditCardExpDate: '2/2023',
  reversalReason: 'Per your instructions',
  contractOwner: '1102833',
  referenceNumber: '121848204',
  contractName: 'Starter Monthly Plan',
  currency: 'USD',
  invoiceInfoURL: 'https://shoppers.bluesnap.com/jsp/order_locator_info.jsp?refId=CB0CF0214668A3BE3282A1D6BBE83FFA&acd=626EFF8B196F0A96',
  remoteAddress: '**************',
  contractChargePrice: '19.00',
  quantity: '1',
  productId: '1002838',
  invoiceChargeCurrency: 'USD',
  cardSubType: 'DEBIT',
  invoiceChargeAmount: '-19.00',
  referrer: '',
  contractPrice: '19.00',
  dpanExpDate: '',
  creditCardLastFourDigits: '7413',
  testMode: 'N',
  contractId: '3434684',
  subscriptionId: '********',
  targetBalance: 'BLUESNAP_ACCOUNT',
  promoteContractsNum: '0',
  addCD: 'N',
  EDWPeriod: '',
  EDWSurcharge: '',
  EDWAmountUSD: '',
  EDWContractId: '',
  EDWSurchargeUSD: '',
  EDWAmount: '',
  cancelReason: 'Per your instructions',
  licenseKey: '',
  reversalAmount: '19.00',
  fullRefund: 'Y',
  transactionDate: '08/09/2018 12:17 AM',
  merchantTransactionId: '5b62223ade7000640c318914',
  vendorId: '',
  vendorName: '',
  sepaMandateId: '',
  sepaIban: '',
  sepaMandateDate: '',
};

module.exports.RECURRING = {
  lastName: 'Almodovar',
  country: 'US',
  zipCode: '00680',
  extension: '',
  personalId: '',
  address2: '',
  city: '',
  address1: '',
  homePhone: '',
  brazilianId: '',
  title: '',
  accountId: '********',
  firstName: 'Ariana',
  mobilePhone: '',
  faxNumber: '',
  company: '',
  workPhone: '',
  state: 'PR',
  email: '<EMAIL>',
  username: '15312297305891707484503466740951',
  shippingCity: '',
  shippingLastName: 'Almodovar',
  shippingCountry: 'US',
  shippingZipCode: '00680',
  shippingMethod: [
    '',
    '',
  ],
  shippingFirstName: 'Ariana',
  shippingState: 'PR',
  shippingAddress2: '',
  shippingAddress1: '',
  invoiceCountry: 'PR',
  invoiceZipCode: '00680',
  invoiceFirstName: 'Ariana',
  invoiceFaxNumber: '',
  invoiceState: '',
  invoiceAddress2: 'Bo La Quinta',
  invoiceEmail: '<EMAIL>',
  invoiceLastName: 'Almodovar',
  invoiceAddress1: 'Calle Balboa 177',
  invoiceCompany: 'Scoreinc.com',
  invoiceCity: 'Mayaguez',
  invoiceExtension: '',
  invoiceMobilePhone: '',
  invoiceWorkPhone: '**********',
  invoiceTitle: 'VP',
  dpanLastFourDigits: '',
  cardCategory: 'BUSINESS',
  invoiceAmount: '19.00',
  language: 'ENGLISH',
  creditCardType: 'MASTERCARD',
  invoiceAmountUSD: '19.00',
  productName: 'ProveSource',
  invoiceLocalCurrency: 'USD',
  paymentType: 'CC',
  creditCardExpDate: '06/2020',
  contractOwner: '1102833',
  referenceNumber: '*********',
  contractName: 'Starter Monthly Plan',
  currency: 'USD',
  invoiceInfoURL: 'https://shoppers.bluesnap.com/jsp/order_locator_info.jsp?refId=8C5425B05EFF3DFB3282A1D6BBE83FFA&acd=E2C7E05947A81807',
  remoteAddress: '',
  quantity: '1',
  contractChargePrice: '19.00',
  productId: '1002838',
  invoiceChargeCurrency: 'USD',
  cardSubType: 'CREDIT',
  shopperOrderUrl: 'https://shoppers.bluesnap.com/jsp/order_locator_info.jsp?refId=8C5425B05EFF3DFB3282A1D6BBE83FFA&acd=E2C7E05947A81807',
  invoiceChargeAmount: '19.00',
  originalRefNum: '*********',
  transactionType: 'RECURRING',
  referrer: '',
  contractPrice: '19.00',
  dpanExpDate: '',
  creditCardLastFourDigits: '8704',
  originalReferenceNumber: '*********',
  testMode: 'N',
  contractId: '3434684',
  paymentMethod: 'CC',
  subscriptionId: '********',
  addCD: 'N',
  untilDate: '09/10/2018 06:35 AM',
  invoiceURL: 'https://www.bluesnap.com/jsp/show_invoice.jsp?ref=8C5425B05EFF3DFB3282A1D6BBE83FFA',
  templateId: '20801',
  promoteContractsNum: '0',
  targetBalance: 'BLUESNAP_ACCOUNT',
  authKey: '',
  vatId: '',
  taxAmountUSD: '0.00',
  taxRate: '',
  taxChargeAmount: '0.00',
  bluesnapNode: '1',
  plimusNode: '1',
  couponCode: '',
  licenseKey: '',
  transactionDate: '08/10/2018 07:00 AM',
  merchantTransactionId: '5b11e6942044ab7ded8fda0d',
  vendorId: '',
  vendorName: '',
  sepaMandateId: '',
  sepaIban: '',
  sepaMandateDate: '',
  cardIssuingOrg: 'ORIENTAL BANK AND TRUST',
  binNumber: '527152',
  cardIssuingCountry: 'pr',
};

module.exports.LIFETIME = {
  invoiceCountry: 'IL',
  invoiceZipCode: '',
  invoiceFirstName: 'Natan',
  invoiceFaxNumber: '',
  invoiceState: '',
  invoiceAddress2: '',
  invoiceEmail: '<EMAIL>',
  invoiceLastName: 'Morales',
  invoiceAddress1: '',
  invoiceCompany: '',
  invoiceCity: '',
  invoiceExtension: '',
  invoiceMobilePhone: '',
  invoiceWorkPhone: '',
  invoiceTitle: '',
  lastName: 'Morales',
  country: 'IL',
  zipCode: '',
  extension: '',
  personalId: '',
  address2: '',
  city: '',
  address1: '',
  homePhone: '',
  brazilianId: '',
  title: '',
  accountId: '********',
  firstName: 'Natan',
  mobilePhone: '',
  faxNumber: '',
  company: '',
  workPhone: '',
  state: '',
  email: '<EMAIL>',
  username: '15238901996811327849137976066259',
  shippingCity: '',
  shippingLastName: 'Morales',
  shippingCountry: 'IL',
  shippingZipCode: '',
  shippingMethod: '',
  shippingFirstName: 'Natan',
  shippingState: '',
  shippingAddress2: '',
  shippingAddress1: '',
  contractChargePrice: '39.00',
  quantity: '1',
  productId: '463043',
  invoiceChargeCurrency: 'USD',
  overridePrice: '0.00',
  invoiceAmount: '39.00',
  invoiceChargeAmount: '39.00',
  language: 'HEBREW',
  invoiceAmountUSD: '39.00',
  templateId: '32399',
  productName: 'ProveSource',
  referrer: '',
  contractOwner: '547461',
  referenceNumber: '**********',
  contractPrice: '39.00',
  testMode: 'N',
  contractId: '3452234',
  contractName: 'Lifetime Deal',
  currency: 'USD',
  invoiceInfoURL: 'https://sandbox.bluesnap.com/jsp/order_locator_info.jsp?refId=********************************&acd=69920576B98D4D1B',
  remoteAddress: '*************',
  creditCardExpDate: '4/2023',
  dpanLastFourDigits: '',
  dpanExpDate: '',
  creditCardLastFourDigits: '9903',
  cardSubType: 'CREDIT',
  cardCategory: 'STANDARD',
  creditCardType: 'MASTERCARD',
  addCD: 'N',
  untilDate: '04/16/2018 07:50 AM',
  paymentMethod: 'CC',
  paymentType: 'CC',
  invoiceLocalAmount: '39.00',
  invoiceURL: 'https://sandbox.bluesnap.com/jsp/show_invoice.jsp?ref=********************************',
  shopperOrderUrl: 'https://sandbox.bluesnap.com/jsp/order_locator_info.jsp?refId=********************************&acd=69920576B98D4D1B',
  originalRequestUrl: '',
  contractLocalPrice: '39.00',
  shopperAdminUrl: 'https://sandbox.bluesnap.com/jsp/order_locator_info.jsp?refId=********************************&acd=69920576B98D4D1B',
  invoiceLocalCurrency: 'USD',
  recurringDisclaimer: 'N',
  targetBalance: 'BLUESNAP_ACCOUNT',
  promoteContractsNum: '0',
  EDWPeriod: '',
  EDWSurcharge: '',
  EDWAmountUSD: '',
  EDWContractId: '',
  EDWSurchargeUSD: '',
  EDWAmount: '',
  vatId: '',
  taxAmountUSD: '0.00',
  taxRate: '',
  taxChargeAmount: '0.00',
  authKey: '77685ea871d3b8efb7e45f8bf0df4da0',
  transactionType: 'CHARGE',
  captureReferenceNumber: '**********',
  bluesnapNode: '99',
  plimusNode: '99',
  couponCode: '',
  licenseKey: '',
  cvvSent: 'Y',
  avsSent: 'N',
  cvvResponse: 'Not processed ',
  avsResponse: 'Issuer unavailable or AVS not supported (US Issuer) ',
  transactionDate: '04/16/2018 07:50 AM',
  merchantTransactionId: '',
  vendorId: '',
  vendorName: '',
  sepaMandateId: '',
  sepaIban: '',
  sepaMandateDate: '',
  cardIssuingOrg: 'ALLIED IRISH BANKS PLC',
  binNumber: '542523',
  cardIssuingCountry: 'ie',
  '3DStatus': '3DS Not Enabled',
};

module.exports.LIFETIME_PAYPAL = {
  invoiceCountry: 'IL',
  invoiceZipCode: '',
  invoiceFirstName: 'Natan',
  invoiceFaxNumber: '',
  invoiceState: '',
  invoiceAddress2: '',
  invoiceEmail: '<EMAIL>',
  invoiceLastName: 'Abramov',
  invoiceAddress1: '',
  invoiceCompany: '',
  invoiceCity: '',
  invoiceExtension: '',
  invoiceMobilePhone: '',
  invoiceWorkPhone: '',
  invoiceTitle: '',
  lastName: 'Abramov',
  country: 'IL',
  zipCode: '',
  extension: '',
  personalId: '',
  address2: '',
  city: '',
  address1: '',
  homePhone: '',
  brazilianId: '',
  title: '',
  accountId: '********',
  firstName: 'Natan',
  mobilePhone: '',
  faxNumber: '',
  company: '',
  workPhone: '',
  state: '',
  email: '<EMAIL>',
  username: '15238966633751978389288208479677',
  shippingCity: '',
  shippingLastName: 'Abramov',
  shippingCountry: 'IL',
  shippingZipCode: '',
  shippingMethod: '',
  shippingFirstName: 'Natan',
  shippingState: '',
  shippingAddress2: '',
  shippingAddress1: '',
  contractChargePrice: '39.00',
  quantity: '1',
  productId: '463043',
  invoiceChargeCurrency: 'USD',
  invoiceAmount: '39.00',
  invoiceChargeAmount: '39.00',
  language: 'ENGLISH',
  invoiceAmountUSD: '39.00',
  templateId: '32399',
  productName: 'ProveSource',
  referrer: '',
  contractOwner: '547461',
  referenceNumber: '**********',
  contractPrice: '39.00',
  testMode: 'N',
  contractId: '2401357',
  contractName: 'Lifetime Deal',
  currency: 'USD',
  invoiceInfoURL: 'https://sandbox.bluesnap.com/jsp/order_locator_info.jsp?refId=********************************&acd=72A30F63AFB1B6C0',
  remoteAddress: '*************',
  creditCardExpDate: '',
  dpanLastFourDigits: '',
  dpanExpDate: '',
  creditCardLastFourDigits: '',
  cardSubType: '',
  cardCategory: '',
  creditCardType: '',
  addCD: 'N',
  untilDate: '04/16/2018 09:38 AM',
  paymentMethod: 'PAYPAL',
  paymentType: 'PAYPAL',
  offlineOrderId: '9177861',
  invoiceLocalAmount: '39.00',
  invoiceURL: 'https://sandbox.bluesnap.com/jsp/show_invoice.jsp?ref=********************************',
  shopperOrderUrl: 'https://sandbox.bluesnap.com/jsp/order_locator_info.jsp?refId=********************************&acd=72A30F63AFB1B6C0',
  originalRequestUrl: 'http://localhost:3001/app/',
  contractLocalPrice: '39.00',
  shopperAdminUrl: 'https://sandbox.bluesnap.com/jsp/order_locator_info.jsp?refId=********************************&acd=72A30F63AFB1B6C0',
  invoiceLocalCurrency: 'USD',
  recurringDisclaimer: 'N',
  targetBalance: 'VENDOR_PAYPAL_ACCOUNT',
  paypalTransactionId: '20X551274Y705002T',
  paypalSubscriptionId: '',
  promoteContractsNum: '0',
  EDWPeriod: '',
  EDWSurcharge: '',
  EDWAmountUSD: '',
  EDWContractId: '',
  EDWSurchargeUSD: '',
  EDWAmount: '',
  vatId: '',
  taxAmountUSD: '0.00',
  taxRate: '',
  taxChargeAmount: '0.00',
  authKey: 'ba8e8f438d840d36c44a553114b80ec6',
  transactionType: 'CHARGE',
  captureReferenceNumber: '**********',
  bluesnapNode: '99',
  plimusNode: '99',
  couponCode: '',
  licenseKey: '',
  transactionDate: '04/16/2018 09:38 AM',
  merchantTransactionId: '5a8e8e19c8b15f18216688c0',
  vendorId: '',
  vendorName: '',
  sepaMandateId: '',
  sepaIban: '',
  sepaMandateDate: '',
  '3DStatus': '3DS Not Enabled',
};

module.exports.CONTRACT_CHANGE = {
  transactionType: 'CONTRACT_CHANGE',
  lastName: 'Stewart',
  country: 'UK',
  zipCode: 'EH46DJ',
  extension: '',
  personalId: '',
  address2: '',
  city: '',
  address1: '',
  homePhone: '',
  brazilianId: '',
  title: '',
  accountId: '********',
  firstName: 'Jonathan',
  mobilePhone: '',
  faxNumber: '',
  company: '',
  workPhone: '',
  state: '',
  email: '<EMAIL>',
  username: '15385168179841703495269426191268',
  invoiceCountry: 'UK',
  invoiceZipCode: 'EH46DJ',
  invoiceFirstName: 'Jonathan',
  invoiceFaxNumber: '',
  invoiceState: '',
  invoiceAddress2: '',
  invoiceEmail: '<EMAIL>',
  invoiceLastName: 'Stewart',
  invoiceAddress1: '',
  invoiceCompany: '',
  invoiceCity: '',
  invoiceExtension: '',
  invoiceMobilePhone: '',
  invoiceWorkPhone: '',
  invoiceTitle: '',
  shippingCity: '',
  shippingLastName: 'Stewart',
  shippingCountry: 'UK',
  shippingZipCode: 'EH46DJ',
  shippingMethod: '',
  shippingFirstName: 'Jonathan',
  shippingState: '',
  shippingAddress2: '',
  shippingAddress1: '',
  contractChargePrice: '14.95',
  quantity: '1',
  productId: '1002838',
  invoiceChargeCurrency: 'GBP',
  overridePrice: '0.00',
  invoiceAmount: '19.00',
  invoiceChargeAmount: '14.95',
  language: 'ENGLISH',
  invoiceAmountUSD: '19.00',
  templateId: '20801',
  productName: 'ProveSource',
  referrer: '',
  contractOwner: '1102833',
  referenceNumber: '********',
  contractPrice: '19.00',
  testMode: 'N',
  contractId: '3434684',
  contractName: 'Starter Monthly Plan',
  currency: 'GBP',
  subscriptionId: '36405129',
  invoiceInfoURL: 'https://shoppers.bluesnap.com/jsp/order_locator_info.jsp?refId=C52D5E26388FB04A&acd=D81A76A9B9F9B0E0',
  remoteAddress: '*************',
  creditCardExpDate: '3/2020',
  dpanLastFourDigits: '',
  dpanExpDate: '',
  creditCardLastFourDigits: '7876',
  cardSubType: 'DEBIT',
  cardCategory: 'BUSINESS',
  creditCardType: 'VISA',
  addCD: 'N',
  untilDate: '11/21/2018 08:11 AM',
  invoiceURL: 'https://www.bluesnap.com/jsp/show_invoice.jsp?ref=C52D5E26388FB04A',
  originalInvoiceId: '********',
  shopperOrderUrl: 'https://shoppers.bluesnap.com/jsp/order_locator_info.jsp?refId=C52D5E26388FB04A&acd=D81A76A9B9F9B0E0',
  resultCode: 'SUCCESS',
  shopperAdminUrl: 'https://shoppers.bluesnap.com/jsp/order_locator_info.jsp?refId=C52D5E26388FB04A&acd=D81A76A9B9F9B0E0',
  targetBalance: 'BLUESNAP_ACCOUNT',
  taxAmountUSD: '0.00',
  taxRate: '',
  taxChargeAmount: '0.00',
  vatId: '',
  oldContractName: 'ProveSource Starter Monthly Plan',
  oldProductId: '1002838',
  newContractId: '3434676',
  oldProductName: 'ProveSource',
  newProductId: '1002838',
  newProductName: 'ProveSource',
  oldContractId: '3434684',
  originalRefNum: '********',
  newContractName: 'ProveSource Growth Monthly Plan',
  paymentType: 'CC',
  paymentMethod: 'CC',
  bluesnapNode: '2',
  plimusNode: '2',
  couponCode: '',
  authKey: '',
  licenseKey: '',
  transactionDate: '10/21/2018 08:11 AM',
  merchantTransactionId: '5b754992a6dffd66fe7e30c4',
  vendorId: '',
  vendorName: '',
  cardIssuingOrg: 'THE ROYAL BANK OF SCOTLAND PLC',
  binNumber: '475748',
  cardIssuingCountry: 'gb',
};

module.exports.SUBSCRIPTION_CHARGE_FAILURE = {
  lastName: 'BARTULIS',
  country: 'LV',
  zipCode: 'LV-1073',
  extension: '',
  personalId: '',
  address2: '',
  city: '',
  address1: '',
  homePhone: '',
  brazilianId: '',
  title: '',
  accountId: '********',
  firstName: 'MATISS',
  mobilePhone: '',
  faxNumber: '',
  company: '',
  workPhone: '',
  state: '',
  email: '<EMAIL>',
  username: '15383032195481807297905100612333',
  invoiceCountry: 'LV',
  invoiceZipCode: 'LV-1073',
  invoiceFirstName: 'Matiss',
  invoiceFaxNumber: '',
  invoiceState: '',
  invoiceAddress2: '',
  invoiceEmail: '<EMAIL>',
  invoiceLastName: 'Bartulis',
  invoiceAddress1: 'Slavu iela 15-67',
  invoiceCompany: 'Maverick Solutions, SIA',
  invoiceCity: 'Riga',
  invoiceExtension: '',
  invoiceMobilePhone: '',
  invoiceWorkPhone: '********',
  invoiceTitle: '',
  transactionType: 'SUBSCRIPTION_CHARGE_FAILURE',
  contractChargePrice: '44.30',
  referenceNumber: '********',
  contractPrice: '49.00',
  failureReason: 'Insufficient funds. Please use another card or contact your bank for assistance (PV-51)',
  proccessorCode: '',
  shopperAdminURL: 'https://shoppers.bluesnap.com/jsp/order_locator_info.jsp?refId=463C35D776BD2515&acd=2A05F288EC0145A1',
  errorProcessingCode: '51',
  armadilloHardwareId: '',
  untilDate: '12/15/2018 01:11 PM',
  targetBalance: 'BLUESNAP_ACCOUNT',
  password: '**contact your system administrator**',
  bluesnapNode: '1',
  plimusNode: '1',
  quantity: '1',
  productId: '1002838',
  contractOwner: '1102833',
  contractId: '3434676',
  shopperOrderUrl: 'https://shoppers.bluesnap.com/jsp/order_locator_info.jsp?refId=463C35D776BD2515&acd=2A05F288EC0145A1',
  paymentMethod: 'CC',
  contractName: 'Growth Monthly Plan',
  language: 'RUSSIAN',
  subscriptionId: '********',
  productName: 'ProveSource',
  originalRefNum: '********',
  paymentType: 'CC',
  creditCardExpDate: '3/2021',
  dpanLastFourDigits: '',
  dpanExpDate: '',
  creditCardLastFourDigits: '8055',
  cardSubType: 'DEBIT',
  cardCategory: 'BUSINESS',
  creditCardType: 'MASTERCARD',
  currency: 'EUR',
  daysTillCancelDate: '5',
  licenseKey: '',
  transactionDate: '12/15/2018 01:02 PM',
  merchantTransactionId: '5b9f512bb004280ec2f49f66',
  vendorId: '',
  vendorName: '',
  numberOfAttemptsDone: '1',
  lastAttempt: 'N',
  cardIssuingOrg: 'SWEDBANK AS',
  binNumber: '535549',
  cardIssuingCountry: 'lv',
};

module.exports.CANCELLATION = {
  lastName: 'Amos',
  country: 'ZA',
  zipCode: '100263',
  extension: '',
  personalId: '',
  address2: '',
  city: '',
  address1: '',
  homePhone: '',
  brazilianId: '',
  title: '',
  accountId: '********',
  firstName: 'Edidiong',
  mobilePhone: '',
  faxNumber: '',
  company: '',
  workPhone: '',
  state: '',
  email: '<EMAIL>',
  username: '15443799430474459747351321825104',
  invoiceCountry: 'ZA',
  invoiceZipCode: '100263',
  invoiceFirstName: 'Edidiong',
  invoiceFaxNumber: '',
  invoiceState: '',
  invoiceAddress2: '',
  invoiceEmail: '<EMAIL>',
  invoiceLastName: 'Amos',
  invoiceAddress1: '',
  invoiceCompany: '',
  invoiceCity: '',
  invoiceExtension: '',
  invoiceMobilePhone: '',
  invoiceWorkPhone: '',
  invoiceTitle: '',
  shippingCity: '',
  shippingLastName: 'Amos',
  shippingCountry: 'ZA',
  shippingZipCode: '100263',
  shippingMethod: [
    '',
    '',
  ],
  shippingFirstName: 'Edidiong',
  shippingState: '',
  shippingAddress2: '',
  shippingAddress1: '',
  dpanLastFourDigits: '',
  cardCategory: 'CLASSIC',
  invoiceAmount: '49.00',
  language: 'ENGLISH',
  creditCardType: 'VISA',
  invoiceAmountUSD: '49.00',
  productName: 'ProveSource',
  paymentType: 'CC',
  creditCardExpDate: '3/2022',
  contractOwner: '1102833',
  referenceNumber: '********',
  binCategory: 'CONSUMER',
  contractName: 'Monster Monthly Plan',
  currency: 'USD',
  cancelReason: 'not using sales pages',
  invoiceInfoURL: 'https://shoppers.bluesnap.com/jsp/order_locator_info.jsp?refId=E26D906C73EC50B8&acd=****************',
  remoteAddress: '**************',
  quantity: '1',
  productId: '1002838',
  invoiceChargeCurrency: 'USD',
  cardSubType: 'DEBIT',
  invoiceChargeAmount: '49.00',
  originalRefNum: '********',
  regulatedCard: 'N',
  transactionType: 'CANCELLATION',
  referrer: '',
  dpanExpDate: '',
  creditCardLastFourDigits: '0502',
  testMode: 'N',
  contractId: '3434694',
  paymentMethod: 'CC',
  subscriptionId: '********',
  addCD: 'N',
  untilDate: '05/20/2019 05:38 AM',
  authKey: '',
  targetBalance: 'BLUESNAP_ACCOUNT',
  taxAmountUSD: '0.00',
  taxRate: '',
  taxChargeAmount: '0.00',
  vatId: '',
  cancelledContractId: '3434694',
  overridePrice: '0.00',
  shopperOrderUrl: 'https://shoppers.bluesnap.com/jsp/order_locator_info.jsp?refId=E26D906C73EC50B8&acd=****************',
  templateId: '20801',
  licenseKey: '',
  promoteContractsNum: '0',
  bluesnapNode: '1',
  plimusNode: '1',
  couponCode: '',
  transactionDate: '04/23/2019 02:09 AM',
  merchantTransactionId: '5c07baf5feff26351251ed59',
  vendorId: '',
  vendorName: '',
  sepaMandateId: '',
  sepaIban: '',
  sepaMandateDate: '',
};

module.exports.withCompany = {
  lastName: 'Korenman',
  country: 'US',
  zipCode: '94577',
  extension: '',
  personalId: '',
  address2: '',
  city: 'San Leandro',
  address1: '821 San Leandro Blvd.',
  homePhone: '',
  brazilianId: '',
  title: '',
  accountId: '********',
  firstName: 'Ed',
  mobilePhone: '',
  faxNumber: '',
  company: 'ZippGo',
  workPhone: '',
  state: 'CA',
  email: '<EMAIL>',
  username: '15319426834025132077861565300681',
  shippingCity: '',
  shippingLastName: 'Sud',
  shippingCountry: 'US',
  shippingZipCode: '94568',
  shippingMethod: [
    '',
    '',
  ],
  shippingFirstName: 'Ash',
  shippingState: 'CA',
  shippingAddress2: '',
  shippingAddress1: '',
  invoiceCountry: 'US',
  invoiceZipCode: '94568',
  invoiceFirstName: 'Ash',
  invoiceFaxNumber: '',
  invoiceState: 'CA',
  invoiceAddress2: '',
  invoiceEmail: '<EMAIL>',
  invoiceLastName: 'Sud',
  invoiceAddress1: '',
  invoiceCompany: 'ZippGo Invoice',
  invoiceCity: '',
  invoiceExtension: '',
  invoiceMobilePhone: '',
  invoiceWorkPhone: '',
  invoiceTitle: '',
  dpanLastFourDigits: '',
  cardCategory: 'EXECUTIVE',
  invoiceAmount: '19.00',
  language: 'ENGLISH',
  creditCardType: 'MASTERCARD',
  invoiceAmountUSD: '19.00',
  productName: 'ProveSource',
  invoiceLocalCurrency: 'USD',
  paymentType: 'CC',
  creditCardExpDate: '09/2023',
  contractOwner: '1102833',
  referenceNumber: '*********',
  binCategory: 'COMMERCIAL',
  contractName: 'Starter Monthly Plan',
  currency: 'USD',
  invoiceInfoURL: 'https://shoppers.bluesnap.com/jsp/order_locator_info.jsp?refId=DCF97239150C05CDBC4473C1C9BF6801&acd=BEC3D8347866A9FE',
  remoteAddress: '',
  quantity: '1',
  contractChargePrice: '19.00',
  productId: '1002838',
  invoiceChargeCurrency: 'USD',
  cardSubType: 'CREDIT',
  shopperOrderUrl: 'https://shoppers.bluesnap.com/jsp/order_locator_info.jsp?refId=DCF97239150C05CDBC4473C1C9BF6801&acd=BEC3D8347866A9FE',
  invoiceChargeAmount: '19.00',
  originalRefNum: '*********',
  regulatedCard: '',
  transactionType: 'RECURRING',
  referrer: '',
  contractPrice: '19.00',
  dpanExpDate: '',
  creditCardLastFourDigits: '7209',
  originalReferenceNumber: '*********',
  testMode: 'N',
  contractId: '3434684',
  paymentMethod: 'CC',
  subscriptionId: '********',
  addCD: 'N',
  untilDate: '06/18/2019 12:38 PM',
  invoiceURL: 'https://www.bluesnap.com/jsp/show_invoice.jsp?ref=DCF97239150C05CDBC4473C1C9BF6801',
  templateId: '20801',
  promoteContractsNum: '0',
  targetBalance: 'BLUESNAP_ACCOUNT',
  authKey: '',
  vatId: '',
  taxAmountUSD: '0.00',
  taxRate: '',
  taxChargeAmount: '0.00',
  bluesnapNode: '1',
  plimusNode: '1',
  couponCode: '',
  licenseKey: '',
  transactionDate: '05/18/2019 12:32 PM',
  merchantTransactionId: '5b463d12d7e3e41b4d5b0172',
  vendorId: '',
  vendorName: '',
  sepaMandateId: '',
  sepaIban: '',
  sepaMandateDate: '',
  cardIssuingOrg: 'BANK OF AMERICA, N.A.',
  binNumber: '547415',
  cardIssuingCountry: 'us',
};

module.exports.firstLast = {
  lastName: 'laight',
  country: 'UK',
  zipCode: 'n10na',
  extension: '',
  personalId: '',
  address2: '',
  city: '',
  address1: '',
  homePhone: '',
  brazilianId: '',
  title: '',
  accountId: '********',
  firstName: 'heloise',
  mobilePhone: '',
  faxNumber: '',
  company: '',
  workPhone: '',
  state: '',
  email: '<EMAIL>',
  username: '1542972146334998390747324655160',
  shippingCity: '',
  shippingLastName: 'laight',
  shippingCountry: 'UK',
  shippingZipCode: 'n10na',
  shippingMethod: [
    '',
    '',
  ],
  shippingFirstName: 'heloise',
  shippingState: '',
  shippingAddress2: '',
  shippingAddress1: '',
  invoiceCountry: 'UK',
  invoiceZipCode: 'n10na',
  invoiceFirstName: 'heloise',
  invoiceFaxNumber: '',
  invoiceState: '',
  invoiceAddress2: '',
  invoiceEmail: '<EMAIL>',
  invoiceLastName: 'laight',
  invoiceAddress1: '',
  invoiceCompany: '',
  invoiceCity: '',
  invoiceExtension: '',
  invoiceMobilePhone: '',
  invoiceWorkPhone: '',
  invoiceTitle: '',
  dpanLastFourDigits: '',
  cardCategory: 'PERSONAL REVOLVE',
  invoiceAmount: '19.00',
  language: 'ENGLISH',
  creditCardType: 'AMEX',
  invoiceAmountUSD: '19.00',
  productName: 'ProveSource',
  invoiceLocalCurrency: 'USD',
  paymentType: 'CC',
  creditCardExpDate: '04/2022',
  contractOwner: '1102833',
  referenceNumber: '*********',
  binCategory: 'CONSUMER',
  contractName: 'Starter Monthly Plan',
  currency: 'USD',
  invoiceInfoURL: 'https://shoppers.bluesnap.com/jsp/order_locator_info.jsp?refId=AFB55778DF8930483282A1D6BBE83FFA&acd=34617F6B3C68B422',
  remoteAddress: '',
  quantity: '1',
  contractChargePrice: '19.00',
  productId: '1002838',
  invoiceChargeCurrency: 'USD',
  cardSubType: 'CREDIT',
  shopperOrderUrl: 'https://shoppers.bluesnap.com/jsp/order_locator_info.jsp?refId=AFB55778DF8930483282A1D6BBE83FFA&acd=34617F6B3C68B422',
  invoiceChargeAmount: '19.00',
  originalRefNum: '********',
  regulatedCard: 'N',
  transactionType: 'RECURRING',
  referrer: '',
  contractPrice: '19.00',
  dpanExpDate: '',
  creditCardLastFourDigits: '2004',
  originalReferenceNumber: '********',
  testMode: 'N',
  contractId: '3434684',
  paymentMethod: 'CC',
  subscriptionId: '********',
  addCD: 'N',
  untilDate: '06/23/2019 03:22 AM',
  invoiceURL: 'https://www.bluesnap.com/jsp/show_invoice.jsp?ref=AFB55778DF8930483282A1D6BBE83FFA',
  templateId: '20801',
  promoteContractsNum: '0',
  targetBalance: 'BLUESNAP_ACCOUNT',
  authKey: '',
  vatId: '',
  taxAmountUSD: '0.00',
  taxRate: '',
  taxChargeAmount: '0.00',
  bluesnapNode: '1',
  plimusNode: '1',
  couponCode: '',
  licenseKey: '',
  transactionDate: '05/23/2019 03:33 AM',
  merchantTransactionId: '5bf7c7eca3a2431dec2233b8',
  vendorId: '',
  vendorName: '',
  sepaMandateId: '',
  sepaIban: '',
  sepaMandateDate: '',
  cardIssuingOrg: 'AMERICAN EXPRESS UNITED KINGDOM - GLOBESTAR',
  binNumber: '371785',
  cardIssuingCountry: 'gb',
};
