const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const httpUtils = require('../httpUtils');
const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;


xdescribe('GET /billing/documents (#slow)', () => {
  let server;
  before(async () => {
    server = await require('../../server');
    if(!server) throw new Error('server not loaded');
  });

  afterEach(async () => {
    sandbox.restore();
  });

  it('return documents for account', async () => {
    const acc = AccountFactory.default();
    acc.subscription.invoiceNames = ['קונביז מדס בעמ'];
    sandbox.stub(Account, 'findOne').resolves(acc);
    const res = await httpUtils.consoleRequest(server, '/billing/documents');
    const doc = res.body[0];
    expect(doc.url).to.not.be.empty;
    expect(doc.date).to.not.be.empty;
    expect(doc.number).to.not.be.empty;
  });
});
