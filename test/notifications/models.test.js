const chai = require('chai');

const { expect } = chai;
const constants = require('../constants');
const PageVisits = require('../../app/notifications/models/PageVisits');
const Conversion = require('../../app/notifications/models/Conversion');
const notifConsts = require('../../app/notifications/constants');

describe('Model - Notification', () => {
  describe('PageVisits', () => {
    it('should check urlTypes', () => {
      const notif = new PageVisits({ urlTypes: { track: '123', display: 'hey' } });
      let result = notif.validateSync();
      expect(result.errors).to.have.property('urlTypes.track');
      expect(result.errors).to.have.property('urlTypes.display');

      notif.urlTypes = { track: notifConsts.URL_TYPES.simple, display: notifConsts.URL_TYPES.contains };
      result = notif.validateSync();
      expect(result.errors).to.not.have.property('urlTypes.track');
      expect(result.errors).to.not.have.property('urlTypes.display');
    });
  });

  describe('Conversion', () => {
    it('should check urlTypes', () => {
      const notif = new Conversion({ urlTypes: { track: '123', display: 'hey' } });
      let result = notif.validateSync();
      expect(result.errors).to.have.property('urlTypes.track');
      expect(result.errors).to.have.property('urlTypes.display');

      notif.urlTypes = { track: notifConsts.URL_TYPES.simple, display: notifConsts.URL_TYPES.contains };
      result = notif.validateSync();
      expect(result.errors).to.not.have.property('urlTypes.track');
      expect(result.errors).to.not.have.property('urlTypes.display');
    });
  });
});
