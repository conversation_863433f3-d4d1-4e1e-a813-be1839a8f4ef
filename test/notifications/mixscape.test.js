const sinon = require('sinon');
const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');

const Combo = require('../../app/notifications/models/Combo');
const { Notification } = require('../factories/NotificationFactory');
const EFactory = require('../factories/EventFactory');

describe('combo notification showing undefined', () => {
  let server; let
    sandbox;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should not return undefined', async () => {
    const { accountId } = constants;
    const params = {
      urlTypes: {
        track: 'simple',
        display: 'simple',
      },
      settings: {
        allowClose: false,
        hideExactNumber: {
          active: false,
          max: 0,
        },
        link: {
          active: true,
          value: 'https://www.devenirbeatmaker.com/collections?category=formations-mao',
        },
        timeLimit: {
          active: false,
        },
        combo: {
          period: 'week',
          type: 'conversions',
        },
        position: 'Bottom Left',
        displayHold: 10,
      },
      type: 'combo',
      manuallyShowNotification: false,
      active: true,
      refer: 'membres',
      localization: 'fr',
      __t: 'Combo',
      autoTrack: true,
      webhookId: '15a567ce9c5e55953b5f6f666c3b6cc8',
      trackURL: [
        'https://www.devenirbeatmaker.com/users/sign_in',
        'https://www.devenirbeatmaker.com/users/auth/linkedin',
        'https://courses.thinkific.com/users/auth/facebook?ss%5breferral%5d=&ss%5buser_return_to%5d=&ss%5bvisitor_id%5d=*********',
        'https://courses.thinkific.com/users/auth/google?ss%5breferral%5d=&ss%5buser_return_to%5d=&ss%5bvisitor_id%5d=*********',
      ],
      displayURLs: [
        'https://www.devenirbeatmaker.com',
      ],
      message: 'ont amélioré leurs compétences',
      name: 'Connectés Compte',
      image: 'https://cdn.provesrc.com/icon.gif',
    };
    const notif = new Combo(params);
    notif.accountId = accountId;
    const url = params.displayURLs[0];
    sandbox.stub(Notification, 'find').yields(null, [notif]);
    const event = EFactory.FormEvent(accountId, url, new Date());
    const eStub = sandbox.stub(EFactory.models.FormEvent, 'find');
    eStub.returns({ hint: () => ({ limit: () => {} }) });
    eStub.yields(null, [event]);

    const res = await httpUtils.notificationsGet(server, { url });
    expect(res).to.have.status(200);
    expect(res.body).to.have.length(1);
    expect(res.body[0].message).to.be.equal(params.message);
  });
});
