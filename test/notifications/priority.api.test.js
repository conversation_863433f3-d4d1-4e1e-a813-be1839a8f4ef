const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const httpUtils = require('../httpUtils');
const testUtils = require('../testUtils');

const constants = require('../constants');
const priority = require('../../app/notifications/priority');
const accountStats = require('../../app/account/account.stats');

const ENDPOINT = '/notifications/priority';

describe('/notifications/priority', () => {
  let server;
  before(async () => {
    server = await require('../../server');
    if(!server) throw new Error('server not loaded');
  });

  afterEach(async () => {
    sandbox.restore();
  });

  it('should fail no ids array or empty or not ObjectIds', async () => {
    let res = await httpUtils.consoleRequest(server, ENDPOINT, 'POST').send();
    expect(res).to.have.status(400);

    res = await httpUtils.consoleRequest(server, ENDPOINT, 'POST').send([]);
    expect(res).to.have.status(400);

    res = await httpUtils.consoleRequest(server, ENDPOINT, 'POST').send([123, '456']);
    expect(res).to.have.status(400);
  });

  it('should set priority', async () => {
    const changedPriority = sandbox.stub(accountStats, 'changedPriority').resolves();
    const setPriority = sandbox.stub(priority, 'setPriority').resolves();

    const ids = [testUtils.ObjectId(), testUtils.ObjectId()];
    const res = await httpUtils.consoleRequest(server, ENDPOINT, 'POST').send(ids);
    expect(res).to.have.status(200);

    const stringIds = JSON.parse(JSON.stringify(ids));
    expect(setPriority).to.have.been.calledWith(httpUtils.accountId, sinon.match.array.deepEquals(stringIds));
    expect(changedPriority).to.have.been.calledWith(httpUtils.accountId);
  });

  it('should return 500 on processing error', async () => {
    const err = Error('because of reasons');
    sandbox.stub(priority, 'setPriority').throws(err);

    const ids = [testUtils.ObjectId(), testUtils.ObjectId()];
    const res = await httpUtils.consoleRequest(server, ENDPOINT, 'POST').send(ids);

    expect(res).to.have.status(500);
    expect(res.body.error).to.include(err.message);
  });
});
