const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');
const factory = require('../factories/NotificationFactory');
const redis = require('../../lib/redisClient').getClient();

describe('/notification/get ps_debug=true', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    await factory.Notification.remove();
  });

  afterEach(async () => {
    await redis.flushallAsync();
  });

  it('should return {active=false} notification when {ps_debug=true}', async () => {
    const notification = factory.PageVisits(constants.accountId);
    notification.active = false;

    const track = notification.trackURL[0];
    await Promise.all([
      notification.save(),
      httpUtils.eventTrack(server, { url: track, unique: true }),
    ]);

    const url = notification.displayURLs[0];
    const res = await httpUtils.notificationsGet(server, { url, headers: { Cookie: 'ps_debug=true' } });
    expect(res.body).to.have.lengthOf(1);
  });

  it('should bypass visitor limit check', async () => {
    const notification = factory.PageVisits(constants.accountId);
    notification.active = false;

    const track = notification.trackURL[0];
    await Promise.all([
      notification.save(),
      redis.hmset(constants.accountId, 'limit', 10, 'visitorCount', 50, 'expires', new Date('2038'), 'cycleDate', new Date('2038')),
      httpUtils.eventTrack(server, { url: track, unique: true }),
    ]);

    const url = notification.displayURLs[0];
    const res = await httpUtils.notificationsGet(server, { url, headers: { Cookie: 'ps_debug=true' } });
    expect(res.body).to.have.lengthOf(1);
  });
});
