const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');
const testUtils = require('../testUtils');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();

const dateUtils = require('../../lib/utils/dateUtils');

const NotificationFactory = require('../factories/NotificationFactory');

const { Notification } = NotificationFactory;
const notifConstants = NotificationFactory.notificationConstants;
const AnalyticsEventFactory = require('../factories/AnalyticsEventFactory');

const { AnalyticsEvent } = AnalyticsEventFactory;
const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;

describe('/notifications/analytics', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    await AnalyticsEvent.remove();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('POST', () => {
    it('should create a Notifications and Account AnalyticsEvent', async () => {
      const { accountId } = constants;
      const notification = NotificationFactory.PageVisits(accountId);
      await notification.save();

      const notificationId = notification._id;
      const event = notifConstants.ANALYTICS_EVENTS.view;
      await httpUtils.sendAnalytics(server, { id: notification._id, event });

      await testUtils.sleep(50);

      const [event1, event2] = await Promise.all([
        AnalyticsEvent.findOne({ accountId, notificationId }),
        AnalyticsEvent.findOne({ accountId, notificationId: null }),
      ]);
      expect(event1, 'notification level AnalyticsEvent').to.exist;
      expect(event2, 'account level AnalyticsEvent').to.exist;

      expect(event1.date.getTime()).to.be.equal(dateUtils.todayNormalized12am());
      expect(event2.date.getTime()).to.be.equal(dateUtils.todayNormalized12am());
    });

    it('should support views', async () => {
      const { accountId } = constants;
      const notification = NotificationFactory.PageVisits(accountId);
      await notification.save();

      const notificationId = notification._id;
      const event = notifConstants.ANALYTICS_EVENTS.view;
      await httpUtils.sendAnalytics(server, { id: notification._id, event });

      await testUtils.sleep(50);

      const [event1, event2] = await Promise.all([
        AnalyticsEvent.findOne({ accountId, notificationId }),
        AnalyticsEvent.findOne({ accountId, notificationId: null }),
      ]);

      expect(event1.total.views).to.be.equal(1);
      expect(event2.total.views).to.be.equal(1);
    });

    it('should support segmentUserId', async () => {
      const { accountId } = constants;
      const notification = NotificationFactory.PageVisits(accountId);
      await notification.save();
      const event = notifConstants.ANALYTICS_EVENTS.view;
      const res = await httpUtils.sendAnalytics(server, {
			  id: notification.id,
        event,
        extraData: {
			    segmentUserId: '12345',
			  },
      });
      expect(res).to.have.status(200);
    });

    it('should support hovers', async () => {
      const { accountId } = constants;
      const notification = NotificationFactory.PageVisits(accountId);
      await notification.save();

      const notificationId = notification._id;
      const event = notifConstants.ANALYTICS_EVENTS.hover;
      await httpUtils.sendAnalytics(server, { id: notification._id, event });

      const [event1, event2] = await Promise.all([
        AnalyticsEvent.findOne({ accountId, notificationId }),
        AnalyticsEvent.findOne({ accountId, notificationId: null }),
      ]);

      expect(event1.total.hovers).to.be.equal(1);
      expect(event2.total.hovers).to.be.equal(1);
    });

    it('should support clicks', async () => {
      const { accountId } = constants;
      const notification = NotificationFactory.PageVisits(accountId);
      await notification.save();

      const notificationId = notification._id;
      const event = notifConstants.ANALYTICS_EVENTS.click;
      await httpUtils.sendAnalytics(server, { id: notification._id, event });

      const [event1, event2] = await Promise.all([
        AnalyticsEvent.findOne({ accountId, notificationId }),
        AnalyticsEvent.findOne({ accountId, notificationId: null }),
      ]);
      expect(event1.total.clicks).to.be.equal(1);
      expect(event2.total.clicks).to.be.equal(1);
    });

    it('should not accept unknown events', async () => {
      const { accountId } = constants;
      const notification = NotificationFactory.PageVisits(accountId);
      const event = 'blaasdfafh';
      const res = await httpUtils.sendAnalytics(server, { id: notification._id, event });
      expect(res).to.have.status(400);
    });

    it('should not count events if notification not found', async () => {
      const { accountId } = constants;
      const notification = NotificationFactory.PageVisits(accountId);
      const notificationId = notification._id;
      const event = notifConstants.ANALYTICS_EVENTS.view;
      await httpUtils.sendAnalytics(server, { id: notification._id, event });

      const [event1, event2] = await Promise.all([
        AnalyticsEvent.findOne({ accountId, notificationId }),
        AnalyticsEvent.findOne({ accountId, notificationId: null }),
      ]);
      expect(event1).to.be.null;
      expect(event2).to.be.null;
    });

    it('should count visitor in analytics', async () => {
      sandbox.stub(Account, 'findOne').resolves(AccountFactory.default());
      sandbox.stub(Account.prototype, 'save').resolves();
      await httpUtils.accountConfiguration(server);

      const event = await AnalyticsEvent.findOne({ accountId: constants.accountId, notificationId: null });
      expect(event.total.visitors).to.be.equal(1);
    });

    it('should count engaged visitors', async () => {
      const { accountId } = constants;
      const notification = NotificationFactory.PageVisits(accountId);
      sandbox.stub(Notification, 'findOne').returns({ select: () => notification });

      const { view } = notifConstants.ANALYTICS_EVENTS;
      await httpUtils.sendAnalytics(server, { id: notification._id, event: view, visitor: true });

      const event = await AnalyticsEvent.findOne({ accountId, notificationId: null });
      expect(event.total.engagedVisitors).to.be.equal(1);
    });
  });

  describe('GET', () => {
    it('should return analytics for notification (max 7 days back)', async () => {
      const { accountId } = constants;
      const notification = NotificationFactory.PageVisits(accountId);
      const notificationId = notification._id;
      const event = AnalyticsEventFactory.makeEvent({ accountId, notificationId });
      const event2 = AnalyticsEventFactory.makeEvent({
        accountId,
        notificationId,
        date: dateUtils.todayWithAddedDays(-2),
      });
      const event3 = AnalyticsEventFactory.makeEvent({
        accountId,
        notificationId,
        date: dateUtils.todayWithAddedDays(-8),
      }); // Also check that it's only 7 days back
      await Promise.all([event.save(), event2.save(), event3.save()]);

      const res = await httpUtils.getNotificationAnalytics(server, { id: notificationId });
      expect(res.body.views).to.be.equal(event.total.views + event2.total.views);
      expect(res.body.hovers).to.be.equal(event.total.hovers + event2.total.hovers);
      expect(res.body.clicks).to.be.equal(event.total.clicks + event2.total.clicks);
      expect(res.body.dates).to.have.lengthOf(2);
      const date = new Date(res.body.dates[0].date);
      expect(date.getTime()).to.be.equal(event.date.getTime());
    });

    it('should return analytics for all notifications (max 7 days back)', async () => {
      const { accountId } = constants;
      const event = AnalyticsEventFactory.makeEvent({ accountId });
      const event2 = AnalyticsEventFactory.makeEvent({ accountId, date: dateUtils.todayWithAddedDays(-2) });
      const event3 = AnalyticsEventFactory.makeEvent({ accountId, date: dateUtils.todayWithAddedDays(-8) });
      await Promise.all([event.save(), event2.save(), event3.save()]);

      const res = await httpUtils.getNotificationAnalytics(server);
      expect(res.body.views).to.be.equal(event.total.views + event2.total.views);
      expect(res.body.hovers).to.be.equal(event.total.hovers + event2.total.hovers);
      expect(res.body.clicks).to.be.equal(event.total.clicks + event2.total.clicks);
      expect(res.body.conversions).to.be.equal(event.total.conversions + event2.total.conversions);
      expect(res.body.viewConversions).to.be.equal(event.total.viewConversions + event2.total.viewConversions);
      expect(res.body.hoverConversions).to.be.equal(event.total.hoverConversions + event2.total.hoverConversions);
      expect(res.body.clickConversions).to.be.equal(event.total.clickConversions + event2.total.clickConversions);
      expect(res.body.webhooks).to.be.equal(event.total.webhooks + event2.total.webhooks);
      expect(res.body.woocommerceOrders).to.be.equal(event.total.woocommerceOrders + event2.total.woocommerceOrders);
      expect(res.body.dates).to.have.lengthOf(2);

      const resEvent = res.body.dates[0];
      const keys = [
        'date', 'views', 'hovers', 'clicks',
        'conversions', 'viewConversions', 'hoverConversions', 'clickConversions',
        'webhooks', 'woocommerceOrders', 'engagedVisitors', 'visitors',
      ];
      expect(resEvent).to.have.keys(keys);
      const date = new Date(res.body.dates[0].date);
      expect(date.getTime()).to.be.equal(event.date.getTime());
    });

    it('should analytics based on dates', async () => {
      const { accountId } = constants;
      const start = dateUtils.todayWithAddedDays(-8);
      const end = dateUtils.todayWithAddedDays(-7);
      const event = AnalyticsEventFactory.makeEvent({ accountId, date: start });
      const event2 = AnalyticsEventFactory.makeEvent({ accountId, date: dateUtils.todayWithAddedDays(1) });
      await Promise.all([event.save(), event2.save()]);

      const startDate = `${start.getUTCFullYear()}-${start.getUTCMonth() + 1}-${start.getUTCDate()}`;
      const endDate = `${end.getUTCFullYear()}-${end.getUTCMonth() + 1}-${end.getUTCDate()}`;
      const res = await httpUtils.getNotificationAnalytics(server, { startDate, endDate });
      expect(res.body.views).to.be.equal(event.total.views);
      expect(res.body.hovers).to.be.equal(event.total.hovers);
      expect(res.body.clicks).to.be.equal(event.total.clicks);
      expect(res.body.visitors).to.be.equal(event.total.visitors);
      expect(res.body.engagedVisitors).to.be.equal(event.total.engagedVisitors);
      expect(res.body.dates).to.have.lengthOf(1);
      const eventDate = new Date(res.body.dates[0].date);
      expect(eventDate.getTime()).to.be.equal(event.date.getTime());
      expect(res.body.dates[0]).to.have.include.keys('visitors', 'views', 'hovers', 'clicks', 'engagedVisitors');
    });

    it('should switch dates if the other way around', async () => {
      const { accountId } = constants;
      const start = dateUtils.todayWithAddedDays(-7);
      const end = dateUtils.todayWithAddedDays(-8);
      const event = AnalyticsEventFactory.makeEvent({ accountId, date: start });
      const event2 = AnalyticsEventFactory.makeEvent({ accountId, date: dateUtils.todayWithAddedDays(1) });
      await Promise.all([event.save(), event2.save()]);

      const startDate = `${start.getUTCFullYear()}-${start.getUTCMonth() + 1}-${start.getUTCDate()}`;
      const endDate = `${end.getUTCFullYear()}-${end.getUTCMonth() + 1}-${end.getUTCDate()}`;
      const res = await httpUtils.getNotificationAnalytics(server, { startDate, endDate });
      expect(res.body.views).to.be.equal(event.total.views);
      expect(res.body.hovers).to.be.equal(event.total.hovers);
      expect(res.body.clicks).to.be.equal(event.total.clicks);
      expect(res.body.dates).to.have.lengthOf(1);
      const eventDate = new Date(res.body.dates[0].date);
      expect(eventDate.getTime()).to.be.equal(event.date.getTime());
    });

    it('should support startDate == endDate', async () => {
      const { accountId } = constants;
      const start = dateUtils.todayWithAddedDays(-7);
      const end = start;
      const event = AnalyticsEventFactory.makeEvent({ accountId, date: start });
      const event2 = AnalyticsEventFactory.makeEvent({ accountId, date: dateUtils.todayWithAddedDays(1) });
      await Promise.all([event.save(), event2.save()]);

      const startDate = `${start.getUTCFullYear()}-${start.getUTCMonth() + 1}-${start.getUTCDate()}`;
      const endDate = `${end.getUTCFullYear()}-${end.getUTCMonth() + 1}-${end.getUTCDate()}`;
      const res = await httpUtils.getNotificationAnalytics(server, { startDate, endDate });
      expect(res.body.views).to.be.equal(event.total.views);
      expect(res.body.hovers).to.be.equal(event.total.hovers);
      expect(res.body.clicks).to.be.equal(event.total.clicks);
      expect(res.body.dates).to.have.lengthOf(1);
      const eventDate = new Date(res.body.dates[0].date);
      expect(eventDate.getTime()).to.be.equal(event.date.getTime());
    });

    it('should work when no endDate', async () => {
      const { accountId } = constants;
      const start = dateUtils.todayWithAddedDays(-7);
      const event = AnalyticsEventFactory.makeEvent({ accountId, date: start });
      const event2 = AnalyticsEventFactory.makeEvent({ accountId, date: Date.now() });
      await Promise.all([event.save(), event2.save()]);

      const startDate = `${start.getUTCFullYear()}-${start.getUTCMonth() + 1}-${start.getUTCDate()}`;
      const res = await httpUtils.getNotificationAnalytics(server, { startDate });
      expect(res.body.views).to.be.equal(event.total.views + event2.total.views);
      expect(res.body.hovers).to.be.equal(event.total.hovers + event2.total.hovers);
      expect(res.body.clicks).to.be.equal(event.total.clicks + event2.total.clicks);
      expect(res.body.dates).to.have.lengthOf(2);
      const eventDate = new Date(res.body.dates[0].date);
      expect(eventDate.getTime()).to.be.equal(event2.date.getTime());
    });
  });
});
