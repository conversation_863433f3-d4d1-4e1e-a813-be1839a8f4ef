const chai = require('chai');

const { expect } = chai;
const chaiHttp = require('chai-http');

chai.use(chaiHttp);
const _ = require('lodash');
const sinon = require('sinon');
const httpUtils = require('../../httpUtils');

const sandbox = sinon.createSandbox();

const serverPromise = require('../../../server');
const constants = require('../../constants');
const dateUtils = require('../../../lib/utils/dateUtils');

const Notification = require('../../../app/notifications/models/Notification');
const Factory = require('../../factories/NotificationFactory');

const { notifications } = Factory;
const notifConstants = require('../../../app/notifications/constants');

const EventFactory = require('../../factories/EventFactory');
const Event = require('../../../app/events/models/Event');
const WebsiteEvent = require('../../../app/events/models/WebsiteEvent');

const AccountFactory = require('../../factories/AccountFactory');

const { Account } = AccountFactory;

const NOTIFICATIONS_GET = '/notifications/get';
const TRACK_WEBHOOK = '/webhooks/track';
const TRACK_EVENT = '/events/track';
const TRACK_FORM = '/events/trackForm';

describe('/notifications/get old', () => {
  let server;
  before(() => serverPromise.then((app) => {
    server = app;
    return Notification.remove({});
  }).then(() => {
    // let {mobile, conversion, conversionWithDisplayUrls, pageVisits} = notifications;
    // return Promise.all([mobile.save(), conversion.save(), conversionWithDisplayUrls.save(), pageVisits.save()]);
    const items = _.map(notifications, item => item.save());
    return Promise.all(items);
  }));

  let getNotificationsRequest;
  beforeEach(async () => {
    sandbox.stub(Account, 'findOne').resolves(AccountFactory.default());

    getNotificationsRequest = chai.request(server).post(NOTIFICATIONS_GET)
      .set('authorization', constants.authorizationHeader)
      .set('x-ps-uid', '12345');

    await Promise.all([Event.remove(), Notification.remove()]);
  });

  afterEach(async () => {
    sandbox.restore();
  });

  it('should not return notifications with {count=0} (no events in filter)', () => {
    const testNotif = Factory.notificationsFactory.conversion();
    return testNotif.save().then(() => httpUtils.notificationsGet(server).send({ name: testNotif.name }))
      .then((res) => {
        expect(res).to.have.status(200);
        expect(res.body).to.be.an('array');
        expect(res.body).to.have.lengthOf(0);
      });
  });

  it('should return expected properties', async () => {
    const notification = Factory.PageVisits(constants.accountId);
    const cta = { active: true, text: 'woohoo', color: '#AA1234' };
    notification.settings.link = { cta };
    await notification.save();

    await httpUtils.notificationsGet(server).send({ url: notification.trackURL[0], unique: true });
    const res = await httpUtils.notificationsGet(server).send({ url: notification.displayURLs[0] });
    const notif = res.body[0];
    expect(notif).to.have.property('localization');
    expect(notif.settings.link.cta).to.be.deep.equal(cta);
  });

  describe('url contains', () => {
    it('should a notification when {displayURLs contains}', async () => {
      const notification = Factory.notificationsFactory.pageVisits();
      notification.urlTypes = { display: notifConstants.URL_TYPES.contains, track: notifConstants.URL_TYPES.simple };
      notification.displayURLs[0] = 'zubi';

      const trackUrl = notification.trackURL[0];
      let res = await httpUtils.eventTrack(server).send({ url: trackUrl, unique: true });
      expect(res).to.have.status(200);

      await notification.save();
      const url = `https://www.google.com/${notification.displayURLs[0]}`;
      res = await httpUtils.notificationsGet(server).send({ url });
      expect(res).to.have.status(200);
      expect(res.body).to.have.lengthOf(1);
      expect(res.body[0]).to.have.property('count', 1);
    });

    it('should find events and return a notification when {trackURL contains}', async () => {
      const notification = Factory.notificationsFactory.pageVisits();
      notification.urlTypes = { display: notifConstants.URL_TYPES.simple, track: notifConstants.URL_TYPES.contains };
      notification.trackURL[0] = 'zubi';

      const trackUrl = `https://google.com/${notification.trackURL[0]}`;
      let [res] = await Promise.all([
        httpUtils.eventTrack(server).send({ url: trackUrl, unique: true }),
        notification.save(),
      ]);
      expect(res).to.have.status(200);

      const url = notification.displayURLs[0];
      res = await httpUtils.notificationsGet(server).send({ url });
      expect(res).to.have.status(200);
      expect(res.body).to.have.lengthOf(1);
      expect(res.body[0]).to.have.property('count', 1);
    });
  });

  describe('{settings.allTimeEvents=true}', () => {
    beforeEach(async () => {
      await Promise.all([Event.remove(), Notification.remove()]);
    });

    it('should pull events from all time', async () => {
      const url = 'http://provesrc.com/home';
      const now = (new Date()).normalizeTo12Am();
      const notification = Factory.PageVisits(constants.accountId, url, url);
      notification.settings = Object.assign(notification.settings, { allTimeEvents: true });
      const event1 = EventFactory.WebsiteEvent(constants.accountId, url, now);
      const event2 = EventFactory.WebsiteEvent(constants.accountId, url, Date.dateByAddingDays(now, -1));
      await Promise.all([notification.save(), event1.save(), event2.save()]);

      const expectedTotal = event1.getTotal().sum + event2.getTotal().sum;
      const expectedMinutes = (Date.MINUTES_IN_DAY - event2.getTotal().minMinute) + Date.minuteOfTheDay();

      const res = await httpUtils.notificationsGet(server).send({ url, unique: false });
      expect(res).to.have.status(200);
      expect(res.body).to.have.lengthOf(1);
      expect(res.body[0]).to.have.property('count', expectedTotal);
      expect(res.body[0]).to.have.property('minutes', expectedMinutes);
    });

    it('should calculate minutes correctly based on event date', async () => {
      const url = 'http://provesrc.com/home';
      const notification = Factory.PageVisits(constants.accountId, url, url);
      notification.settings = Object.assign(notification.settings, { allTimeEvents: true });

      const days = Math.floor(Math.random() * 5 + 1);
      const date = Date.dateByAddingDays(dateUtils.todayNormalized12am(), -days);
      const event1 = EventFactory.WebsiteEvent(constants.accountId, url, date);

      await Promise.all([notification.save(), event1.save()]);

      const expectedTotal = event1.getTotal().sum;
      const expectedMinutes = Date.minuteOfTheDay()
        + (Date.MINUTES_IN_DAY * (days - 1))
        + (Date.MINUTES_IN_DAY - event1.getTotal().minMinute);

      const res = await httpUtils.notificationsGet(server).send({ url, unique: false });
      expect(res).to.have.status(200);
      expect(res.body).to.have.lengthOf(1);
      expect(res.body[0]).to.have.property('count', expectedTotal);
      expect(res.body[0]).to.have.property('minutes', expectedMinutes);
    });
  });

  describe('multiple trackURLs', () => {
    beforeEach(async () => {
      await Promise.all([Event.remove(), Notification.remove()]);
    });

    it('should return aggregate of all event', async () => {
      const url = 'http://provesrc.com/home';
      const url2 = 'http://provesrc.com/about';
      const notification = Factory.PageVisits(constants.accountId, [url, url2], url);
      notification.settings = Object.assign(notification.settings, { allTimeEvents: true });

      const now = (new Date()).normalizeTo12Am();
      const event1 = EventFactory.WebsiteEvent(constants.accountId, url, now);
      const event2 = EventFactory.WebsiteEvent(constants.accountId, url2, now);

      await Promise.all([notification.save(), event1.save(), event2.save()]);

      const expectedTotal = event1.getTotal().sum + event2.getTotal().sum;
      const minMinute = Math.min(event1.getTotal().minMinute, event2.getTotal().minMinute);
      const expectedMinutes = (Date.minuteOfTheDay() - minMinute);

      const res = await httpUtils.notificationsGet(server).send({ url, unique: false });
      expect(res).to.have.status(200);
      expect(res.body).to.have.lengthOf(1);
      expect(res.body[0]).to.have.property('count', expectedTotal);
      expect(res.body[0]).to.have.property('minutes', expectedMinutes);
    });

    it('should return aggregate multiple events', async () => {
      const url = 'http://provesrc.com/home';
      const url2 = 'http://provesrc.com/about';
      const notification = Factory.PageVisits(constants.accountId, [url, url2], url);
      notification.settings = Object.assign(notification.settings, { allTimeEvents: false });

      const now = (new Date()).normalizeTo12Am();
      const event1 = EventFactory.WebsiteEvent(constants.accountId, url, now);
      const event2 = EventFactory.WebsiteEvent(constants.accountId, url2, now);

      const expectedTotal = event1.getTotal().sum + event2.getTotal().sum;
      const minMinute = Math.min(event1.getTotal().minMinute, event2.getTotal().minMinute);
      const expectedMinutes = (Date.minuteOfTheDay() - minMinute);

      // This is curcial
      notification.settings.minimumToDisplay = expectedTotal;

      await Promise.all([notification.save(), event1.save(), event2.save()]);

      const res = await httpUtils.notificationsGet(server).send({ url, unique: false });
      expect(res).to.have.status(200);
      expect(res.body).to.have.lengthOf(1);
      expect(res.body[0]).to.have.property('count', expectedTotal);
      expect(res.body[0]).to.have.property('minutes', expectedMinutes);
    });
  });

  describe('get conversion/webhook notifications', () => {
    it('should get all conversion notifications', (done) => {
      const CONVERSION_GUID = '123';
      const notification = Factory.notificationsFactory.conversion();
      notification.save().then(() => {
        const { webhookId } = notification;
        return Promise.all([
          trackWebhook(webhookId, CONVERSION_GUID),
          trackWebhook(webhookId, CONVERSION_GUID),
        ]);
      }).then(() => {
        const body = { name: notification.name, guid: CONVERSION_GUID };
        return getNotificationsRequest.send(body);
      }).then((res) => {
        expect(res).to.have.status(200);
        expect(res.body).to.be.an('array');
        expect(res.body).to.have.lengthOf(1);
        expect(res.body[0]).to.have.property('count', 2);
        expect(res.body[0]).to.have.property('minutes', 0);
        done();
      })
        .catch(done);
    });

    it('should get notification with name and guid', (done) => {
      const notification = Factory.notificationsFactory.conversion();
      notification.save().then(() => trackWebhook(notification.webhookId)).then(() => getNotificationsRequest.send({ name: notification.name })).then((res) => {
        expect(res).to.have.status(200);
        expect(res.body).to.be.an('array');
        expect(res.body).to.have.lengthOf(1);
        expect(res.body[0]).to.have.property('count', 1);
        expect(res.body[0]).to.have.property('minutes', 0);
        done();
      })
        .catch(done);
    });

    it('should not return notification {autoTrack=false}', () => {
      const url = notifications.conversionWithTrackUrlsNoAutoTrack.trackURL[0];
      return trackUrl(url).then(() => getNotificationsRequest.send({ url })).then((res) => {
        expect(res.body).to.have.length(0);
      });
    });

    it('should return notification {autoTrack=true}', () => {
      const notification = Factory.notificationsFactory.conversionWithTrackUrls();
      const url = notification.trackURL[0];
      return notification.save()
        .then(() => trackForm(url))
        .then(() => getNotificationsRequest.send({ url }))
        .then((res) => {
          expect(res.body).to.have.length(1);
        });
    });
  });

  describe('get pageVisits', () => {
    let day;
    before(() => {
      day = dateUtils.todayNormalized12am();
    });

    it('should track url when {unique=true}', (done) => {
      const { accountId } = constants;
      const url = 'https://provesrc.com';
      const minuteOfDay = dateUtils.minuteOfTheDay();
      let count = 0;
      const find = { accountId, url, day };
      WebsiteEvent.findOne(find)
        .then((event) => {
          if(event && event.counts) count = event.counts[minuteOfDay] || 0;
        })
        .then(() => getNotificationsRequest.send({ url, unique: true }))
        .then(() => WebsiteEvent.findOne(find))
        .then((event) => {
          expect(event).to.exist;
          expect(event.counts).to.have.property(minuteOfDay, count + 1);
          done();
        })
        .catch(done);
    });

    it('should not track url when {unique=false}', () => {
      const { accountId } = constants;
      const url = 'https://provesrc.com';
      const minuteOfDay = dateUtils.minuteOfTheDay();
      let count = 0;
      const find = { accountId, url, day };
      return trackUrl(url)
        .then(() => WebsiteEvent.findOne(find))
        .then((event) => {
          if(event && event.counts) count = event.counts[minuteOfDay] || 0;
        })
        .then(() => getNotificationsRequest.send({ url, unique: false }))
        .then(() => WebsiteEvent.findOne(find))
        .then((event) => {
          expect(event.counts).to.have.property(minuteOfDay, count);
        });
    });
  });

  describe('no events today or not enough', () => {
    beforeEach(async () => {
      await Promise.all([Event.remove(), Notification.remove()]);
    });

    it('should return notification even if no events today', async () => {
      const { accountId } = constants;
      const url = 'https://provesrc.com/about';
      const event = EventFactory.WebsiteEvent(accountId, url, new Date(Date.now() - Date.MILLISECONDS_IN_DAY));

      const notification = Factory.PageVisits(accountId, url, url);
      await Promise.all([event.save(), notification.save()]);

      const res = await httpUtils.notificationsGet(server).send({ url });
      expect(res.body).to.have.lengthOf(1);
    });

    it('should return a notification even not enough events today (for minimum)', async () => {
      const { accountId } = constants;
      const url = 'https://provesrc.com/about';
      const event1 = EventFactory.WebsiteEvent(accountId, url, new Date(Date.now()));
      const event2 = EventFactory.WebsiteEvent(accountId, url, new Date(Date.now() - Date.MILLISECONDS_IN_DAY));

      const notification = Factory.PageVisits(accountId, url, url);
      const todayTotal = event1.getTotal().sum;
      notification.settings.minimumToDisplay = todayTotal + 1;

      await Promise.all([event1.save(), event2.save(), notification.save()]);

      const res = await httpUtils.notificationsGet(server).send({ url });
      expect(res.body).to.have.lengthOf(1);
      expect(res.body[0]).to.have.property('count').at.least(todayTotal + 1);
    });
  });


  // region helpers
  function trackWebhook(webhook, guid) {
    const url = `${TRACK_WEBHOOK}/${webhook}`;
    return chai.request(server)
      .post(url)
      .set('authorization', constants.authorizationHeader)
      .send({ guid });
  }

  function trackUrl(url) {
    return chai.request(server)
      .post(TRACK_EVENT)
      .set('authorization', constants.authorizationHeader)
      .send({ url, unique: true });
  }

  function trackForm(url) {
    return httpUtils.apiRequest(server, TRACK_FORM, httpUtils.METHODS.POST).send({ url, email: '<EMAIL>' });
  }
  // endregion
});
