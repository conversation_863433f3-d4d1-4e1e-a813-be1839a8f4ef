const chai = require('chai');
const httpUtils = require('../../httpUtils');

const { expect } = chai;
const constants = require('../../constants');
const serverP = require('../../../server');

const dateUtils = require('../../../lib/utils/dateUtils');

const Event = require('../../../app/events/models/Event');
const Combo = require('../../../app/notifications/models/Combo');
const Factory = require('../../factories/NotificationFactory');
const EventFactory = require('../../factories/EventFactory');
const NotifConsts = require('../../../app/notifications/constants');

describe('/notification/get combo', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    await Promise.all([Event.remove(), Combo.remove()]);
  });

  afterEach(async () => {
    await Promise.all([Event.remove(), Combo.remove()]);
  });

  it('should get a conversions from last day', async () => {
    const period = NotifConsts.COMBO_PERIODS.day;
    const type = NotifConsts.COMBO_TYPES.conversions;
    const combo = Factory.Combo(constants.accountId, type, period);
    combo.autoTrack = true;

    const { accountId } = constants;
    const url = combo.trackURL[0];
    const date = dateUtils.todayNormalized12am();
    const event1 = EventFactory.FormEvent(accountId, url, date);
    const event2 = EventFactory.FormEvent(accountId, url, Date.dateByAddingDays(date, -1));

    const minute = Date.minuteOfTheDay();
    const total = event1.getTotal().sum + event2.getTotal(minute).sum; // total 24 hours ago

    await Promise.all([event1.save(), event2.save(), combo.save()]);

    const res = await httpUtils.notificationsGet(server).send({ url });
    expect(res.body).to.have.lengthOf(1);
    expect(res.body[0]).to.have.property('count', total);
    expect(res.body[0]).to.have.property('minutes', Date.MINUTES_IN_DAY);
  });

  it('should not return notification if minimumToDisplay not reached', async () => {
    const { accountId } = constants;

    const period = NotifConsts.COMBO_PERIODS.day;
    const type = NotifConsts.COMBO_TYPES.conversions;
    const combo = Factory.Combo(accountId, type, period);

    const url = combo.trackURL[0];
    const date = dateUtils.todayNormalized12am();
    const event1 = EventFactory.FormEvent(accountId, url, date);
    const event2 = EventFactory.FormEvent(accountId, url, Date.dateByAddingDays(date, -1));

    const minute = Date.minuteOfTheDay();
    const total = event1.getTotal().sum + event2.getTotal(minute).sum; // total 24 hours ago

    combo.settings.minimumToDisplay = total + 1;
    combo.autoTrack = true;

    await Promise.all([event1.save(), event2.save(), combo.save()]);

    const res = await httpUtils.notificationsGet(server).send({ url });
    expect(res.body).to.have.lengthOf(0);
  });

  it('should get a visits from last day', async () => {
    const period = NotifConsts.COMBO_PERIODS.day;
    const type = NotifConsts.COMBO_TYPES.visits;
    const { accountId } = constants;

    const combo = Factory.Combo(accountId, type, period);
    combo.autoTrack = false;
    const url = combo.trackURL[0];
    const date = dateUtils.todayNormalized12am();
    const event1 = EventFactory.WebsiteEvent(accountId, url, date);
    const event2 = EventFactory.WebsiteEvent(accountId, url, Date.dateByAddingDays(date, -1));
    await Promise.all([event1.save(), event2.save(), combo.save()]);

    const minute = Date.minuteOfTheDay();
    const total = event1.getTotal().sum + event2.getTotal(minute).sum; // total 24 hours ago

    const res = await httpUtils.notificationsGet(server).send({ url });
    expect(res.body).to.have.lengthOf(1);
    expect(res.body[0]).to.have.property('count', total);
    expect(res.body[0]).to.have.property('minutes', Date.MINUTES_IN_DAY);
  });

  it('should get a conversions from last week', async () => {
    const period = NotifConsts.COMBO_PERIODS.week;
    const type = NotifConsts.COMBO_TYPES.conversions;
    const combo = Factory.Combo(constants.accountId, type, period);
    combo.autoTrack = true;

    const { accountId } = constants;
    const url = combo.trackURL[0];
    const date = dateUtils.todayNormalized12am();
    const event1 = EventFactory.FormEvent(accountId, url, date);
    const event2 = EventFactory.FormEvent(accountId, url, Date.dateByAddingDays(date, -1));
    const event3 = EventFactory.FormEvent(accountId, url, Date.dateByAddingDays(date, -7));

    await Promise.all([event1.save(), event2.save(), event3.save(), combo.save()]);

    const minute = Date.minuteOfTheDay();
    const total = event1.getTotal().sum + event2.getTotal().sum + event3.getTotal(minute).sum;

    const res = await httpUtils.notificationsGet(server).send({ url });
    expect(res.body).to.have.lengthOf(1);
    expect(res.body[0]).to.have.property('count', total);
    expect(res.body[0]).to.have.property('minutes', Date.MINUTES_IN_DAY * 7);
  });

  it('should get a conversions from last month', async () => {
    const period = NotifConsts.COMBO_PERIODS.month;
    const type = NotifConsts.COMBO_TYPES.conversions;
    const combo = Factory.Combo(constants.accountId, type, period);
    combo.autoTrack = true;

    const { accountId } = constants;
    const url = combo.trackURL[0];
    const date = dateUtils.todayNormalized12am();
    const event1 = EventFactory.FormEvent(accountId, url, date);
    const event2 = EventFactory.FormEvent(accountId, url, Date.dateByAddingDays(date, -1));
    const event3 = EventFactory.FormEvent(accountId, url, Date.dateByAddingDays(date, -30));

    await Promise.all([event1.save(), event2.save(), event3.save(), combo.save()]);

    const minute = Date.minuteOfTheDay();
    const total = event1.getTotal().sum + event2.getTotal().sum + event3.getTotal(minute).sum;

    const res = await httpUtils.notificationsGet(server).send({ url });
    expect(res.body).to.have.lengthOf(1);
    expect(res.body[0]).to.have.property('count', total);
    expect(res.body[0]).to.have.property('minutes', Date.MINUTES_IN_DAY * 30);
  });

  it('should get a conversion from all time', async () => {
    const period = NotifConsts.COMBO_PERIODS.all;
    const type = NotifConsts.COMBO_TYPES.conversions;
    const days = 100;
    const combo = Factory.Combo(constants.accountId, type, period);
    combo.autoTrack = true;

    const { accountId } = constants;
    const url = combo.trackURL[0];
    const date = dateUtils.todayNormalized12am();
    const event1 = EventFactory.FormEvent(accountId, url, date);
    const event2 = EventFactory.FormEvent(accountId, url, Date.dateByAddingDays(date, -1));
    const event3 = EventFactory.FormEvent(accountId, url, Date.dateByAddingDays(date, -days));

    await Promise.all([event1.save(), event2.save(), event3.save(), combo.save()]);

    const total = event1.getTotal().sum + event2.getTotal().sum + event3.getTotal().sum;
    const { minMinute } = event3.getTotal();
    const millisInMinute = dateUtils.MILLISECONDS_IN_MINUTE;
    const expectedMinutes = Math.floor((Date.now() - (event3.getTime() + minMinute * millisInMinute)) / millisInMinute);

    const res = await httpUtils.notificationsGet(server).send({ url });
    expect(res.body).to.have.lengthOf(1);
    expect(res.body[0]).to.have.property('count', total);
    expect(res.body[0]).to.have.property('minutes', expectedMinutes);
  });

  it('should get webhook conversions', async () => {
    const period = NotifConsts.COMBO_PERIODS.day;
    const type = NotifConsts.COMBO_TYPES.conversions;
    const combo = Factory.Combo(constants.accountId, type, period);
    combo.autoTrack = false;

    const url = combo.trackURL[0];
    const date = dateUtils.todayNormalized12am();
    const event1 = EventFactory.WebhookEvent(combo.webhookId, date);
    const event2 = EventFactory.WebhookEvent(combo.webhookId, Date.dateByAddingDays(date, -1));

    const minute = Date.minuteOfTheDay();
    const total = event1.getTotal().sum + event2.getTotal(minute).sum; // total 24 hours ago

    await Promise.all([event1.save(), event2.save(), combo.save()]);

    const res = await httpUtils.notificationsGet(server).send({ url });
    expect(res.body).to.have.lengthOf(1);
    expect(res.body[0]).to.have.property('count', total);
    expect(res.body[0]).to.have.property('minutes', Date.MINUTES_IN_DAY);
  });
});
