const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const httpUtils = require('../../httpUtils');
const constants = require('../../constants');
const urlUtils = require('../../../lib/utils/urlUtils');
const dateUtils = require('../../../lib/utils/dateUtils');

const NotificationFactory = require('../../factories/NotificationFactory');

const { Notification } = NotificationFactory;
const CleanEventFactory = require('../../factories/CleanEventFactory');

const { CleanWebsiteEvent } = CleanEventFactory;
const { CleanFormEvent } = CleanEventFactory;
const { CleanFormStreamEvent } = CleanEventFactory;
const AccountFactory = require('../../factories/AccountFactory');

const { Account } = AccountFactory;

describe('/notifications/get - clean URLs', () => {
  let server;
  before(async () => {
    server = await require('../../../server');
    if(!server) throw new Error('server not loaded');
    sandbox.stub(Account, 'findOne').resolves(AccountFactory.default());
  });

  after(() => {
    sandbox.restore();
  });

  beforeEach(async () => {
    await Promise.all([
      Notification.remove(),
      CleanWebsiteEvent.remove(),
      CleanFormEvent.remove(),
      CleanFormStreamEvent.remove(),
    ]);
  });

  it('should get clean URL page visits count', async () => {
    const { accountId } = constants;
    const trackUrl = 'https://www.provesrc.com/contact-us?ref=productHunt&utm_source=facebook';
    const displayUrl = 'https://www.provesrc.com/home?ref=productHunt&utm_source=facebook';
    const event = CleanEventFactory.getCleanWebsiteEvent(accountId, trackUrl);
    const notification = NotificationFactory.PageVisits(accountId);
    notification.trackURL = [urlUtils.clean(trackUrl)];
    notification.displayURLs = [urlUtils.clean(displayUrl)];
    notification.urlTypes.trackAbs = false;
    notification.urlTypes.displayAbs = false;

    await Promise.all([notification.save(), event.save()]);

    const res = await httpUtils.notificationsGet(server, { url: displayUrl, unique: true });
    expect(res).to.have.status(200);
    const { minMinute, sum } = event.getTotal();
    expect(res.body[0].count).to.be.equal(sum);
    expect(res.body[0].minutes).to.be.equal(dateUtils.minuteOfTheDay() - minMinute);
  });

  it('should get clean URL form submissions count', async () => {
    const { accountId } = constants;
    const trackUrl = 'https://www.provesrc.com/contact-us?ref=productHunt&utm_source=facebook';
    const displayUrl = 'https://www.provesrc.com/home?ref=producthunt&utm_source=facebook';
    const event = CleanEventFactory.getCleanFormEvent(accountId, trackUrl);
    const notification = NotificationFactory.Conversion(accountId, { displayUrl });
    notification.trackURL = [urlUtils.clean(trackUrl)];
    notification.urlTypes.trackAbs = false;

    await Promise.all([notification.save(), event.save()]);

    const res = await httpUtils.notificationsGet(server, { url: displayUrl });
    expect(res).to.have.status(200);
    const totals = event.getTotal();
    expect(res.body[0].count).to.be.equal(totals.sum);
    expect(res.body[0].minutes).to.be.equal(dateUtils.minuteOfTheDay() - totals.minMinute);
  });

  it('should get clean URL stream events', async () => {
    const { accountId } = constants;
    const trackUrl = urlUtils.clean('https://www.provesrc.com/contact-us?ref=productHunt&utm_source=facebook');
    const displayUrl = 'https://www.provesrc.com/home?ref=producthunt&utm_source=facebook';
    const event = CleanEventFactory.getStreamEvent(accountId, { url: trackUrl });
    const notification = NotificationFactory.Stream({ displayUrl });
    notification.trackURL = [trackUrl];
    notification.urlTypes.trackAbs = false;

    await Promise.all([notification.save(), event.save()]);

    const res = await httpUtils.notificationsGet(server, { url: displayUrl });
    expect(res).to.have.status(200);
    expect(res.body[0].data.name).to.be.equal(event.firstName);
  });
});
