const chai = require('chai');
const httpUtils = require('../../httpUtils');

const { expect } = chai;
const constants = require('../../constants');
const serverP = require('../../../server');
const signature = require('cookie-signature');
const config = require('../../../config');
const NotificationFactory = require('../../factories/NotificationFactory');

const { Notification } = NotificationFactory;
const EventFactory = require('../../factories/EventFactory');

const { Event } = EventFactory.models;

const AccountStub = require('../../AccountStub');

describe('/notifications/get limit check', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  let notification;
  const url = 'https://provesrc.com';
  beforeEach(async () => {
    AccountStub.stub();
    const { accountId } = constants;
    const notif = NotificationFactory.PageVisits(accountId, url, url);
    await Promise.all([Notification.remove(), Event.remove()]);
    await Promise.all([notif.save(), EventFactory.WebsiteEvent(accountId, url).save()]);
    notification = notif;
  });

  afterEach(() => {
    AccountStub.restore();
  });

  it('should return notifications', async () => {
    const value = `true|${Date.now()}`;
    const cookieValue = `s:${value}.${signature.sign(value, config.cookie.secret)}`;
    const cookie = `ps${constants.accountId}=${cookieValue}`;
    const res = await httpUtils.notificationsGet(server, { headers: { cookie }, url });
    expect(res).to.have.status(200);
    expect(res.body).to.have.lengthOf(1);
  });

  it('should not return notifications', async () => {
    const value = `false|${Date.now()}`;
    const cookieValue = `s:${signature.sign(value, config.cookie.secret)}`;
    const cookie = `ps${constants.accountId}=${cookieValue}`;
    const res = await httpUtils.notificationsGet(server, { headers: { cookie }, url });
    expect(res).to.have.status(200);
    expect(res.body).to.have.lengthOf(0);
  });
});
