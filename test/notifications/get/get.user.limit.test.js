const chai = require('chai');
const httpUtils = require('../../httpUtils');

const { expect } = chai;
const constants = require('../../constants');
const config = require('../../../config');
const serverP = require('../../../server');
const sinon = require('sinon');

const redis = require('../../../lib/redisClient').getClient();
const dateUtils = require('../../../lib/utils/dateUtils');
const triggers = require('../../../lib/triggers');

const NotificationFactory = require('../../factories/NotificationFactory');
const AccountFactory = require('../../factories/AccountFactory');
const EventFactory = require('../../factories/EventFactory');
const Notification = require('../../../app/notifications/models/Notification');
const Account = require('../../../app/account/models/Account');

const { Event } = EventFactory.models;
const BillFactory = require('../../factories/BillFactory');
const Bill = require('../../../app/billing/Bill');

describe('/notifications/get limit', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  const sandbox = sinon.createSandbox();
  const email = '<EMAIL>';
  let url; let
    account;
  beforeEach(async () => {
    await Promise.all([Notification.remove(), Event.remove(), Account.remove({ email })]);

    account = AccountFactory.monthlySubscription(email).model;
    sandbox.stub(Account, 'findOne').resolves(account);

    const notification = NotificationFactory.PageVisits(account.id);
    url = notification.displayURLs[0];
    const event = EventFactory.WebsiteEvent(account.id, url);
    await Promise.all([
      Bill.remove(),
      notification.save(),
      event.save(),
    ]);
  });

  afterEach(async () => {
    sandbox.restore();
    await Promise.all([redis.flushallAsync(), Notification.remove({}), Bill.remove()]);
  });

  describe('passed limit trigger', () => {
    it('should trigger passed limit', async () => {
      const { accountId } = constants;
      const limit = 1000;
      const visitorCount = 999;
      const daysLeft = 9;
      const expires = dateUtils.todayWithAddedDays(daysLeft);
      const cycleDate = dateUtils.todayNormalized12am();

      await redis.hmsetAsync(accountId,
        'limit', limit,
        'visitorCount', visitorCount,
        'expires', expires,
        'cycleDate', cycleDate);

      const limitReached = sandbox.stub(triggers, 'planLimitReached');

      await httpUtils.notificationsGet(server);

      expect(limitReached).to.have.been.calledWithMatch(accountId, (args) => {
        const diff = Math.abs(args.expires.getTime() - expires.getTime());
        expect(diff).to.be.below(1000).above(0);
        expect(args.limit).to.be.equal(limit);
        expect(args.daysleft).to.be.equal(daysLeft);
        return true;
      });
    });

    it('should not trigger passed limit', async () => {
      const { accountId } = constants;
      const limit = 1000;
      const visitorCount = 1001;
      const daysLeft = 9;
      const expires = dateUtils.todayWithAddedDays(daysLeft);
      const cycleDate = dateUtils.todayNormalized12am();

      await redis.hmsetAsync(accountId,
        'limit', limit,
        'visitorCount', visitorCount,
        'expires', expires,
        'cycleDate', cycleDate);

      const limitReached = sandbox.stub(triggers, 'planLimitReached');

      await httpUtils.notificationsGet(server);

      expect(limitReached).to.have.not.been.called;
    });
  });

  describe('user counters (mongo, redis)', () => {
    beforeEach(async () => {
      await Promise.all([Bill.remove({}), redis.flushallAsync()]);
    });

    afterEach(async () => {
      await Promise.all([Bill.remove({}), redis.flushallAsync()]);
    });

    it('should create a Bill document', async () => {
      const url = 'https://proofsrc.com/pricing';
      const res = await httpUtils.apiRequest(server, constants.NOTIFICATIONS.GET, httpUtils.POST).send({ url });
      expect(res).to.have.status(200);

      const date = await Account.getBillingCycleDate(constants.accountId);
      const bill = await Bill.findOne({ accountId: constants.accountId, date });
      expect(bill).to.exist;
    });

    it('should update an existing Bill document', async () => {
      const bill = BillFactory.makeBill(account.id, account.getBillingCycleDate());

      sandbox.stub(Bill, 'findOne').returns({ sort: () => bill });
      const spy = sandbox.spy(Bill, 'incrementTotal');

      const url = 'https://proofsrc.com/pricing';
      const res = await httpUtils.apiRequest(server, constants.NOTIFICATIONS.GET, httpUtils.POST).send({ url });
      expect(res).to.have.status(200);

      expect(spy).to.have.been.called;
    });

    it('should not increment when pscookie exists', async () => {
      const url = 'https://proofsrc.com/pricing';
      const cookie = httpUtils.makeCookie(`ps${constants.accountId}`, 'true', config.cookie.secret);
      const spy = sandbox.spy(Bill, 'incrementTotal');

      const res = await httpUtils.notificationsGet(server, { headers: { cookie } }).send({ url });
      expect(res).to.have.status(200);

      expect(spy).to.have.not.been.called;
    });
  });

  describe('notifications and limits', () => {
    it('should return notification when below limit', async () => {
      const notification = NotificationFactory.notificationsFactory.pageVisits();
      sandbox.stub(Notification, 'find').returns({
        cache: () => ({ exec: cb => cb(null, [notification]) }),
      });
      // const mock = sandbox.mock(Notification).expects('find').chain('cache').chain('exec');
      // mock.onFirstCall().yields(null, [notification]);
      // mock.onSecondCall().yields(null, [notification]);

      const untilDate = dateUtils.todayWithAddedDays(10);
      await redis.hmsetAsync(account.id, 'untilDate', untilDate, 'limit', 1);

      const agent = httpUtils.Agent(server);
      await agent.notificationsGet(server).send({ url: notification.trackURL[0], unique: true });
      const res = await agent.notificationsGet(server).send({ url: notification.displayURLs[0] });
      expect(res.body).to.have.lengthOf(1);
    });

    it('should not return notification when above limit', async () => {
      const { accountId } = constants;
      const expires = dateUtils.todayWithAddedDays(10);
      await redis.hmsetAsync(accountId,
        'expires', expires,
        'limit', 1,
        'visitorCount', 10,
        'cycleDate', Date.now() - dateUtils.MILLISECONDS_IN_DAY);

      const notification = NotificationFactory.notificationsFactory.pageVisits();
      await notification.save();

      await httpUtils.notificationsGet(server).send({ url: notification.trackURL[0], unique: true });
      const res = await httpUtils.notificationsGet(server).send({ url: notification.displayURLs[0] });
      expect(res.body).to.have.lengthOf(0);
    });

    it('should return notification to old visitor that got notifications in the past', async () => {
      const { accountId } = constants;
      const expires = dateUtils.todayWithAddedDays(10);
      await redis.hmsetAsync(accountId,
        'expires', expires,
        'limit', 1,
        'visitorCount', 0,
        'cycleDate', Date.now() - dateUtils.MILLISECONDS_IN_DAY * 3);

      const notification = NotificationFactory.notificationsFactory.pageVisits();
      await notification.save();

      const agent = httpUtils.Agent(server); // this represents a user, as it stores cookies
      let res = await agent.notificationsGet().send({ url: notification.trackURL[0], unique: true });
      const cookie = res.headers['set-cookie'][1];
      expect(cookie).to.include(`ps${accountId}`);
      expect(cookie).to.include(`s%3A${true}`);

      res = await httpUtils.notificationsGet(server).send({ url: notification.displayURLs[0] });
      expect(res.body).to.have.lengthOf(0);

      res = await agent.notificationsGet().send({ url: notification.displayURLs[0] });
      expect(res.body).to.have.lengthOf(1);
    });
  });

  describe('account with subscription', () => {
    it('should not return a notification', async () => {
      await (new Bill({
        accountId: account._id,
        date: account.subscription.transactionDate,
        total: 10001,
        days: { 30: 10001 },
      })
      ).save();

      const opts = { apiKey: account.apiKey, url };
      const res = await httpUtils.notificationsGet(server, opts);
      expect(res.body).to.have.lengthOf(0);
    });
  });
});
