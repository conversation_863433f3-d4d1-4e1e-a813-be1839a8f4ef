const chai = require('chai');
const httpUtils = require('../../httpUtils');

const { expect } = chai;
const constants = require('../../constants');
const serverP = require('../../../server');

const NotificationFactory = require('../../factories/NotificationFactory');
const EventFactory = require('../../factories/EventFactory');

describe('/notifications/get magento2', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    await Promise.all([
      NotificationFactory.Notification.remove(),
      EventFactory.models.WooEvent.remove(),
    ]);
  });

  it('should return notification', async () => {
    const notification = NotificationFactory.Magento2();
    const event = EventFactory.Magento2Event();
    await Promise.all([
      notification.save(),
      event.save(),
    ]);

    const url = `https://${event.host}/home`;
    const res = await httpUtils.notificationsGet(server, { url });
    expect(res).to.have.status(200);
    expect(res.body).to.have.lengthOf(1);

    const result = res.body[0];
    const nameData = event.getName();
    expect(result.data.initials).to.be.equal(nameData.initials);
    expect(result.data.name).to.be.equal(nameData.name);
    expect(result.data.product).to.be.deep.equal(event.getProduct());
  });
});
