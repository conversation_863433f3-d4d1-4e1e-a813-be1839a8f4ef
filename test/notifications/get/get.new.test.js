const chai = require('chai');
const httpUtils = require('../../httpUtils');

const { expect } = chai;
const constants = require('../../constants');
const serverP = require('../../../server');

const NotificationsFactory = require('../../factories/NotificationFactory');

const { Notification } = NotificationsFactory;
const EventsFactory = require('../../factories/EventFactory');

const { Event } = EventsFactory.models;

const dateUtils = require('../../../lib/utils/dateUtils');
const notifConstants = require('../../../app/notifications/constants');

describe('/notifications/get refactored', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    await Promise.all([Notification.remove(), Event.remove()]);
  });

  describe('PageVisits and Conversion notifications', async () => {
    it('should return events from today', async () => {
      const url = 'https://provesrc.com';
      const notification = NotificationsFactory.PageVisits();
      const event = EventsFactory.WebsiteEvent(constants.accountId, url);
      await Promise.all([notification.save(), event.save()]);

      const res = await httpUtils.notificationsGet(server, { url });
      expect(res.body[0].count).to.be.equal(event.getTotal().sum);
    });

    it('should support timeLimit', async () => {
      const url = 'https://provesrc.com';
      const notification = NotificationsFactory.PageVisits();
      const event = EventsFactory.WebsiteEvent(constants.accountId, url);

      const { minMinute } = event.getTotal();
      const minuteOfDay = dateUtils.minuteOfTheDay();
      const minusOneMinute = minMinute + 1;
      notification.settings.timeLimit = {
        active: true,
        unit: notifConstants.timeUnits.Minutes,
        value: minuteOfDay - minusOneMinute,
      };

      await Promise.all([notification.save(), event.save()]);

      const res = await httpUtils.notificationsGet(server, { url });
      expect(res.body[0].count).to.be.equal(event.getTotal(minusOneMinute).sum);
    });
  });
});
