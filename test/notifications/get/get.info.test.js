const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const httpUtils = require('../../httpUtils');
const testUtils = require('../../testUtils');

const constants = require('../../constants');
const Factory = require('../../factories/NotificationFactory');

const { Notification } = Factory;

describe('/notifications/get - Info', () => {
  let server;
  before(async () => {
    server = await require('../../../server');
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    await Notification.remove();
  });

  afterEach(async () => {
    sandbox.restore();
  });

  it('should get info notification', async () => {
    const url = 'https://mynicewebsite.com';
    const title = 'zubi karubi';
    const info = Factory.getInfo({ title, display: url });
    await httpUtils.createNotification(server, info);

    const res = await httpUtils.notificationsGet(server, { url });
    expect(res).to.have.status(200);

    expect(res.body[0].title).to.be.equal(title);
  });
});
