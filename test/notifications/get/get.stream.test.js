const _ = require('lodash');
const moment = require('moment');
const httpUtils = require('../../httpUtils');
const chai = require('chai');

const { expect } = chai;
const sinon = require('sinon');
const constants = require('../../constants');
const serverP = require('../../../server');
const utils = require('../../testUtils');
const config = require('../../../config');

const AccountFactory = require('../../factories/AccountFactory');

const { Account } = AccountFactory;
const Factory = require('../../factories/NotificationFactory');
const EventFactory = require('../../factories/EventFactory');
const Notification = require('../../../app/notifications/models/Notification');
const Stream = require('../../../app/notifications/models/Stream');
const StreamEvent = require('../../../app/events/models/StreamEvent');
const WebhookStreamEvent = require('../../../app/events/models/StreamEvent');
const Event = require('../../../app/events/models/Event');
const notifConstants = require('../../../app/notifications/constants');
const testConfig = require('../../../config');

describe('/notifications/get stream', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  const sandbox = sinon.createSandbox();
  beforeEach(async () => {
    sandbox.stub(Account, 'findOne').resolves(AccountFactory.default());
    await Promise.all([Stream.remove(), StreamEvent.remove()]);
  });

  afterEach(async () => {
    sandbox.restore();
    await Promise.all([Stream.remove(), StreamEvent.remove()]);
  });

  it('should get stream profile properties and localized', async () => {
    sandbox.stub(testConfig, 'getSocialProfiles').value(true);
    const locale = 'de';
    const params = Factory.StreamParams({ localization: locale });
    params.image = 'https://provesrc.com/niceimage.png';
    await httpUtils.createNotification(server, params);

    const url = params.trackURL[0];
    const email = '<EMAIL>';
    const timestamp = Date.now();
    await httpUtils.trackForm(server, url, email, timestamp, '208.66.31.226');

    await utils.sleep(2000);

    const res = await httpUtils.notificationsGet(server, { url });
    expect(res.body[0].image).to.be.equal(params.image);
    const { data } = res.body[0];
    expect(data.name).to.equal('natanavra');
    expect(data.initials).to.equal('N');
    expect(data.photo.length).to.be.above(1);

    const momentLocalized = moment(timestamp);
    momentLocalized.locale(locale);
    const timeText = momentLocalized.fromNow();
    expect(res.body[0].data).to.have.property('timeText', timeText);
    expect(res.body[0].data.location).to.be.have.keys('city', 'country', 'countryCode', 'state', 'stateCode');
  });

  it('should filter same events when multiple notifications and same events', async () => {
    const params1 = Factory.StreamParams();
    const params2 = Factory.StreamParams({ name: 'stream-2' });
    await Promise.all([
      httpUtils.createNotification(server, params1),
      httpUtils.createNotification(server, params2),
    ]);

    const url = params1.trackURL[0];
    const email1 = '<EMAIL>';
    const email2 = '<EMAIL>';
    await Promise.all([
      httpUtils.trackForm(server, url, email1, Date.now()),
      httpUtils.trackForm(server, url, email2, Date.now() - 5000),
    ]);

    await utils.sleep(2000);

    const res = await httpUtils.notificationsGet(server, { url });
    expect(res.body).to.have.lengthOf(2);
    const names = res.body.map(item => item.data.name);
    expect(names).to.have.lengthOf(2);
  });

  it('should work with webhook streams', async () => {
    const params = Factory.StreamParams({ autoTrack: false });
    await httpUtils.createNotification(server, params);

    const url = params.trackURL[0];
    const email1 = '<EMAIL>';
    const email2 = '<EMAIL>';
    await Promise.all([
      httpUtils.trackWebhook(server, params.webhookId, email1, Date.now()),
      httpUtils.trackWebhook(server, params.webhookId, email2, Date.now()),
    ]);

    const res = await httpUtils.notificationsGet(server, { url });
    expect(res.body).to.have.lengthOf(2);
    const names = res.body.map(item => item.data.name);
    expect(names).to.lengthOf(2);
  });

  it('should not return the same email more than once', async () => {
    const params = Factory.StreamParams({ autoTrack: false });
    await httpUtils.createNotification(server, params);

    const url = params.trackURL[0];
    const email = '<EMAIL>';
    await Promise.all([
      httpUtils.trackWebhook(server, params.webhookId, email, Date.now()),
      httpUtils.trackWebhook(server, params.webhookId, email, Date.now() - 5000),
    ]);

    const res = await httpUtils.notificationsGet(server, { url });
    const names = res.body.map(item => item.data.name);
    expect(names).to.lengthOf(1);
  });

  it('should not return "Someone" when filterAnonymous=true', async () => {
    sandbox.stub(testConfig, 'getSocialProfiles').value(true);
    const params = Factory.StreamParams();
    params.settings.filterAnonymous = true;
    await httpUtils.createNotification(server, params);

    const url = params.trackURL[0];
    await Promise.all([
      await httpUtils.trackForm(server, url, '<EMAIL>', Date.now(), '***************'),
      await httpUtils.trackForm(server, url, '<EMAIL>', Date.now() - 5000, '***************'),
    ]);

    await utils.sleep(1500);

    const res = await httpUtils.notificationsGet(server, { url: params.displayURLs[0] });
    const names = res.body.map(item => item.data.name);
    expect(names).to.not.include('Someone');
    expect(names).to.lengthOf(1);
  });

  it('should check for date based on timeLimit', async () => {
    const params = Factory.StreamParams();
    params.settings.timeLimit = { active: true, unit: notifConstants.timeUnits.Hours, value: 3 };
    await httpUtils.createNotification(server, params);

    const url = params.trackURL[0];
    const ip = '***************';
    const opts = { headers: { 'x-real-ip': ip } };
    const data1 = {
      firstName: 'Natan', email: '<EMAIL>', url, timestamp: Date.now(),
    };
    const data2 = { email: '<EMAIL>', url, timestamp: Date.now() - 86400000 };
    await Promise.all([
      await httpUtils.trackFormData(server, opts, data1),
      await httpUtils.trackFormData(server, opts, data2),
    ]);

    await utils.sleep(1500);

    const res = await httpUtils.notificationsGet(server, { url: params.displayURLs[0] });
    const names = res.body.map(item => item.data.name);
    expect(names).to.include('Natan');
    expect(names).to.lengthOf(1);
  });

  it('should not return more notifications than maxConversions', async () => {
    const url = 'https://provesrc.com/buy';
    const events = [
      EventFactory.FormStreamEvent({ email: '<EMAIL>', url }),
      EventFactory.FormStreamEvent({ email: '<EMAIL>', url }),
      EventFactory.FormStreamEvent({ email: '<EMAIL>', url }),
    ];

    const notification = Factory.Stream({ displayUrl: url, trackUrl: url });
    notification.settings.maxConversions = 2;

    await Promise.all([notification.save(), events.map(ev => ev.save())]);

    const res = await httpUtils.notificationsGet(server, { url });
    expect(res.body).to.have.length(2);
  });

  it('should return maxConversions regardless of filtered emails (if enough events)', async () => {
    /**
		 * The issue was maxConversions=3 and from the 3 events pulled (because of limit)
		 * 2 of those events had the same email, so we got only 2 notifications back
		 */
    const url = 'https://provesrc.com/buy';
    const events = [
      EventFactory.FormStreamEvent({ email: '<EMAIL>', url }),
      EventFactory.FormStreamEvent({ email: '<EMAIL>', url }),
      EventFactory.FormStreamEvent({ email: '<EMAIL>', url, date: Date.now() - 5000 }),
    ];

    const notification = Factory.Stream({ displayUrl: url, trackUrl: url });
    notification.settings.maxConversions = 2;

    await Promise.all([notification.save(), events.map(ev => ev.save())]);

    const res = await httpUtils.notificationsGet(server, { url });
    expect(res.body).to.have.length(2);
  });

  it('should work even if no events', async () => {
    const url = 'https://provesrc.com/buy';
    const notification = Factory.Stream({ displayUrl: url, trackUrl: url });
    notification.settings.maxConversions = 2;

    await Promise.all([notification.save()]);

    const res = await httpUtils.notificationsGet(server, { url });
    expect(res.body).to.have.length(0);
  });

  it('should sort output when multiple notifications (multiple event sources with different times)', async () => {
    const display = 'https://provesrc.com';
    const notif1 = Factory.Stream({ displayUrl: display, trackUrl: 'https://provesrc.com/contact', name: 'contact' });
    const notif2 = Factory.Stream({ displayUrl: display, trackUrl: 'https://provesrc.com/sales', name: 'sales' });
    const [event1, event2] = [
      EventFactory.FormStreamEvent({ date: Date.now() - 86400 * 1000, email: '<EMAIL>', url: notif1.trackURL[0] }),
      EventFactory.FormStreamEvent({ date: Date.now(), email: '<EMAIL>', url: notif2.trackURL[0] }),
    ];
    await notif1.save(); // save it before notif2 so it's always the first result in notification find query
    await Promise.all([notif2.save(), event1.save(), event2.save()]);

    const res = await httpUtils.notificationsGet(server, { url: display });
    console.log(res.body);
    const date1 = new Date(res.body[0].timestamp);
    const date2 = new Date(res.body[1].timestamp);
    expect(date1.getTime()).to.be.above(date2.getTime());
  });

  it('should anonymize all stream notifications', async () => {
    const url = 'https://provesrc.com/buy';
    const events = [
      EventFactory.FormStreamEvent({ email: '<EMAIL>', url, firstName: 'Natan' }),
      EventFactory.FormStreamEvent({ email: '<EMAIL>', url, firstName: 'Nattttaan' }),
    ];
    const notification = Factory.Stream({ displayUrl: url, trackUrl: url });
    notification.settings.anonymize = true;
    await Promise.all([events.map(event => event.save()), notification.save()]);

    const res = await httpUtils.notificationsGet(server, { url });
    expect(res.body).to.have.lengthOf(2);
    const names = _.map(res.body, 'data.name');
    expect(names).to.include('Someone');
  });

  it('should use alternatives to Someone', async () => {
    const url = 'https://provesrc.com/buy';
    const alternatives = ['Marketer', 'Salesman'];
    const events = [
      EventFactory.FormStreamEvent({ email: '<EMAIL>', url }),
      EventFactory.FormStreamEvent({ email: '<EMAIL>', url }),
    ];
    const notification = Factory.Stream({ displayUrl: url, trackUrl: url });
    notification.settings.anonymize = true;
    notification.someoneAlternatives = alternatives;
    await Promise.all([events.map(event => event.save()), notification.save()]);

    const res = await httpUtils.notificationsGet(server, { url });
    expect(res.body[0].data.name).to.be.oneOf(alternatives);
    expect(res.body[1].data.name).to.be.oneOf(alternatives);
  });

  it('should return stream notification with guid', async () => {
    const url = 'https://provesrc.com/productPage';
    const guid = 123;

    const notification = Factory.Stream({ url });
    notification.manuallyShowNotification = true;
    notification.autoTrack = false;
    notification.displayURLs = [url];

    const event = EventFactory.WebhookStreamEvent({ webhookId: notification.webhookId });
    event.guid = guid;

    await Promise.all([event.save(), notification.save()]);

    const dbEvent = await EventFactory.models.WebhookStreamEvent.findOne();
    expect(dbEvent.guid).to.be.equal(guid);
    const params = { name: notification.name, guid };
    const res = await httpUtils.apiRequest(server, constants.NOTIFICATIONS.GET, httpUtils.POST).send(params);
    expect(res.body).to.have.lengthOf(1);
  });

  it('should return product details', async () => {
    const notification = Factory.Stream({ autoTrack: false });
    await notification.save();

    const name = 'sneakers';
    const data = { email: '<EMAIL>', firstName: 'Natan', products: [{ name, link: 'sneakers.com' }] };
    await httpUtils.trackWebhookData(server, notification.webhookId, data);

    const res = await httpUtils.notificationsGet(server, { url: notification.displayURLs[0] });
    expect(res.body).to.have.lengthOf(1);
    expect(res.body[0].data.product.name).to.be.equal(name);
  });

  it('should hide product details', async () => {
    const notification = Factory.Stream({ autoTrack: false });
    notification.settings.hideProduct = true;
    sandbox.mock(Notification).expects('find').chain('cache').chain('exec')
      .yields(null, [notification]);

    const event = EventFactory.WebhookStreamEvent({
      email: '<EMAIL>',
      firstName: 'Natan',
      products: [{ name: 'sneakers', link: 'sneakers.com' }],
      webhookId: notification.webhookId,
    });
    await event.save();

    const res = await httpUtils.notificationsGet(server, { url: notification.displayURLs[0] });
    expect(res.body).to.have.lengthOf(1);
    expect(res.body[0].data.product).to.not.exist;
  });

  it('should filter own conversion', async () => {
    const url = 'https://provesrc.com/buy';
    const psuid = '1234';
    const events = [
      EventFactory.FormStreamEvent({ email: '<EMAIL>', url, uid: psuid }),
      EventFactory.FormStreamEvent({ email: '<EMAIL>', url }),
    ];
    const notification = Factory.Stream({ displayUrl: url, trackUrl: url });
    notification.settings.hideOwnConversions = true;
    await Promise.all([events.map(event => event.save()), notification.save()]);

    const cookie = httpUtils.makeCookie('psuid', psuid, config.cookie.secret);
    const res = await httpUtils.notificationsGet(server, { url, headers: { cookie } });
    expect(res.body).to.have.lengthOf(1);
    expect(events[1]._id.equals(res.body[0].data.id)).to.be.true;
  });
});
