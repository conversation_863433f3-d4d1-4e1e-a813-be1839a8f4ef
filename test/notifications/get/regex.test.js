const chai = require('chai');
const httpUtils = require('../../httpUtils');

const { expect } = chai;
const constants = require('../../constants');
const serverP = require('../../../server');

const NotificationFactory = require('../../factories/NotificationFactory');
const notifConstants = require('../../../app/notifications/constants');

const { Notification } = NotificationFactory;

const EventFactory = require('../../factories/EventFactory');

const { Event } = EventFactory.models;

describe('/notifications/get regex', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    await Promise.all([Event.remove(), Notification.remove()]);
  });

  it('should find events based on regex', async () => {
    const url = 'https://provesrc.com/about?test=ok';
    const expression = 'provesrc.com.*?test=ok';
    const notification = NotificationFactory.PageVisits(null, expression, url);
    const event = EventFactory.WebsiteEvent(constants.accountId, url);
    notification.urlTypes = { track: notifConstants.URL_TYPES.regex };

    await Promise.all([notification.save(), event.save()]);

    const res = await httpUtils.notificationsGet(server, { url });
    expect(res.body).to.have.length(1);
  });

  it('should find stream events based on regex', async () => {
    const url = 'https://provesrc.com/about?test=ok';
    const expression = 'provesrc.com.*?test=ok';
    const notification = NotificationFactory.Stream({ trackUrl: expression, displayUrl: url });
    const event = EventFactory.FormStreamEvent({ url });
    notification.urlTypes = { track: notifConstants.URL_TYPES.regex };

    await Promise.all([notification.save(), event.save()]);

    const res = await httpUtils.notificationsGet(server, { url });
    expect(res.body).to.have.length(1);
  });

  it('should support display based on regex', async () => {
    const trackUrl = 'https://home.com';
    const displayUrl = 'https://provesrc.com/asdlk?test=ok';
    const expression = 'provesrc.com.*?test=ok';
    const notification = NotificationFactory.PageVisits(null, trackUrl, expression);
    const event = EventFactory.WebsiteEvent(constants.accountId, trackUrl);
    notification.urlTypes = { display: notifConstants.URL_TYPES.regex };

    await Promise.all([notification.save(), event.save()]);

    const res = await httpUtils.notificationsGet(server, { url: displayUrl });
    expect(res.body).to.have.length(1);
  });
});
