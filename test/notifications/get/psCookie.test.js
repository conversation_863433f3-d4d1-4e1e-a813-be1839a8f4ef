const httpUtils = require('../../httpUtils');
const chai = require('chai');

const { expect } = chai;
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const constants = require('../../constants');
const serverP = require('../../../server');
const AccountFactory = require('../../factories/AccountFactory');

const { Account } = AccountFactory;
const redis = require('../../../lib/redisClient').getClient();

describe('psAccount cookie', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should set a psAccount cookie', () => {
    sandbox.stub(Account, 'findOne').resolves(AccountFactory.default());
    const url = 'http://www.google.com';
    return httpUtils.apiRequest(server, constants.NOTIFICATIONS.GET, httpUtils.POST).send({ url }).then((res) => {
      expect(res).to.have.status(200);
      expect(res).to.have.cookie('psuid');
      expect(res).to.have.cookie(`ps${constants.accountId}`);
    });
  });

  it('should not set psAccount cookie if exists', () => {
    sandbox.stub(Account, 'findOne').resolves(AccountFactory.default());
    const url = 'http://www.google.com';
    const agent = httpUtils.Agent(server);
    const accountIdCookie = `ps${constants.accountId}`;
    return agent.apiRequest(server, constants.NOTIFICATIONS.GET, httpUtils.POST).send({ url }).then((res) => {
      expect(res).to.have.status(200);
      expect(res).to.have.cookie('psuid');
      expect(res).to.have.cookie(accountIdCookie);
    }).then(() => agent.apiRequest(server, constants.NOTIFICATIONS.GET, httpUtils.POST).send({ url }))
      .then((res) => {
        expect(res).to.have.status(200);
        expect(res).to.not.have.cookie('psuid');
        expect(res).to.not.have.cookie(accountIdCookie);
      });
  });

  describe('cookie timestamp tests', () => {
    let account;
    before(async () => {
      account = AccountFactory.monthlySubscription('<EMAIL>').model;
      sandbox.stub(Account, 'findOne').resolves(account);
    });

    it('should set boolean|timestamp for cookie value', async () => {
      const url = 'http://www.google.com';
      return httpUtils.apiRequest(server, constants.NOTIFICATIONS.GET, httpUtils.POST).send({ url }).then((res) => {
        expect(res).to.have.status(200);
        expect(res).to.have.cookie('psuid');
        expect(res).to.have.cookie(`ps${constants.accountId}`);
        const cookie = res.headers['set-cookie'][1];
        const value = decodeURI(cookie.substring(cookie.indexOf('A') + 1, cookie.indexOf('.')));
        const [enable, timestamp] = value.split('|');
        expect(enable).to.be.equal('true');
        expect(parseInt(timestamp, 10)).to.be.a('number').to.be.above(0);
      });
    });
  });
});
