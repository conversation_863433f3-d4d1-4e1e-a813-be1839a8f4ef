const chai = require('chai');

const { expect } = chai;
const sinon = require('sinon');
const constants = require('../../constants');
const dateUtils = require('../../../lib/utils/dateUtils');
const redis = require('../../../lib/redisClient').getClient();
const mockUtils = require('../../mockUtils');

const AccountFactory = require('../../factories/AccountFactory');
const Account = require('../../../app/account/models/Account');
const Bill = require('../../../app/billing/Bill');
const Plans = require('../../../app/account/plansEnum');

const getLimitInfo = require('../../../app/visitorCount/getLimitInfo');
const saveLimitInfo = require('../../../app/visitorCount/saveToRedis');

describe('/notification/get limit units', () => {
  const sandbox = sinon.createSandbox();
  before(async () => {
    await redis.flushallAsync();
  });

  afterEach(async () => {
    sandbox.restore();
    await redis.flushallAsync();
  });

  it('should pull from redis', async () => {
    const { accountId } = constants;
    const expires = dateUtils.todayWithAddedDays(2);
    const limit = 20;
    const cycleDate = new Date(Date.now() - dateUtils.MILLISECONDS_IN_DAY * 2);

    await redis.hmsetAsync(accountId,
      'limit', limit,
      'expires', expires,
      'visitorCount', 1,
      'cycleDate', cycleDate.getTime());

    const limitInfo = await getLimitInfo(accountId);
    expect(limitInfo.limit).to.be.equal(limit);
    expect(limitInfo.cycleDate).to.be.equal(cycleDate.getTime());
  });

  it('should save to redis', async () => {
    const { accountId } = constants;
    const limit = 10; const visitorCount = 5; const expires = new Date('2030'); const
      email = '<EMAIL>';
    const cycleDate = Date.now() - ********;
    await saveLimitInfo(accountId, {
      limit, visitorCount, expires, email, cycleDate,
    });

    const res = await redis.hmgetAsync(accountId, 'limit', 'visitorCount', 'expires', 'email', 'cycleDate');
    expect(res[0]).to.be.equal(`${limit}`);
    expect(res[1]).to.be.equal(`${visitorCount}`);
    const date = new Date(res[2]);
    expect(date.getTime()).to.be.equal(expires.getTime());
    expect(res[3]).to.be.equal(email);
    expect(parseInt(res[4], 10)).to.be.equal(cycleDate);
  });

  it('should not query mongo when redis {noSubscription=true}', async () => {
    const { accountId } = constants;
    const findStub = sandbox.stub(Account, 'findOne');
    sandbox.stub(Bill, 'findOne').resolves(null);

    await redis.hmsetAsync(accountId,
      'visitorCount', 1,
      'limit', 1000,
      'expires', new Date('2020'),
      'cycleDate', Date.now());

    const result = await getLimitInfo(accountId);
    expect(result.fromRedis).to.be.true;
  });
});
