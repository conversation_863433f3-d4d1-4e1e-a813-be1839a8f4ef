const chai = require('chai');
const httpUtils = require('../../httpUtils');

const { expect } = chai;
const constants = require('../../constants');
const serverP = require('../../../server');
const testUtils = require('../../testUtils');

const User = require('../../../app/users/User');
const uuid = require('uuid/v4');

describe('/notifications/get user update', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    await User.remove();
  });

  xit('update the Users db', async () => {
    const uid = uuid();
    const headers = { 'x-ps-uid': uid };
    await httpUtils.notificationsGet(server, { url: 'https://provesrc.com', headers });

    const user = await User.findOne();
    expect(user).to.exist;
  });
});
