const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const httpUtils = require('../../httpUtils');

const constants = require('../../constants');
const NFactory = require('../../factories/NotificationFactory');
const EFactory = require('../../factories/EventFactory');

const { NotifConsts } = NFactory;

describe('/notifications/get - store select', () => {
  let server;
  before(async () => {
    server = await require('../../../server');
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    await Promise.all([
      NFactory.Notification.remove(),
      EFactory.models.WooEvent.remove(),
    ]);
  });

  afterEach(async () => {
    sandbox.restore();
  });

  it('should return only stream events of specified store', async () => {
    const store = 'mystore';
    const storeDomain = `${store}.com`;
    const platform = NotifConsts.PLATFORMS.woocommerce;
    const params = NFactory.StreamParams(constants.accountId);
    params.trackURL = [store];
    params.settings.platform = platform;
    params.urlTypes.display = NotifConsts.URL_TYPES.all;
    let res = await httpUtils.createNotification(server, params);
    expect(res).to.have.status(200);

    await Promise.all([
      EFactory.WooEvent({ host: storeDomain }).save(),
      EFactory.WooEvent({ email: '<EMAIL>', host: 'someshopother.com' }).save(),
    ]);

    res = await httpUtils.notificationsGet(server, { url: 'https://provesrc.com' });
    expect(res.body).to.have.length(1);
  });
});
