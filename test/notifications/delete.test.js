const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');
const Factory = require('../factories/NotificationFactory');
const { Account } = require('../factories/AccountFactory');

describe('/notifications/delete', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    await Factory.Notification.remove();
  });

  xit('should delete notification and update account.stats', async () => {
    await Account.update({ _id: constants.accountId }, {
      $unset: {
        'stats.notifications.deleted': '',
        'stats.notifications.lastDeleted': '',
      },
    });
    const notification = Factory.PageVisits();
    await notification.save();

    const res = await httpUtils.deleteNotification(server, { id: notification._id });
    expect(res).to.have.status(200);

    const dbNotif = await Factory.Notification.findOne({ _id: notification._id });
    expect(dbNotif).to.not.exist;

    const acc = await Account.findOne({ _id: constants.accountId });
    expect(acc.stats.notifications.deleted).to.be.equal(1);
    expect(acc.stats.notifications.lastDeleted).to.not.be.null;
    const diff = Date.now() - acc.stats.notifications.lastDeleted;
    expect(diff).to.be.below(100);
  });
});
