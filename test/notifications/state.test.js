const httpUtils = require('../httpUtils');
const chai = require('chai');

const { expect } = chai;
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const constants = require('../constants');
const serverP = require('../../server');
const testUtils = require('../testUtils');

const Notification = require('../../app/notifications/models/Notification');
const Factory = require('../factories/NotificationFactory');
const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;

const AccountStub = require('../AccountStub');

describe('Notification.active', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    AccountStub.stub();
    await Notification.remove();
  });

  afterEach(() => {
    AccountStub.restore();
  });

  describe('/notifications/list', () => {
    it('should return non active notifications as well', async () => {
      const url = 'https://provesrc.com';
      const notif1 = Factory.PageVisits(constants.accountId, url, url, 'visits 1');
      const notif2 = Factory.PageVisits(constants.accountId, url, url, 'visits 2');
      notif2.active = false;
      await Promise.all([notif1.save(), notif2.save()]);

      const res = await httpUtils.consoleRequest(server, constants.NOTIFICATIONS.LIST, httpUtils.GET);
      const { notifications } = res.body;
      expect(notifications).to.have.lengthOf(2);
    });
  });

  describe('/notifications/get', () => {
    it('should aggregate only active notifications', async () => {
      const url = 'https://provesrc.com';
      const notif1 = Factory.PageVisits(constants.accountId, url, url, 'visits 1');
      const notif2 = Factory.PageVisits(constants.accountId, url, url, 'visits 2');
      notif2.active = false;
      await Promise.all([notif1.save(), notif2.save()]);

      await Promise.all([
        httpUtils.notificationsGet(server).send({ url, unique: true }),
        httpUtils.notificationsGet(server).send({ url, unique: true }),
      ]);

      const res = await httpUtils.notificationsGet(server).send({ url });
      expect(res.body).to.have.lengthOf(1);
    });
  });

  describe('/notifications/state', () => {
    it('should disable the notification {active=false}', async () => {
      const url = 'https://provesrc.com';
      const notif1 = Factory.PageVisits(constants.accountId, url, url, 'visits 1');
      await notif1.save();

      const send = { id: notif1._id, active: false };
      const res = await httpUtils.consoleRequest(server, constants.NOTIFICATIONS.STATE, httpUtils.POST).send(send);
      expect(res.body).to.have.property('success', true);

      const notification = await Notification.findOne({ _id: notif1._id });
      expect(notification).to.have.property('active', false);
    });

    it('should return an error if notification does not exist', async () => {
      const send = { id: Factory.notifications.pageVisits._id, active: false };
      try {
        await httpUtils.consoleRequest(server, constants.NOTIFICATIONS.STATE, httpUtils.POST).send(send);
      } catch(err) {
        expect(err.response).to.have.status(400);
        expect(err.response.body).to.have.property('error');
      }
    });

    it('should not allow more than 1 active notification for free users', async () => {
      AccountStub.restore();
      const acc = AccountFactory.free();
      const notification = Factory.Stream();
      notification.active = false;
      sandbox.stub(Account, 'findOne').resolves(acc);
      sandbox.stub(Notification, 'count').resolves(1);
      sandbox.stub(Notification, 'findOne').resolves(notification);

      const res = await httpUtils.setNotificationState(server, { id: notification.id, active: true });
      expect(res).to.have.status(400);
      expect(notification.active).to.be.false;
    });

    it('should allow more than 1 active notification for paying users', async () => {
      AccountStub.restore();
      const acc = AccountFactory.monthlySubscription().model;
      const notification = Factory.Stream();
      notification.active = false;
      sandbox.stub(Account, 'findOne').resolves(acc);
      sandbox.stub(Notification, 'count').resolves(5);
      sandbox.stub(Notification, 'findOne').resolves(notification);

      const res = await httpUtils.setNotificationState(server, { id: notification.id, active: true });
      expect(res).to.have.status(200);
      expect(notification.active).to.be.true;
    });
  });
});
