const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const testUtils = require('../testUtils');

const priority = require('../../app/notifications/priority');

const constants = require('../constants');
const NotificationFactory = require('../factories/NotificationFactory');

const { Notification } = NotificationFactory;

describe('notifications priority', () => {
  afterEach(async () => {
    sandbox.restore();
  });

  it('should set priority fields on notifications', async () => {
    const bulkWrite = sandbox.stub(Notification, 'bulkWrite').resolves();
    const ids = [1, 2, 3];
    const accountId = '1234'; // so users can't set priority for other accounts
    const ops = [];
    for(let i = 0; i < ids.length; i++) {
      ops.push({
        updateOne: {
          filter: { _id: ids[i], accountId },
          update: { $set: { priority: i } },
        },
      });
    }

    priority.setPriority(accountId, ids);
    expect(bulkWrite).to.have.been.calledWith(ops);
  });

  it('should do nothing if no accountId', async () => {
    const bulkWrite = sandbox.stub(Notification, 'bulkWrite').resolves();
    const ids = [1, 2, 3];
    priority.setPriority(null, ids);

    expect(bulkWrite).to.have.not.been.called;
  });

  it('should do nothing if no notification ids', async () => {
    const bulkWrite = sandbox.stub(Notification, 'bulkWrite').resolves();
    const accountId = '123';
    priority.setPriority(accountId, []);
    priority.setPriority(accountId, null);

    expect(bulkWrite).to.have.not.been.called;
  });
});
