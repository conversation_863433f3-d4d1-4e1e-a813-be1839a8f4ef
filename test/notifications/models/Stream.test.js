const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();

const testUtils = require('../../testUtils');
const server = require('../../../server');

const Stream = require('../../../app/notifications/models/Stream');

describe('Stream - Model', () => {
  afterEach(() => {
    sandbox.restore();
  });

  it('should have settings.hideProduct property', async () => {
    const notif = new Stream({ settings: { hideProduct: true } });
    expect(notif.settings.hideProduct).to.be.true;
  });

  describe('middleware tests #integration', () => {
    it('should not run the pre save middleware', async () => {
      const notif = new Stream({
        name: 'suka tests',
        accountId: testUtils.ObjectId(),
        autoTrack: true,
        message: 'hi',
      });
      await notif.save();
      expect(notif.type).to.be.equal('stream');
    });
  });
});
