const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();

const Notification = require('../../../app/notifications/models/Notification');

describe('Notification Model', () => {
  afterEach(() => {
    sandbox.restore();
  });

  it('should have priority property', async () => {
    const priority = 5;
    const notification = new Notification({ priority });
    expect(notification.priority).to.be.equal(priority);
  });
});
