require('../../../lib/mongooseLoader');
const chai = require('chai');

const { expect } = chai;
const Combo = require('../../../app/notifications/models/Combo');
const constants = require('../../constants');
const NotifConsts = require('../../../app/notifications/constants');
const Factory = require('../../factories/NotificationFactory');

describe('Model - Combo', () => {
  beforeEach(async () => {
    await Combo.remove();
  });

  it('check model creation', async () => {
    const period = NotifConsts.COMBO_PERIODS.day;
    const comboType = NotifConsts.COMBO_TYPES.conversions;
    const combo = Factory.Combo(constants.accountId, comboType, period);
    await combo.save();

    const notification = await Combo.findOne({ _id: combo._id });
    expect(notification.settings.combo).to.have.property('period', period);
    expect(notification.settings.combo).to.have.property('type', comboType);
  });
});
