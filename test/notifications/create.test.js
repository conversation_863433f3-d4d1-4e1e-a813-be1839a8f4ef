const _ = require('lodash');
const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const constants = require('../constants');
const server = require('../../server');
const urlUtils = require('../../lib/utils/urlUtils');

const Factory = require('../factories/NotificationFactory');
const Notification = require('../../app/notifications/models/Notification');
const PageVisits = require('../../app/notifications/models/PageVisits');
const Conversion = require('../../app/notifications/models/Conversion');
const Stream = require('../../app/notifications/models/Stream');
const NotifConstants = require('../../app/notifications/constants');
const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;
const create = require('../../app/notifications/create');

const ENDPOINT = '/notifications/create';

describe('/notifications/create', () => {
  beforeEach(() => {
    const account = AccountFactory.default();
    sandbox.stub(Account, 'findOne').resolves(account);
    return Notification.remove({});
  });

  afterEach(async () => {
    sandbox.restore();
  });

  describe('Schema', () => {
    it('should accept properties', async () => {
      const displayHold = 1;
      const filterAnonymous = true;
      const anonymize = false;
      const platform = NotifConstants.PLATFORMS.woocommerce;
      const sessionShowOnce = true;
      const cta = { active: true, text: 'click me', color: '#123456' };
      const storeDomain = 'mystore.com';
      const params = Factory.StreamParams(constants.accountId);
      params.trackURL = [storeDomain];
      params.settings = Object.assign({}, params.settings, {
        displayHold, filterAnonymous, anonymize, platform, sessionShowOnce,
      });
      params.settings.hideOwnConversions = true;
      params.settings.mobileTop = true;
      params.settings.link = { cta };
      params.design = { bulbul: 'big' };
      params.settings.hideLocation = true;
      const res = await httpUtils.createNotification(server, params);
      expect(res).to.have.status(200);

      const notification = await Notification.findOne({ name: params.name });
      expect(notification.trackURL[0]).to.be.equal(storeDomain);
      expect(notification.settings.displayHold).to.be.equal(displayHold);
      expect(notification.settings.filterAnonymous).to.be.equal(filterAnonymous);
      expect(notification.settings.anonymize).to.be.equal(anonymize);
      expect(notification.settings.platform).to.be.equal(platform);
      expect(notification.settings.sessionShowOnce).to.be.true;
      expect(notification.settings.hideOwnConversions).to.be.true;
      expect(notification.design).to.be.deep.equal(params.design);
      expect(notification.settings.mobileTop).to.be.true;
      expect(notification.settings.link.cta.toObject()).to.be.deep.equal(cta);
      expect(notification.settings.hideLocation).to.be.true;
    });

    it('should convert input to URL on shop platforms', async () => {
      const platform = NotifConstants.PLATFORMS.woocommerce;
      const params = Factory.StreamParams(constants.accountId);
      const storeDomain = 'mystore';
      params.trackURL = [storeDomain];
      params.settings = Object.assign({}, params.settings, { platform });
      const res = await httpUtils.createNotification(server, params);
      expect(res).to.have.status(200);

      const notification = await Notification.findOne({ name: params.name });
      expect(notification.trackURL[0]).to.be.equal(storeDomain);
      expect(notification.urlTypes.track).to.be.equal(NotifConstants.URL_TYPES.contains);
    });

    it('should clean shop track URLs (domain only)', async () => {
      const platform = NotifConstants.PLATFORMS.woocommerce;
      const params = Factory.StreamParams(constants.accountId);
      const trackUrl = 'https://shop.myshop.com/path?what=yes';
      const domain = 'shop.myshop.com';
      params.trackURL = [trackUrl];
      params.settings = Object.assign({}, params.settings, { platform });
      const res = await httpUtils.createNotification(server, params);
      expect(res).to.have.status(200);

      const notification = await Notification.findOne({ name: params.name });
      expect(notification.trackURL[0]).to.be.equal(domain);
      expect(notification.urlTypes.track).to.be.equal(NotifConstants.URL_TYPES.contains);
    });

    it('should accept shopify platform', async () => {
      const platform = NotifConstants.PLATFORMS.shopify;
      const params = Factory.StreamParams(constants.accountId);
      params.settings.platform = platform;
      const res = await httpUtils.createNotification(server, params);
      expect(res).to.have.status(200);
    });

    it('should accept regex urlTypes', async () => {
      const params = Factory.PageVisitsParams();
      const regexType = NotifConstants.URL_TYPES.regex;
      params.urlTypes = { track: regexType, display: regexType };
      const res = await createNotification(params);
      expect(res).to.have.status(200);
    });

    it('should not accept invalid regex', async () => {
      const params = Factory.PageVisitsParams();
      const regexType = NotifConstants.URL_TYPES.regex;
      params.urlTypes = { track: regexType, display: regexType };
      params.trackURL[0] = '(*.?)';
      let res = await createNotification(params);

      expect(res.body.error).to.include('Track expression');
      params.trackURL[0] = '123';
      params.displayURLs[0] = '(*.?)';
      res = await createNotification(params);
      expect(res.body.error).to.include('Display expression');
    });

    it('should not accept invalid regex 2', async () => {
      const params = Factory.PageVisitsParams();
      const regexType = NotifConstants.URL_TYPES.regex;
      params.urlTypes = { track: regexType, display: regexType };
      params.displayURLs[0] = 'https://dhcc.pw\\/';
      const res = await createNotification(params);
      expect(res.body.error).to.include('Display expression');
    });

    it('should not accept invalid "contains" values (regex)', async () => {
      const params = Factory.PageVisitsParams();
      const { contains } = NotifConstants.URL_TYPES;
      params.urlTypes = { track: contains, display: contains };
      params.trackURL[0] = '(*.?)';
      let res = await createNotification(params);
      expect(res.body.error).to.include('Track expression');

      params.trackURL[0] = '123';
      params.displayURLs[0] = '(*.?)';
      res = await createNotification(params);
      expect(res.body.error).to.include('Display expression');
    });

    it('should remove trailing slashes and lowercase non-simple URL expressions', async () => {
      const params = Factory.PageVisitsParams();
      params.urlTypes = { track: NotifConstants.URL_TYPES.contains, display: NotifConstants.URL_TYPES.regex };
      const track = 'Get-started';
      const display = 'Signup';
      params.trackURL[0] = `${track}/`;
      params.displayURLs[0] = `${display}/`;
      const res = await createNotification(params);
      expect(res).to.have.status(200);
      const notif = await Notification.findOne();
      expect(notif.trackURL[0]).to.be.equal(track.toLowerCase());
      expect(notif.displayURLs[0]).to.be.equal(display.toLowerCase());
    });

    it('should remove www and http/s in non-simple URL expressions', async () => {
      const params = Factory.PageVisitsParams();
      params.urlTypes = {
        track: NotifConstants.URL_TYPES.contains,
        display: NotifConstants.URL_TYPES.regex,
        trackAbs: false,
        displayAbs: false,
      };
      const track = 'dianzboutique.com';
      const display = 'dianzboutique.com';
      params.trackURL[0] = `http://www.${track}`;
      params.displayURLs[0] = `https://${display}`;
      const res = await createNotification(params);
      expect(res).to.have.status(200);
      const notif = await Notification.findOne();
      expect(notif.trackURL[0]).to.be.equal(track.toLowerCase());
      expect(notif.displayURLs[0]).to.be.equal(display.toLowerCase());
    });

    it('should not fail when no displayURLs or trackURL (woocommerce)', async () => {
      const params = Factory.Woo({ model: false });
      const res = await createNotification(params);
      expect(res).to.have.status(200);
    });

    it('should not accept anonymize === true && filterAnonymous ==== true ', async () => {
      const filterAnonymous = true;
      const anonymize = true;
      const params = Factory.PageVisitsParams(constants.accountId);
      params.settings = Object.assign({}, params.settings, { filterAnonymous, anonymize });

      const res = await httpUtils.createNotification(server, params);
      expect(res).to.have.status(400);
    });

    it('should allow empty link for woocommerce', async () => {
      const params = Factory.Woo({ model: false });
      params.settings.link = { active: true };
      const res = await httpUtils.createNotification(server, params);
      expect(res).to.have.status(200);

      const notification = await Notification.findOne({ name: params.name });
      expect(notification.settings.link.active).to.be.true;
    });

    it('should not url normalize regex url types', async () => {
      const params = Factory.PageVisitsParams();
      const regexType = NotifConstants.URL_TYPES.regex;
      params.urlTypes = { track: regexType, display: regexType };
      params.displayURLs = ['www.90dakika24.com/(tr#|#|\\?utm|.*register)'];
      const res = await createNotification(params);
      expect(res).to.have.status(200);

      const notification = await Notification.findOne({ name: params.name });
      expect(notification.displayURLs[0]).to.be.equal(params.displayURLs[0]);
    });

    it('should clean track and display URLs when track/display absolute is false', async () => {
      const params = Factory.PageVisitsParams();
      params.urlTypes = { trackAbs: false, displayAbs: false };
      const url = 'https://www.provesrc.com/optin?utm_source=facebook#thanks';
      params.displayURLs = [url];
      params.trackURL = [url];

      const res = await httpUtils.createNotification(server, params);
      expect(res).to.have.status(200);

      const cleanUrl = urlUtils.clean(url);
      const notification = await Notification.findOne({ name: params.name });
      expect(notification.urlTypes.trackAbs).to.be.false;
      expect(notification.urlTypes.displayAbs).to.be.false;
      expect(notification.trackURL[0]).to.be.equal(cleanUrl);
      expect(notification.displayURLs[0]).to.be.equal(cleanUrl);
    });
  });

  describe('Localization', () => {
    it('should save localization', async () => {
      const localization = 'he';
      const params = Factory.PageVisitsParams(constants.accountId);
      params.localization = localization;
      const res = await createNotification(params);
      const { _id } = res.body;
      const notification = await Notification.findOne({ _id });
      expect(notification.localization).to.be.equal(localization);
    });
  });

  describe('Stream Notification', () => {
    it('should accept stream specific properties', async () => {
      const params = Factory.StreamParams();
      const maxConversions = 12;
      params.settings.maxConversions = maxConversions;
      params.someoneAlternatives = ['Marketer', 'Salesman'];

      _.set(params, 'settings.theme.title.backgroundColor', '#FFAABB');
      _.set(params, 'settings.hideProduct', true);
      const res = await httpUtils.createNotification(server).send(params);
      expect(res).to.have.status(200);

      const notification = await Stream.findOne({ name: params.name });
      expect(notification.__t).to.be.equal('Stream');
      expect(notification.settings).to.have.property('maxConversions', maxConversions);
      expect(notification.someoneAlternatives).to.deep.equal(params.someoneAlternatives);
      expect(notification.settings.theme.title.backgroundColor).to.be.undefined;
      expect(notification.settings.hideProduct).to.be.true;
    });
  });

  describe('Combo create', () => {
    it('should create Combo notification', async () => {
      const comboType = NotifConstants.COMBO_TYPES.conversions;
      const period = NotifConstants.COMBO_PERIODS.day;
      const combo = Factory.ComboParams(constants.accountId, comboType, period);

      const res = await httpUtils.createNotification(server, combo);
      expect(res).to.have.status(200);

      const notification = await Notification.findOne({ name: combo.name });
      expect(notification.settings.combo).to.have.property('type', comboType);
      expect(notification.settings.combo).to.have.property('period', period);
      expect(notification).to.have.property('type', NotifConstants.notificationTypes.combo);
    });

    it('autoTrack should no be required {type=visits}', async () => {
      const comboType = NotifConstants.COMBO_TYPES.visits;
      const period = NotifConstants.COMBO_PERIODS.day;
      const combo = Factory.ComboParams(constants.accountId, comboType, period);
      delete combo.autoTrack;

      const res = await httpUtils.createNotification(server, combo);
      expect(res).to.have.status(200);
    });
  });

  describe('Info', () => {
    it('should create info notification', async () => {
      const name = '123';
      const title = 'some title';
      const message = 'some message';
      const params = Factory.getInfo({ name, title, message });
      const res = await httpUtils.createNotification(server, params);
      expect(res).to.have.status(200);

      const notif = await Factory.Info.findOne();
      expect(notif.name).to.be.equal(name);
      expect(notif.title).to.be.equal(title);
      expect(notif.message).to.be.equal(message);
    });

    it('should NOT affect other notification types properties (schema.remove)', async () => {
      // This does not work. see Info.js Notification model
      const pvisits = new PageVisits({
        name: 'pv',
        accountId: constants.accountId,
        message: 'visited',
        urlTypes: { track: 'contains' },
      });
      expect(pvisits.urlTypes.track).to.be.equal('contains');
    });
  });

  describe('notification create', () => {
    xit('should update account stats', async () => {
      await Account.update({ _id: constants.accountId }, {
        $unset: {
          'stats.notifications.created': 0,
          'stats.notifications.lastCreated': null,
        },
      });

      const params = Factory.params.pageVisits();
      params.settings = { allTimeEvents: true };
      await createNotification(params);

      const account = await Account.findOne({ _id: constants.accountId });
      expect(account.stats.notifications.created).to.be.equal(1);
      expect(account.stats.notifications.lastCreated).to.not.be.null;
      const diff = Date.now() - account.stats.notifications.lastCreated.getTime();
      expect(diff).to.be.below(100);
    });

    it('should create PageVisits notification', () => {
      const params = Factory.params.pageVisits();
      params.settings = { allTimeEvents: true };
      return createNotification(params).then((res) => {
        const { _id } = res.body;
        return Notification.findOne({ _id });
      }).then((notification) => {
        expect(notification.settings).to.have.property('allTimeEvents', true);
      });
    });

    it('should create Conversion notification', () => {
      const params = Factory.params.conversion();
      return createNotification(params).then(() => Conversion.findOne({ name: params.name })).then((notification) => {
        expect(notification.manuallyShowNotification).to.be.true;
      });
    });

    it('should create conversion notification with displayURLs', () => {
      const params = Factory.params.conversionWithDisplayUrls();
      return createNotification(params).then(() => Notification.findOne(params)).then((notification) => {
        expect(notification.manuallyShowNotification).to.be.false;
      });
    });

    it('should fail create if name exists', async () => {
      const notification = Factory.notificationsFactory.pageVisits();
      const params = Factory.params.pageVisits();
      await notification.save();
      const res = await createNotification(params);
      expect(res).to.have.status(400);
      expect(res.body.error).to.include('exists');
    });

    it('should allow live visitor type', async () => {
      const n = Factory.LiveVisitors({}, { lean: true });
      const res = await createNotification(n);
      expect(res).to.have.status(200);
    });

    it('should create disabled if free user && has active notification', async () => {
      sandbox.restore();
      sandbox.stub(Notification, 'findOne').resolves(null);

      const n = Factory.LiveVisitors({}, { lean: true });
      const res = await createNotification(n);
      expect(res).to.have.status(200);

      sandbox.restore();
      const dbNotif = await Notification.findOne();
      expect(dbNotif.active).to.be.false;
    });
  });

  describe('notification update', () => {
    it('should save someoneAlternative (discriminator properties)', async () => {
      const notif = Factory.Stream();
      await notif.save();

      notif.someoneAlternatives = ['alter', 'alter2'];
      await httpUtils.createNotification(server).send(notif.toObject());

      const dbNotif = await Notification.findOne();
      expect(dbNotif.someoneAlternatives).to.be.eql(notif.someoneAlternatives);
    });

    it('should not set {active=true}', async () => {
      sandbox.restore();
      const url = 'https://provesrc.com/about';
      const notif = Factory.PageVisits(constants.accountId, url, url);
      notif.active = false;
      await notif.save();

      await httpUtils.createNotification(server).send(_.omit(notif.toObject(), 'active'));

      const notification = await Notification.findOne({ _id: notif._id });
      expect(notification.active).to.be.false;
    });

    it('should edit notification when _id is present', () => {
      const expectNotif = Factory.notificationsFactory.pageVisits();
      expectNotif.image = 'https://provesrc.com/icon.png';
      return expectNotif.save().then(() => {
        expectNotif.name = 'new name';
        return createNotification(expectNotif);
      }).then(() => Notification.findOne({ _id: expectNotif._id })).then((notification) => {
        const given = _.omit(notification.toObject(), 'updatedAt');
        const expected = _.omit(expectNotif.toObject(), 'updatedAt');
        expect(given).to.be.deep.equal(expected);
      });
    });

    it('should fail if notification _id doesn\'t exist', async () => {
      const notification = Factory.notificationsFactory.pageVisits();
      const res = await createNotification(notification);
      expect(res).to.have.status(400);
    });

    it('should update urlType and trackURL', async () => {
      const url = 'https://provesrc.com/about';
      const params = Factory.PageVisitsParams(constants.accountId, url, url);
      const res = await createNotification(params);
      const { _id } = res.body;
      const notification = await Notification.findOne({ _id });

      notification.urlTypes.track = NotifConstants.URL_TYPES.contains;
      notification.trackURL = ['about'];
      await httpUtils.createNotification(server).send(notification.toObject());

      const dbNotif = await Notification.findOne({ _id });
      expect(dbNotif.urlTypes).to.have.property('track', NotifConstants.URL_TYPES.contains);
      expect(dbNotif.trackURL[0]).to.equal('about');
    });

    xit('should update account.stats', async () => {
      await Account.update({ _id: constants.accountId }, {
        $unset: {
          'stats.notifications.updated': '',
          'stats.notifications.lastUpdated': '',
        },
      });

      const expectNotif = Factory.notificationsFactory.pageVisits();
      expectNotif.image = 'https://provesrc.com/icon.png';
      await expectNotif.save();

      expectNotif.name = 'new name';
      await createNotification(expectNotif);

      const account = await Account.findOne({ _id: constants.accountId });
      expect(account.stats.notifications.updated).to.be.equal(1);
      expect(account.stats.notifications.lastUpdated).to.not.be.null;
      const diff = Date.now() - account.stats.notifications.lastUpdated.getTime();
      expect(diff).to.be.below(100);
    });

    it('should change type from pageVisits to liveVisitors', async () => {
      const notif = Factory.PageVisits(constants.accountId);
      await notif.save();
      notif.type = NotifConstants.notificationTypes.live;
      await httpUtils.createNotification(server).send(notif);

      const dbNotification = await Notification.findOne({ _id: notif.id });
      expect(dbNotification.__t).to.be.equal(Factory.models.LiveVisitors.modelName);
    });
  });

  describe('validateParams', () => {
    describe('capterra', () => {
      it('should allow page URLs', async () => {
        const params = {
          placeId: 'https://www.capterra.com/p/197469/Nrby/#reviews',
          type: 'review',
          source: 'capterra',
          manuallyShowNotification: true,
          urlTypes: { track: 'all' },
        };
        const normalized = create.normalizeData(params);
        const error = create.validateParams(normalized);
        expect(error).to.not.exist;
      });
    });
  });

  it('should allow {displayURLs.length=0} when {manuallyShowNotification=true}', () => {
    const params = Factory.params.conversion();
    params.displayURLs = [];
    params.manuallyShowNotification = true;

    return httpUtils.consoleRequest(server, ENDPOINT, httpUtils.METHODS.POST).send(params).then((res) => {
      expect(res).to.have.status(200);
    });
  });

  it('should not allow {displayURLs.length=0} when {manuallyShowNotification=false}', () => {
    const params = Factory.params.conversion();
    params.displayURLs = [];
    params.manuallyShowNotification = false;

    return httpUtils.consoleRequest(server, ENDPOINT, httpUtils.METHODS.POST).send(params).catch((err) => {
      expect(err).to.have.status(400);
    });
  });

  function createNotification(params) {
    const request = chai.request(server).post(ENDPOINT).set('cookie', constants.cookieHeader);
    return request.send(params);
  }
});
