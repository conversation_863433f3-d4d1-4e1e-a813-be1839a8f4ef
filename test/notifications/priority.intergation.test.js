const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const httpUtils = require('../httpUtils');
const testUtils = require('../testUtils');

const NotificationFactory = require('../factories/NotificationFactory');

const { Notification } = NotificationFactory;
const EventFactory = require('../factories/EventFactory');

const { Event } = EventFactory.models;
const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;

describe('/notifications/priority (#e2e #slow)', () => {
  let server;
  before(async () => {
    server = await require('../../server');
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    await cleanDb();
  });

  afterEach(async () => {
    await cleanDb();
    sandbox.restore();
  });

  async function cleanDb() {
    return await Promise.all([
      Account.remove(),
      Notification.remove(),
      Event.remove(),
    ]);
  }

  it('should update priority', async () => {
    await AccountFactory.default().save();
    const notif1 = NotificationFactory.getInfo(null, { model: true });
    const notif2 = NotificationFactory.getInfo({ name: 'whoay' }, { model: true });
    const notif3 = NotificationFactory.Stream({ name: 'whoay2' });
    const notifications = [notif1, notif2, notif3];
    await Promise.all(notifications.map(n => n.save()));

    const ids = notifications.map(n => n.id);
    const res = await httpUtils.consoleRequest(server, '/notifications/priority', 'POST').send(ids);
    expect(res).to.have.status(200);

    const [n1, n2, n3, acc] = await Promise.all([
      Notification.findOne({ _id: notif1.id }),
      Notification.findOne({ _id: notif2.id }),
      Notification.findOne({ _id: notif3.id }),
      Account.findOne({ _id: notif1.accountId }),
    ]);

    expect(acc.stats.notifications.priorityChanged).to.be.above(0);
    expect(n1.priority).to.be.equal(0);
    expect(n2.priority).to.be.equal(1);
    expect(n3.priority).to.be.equal(2);
  });

  it('/notifications/list should be sorted by priority', async () => {
    const notif1 = NotificationFactory.getInfo({ priority: 3 }, { model: true });
    const notif2 = NotificationFactory.getInfo({ name: 'whoay', priority: 2 }, { model: true });
    const notif3 = NotificationFactory.Stream({ name: 'whoay2', priority: 1 });
    await Promise.all([notif1, notif2, notif3].map(n => n.save()));

    const res = await httpUtils.consoleRequest(server, '/notifications/list', 'GET');
    expect(res).to.have.status(200);

    const { notifications } = res.body;
    // order should be notif3, notif2, notif3
    expect(notifications[0]._id).to.be.equal(notif3.id);
    expect(notifications[1]._id).to.be.equal(notif2.id);
    expect(notifications[2]._id).to.be.equal(notif1.id);
  });

  it('/notifications/get should be sorted by priority', async () => {
    sandbox.stub(Account, 'findOne').resolves(AccountFactory.default());
    const url = 'https://provesrc.com';
    const notif1 = NotificationFactory.getInfo({ displayURLs: [url], priority: 3 }, { model: true });
    const notif2 = NotificationFactory.getInfo({ name: 'whoay', displayURLs: [url], priority: 2 }, { model: true });
    const notif3 = NotificationFactory.Stream({ name: 'whoay2', displayURLs: [url], priority: 1 });
    const streamEvent = EventFactory.FormStreamEvent({ url: notif3.trackURL[0] });
    await Promise.all([notif1, notif2, notif3, streamEvent].map(item => item.save()));

    const res = await httpUtils.notificationsGet(server, { url });
    expect(res).to.have.status(200);

    expect(res.body).to.have.lengthOf(3);
    expect(res.body[0].name).to.be.equal(notif3.name);
    expect(res.body[1].name).to.be.equal(notif2.name);
    expect(res.body[2].name).to.be.equal(notif1.name);
  });
});
