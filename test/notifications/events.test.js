const chai = require('chai');
const sinon = require('sinon');
const app = require('../../server');

const { expect } = chai;


describe('/notifications/:id/events', () => {
  afterEach(async () => {
    sinon.restore();
  });

  describe('GET', async () => {
    it('should find route', async () => {
      const id = '1234';
      const res = await chai.request(app).get(`/notifications/${id}/events`).session('1234');
      expect(res).to.have.status(200);
    });
  });
});
