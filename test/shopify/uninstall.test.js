const sinon = require('sinon');
const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');
const config = require('../../config');
const slack = require('../../lib/apis/slackNotifier');
const Account = require('../../app/account/models/Account');
const AccountFactory = require('../factories/AccountFactory');
const redis = require('../../lib/redisClient').getClient();
const triggers = require('../../lib/triggers');
const profitWell = require('../../app/billing/profitwell.controller');
const shopifyService = require('../../app/shopify/shopify.service');

describe('/shopify/uninstall', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  const sandbox = sinon.createSandbox();
  afterEach(() => {
    sandbox.restore();
  });

  /**
  Don't throw error because shopify get angry at us and might remove app
   it('should throw error if no such account', (done) => {
    sandbox.stub(Account, 'findOne').resolves(null);
    const shop = 'test.shop.com';
    const data = {id: 123};
    httpUtils.noAuthRequest(server, '/shopify/uninstall', httpUtils.POST).set({'x-shopify-shop-domain': shop}).send(data).end(err => {
      expect(err).to.exist;
      expect(err.response.body.error).to.be.equal(`no account associated with shop ${shop}`);
      done();
    });
   });
  */

  it('should notify slack of uninstall', async () => {
    const email = '<EMAIL>';
    const shop = 'test.shop.com';
    const notifier = sandbox.stub(slack, 'notify').resolves();
    const account = AccountFactory.free(email);
    sandbox.stub(Account.prototype, 'save').resolves();
    sandbox.stub(Account, 'findOne').resolves(account);
    sandbox.stub(account, 'getShop').returns({});

    const data = { id: 123 };
    await httpUtils.noAuthRequest(server, '/shopify/uninstall', httpUtils.POST).set({ 'x-shopify-shop-domain': shop }).send(data);
    expect(notifier.args[0][0]).to.be.equal(`shopify uninstalled by ${email} from ${shop}`);
    expect(notifier.args[0][2]).to.be.deep.equal({ webhook: config.slack.shopify });
  });

  it('should churn subscription in profitWell', async () => {
    const shop = 'test.shop.com';
    const data = { id: 123 };
    const account = AccountFactory.shopify('<EMAIL>');
    account.id = data.id;
    account.subscription = {
      source: 'shopify',
    };

    sandbox.stub(Account.prototype, 'save').rejects({});
    sandbox.stub(Account, 'findOne').resolves(account);
    sandbox.stub(account, 'getShop').returns({});
    sandbox.stub(slack, 'notify').resolves();
    sandbox.stub(redis, 'delAsync').resolves({});
    sandbox.stub(triggers, 'subscriptionStateChanged').resolves({});
    const churnSub = sandbox.stub(profitWell, 'churnSubscription').resolves({});
    sandbox.stub(shopifyService, 'saveEvent').resolves({});
    sandbox.stub(slack, 'notifyError').resolves({});

    await httpUtils.noAuthRequest(server, '/shopify/uninstall', httpUtils.POST).set({ 'x-shopify-shop-domain': shop }).send(data);

    const diff = (Date.now()) - Date.parse(churnSub.args[0][1]);

    expect(churnSub).to.have.been.called;
    expect(churnSub).to.have.been.calledWith(account.id);
    expect(diff).to.be.within(1, 1000);
  });
});
