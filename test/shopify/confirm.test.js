const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const httpUtils = require('../httpUtils');

const constants = require('../constants');
const chargeRes = require('./recChargeRes');

const { Shopify } = require('../../lib/apis/shopify');
const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;
const profitWell = require('../../app/billing/profitwell.controller');
const ProfitWell = require('../../lib/apis/profitwell');
const shopifyService = require('../../app/shopify/shopify.service');
const redis = require('../../lib/redisClient').getClient();
const triggers = require('../../lib/triggers');
const { PRICES, PLANS } = require('../../app/account/plansEnum');
const slack = require('../../lib/apis/slackNotifier');

describe('/shopify/confirm', () => {
  let server;
  before(async () => {
    server = await require('../../server');
    if(!server) throw new Error('server not loaded');
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should not confirm if no such chargeId', async () => {
    const number = parseInt(Math.random() * 10000, 10);
    const url = `/shopify/confirm?charge_id=${number}`;
    return httpUtils.consoleRequest(server, url, httpUtils.GET).catch((err) => {
      expect(err).to.exist;
      expect(err.response.body.error).to.be.equal('charge id not found');
    });
  });

  it('should not confirm if charge status not accepted', async () => {
    sandbox.stub(Shopify.prototype, 'getCharge').resolves(chargeRes);
    sandbox.stub(Account, 'findOne').resolves(AccountFactory.shopify('<EMAIL>'));
    const url = '/shopify/confirm?charge_id=********';

    const res = await httpUtils.consoleRequest(server, url, httpUtils.GET).redirects(0);
    expect(res).to.redirect;
    expect(res.headers.location).to.include('/shopify-decline?shop=');
  });

  describe('profitwell reporting', () => {
    afterEach(() => {
      sinon.restore();
    });

    function stubs() {
      const updateSub = sinon.stub(profitWell, 'updateSubscription');
      return { updateSub };
    }

    it('should update subscriber in profitwell', async () => {
      const { updateSub } = stubs();
      const chargeResponse = Object.assign({}, chargeRes);
      chargeResponse.status = 'accepted';
      const activatedCharge = {
        status: 'active',
        name: chargeResponse.name,
      };
      const account = AccountFactory.shopify('<EMAIL>');
      const params = {
        email: account.email,
        plan: `${chargeResponse.name} (Shopify)`,
        value: PRICES[PLANS.STARTER] * 100,
        interval: ProfitWell.INTERVALS.month,
      };

      sandbox.stub(Shopify.prototype, 'getCharge').resolves(chargeResponse);
      sandbox.stub(Shopify.prototype, 'activateCharge').resolves(activatedCharge);
      sandbox.stub(Account, 'findOne').resolves(account);
      sandbox.stub(Account.prototype, 'save').resolves({});
      updateSub.resolves({});
      sandbox.stub(shopifyService, 'saveEvent').resolves({});
      sandbox.stub(redis, 'delAsync').resolves({});
      sandbox.stub(triggers, 'subscriptionStateChanged').resolves({});
      sandbox.stub(slack, 'notify').resolves({});

      const url = '/shopify/confirm?charge_id=********';

      const res = await httpUtils.consoleRequest(server, url, httpUtils.GET).redirects(0);

      expect(res).to.redirect;
      expect(updateSub).to.have.been.called;
      expect(updateSub).to.have.been.calledWith(account.id, params);
    });
  });
});
