const httpUtils = require('../httpUtils');
const chai = require('chai');

const { expect } = chai;
const sinon = require('sinon');
const serverP = require('../../server');
const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;
const { Shopify } = require('../../lib/apis/shopify');
const coupons = require('../../app/shopify/coupons');

describe('/shopify/charge', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  const sandbox = sinon.createSandbox();
  afterEach(() => {
    sandbox.restore();
  });

  it('should not accept non-existant plans', async () => {
    const url = '/shopify/charge?plan=abasc';
    const res = await httpUtils.consoleRequest(server, url, httpUtils.GET);
    expect(res).to.not.have.status(200);
    expect(res.body.error).to.include('instance.plan');
  });

  it('should error if account is not using shopify', async () => {
    sandbox.stub(Account, 'findOne').resolves(AccountFactory.default());
    const url = '/shopify/charge?plan=starter';
    const res = await httpUtils.consoleRequest(server, url, httpUtils.GET);
    expect(res).to.not.have.status(200);
    expect(res.body.error).to.be.equal('account is not using shopify');
  });

  it('should error on non-existant coupon', async () => {
    const url = '/shopify/charge?plan=starter&coupon=shit-head';
    sandbox.stub(Account, 'findOne').resolves(AccountFactory.shopify('<EMAIL>'));
    const res = await httpUtils.consoleRequest(server, url, httpUtils.GET);
    expect(res).to.not.have.status(200);
    expect(res.body.error).to.be.equal('coupon is invalid or has expired');
  });

  it('should error on already used coupon', async () => {
    const coupon = 'someCoupon';
    const url = `/shopify/charge?plan=starter&coupon=${coupon}`;
    const account = AccountFactory.shopify('<EMAIL>');
    account.coupons = coupon;
    sandbox.stub(Account, 'findOne').resolves(account);
    const res = await httpUtils.consoleRequest(server, url, httpUtils.GET);
    expect(res).to.not.have.status(200);
    expect(res.body.error).to.be.equal('coupon has already been used');
  });

  it('should create charge with discount name and price', async () => {
    const chargeRes = require('./recChargeRes');
    const coupon = 'NATAN-TEST';
    const { factor, descripton } = coupons[coupon];
    const url = `/shopify/charge?plan=starter&coupon=${coupon}`;

    const account = AccountFactory.shopify('<EMAIL>');
    sandbox.stub(Account, 'findOne').resolves(account);
    const saveStub = sandbox.stub(account, 'save').resolves();
    const chargeStub = sandbox.stub(Shopify.prototype, 'charge').resolves(chargeRes);

    await httpUtils.consoleRequest(server, url, httpUtils.GET);
    expect(chargeStub).to.have.been.calledWith(sinon.match(new RegExp(descripton)), 19 * factor);
    expect(saveStub).to.have.been.calledOnce;
    expect(account.coupons).to.include(coupon);
  });

  it('should create charge with discount name and price for staff automatically', async () => {
    const chargeRes = require('./recChargeRes');
    const url = '/shopify/charge?plan=starter';

    const account = AccountFactory.shopify('<EMAIL>');
    account.shopify[0].plan_name = 'staff_business';
    sandbox.stub(Account, 'findOne').resolves(account);
    const saveStub = sandbox.stub(account, 'save').resolves();
    const chargeStub = sandbox.stub(Shopify.prototype, 'charge').resolves(chargeRes);

    await httpUtils.consoleRequest(server, url, httpUtils.GET);
    const coupon = 'WeLoveShopify50';
    const { factor, descripton } = coupons[coupon];
    expect(chargeStub).to.have.been.calledWith(sinon.match(new RegExp(descripton)), 19 * factor);
    expect(saveStub).to.have.been.calledOnce;
    expect(account.coupons).to.include(coupon);
  });
});
