const sinon = require('sinon');
const chai = require('chai');

const { expect } = chai;
const httpUtils = require('../httpUtils');
const testUtils = require('../testUtils');
const app = require('../../server');

describe('/shopify/shop-update', () => {
  afterEach(async () => {
    sinon.restore();
  });

  describe('endpoint', () => {
    it('should find the API', async () => {
      const res = await chai.request(app).post('/shopify/shop-update');
      expect(res).to.have.status(200);
    });
  });
});
