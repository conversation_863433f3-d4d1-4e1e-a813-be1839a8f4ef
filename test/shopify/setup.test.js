const server = require('../../server');
const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();

const _ = require('lodash');
const httpUtils = require('../httpUtils');
const constants = require('../constants');
const setup = require('../../app/shopify/setup');

const { Notification } = require('../factories/NotificationFactory');

describe('/shopify/setup', () => {
  afterEach(() => {
    sandbox.restore();
  });

  describe('getAccount', () => {
    const { Account } = require('../factories/AccountFactory');
    const { getAccount } = setup.units;

    it('should auto login if shop found', async () => {
      const req = { cookies: {}, session: {} };
      const shop = { id: 1234 };
      const acc = new Account({ shopify: [shop], email: '<EMAIL>' });
      sandbox.stub(Account, 'findOne').withArgs(sinon.match.hasOwn('shopify.id')).resolves(acc);
      sandbox.stub(Account.prototype, 'save').resolves();

      const { account } = await getAccount(req, sandbox.stub(), shop);
      expect(account).to.be.deep.equal(acc);
      expect(req.session).to.be.deep.equal({ accountId: acc._id.toString(), apiKey: acc.apiKey, email: acc.email });
    });

    it('should add shop to current account if logged in', async () => {
      const email = '<EMAIL>';
      const shop = { id: 12, email: '<EMAIL>' };
      const acc = new Account({ email, password: '12345' });
      const req = { cookies: {}, session: { email, accountId: acc._id.toString, apiKey: acc.apiKey } };
      const findOne = sandbox.stub(Account, 'findOne');
      findOne.withArgs(sinon.match.hasOwn('_id')).resolves(acc);
      sandbox.stub(Account.prototype, 'save').resolves();

      const { account } = await getAccount(req, {}, shop);
      expect(account).to.be.deep.equal(acc);
      expect(account.shopify[0].id).to.be.equal(shop.id);
    });

    it('should return null if shopify email exists and not logged in', async () => {
      const email = '<EMAIL>';
      const shop = { id: 12, email: '<EMAIL>' };
      const acc = new Account({ email, password: '12345' });
      const req = { cookies: {}, session: null };
      const findOne = sandbox.stub(Account, 'findOne');
      findOne.withArgs(sinon.match.hasOwn('email')).resolves(acc);
      sandbox.stub(Account.prototype, 'save').resolves();

      const results = await getAccount(req, sandbox.stub(), shop);
      expect(results).to.be.null;
    });

    it('should create account if no email and no shop', async () => {
      const req = { cookies: {}, session: {} };
      const shop = { email: '<EMAIL>' };
      sandbox.stub(Account, 'findOne').resolves(null);
      sandbox.stub(Account.prototype, 'save').resolves();

      const { account } = await getAccount(req, { cookie: () => {} }, shop);
      expect(account).to.exist;
      expect(account.ip).to.not.be.null;
      expect(req.session).to.be.deep.equal({ accountId: account._id.toString(), apiKey: account.apiKey, email: shop.email });
      expect(account.active).to.be.true;
    });
  });

  describe('getShopifyNotification', () => {
    const { getShopifyNotification } = setup.units;

    beforeEach(async () => {
      await server;
    });

    afterEach(async () => {
      await Notification.remove();
    });

    it('should be able to save Shopify notification', async () => {
      const notif = getShopifyNotification(constants.accountId);
      await notif.save();

      expect(notif.type).to.be.equal('stream');
    });
  });
});
