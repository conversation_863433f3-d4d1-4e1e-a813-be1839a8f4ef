const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');
const config = require('../../config');
const { Account } = require('../factories/AccountFactory');

describe('/shopify/install', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  it('should redirect to shopify store', async () => {
    const shop = 'provesrctest.myshopify.com';
    const res = await httpUtils.consoleRequest(server, `/shopify/install?shop=${shop}`, httpUtils.GET);
    const url = `https://${shop}/admin/oauth/authorize?client_id=${config.shopify.apiKey}&scope=${config.shopify.scope}&redirect_uri=http://localhost:3000/shopify/setup`;
    expect(res).to.redirectTo(url);
  });

  it('should throw error because of bad HMAC', async () => {
    const query = `shop=provesrctest&hmac=12351&code=1234&timestamp=${Date.now()}`;
    expect(httpUtils.consoleRequest(server, `/shopify/install?${query}`, httpUtils.GET)).to.eventually.be.rejected;
  });
});
