const sinon = require('sinon');
const { expect } = require('chai');
const httpUtils = require('../httpUtils');
const constants = require('../constants');

const NotifFactory = require('../factories/NotificationFactory');
const Notification = require('../../app/notifications/models/Notification');
const AccountFactory = require('../factories/AccountFactory');

const Event = require('../../app/events/models/Event');
const { SHOPIFY_EVENT_TYPES } = require('../../app/constants');

const { Account } = AccountFactory;

describe('shopify type', () => {
  let server;
  before(async () => {
    server = await require('../../server');
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(() => Promise.all([Notification.remove({}), Event.remove({}), Account.remove({})]));

  describe('/notifications/create', () => {
    it('should save the correct event type on creation', async () => {
      const eventType = SHOPIFY_EVENT_TYPES.pos;
      const account = AccountFactory.shopify('<EMAIL>', 'test.shop.com');
      await account.save();
      const params = NotifFactory.Shopify();
      params.settings.eventType = eventType;

      const res = await httpUtils.Agent(server, account.apiKey).consoleRequest(server, constants.NOTIFICATIONS.CREATE, httpUtils.POST).send(params);

      expect(res).to.have.status(200);
      const notification = await Notification.findOne({
        accountId: account.id,
        name: params.name,
      });
      expect(notification).to.exist;
      expect(notification.settings.eventType).to.equal(eventType);
    });

    it('should not accept non existing event type', async () => {
      const account = AccountFactory.shopify('<EMAIL>', 'test.shop.com');
      await account.save();
      const params = NotifFactory.Shopify({ eventType: 'zabi' });
      const res = await httpUtils.Agent(server, account.apiKey).consoleRequest(server, constants.NOTIFICATIONS.CREATE, httpUtils.POST).send(params);
      expect(res).to.have.status(400);
      expect(res.body.error).to.include('event type');
    });

    it('should set default event type if nothing is sent', async () => {
      const account = AccountFactory.shopify('<EMAIL>', 'test.shop.com');
      await account.save();
      const params = NotifFactory.Shopify({});
      const res = await httpUtils.Agent(server, account.apiKey).consoleRequest(server, constants.NOTIFICATIONS.CREATE, httpUtils.POST).send(params);
      const notification = await Notification.findOne({
        accountId: account.id,
        name: params.name,
      });
      expect(res).to.have.status(200);
      expect(notification.settings.eventType).to.equal('all');
    });
  });
});
