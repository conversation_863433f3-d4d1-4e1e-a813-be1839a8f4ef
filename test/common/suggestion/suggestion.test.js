const chai = require('chai');
const sinon = require('sinon');
const server = require('../../../server');
const UrlSuggestion = require('../../../app/common/urlSuggestions/models/UrlSuggestion');
const FormUrlSuggestion = require('../../../app/common/urlSuggestions/models/FormUrlSuggestion');
const httpUtils = require('../../httpUtils');

const { expect } = chai;

describe('urlSuggestion', () => {
  let app;
  beforeEach(async () => {
    app = await server;
  });

  afterEach(() => {
    sinon.restore();
  });

  function stubs() {
    const urlSuggestion = sinon
      .mock(UrlSuggestion)
      .expects('find')
      .chain('select')
      .chain('sort')
      .chain('lean');

    const formUrlSuggestion = sinon
      .mock(FormUrlSuggestion)
      .expects('find')
      .chain('select')
      .chain('sort')
      .chain('lean');

    return { urlSuggestion, formUrlSuggestion };
  }

  it('should use model by type(query input)', async () => {
    const { urlSuggestion, formUrlSuggestion } = stubs();
    urlSuggestion.resolves({});
    formUrlSuggestion.resolves({});

    const res = await httpUtils.consoleRequest(app, '/suggestions?type=form&search=', 'GET');

    expect(res).to.have.status(200);
    expect(formUrlSuggestion).to.be.calledOnce;
    expect(urlSuggestion).not.to.be.called;
  });

  it('should return empty array if type(query input) is not correct', async () => {
    const { urlSuggestion } = stubs();
    urlSuggestion.resolves({});

    const res = await httpUtils.consoleRequest(app, '/suggestions?type=some&search=', 'GET');

    expect(res).to.have.status(400);
    expect(res).with.property('text', '{"error":"instance.type is not one of enum values: form,display"}');
  });

  it('should return array of suggestions that filtered by search (query input)', async () => {
    const { formUrlSuggestion } = stubs();
    const suggestions = [{
      url: 'google.com',
      hit: 10,
      lastHit: Date.parse('0'),
    }, {
      url: 'walla.com',
      hit: 100,
      lastHit: Date.parse('11'),
    }];
    formUrlSuggestion.resolves(suggestions);

    const res = await httpUtils.consoleRequest(app, '/suggestions?type=form&search=', 'GET');

    expect(res).to.have.status(200);
    expect(res.body).to.be.eql({ suggestions });
  });
});
