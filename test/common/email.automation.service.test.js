const chai = require('chai');
const sinon = require('sinon');
const emailService = require('../../app/common/email.automation.service');
const notifier = require('../../app/common/notifier.service');
const mailerlite = require('../../lib/apis/mailerlite');

const { expect } = chai;

describe('email.automation.service', () => {
  afterEach(() => {
    sinon.restore();
  });

  function stubs() {
    const mailerliteSubscribe = sinon.stub(mailerlite, 'subscribe');
    const mailerliteUpdate = sinon.stub(mailerlite, 'updateSubscriber');
    const notifierError = sinon.stub(notifier, 'notifyError');
    return { mailerliteSubscribe, mailerliteUpdate, notifierError };
  }
  describe('createSubscription', () => {
    it('should call notifier service if failed to create subscription', async () => {
      const { mailerliteSubscribe, notifierError } = stubs();

      const errStr = 'failed to create subscription';
      const email = '<EMAIL>';
      const group = 'customers';
      const fields = { id: 1 };
      const msg = 'mailerlite: failed to create subscription';

      mailerliteSubscribe.rejects({ errStr });
      notifierError.resolves({});

      await emailService.createSubscription(email, { group, fields });

      expect(notifierError).to.be.called;
      expect(mailerliteSubscribe).to.be.called;
      expect(notifierError).to.be.calledWith({ errStr }, msg, { email, group, fields });
    });

    it('should correctly call mailerlite api lib', () => {
      const { mailerliteSubscribe } = stubs();

      const email = '<EMAIL>';
      const group = 'customers';
      const fields = { id: 1 };

      mailerliteSubscribe.resolves({});
      emailService.createSubscription(email, { group, fields });

      expect(mailerliteSubscribe).to.be.calledWith(email, group, fields);
    });
  });

  describe('updateSubscription', () => {
    it('should call notifier service if failed to update subscription', async () => {
      const { notifierError, mailerliteUpdate } = stubs();
      const email = '<EMAIL>';
      const fields = { id: 1 };
      const errStr = 'failed to create subscription';
      const msg = 'mailerlite: failed to update subscription';

      await mailerliteUpdate.rejects({ errStr });
      await notifierError.resolves({});

      await emailService.updateSubscription(email, fields);

      expect(notifierError).to.be.called;
      expect(notifierError).to.have.been.calledWith({ errStr }, msg, { email, fields });
    });

    it('should call correctly the mailerlite api lib', () => {
      const { mailerliteUpdate } = stubs();

      const email = '<EMAIL>';
      const fields = { id: 1 };

      mailerliteUpdate.resolves({});
      emailService.updateSubscription(email, fields);

      expect(mailerliteUpdate).to.be.calledWith(email, fields);
    });
  });
});
