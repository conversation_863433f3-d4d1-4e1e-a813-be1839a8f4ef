const chai = require('chai');
const cookieSig = require('cookie-signature');
const mockSession = require('mock-session');
const uuid = require('uuid/v4');
const querystring = require('querystring');
const config = require('../config');
const constants = require('./constants');

const METHODS = {
  POST: 'POST', GET: 'GET', DELETE: 'DELETE', PUT: 'PUT',
};

module.exports = new Agent();
module.exports.GET = METHODS.GET;
module.exports.POST = METHODS.POST;
module.exports.DELETE = METHODS.DELETE;
module.exports.PUT = METHODS.PUT;
module.exports.METHODS = METHODS;
/**
 * An agent is an http client that stores cookies, for our tests it represents a visitor
 * @returns {Agent}
 */
module.exports.Agent = (server, apiKey) => new Agent(server, apiKey);

/**
 * http client agent that stores cookies, for our tests it represents a visitor
 * @class Agent
 * @param app the express app
 * @param apiKey the account's apiKey
 * @constructor
 */
function Agent(app, apiKey) {
  if(app) this.agent = chai.request.agent(app);
  this.apiKey = apiKey;
  if(apiKey) {
    const start = apiKey.indexOf('.');
    const end = apiKey.lastIndexOf('.');
    try {
      const string = Buffer.from(apiKey.substring(start, end + 1), 'base64').toString('utf8');
      const json = JSON.parse(string);
      if(json) this.accountId = json.accountId;
    } catch(err) {}
  } else {
    this.accountId = constants.accountId;
  }
  this.makeRequest = makeRequest;
}

Agent.prototype.consoleRequest = function (server, endpoint, method, opts) {
  let cookie = constants.cookieHeader;
  if(this.apiKey) {
    cookie = this.makeSession(this.accountId);
  }
  return this.makeRequest(server, endpoint, method).set('cookie', cookie);
};

Agent.prototype.apiRequest = function (server, endpoint, method, options) {
  const opts = options || {};
  const apiKey = this.apiKey || opts.apiKey || constants.apiKey;
  const headers = Object.assign({ authorization: `Bearer ${apiKey}` }, opts.headers);
  if(!headers['x-ps-uid']) headers['x-ps-uid'] = uuid();

  return this.makeRequest(server, endpoint, method).set(headers);
};

Agent.prototype.noAuthRequest = function (server, endpoint, method) {
  return this.makeRequest(server, endpoint, method);
};

/**
 * @param server
 * @param {object?} opts additional options
 * @param {string?} opts.apiKey API key to send as bearer
 * @param {string?} opts.url the url to get notifications for
 * @param {string?} opts.unique wether the visit to the url is unique
 */
Agent.prototype.eventTrack = Agent.prototype.notificationsGet = function (server, opts) {
  if(!opts) opts = {};
  const apiKey = this.apiKey || opts.apiKey || constants.apiKey;
  const { url } = opts;
  const { unique } = opts;
  const headers = opts.headers || {};
  Object.assign(headers, { authorization: `Bearer ${apiKey}`, 'x-ps-uid': uuid() });
  if(url) {
    return this
      .apiRequest(server, constants.NOTIFICATIONS.GET, METHODS.POST)
      .set(headers)
      .send({ url, unique: !!unique });
  }
  return this.makeRequest(server, constants.NOTIFICATIONS.GET, METHODS.POST).set(headers);
};

Agent.prototype.createNotification = function (server, params) {
  return this
    .makeRequest(server, constants.NOTIFICATIONS.CREATE, METHODS.POST)
    .set('cookie', constants.cookieHeader)
    .send(params);
};

Agent.prototype.setNotificationState = function (server, params) {
  const { id } = params;
  const { active } = params;
  const data = { id, active };
  return this.consoleRequest(server, constants.NOTIFICATIONS.STATE, METHODS.POST).send(data);
};

/**
 * @param server
 * @param {object} opts
 * @param {string} opts.id the notification id
 */
Agent.prototype.deleteNotification = function (server, opts) {
  if(!opts) throw new Error('must provide options');
  return this.consoleRequest(server, constants.NOTIFICATIONS.DELETE, METHODS.POST).send({ _id: opts.id });
};

/**
 * @param server
 * @param {object} opts
 * @param {string} opts.id
 * @param {string} opts.event
 * @param {object} opts.extraData
 */
Agent.prototype.sendAnalytics = function (server, opts) {
  const params = { id: opts.id, event: opts.event };
  Object.assign(params, opts.extraData);
  if(opts.visitor) params.visitor = true;
  return this.apiRequest(server, constants.NOTIFICATIONS.ANALYTICS, METHODS.POST).send(params);
};

/**
 * @param server
 * @param {object} opts
 * @param {string?} opts.id notification id
 * @param {string?} opts.startDate analytics start date YYYY-MM-DD
 * @param {string?} opts.endDate analytics end date YYYY-MM-DD
 */
Agent.prototype.getNotificationAnalytics = function (server, opts) {
  let endpoint = constants.NOTIFICATIONS.ANALYTICS;
  const query = [];
  if(!opts) opts = {};
  if(opts.id) query.push(`id=${opts.id}`);
  if(opts.startDate) query.push(`startDate=${opts.startDate}`);
  if(opts.endDate) query.push(`endDate=${opts.endDate}`);
  endpoint += `?${query.join('&')}`;
  return this.consoleRequest(server, endpoint, METHODS.GET);
};

Agent.prototype.pingSimple = function (server, uid, host) {
  const cookie = this.makeCookie('psuid', uid, config.cookie.secret);
  return this.ping(server, { cookie, origin: host });
};

Agent.prototype.ping = function (server, headers) {
  return this.apiRequest(server, '/notifications/ping', METHODS.POST, { headers });
};

Agent.prototype.getOnboarding = function (server) {
  return this.consoleRequest(server, '/account/onboarding');
};

/**
 * @param server
 * @param {object} params
 * @param {string} params.website
 * @param {string} params.refer
 * @param {string} params.message
 */
Agent.prototype.postOnboarding = function (server, params) {
  return this.consoleRequest(server, '/account/onboarding', METHODS.POST).send(params);
};

// region webhooks

Agent.prototype.trackWebhook = function (server, webhookId, email, timestamp, ip) {
  const req = this.noAuthRequest(server, `${constants.WEBHOOKS.TRACK}/${webhookId}`, METHODS.POST);
  if(email) {
    return req.send({ email, timestamp: timestamp || Date.now(), ip });
  }
  return req;
};

Agent.prototype.trackWebhookOpts = function (server, opts) {
  if(!opts) opts = {};
  const {
    webhookId, email, timestamp, ip, firstName, lastName, city, country, countryCode, order,
  } = opts;
  const req = this.noAuthRequest(server, `${constants.WEBHOOKS.TRACK}/${webhookId}`, METHODS.POST);

  if(email) {
    return req.send({
      email, timestamp: timestamp || Date.now(), ip, firstName, lastName, city, country, countryCode, order,
    });
  }
  return req;
};

Agent.prototype.trackWebhookData = function (server, webhookId, data, headers, query) {
  if(!headers) headers = {};

  let type = 'json';
  if(headers['content-type'] === 'application/x-www-form-urlencoded') type = 'form';

  const querystr = querystring.stringify(query);
  return this
    .noAuthRequest(server, `${constants.WEBHOOKS.TRACK}/${webhookId}?${querystr}`, METHODS.POST)
    .type(type)
    .set(headers)
    .send(data);
};

Agent.prototype.trackWooCommerce = function (server, data, opts) {
  return this.apiRequest(server, '/webhooks/track/woocommerce', METHODS.POST, opts).send(data);
};

Agent.prototype.trackMagento2 = function (server, data, opts) {
  return this.apiRequest(server, '/webhooks/track/magento2', METHODS.POST, opts).send(data);
};

Agent.prototype.trackWordpress = function (server, data) {
  return this.apiRequest(server, '/webhooks/track/wordpress', METHODS.POST).send(data);
};

Agent.prototype.trackShopify = function (server, shop, data) {
  const headers = { 'x-shopify-shop-domain': shop };
  return this.noAuthRequest(server, `/webhooks/track/shopify?apiKey=${this.getApiKey()}`, METHODS.POST).set(headers).send(data);
};

// endregion

// region form

Agent.prototype.trackForm = function (server, url, email, timestamp, ip) {
  const req = this.apiRequest(server, constants.EVENTS.TRACK_FORM, METHODS.POST);
  if(ip) req.set({ 'x-real-ip': ip });
  return req.send({ url, email, timestamp });
};

Agent.prototype.trackFormOpts = function (server, opts) {
  if(!opts) opts = {};
  const {
    url, email, timestamp, ip, firstName, lastName,
  } = opts;

  const req = this.apiRequest(server, constants.EVENTS.TRACK_FORM, METHODS.POST, opts);
  if(ip) req.set({ 'x-real-ip': ip });

  return req.send({
    url, email, timestamp, firstName, lastName,
  });
};

Agent.prototype.trackFormData = function (server, opts, data) {
  return this.apiRequest(server, constants.EVENTS.TRACK_FORM, METHODS.POST, opts).send(data);
};

// endregion

Agent.prototype.accountConfiguration = function (server, options) {
  if(!options) options = {};
  const host = options.host || 'provesrc.com';
  const headers = Object.assign({ origin: host }, options.headers);
  return this.apiRequest(server, constants.ACCOUNT.CONFIGURATION, METHODS.GET, options).set(headers).query({ host });
};

Agent.prototype.configApiGET = function (server, host, options) {
  if(!options) options = {};
  const headers = Object.assign({ origin: host }, options.headers);
  return this.apiRequest(server, constants.ACCOUNT.CONFIGURATION, METHODS.GET, options)
    .set(headers)
    .query({ host, url: options.url });
};

Agent.prototype.configConsoleGET = function (server) {
  return this.consoleRequest(server, constants.ACCOUNT.CONFIGURATION, METHODS.GET);
};

Agent.prototype.configPOST = function (server, data) {
  return this.consoleRequest(server, constants.ACCOUNT.CONFIGURATION, METHODS.POST).send(data);
};

Agent.prototype.signup = function (server, options) {
  const opts = options || {};
  const { email } = opts;
  const { password } = opts;
  return this.noAuthRequest(server, constants.ACCOUNT.SIGNUP, METHODS.POST).send({ email, password });
};

/**
 * @param server
 * @param {object} options
 * @param {string} options.email
 * @param {string} options.password
 */
Agent.prototype.login = function (server, options) {
  const opts = options || {};
  const { email } = opts;
  const { password } = opts;
  return this.noAuthRequest(server, constants.ACCOUNT.LOGIN, METHODS.POST).send({ email, password });
};

// region helpers

Agent.prototype.makeCookie = function (key, value, secret) {
  const signed = cookieSig.sign(value, secret);
  return `${key}=s:${signed}`;
};

Agent.prototype.makeSession = function (accountId) {
  const { name, secret } = config.cookie;
  return mockSession(name, secret, { accountId });
};

Agent.prototype.getApiKey = function (options) {
  const opts = options || {};
  return this.apiKey || opts.apiKey || constants.apiKey;
};

function makeRequest(server, endpoint, method) {
  const request = this.agent || chai.request(server);
  const methodLower = method && method.toLowerCase();
  if(!method || !request[methodLower]) {
    return request.get(endpoint);
  }
  return request[methodLower](endpoint);
}

// endregion
