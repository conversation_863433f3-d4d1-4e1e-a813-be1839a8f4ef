const Account = require('../../app/account/models/Account');
const { PLANS } = require('../../app/account/plansEnum');
const dateUtils = require('../../lib/utils/dateUtils');
const constants = require('../constants');

module.exports = {
  Account,

  default: () => new Account({
    _id: constants.accountId,
    email: constants.email,
    password: constants.password,
    subscription: constants.subscription,
    configuration: {
      whitelabel: false,
    },
    affiliate: {
      id: constants.affiliateId,
    },
    createdAt: new Date('2019-01-01'),
  }),

  inactiveSubscription(email) {
    const date = new Date('2018-01-01');
    const params = {
      email,
      active: true,
      hashedPassword: false,
      password: 'test',
      subscription: {
        created: date,
        transactionDate: date,
        untilDate: date,
        plan: PLANS.STARTER,
      },
      createdAt: date,
    };
    return { params, model: new Account(params) };
  },

  monthlySubscription(email = '<EMAIL>', pass = '12345', options) {
    const opts = options || {};
    const created = new Date();
    const untilDate = created;
    untilDate.setMonth(untilDate.getUTCMonth() + 1);
    const transactionDate = Date.now() - dateUtils.MILLISECONDS_IN_DAY * 4;
    const params = {
      active: true,
      email,
      hashedPassword: false,
      password: pass || 'test',
      subscription: {
        created, untilDate, plan: PLANS.STARTER, contractName: 'Starter Monthly Plan', transactionDate, recentIPN: 'CHARGE',
      },
      createdAt: new Date('2019-01-01'),
    };
    const account = new Account(params);
    if(opts.model) {
      return account;
    }
    return { params, model: account };
  },

  free(email = '<EMAIL>', password = '123') {
    return new Account({
      email, hashedPassword: false, password, active: true, createdAt: Date.now(),
    });
  },

  shopify(email, shop, domain) {
    const account = this.free(email);
    const myshopify = shop || 'natanavra.myshopify.com';
    if(!domain) domain = myshopify;
    account.shopify = [{
      id: 1234, myshopify_domain: myshopify, token: '12314', domain, email, plan_name: 'affiliate',
    }];
    return account;
  },
};
