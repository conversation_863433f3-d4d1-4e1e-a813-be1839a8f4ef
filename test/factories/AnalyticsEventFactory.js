const dateUtils = require('../../lib/utils/dateUtils');
const AnalyticsEvent = require('../../app/notifications/models/AnalyticsEvent');

module.exports = {
  AnalyticsEvent,

  /**
	 * @param {object} opts
	 * @param {string} opts.accountId
	 * @param {string} [opts.notificationId=null]
	 * @param {string} [opts.event='view']
	 * @param {string} [opts.date=today]
	 * @return {AnalyticsEvent}
	 */
  makeEvent(opts) {
    const { accountId } = opts;
    const notificationId = opts.notificationId || null;
    const date = dateUtils.normalize(opts.date) || dateUtils.todayNormalized12am();

    const retval = new AnalyticsEvent({ accountId, notificationId, date });
    addMinutesAndTotals(retval);
    return retval;
  },
};

function addMinutesAndTotals(document) {
  const keys = ['views', 'clicks', 'hovers'];
  keys.push('conversions', 'viewConversions', 'hoverConversions', 'clickConversions');
  keys.push('webhooks', 'woocommerceOrders');
  keys.push('visitors', 'engagedVisitors');

  for(let i = 0; i < keys.length; i++) {
    const key = keys[i];
    const result = randomCounts(10, document.date);
    document.total[key] = result.total;
    document[key] = result.minutes;
  }
}

function randomCounts(numCounts, date) {
  const isToday = dateUtils.isToday(date);
  const max = isToday ? dateUtils.minuteOfTheDay() : Date.MINUTES_IN_DAY;

  const minutes = {};
  let total = 0;
  for(let i = 0; i < numCounts; i++) {
    const key = Math.floor(Math.random() * max);
    const num = Math.floor(Math.random() * 10 + 1);
    minutes[key] = num;
    total += num;
  }
  return { minutes, total };
}
