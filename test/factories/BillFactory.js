const Bill = require('../../app/billing/Bill');
const constants = require('../constants');
const dateUtils = require('../../lib/utils/dateUtils');

module.exports = {
  Bill,

  makeBill(accountId, date, opts) {
    if(!opts) opts = {};
    if(!accountId) accountId = constants.accountId;
    if(!date) date = new Date();

    const limit = opts.limit || 1000;

    const retval = new Bill({ accountId, date, limit });
    fillBill(retval);
    return retval;
  },
};

// Almost like kill bill
function fillBill(bill) {
  bill.days = {};

  const numDays = Math.round((Math.random() + 1) * 10);
  let total = 0;
  for(let i = 0; i <= numDays; i++) {
    const day = Math.round(Math.random() * 30 + 1);
    const num = Math.round(Math.random() * 20);

    if(!bill.days[day]) bill.days[day] = 0;
    bill.days[day] += num;
    total += num;
  }
  bill.total = total;
}
