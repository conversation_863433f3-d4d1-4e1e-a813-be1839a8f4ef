const crypto = require('crypto');
const _ = require('lodash');
const { accountId } = require('../constants');

const Notification = require('../../app/notifications/models/Notification');
const ConversionNotification = require('../../app/notifications/models/Conversion');
const PageVisitsNotification = require('../../app/notifications/models/PageVisits');
const Combo = require('../../app/notifications/models/Combo');
const Stream = require('../../app/notifications/models/Stream');
const LiveVisitors = require('../../app/notifications/models/LiveVisitors');
const Info = require('../../app/notifications/models/Info');

const notifConsts = require('../../app/notifications/constants');
const webhookGenerate = require('../../app/webhooks/generate').generate;
const constants = require('../constants');

const params = {
  conversion() {
    return {
      accountId,
      type: notifConsts.notificationTypes.conversion,
      name: crypto.randomBytes(20).toString('hex'),
      refer: 'converters',
      message: 'are converting',
      webhookId: webhookGenerate().webhookId,
      autoTrack: false,
      manuallyShowNotification: true,
    };
  },
  pageVisits() {
    return {
      accountId,
      type: notifConsts.notificationTypes.pageVisits,
      name: 'page visits notif',
      refer: 'visitors',
      message: 'are around',
      trackURL: ['https://provesrc.com/about'],
      displayURLs: ['https://provesrc.com'],
      autoTrack: true,
    };
  },
};

params.conversionWithDisplayUrls = function () {
  return Object.assign({}, params.conversion(), {
    name: 'conversion with display urls',
    webhookId: webhookGenerate().webhookId,
    displayURLs: params.pageVisits().displayURLs,
    manuallyShowNotification: false,
  });
};

params.conversionWithTrackUrls = function () {
  return Object.assign({}, params.conversionWithDisplayUrls(), {
    name: 'conversion with track url',
    webhookId: webhookGenerate().webhookId,
    trackURL: ['https://provesrc.com/home'],
    displayURLs: ['https://provesrc.com/home'],
    autoTrack: true,
  });
};

params.conversionWithTrackUrlsNoAutoTrack = function () {
  return Object.assign({}, params.conversionWithDisplayUrls(), {
    name: 'conversion with track url but autoTrack false',
    trackURL: ['https://provesrc.com/home2'],
    displayURLs: ['https://provesrc.com/home2'],
    autoTrack: false,
  });
};

const Factory = {
  Notification,
  ConversionNotification,
  params,
  notificationConstants: notifConsts,
  NotifConsts: notifConsts,
  notificationsFactory: {
    conversion: () => new ConversionNotification(params.conversion()),
    pageVisits: () => new PageVisitsNotification(params.pageVisits()),
    conversionWithDisplayUrls: () => new ConversionNotification(params.conversionWithDisplayUrls()),
    conversionWithTrackUrls: () => new ConversionNotification(params.conversionWithTrackUrls()),
    conversionWithTrackUrlsNoAutoTrack: () => new ConversionNotification(params.conversionWithTrackUrlsNoAutoTrack()),
  },
};

Factory.models = {
  Notification,
  PageVisits: PageVisitsNotification,
  Stream,
  Conversion: ConversionNotification,
  Combo,
  LiveVisitors,
  Info,
};

Factory.notifications = _.mapValues(Factory.notificationsFactory, fn => fn());

module.exports = Factory;

module.exports.PageVisitsParams = function (accountId, trackUrls, displayUrls, name) {
  const dUrl = 'https://provesrc.com';
  trackUrls = trackUrls || dUrl;
  displayUrls = displayUrls || dUrl;
  return {
    accountId: accountId || constants.accountId,
    type: notifConsts.notificationTypes.pageVisits,
    settings: {},
    name: name || 'page visits',
    refer: 'visitors',
    message: 'visiting',
    autoTrack: true,
    trackURL: Array.isArray(trackUrls) ? trackUrls : [trackUrls],
    displayURLs: Array.isArray(displayUrls) ? displayUrls : [displayUrls],
    urlTypes: {
      track: 'simple',
      display: 'simple',
    },
  };
};

module.exports.PageVisits = function (accountId, trackUrls, displayUrls, name = 'page visits') {
  return new PageVisitsNotification(this.PageVisitsParams(accountId, trackUrls, displayUrls, name));
};

module.exports.Conversion = function (accountId, opts) {
  if(!opts) opts = {};
  const url = opts.url || 'https://provesrc.com';
  const trackUrls = opts.trackUrls || opts.trackUrl || url;
  const displayUrls = opts.displayUrls || opts.displayUrl || url;
  return new ConversionNotification({
    accountId: accountId || constants.accountId,
    type: notifConsts.notificationTypes.conversion,
    settings: {},
    name: opts.name || 'conversions',
    refer: 'customers',
    message: 'purchased',
    autoTrack: true,
    trackURL: Array.isArray(trackUrls) ? trackUrls : [trackUrls],
    displayURLs: Array.isArray(displayUrls) ? displayUrls : [displayUrls],
  });
};

module.exports.ConversionWebhookParams = function (opts) {
  if(!opts) opts = {};
  const accountId = opts.accountId || constants.accountId;
  const { displayUrl } = opts;
  const name = opts.name || 'conversion with webhook';
  return {
    accountId,
    name,
    type: notifConsts.notificationTypes.conversion,
    refer: 'visitors',
    message: 'conversionsss',
    webhookId: webhookGenerate().webhookId,
    displayUrl,
    manuallyShowNotification: !displayUrl,
    autoTrack: false,
  };
};

module.exports.ConversionWebhook = function (opts) {
  return new ConversionNotification(this.ConversionWebhookParams(opts));
};

module.exports.ComboParams = function (accountId, comboType, period, opts) {
  opts = opts || {};
  const url = 'https://provesrc.com';
  const params = {
    accountId,
    type: notifConsts.notificationTypes.combo,
    name: 'combo',
    urlTypes: {
      track: notifConsts.URL_TYPES.simple,
    },
    autoTrack: opts.autoTrack || true,
    trackURL: [url],
    displayURLs: [url],
    settings: {
      combo: {
        period,
        type: comboType,
      },
    },
    webhookId: webhookGenerate().webhookId,
  };
  Object.assign(params, opts);
  if(comboType === notifConsts.COMBO_TYPES.conversions && !params.message) params.message = 'signed up';
  return params;
};

module.exports.Combo = function (accountId, comboType, period, opts) {
  return new Combo(this.ComboParams(accountId, comboType, period, opts));
};

/**
 * /notifications/create stream params
 * @param {object} [options]
 * @param {boolean} [options.autoTrack=true]
 * @param {string} [options.name='stream']
 * @param {string} [options.url='https://provesrc.com/signup']
 * @param {string} [options.displayUrl='https://provesrc.com/signup']
 * @param {string} [options.trackUrl='https://provesrc.com/signup']
 * @param {string} [options.localization='en']
 */
module.exports.StreamParams = function (options) {
  const opts = options || {};
  const autoTrack = opts.hasOwnProperty('autoTrack') ? opts.autoTrack : true;
  const name = opts.hasOwnProperty('name') ? opts.name : 'stream';
  const url = opts.hasOwnProperty('url') ? opts.url : 'https://provesrc.com/signup';
  const trackUrl = opts.trackUrl || url;
  const displayUrl = opts.displayUrl || url;
  const localization = opts.hasOwnProperty('localization') ? opts.localization : 'en';
  const { eventType } = opts;
  const data = {
    accountId,
    type: notifConsts.notificationTypes.stream,
    message: 'signed up',
    name,
    urlTypes: {
      track: notifConsts.URL_TYPES.simple,
    },
    autoTrack,
    trackURL: [trackUrl],
    displayURLs: [displayUrl],
    settings: {
      eventType,
    },
    webhookId: webhookGenerate().webhookId,
    localization,
  };
  Object.assign(data, opts);
  return data;
};

/**
 * /notifications/create stream params
 * @param {object} [options]
 * @param {boolean} [options.autoTrack=true]
 * @param {string} [options.name='stream']
 * @param {string} [options.url='https://provesrc.com/signup']
 * @param {string} [options.displayUrl='https://provesrc.com/signup']
 * @param {string} [options.trackUrl='https://provesrc.com/signup']
 * @param {string} [options.localization='en']
 */
module.exports.Stream = function (options) {
  return new Stream(this.StreamParams(options));
};

/**
 * @param {object} options
 * @param {boolean} [options.model=true]
 */
module.exports.Woo = function (options) {
  if(!options) options = {};
  const params = this.StreamParams(options);
  params.settings.platform = notifConsts.PLATFORMS.woocommerce;
  params.urlTypes.display = 'all';
  delete params.displayURLs;
  delete params.trackURL;
  if(options.model == null || options.model === true) {
    return new Stream(params);
  }
  return params;
};

module.exports.Wordpress = function (data, opts) {
  if(!opts) opts = {};
  const params = this.StreamParams(data);
  Object.assign(params, data);
  params.settings.platform = notifConsts.PLATFORMS.wordpress;
  delete params.trackURL;

  if(opts.lean) return params;
  return new Stream(params);
};

module.exports.Magento2 = function (options) {
  if(!options) options = {};
  const params = this.StreamParams(options);
  params.settings.platform = notifConsts.PLATFORMS.magento;
  params.urlTypes.display = 'all';
  delete params.displayURLs;
  delete params.trackURL;
  if(options.model == null || options.model === true) {
    return new Stream(params);
  }
  return params;
};

module.exports.Shopify = function (opts) {
  if(!opts) opts = {};
  const params = this.StreamParams(opts);
  params.settings.platform = notifConsts.PLATFORMS.shopify;
  delete params.trackURL;

  if(opts.model) return new Stream(params);
  return params;
};

module.exports.LiveVisitors = function (params, opts) {
  if(!opts) opts = {};
  if(!params) params = {};

  const display = params.display || 'https://provesrc.com';
  const data = {
    accountId: params.accountId || constants.accountId,
    type: notifConsts.notificationTypes.live,
    name: params.name || 'live visitors count',
    refer: params.refer || 'online',
    displayURLs: Array.isArray(display) ? display : [display],
  };
  if(opts.lean) return data;
  return new LiveVisitors(data);
};

module.exports.Info = Info;
/**
 * @param {object} [params]
 * @param {object} [opts]
 * @param {boolean} [opts.model] return a mongoose object or plain js object
 * @return {*}
 */
module.exports.getInfo = function (params, opts) {
  if(!opts) opts = {};
  if(!params) params = {};

  const display = params.display || 'https://provesrc.com';
  const data = {
    accountId: params.accountId || constants.accountId,
    type: notifConsts.notificationTypes.info,
    name: params.name || 'info notification',
    refer: params.refer || 'online',
    title: params.title || 'titlee',
    message: params.message || 'message',
    displayURLs: Array.isArray(display) ? display : [display],
  };
  Object.assign(data, params);

  if(opts.model) return new Info(data);
  return data;
};

module.exports.getWebhook = function () {
  return webhookGenerate().webhookId;
};
