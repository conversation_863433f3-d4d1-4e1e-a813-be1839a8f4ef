const urlModule = require('url');
const _ = require('lodash');
const constants = require('../constants');

const webhookGenerate = require('../../app/webhooks/generate').generate;
const dateUtils = require('../../lib/utils/dateUtils');

const WebhookEvent = require('../../app/events/models/WebhookEvent');
const WebsiteEvent = require('../../app/events/models/WebsiteEvent');
const FormEvent = require('../../app/events/models/FormEvent');
const FormStreamEvent = require('../../app/events/models/FormStreamEvent');
const WebhookStreamEvent = require('../../app/events/models/WebhookStreamEvent');
const WooEvent = require('../../app/events/models/WooEvent').model;
const ShopifyEvent = require('../../app/events/models/ShopifyEvent');
const Magento2Event = require('../../app/events/models/Magento2Event');
const WPEvent = require('../../app/events/models/WPEvent');

module.exports.models = {
  Event: require('../../app/events/models/Event'),
  WebsiteEvent,
  FormEvent,
  WooEvent,
  WPEvent,
  ShopifyEvent,
  WebhookEvent,
  WebhookStreamEvent,
};

module.exports.WebsiteEvent = function (accountId, url, date, numCounts = 10) {
  return webEvent(WebsiteEvent, accountId, url, date, numCounts);
};

module.exports.FormEvent = function (accountId, url, date, numCounts = 10) {
  return webEvent(FormEvent, accountId, url, date, numCounts);
};

module.exports.WebhookEvent = function (webhookId, date, guid = null) {
  const day = dateUtils.normalize(date.getTime ? date.getTime() : date);
  const event = new WebhookEvent({
    day,
    accountId: constants.accountId,
    guid,
    type: 'conversion',
    webhookId: webhookId || webhookGenerate().webhookId,
  });
  event.counts = randomCounts(10, date);
  return event;
};

/**
 * @param {object} [options]
 * @param {string} [options.accountId]
 * @param {number|date} [options.date]
 * @param {string} [options.email]
 * @param {string} [options.ip]
 * @param {string} [options.url]
 */
module.exports.FormStreamEvent = function (options) {
  if(!options) options = {};
  const accountId = options.accountId || constants.accountId;
  const date = options.date || Date.now();
  const email = options.email || '<EMAIL>';
  const ip = options.ip || '*************';
  const { firstName } = options;
  const { lastName } = options;
  const url = options.url || 'http://provesrc.com/signup';
  const user_uid = options.uid;
  const { host, pathname, query } = urlModule.parse(url);
  return new FormStreamEvent({
    date, email, accountId, ip, url, host, pathname, query, firstName, lastName, user_uid,
  });
};

module.exports.WebhookStreamEvent = function (options) {
  if(!options) options = {};
  const accountId = options.accountId || constants.accountId;
  const date = options.date || Date.now();
  const email = options.email || '<EMAIL>';
  const ip = options.ip || '*************';
  const { firstName } = options;
  const { lastName } = options;
  const { webhookId } = options;
  return new WebhookStreamEvent({
    date, email, accountId, ip, webhookId, firstName, lastName, products: options.products,
  });
};

module.exports.WooEvent = function (options) {
  if(!options) options = {};
  const host = options.host || 'shop.provesrc.com';
  const params = {
    accountId: options.accountId || constants.accountId,
    date: options.date || Date.now(),
    email: options.email || '<EMAIL>',
    firstName: options.firstName || 'Jane',
    lastName: options.lastName || 'Shopper',
    host,
    total: options.total || 10,
    currency: options.currency || 'USD',
    products: options.products || [{
      id: 1,
      quantity: 1,
      price: 10,
      name: 'wooProduct',
      link: `${host}/wooProduct`,
      image: `${host}/wooProduct.jpg`,
    }],
  };
  if(options.lean) {
    const omitted = _.omit(params, ['accountId', 'date', 'host']);
    omitted.siteUrl = options.siteUrl || 'https://shop.provesrc.com';
    return omitted;
  }
  return new WooEvent(params);
};

module.exports.WPEvent = function (data, opts) {
  if(!opts) opts = {};

  const params = Object.assign({
    accountId: constants.accountId,
    email: '<EMAIL>',
    date: Date.now(),
    firstName: 'test',
    lastName: 'testov',
    ip: '***********',
    host: 'provesrc.com',
  }, data);

  if(opts.lean) return params;
  return new WPEvent(params);
};

module.exports.ShopifyEvent = function (options) {
  if(!options) options = {};
  const shop = options.shop || 'shop.provesrc.com';
  return new ShopifyEvent({
    accountId: options.accountId || constants.accountId,
    orderId: Math.random() * 100,
    date: options.date || Date.now(),
    email: options.email || '<EMAIL>',
    firstName: options.firstName || 'Jane',
    lastName: options.lastName || 'Shopper',
    shop,
    total: options.total || 10,
    currency: options.currency || 'USD',
    products: options.products || [{
      id: 1,
      quantity: 1,
      price: 10,
      name: 'prrroduct',
      link: `${shop}/prrroduct`,
      image: `${shop}/prrroduct.jpg`,
    }],
  });
};

module.exports.Magento2Event = function (options) {
  if(!options) options = {};
  const host = options.host || 'shop.provesrc.com';
  return new Magento2Event({
    accountId: options.accountId || constants.accountId,
    date: options.date || Date.now(),
    email: options.email || '<EMAIL>',
    firstName: options.firstName || 'Jane',
    lastName: options.lastName || 'Shopper',
    host,
    total: options.total || 10,
    currency: options.currency || 'USD',
    products: options.products || [{
      id: 1,
      quantity: 1,
      price: 10,
      name: 'mag2product',
      link: `${host}/mag2product`,
      image: `${host}/mag2product.jpg`,
    }],
  });
};

// region helpers

function webEvent(Model, accountId, url, date, numCounts = 10) {
  const useDate = date && date.getTime && date.getTime() || Date.now();
  const normalizedDate = dateUtils.normalize(useDate);
  const parts = urlModule.parse(url);
  const event = new Model({
    accountId,
    day: normalizedDate,
    counts: {},
    url,
    host: parts.host,
    pathname: parts.path,
    query: parts.query,
  });

  event.counts = randomCounts(numCounts, normalizedDate);
  return event;
}

function randomCounts(numCounts, date) {
  const isToday = dateUtils.isToday(date);
  const max = isToday ? dateUtils.minuteOfTheDay() : Date.MINUTES_IN_DAY;

  const counts = {};
  for(let i = 0; i < numCounts; i++) {
    const key = Math.floor(Math.random() * max);
    counts[key] = Math.floor(Math.random() * 10 + 1);
  }
  return counts;
}

// endregion
