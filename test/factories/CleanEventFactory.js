const urlUtils = require('../../lib/utils/urlUtils');
const dateUtils = require('../../lib/utils/dateUtils');

const CleanWebsiteEvent = require('../../app/events/models/CleanWebsiteEvent');
const CleanFormEvent = require('../../app/events/models/CleanFormEvent');
const CleanFormStreamEvent = require('../../app/events/models/CleanFormStreamEvent');

module.exports.CleanWebsiteEvent = CleanWebsiteEvent;
module.exports.CleanFormEvent = CleanFormEvent;
module.exports.CleanFormStreamEvent = CleanFormStreamEvent;

module.exports.getCleanWebsiteEvent = function (accountId, url, date, numCounts = 10) {
  let normalizedDate = dateUtils.normalize(date);
  if(!normalizedDate) normalizedDate = dateUtils.todayNormalized12am();

  const hits = getRandomHits(numCounts, normalizedDate);
  return new CleanWebsiteEvent({
    accountId,
    date: normalizedDate,
    url: urlUtils.clean(url),
    firstMinute: hits.firstMinute,
    total: hits.total,
    counts: hits.counts,
  });
};

module.exports.getCleanFormEvent = function (accountId, url, date, numCounts = 10) {
  const useDate = date && date.getTime && date.getTime() || Date.now();
  const normalizedDate = dateUtils.normalize(useDate);

  const hits = getRandomHits(numCounts, normalizedDate);
  return new CleanFormEvent({
    accountId,
    date: normalizedDate,
    url: urlUtils.clean(url),
    firstMinute: hits.firstMinute,
    total: hits.total,
    counts: hits.counts,
  });
};

module.exports.getStreamEvent = function (accountId, opts) {
  if(!opts) opts = {};
  const date = opts.date || Date.now();
  const email = opts.email || '<EMAIL>';
  const ip = opts.ip || '*************';
  const firstName = opts.firstName || 'John';
  const lastName = opts.lastName || 'Smith';
  const url = urlUtils.clean(opts.url || 'http://provesrc.com/signup');
  const user_uid = opts.uid || '12345';
  return new CleanFormStreamEvent({
    date, email, accountId, ip, url, firstName, lastName, user_uid,
  });
};

function getRandomHits(numCounts, date) {
  const isToday = dateUtils.isToday(date);
  const max = isToday ? dateUtils.minuteOfTheDay() : Date.MINUTES_IN_DAY;
  let total = 0;
  let firstMinute;

  const counts = {};
  for(let i = 0; i < numCounts; i++) {
    const key = Math.floor(Math.random() * max);
    if(!firstMinute || firstMinute > key) firstMinute = key;

    const num = Math.floor(Math.random() * 10 + 1);
    counts[key] = num;
    total += num;
  }
  return { counts, total, firstMinute };
}
