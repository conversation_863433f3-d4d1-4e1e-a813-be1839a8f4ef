
const GoalAnalytics = require('../../app/goals/GoalAnalytics');

module.exports.GoalAnalytics = GoalAnalytics;

module.exports.getEvent = function (accountId, goalId, date, notificationIds) {
  if(notificationIds && !notificationIds.length) notificationIds = [notificationIds];
  else notificationIds = [];

  const event = new GoalAnalytics({ accountId, goalId, date });
  const keys = ['conversions', 'clickConversions', 'views', 'hovers', 'clicks'];
  for(let i = 0; i < keys.length; i++) {
    const key = keys[i];
    event[key] = Math.trunc(Math.random() * 100 + 1);

    for(let j = 0; j < notificationIds.length; j++) {
      const notificationId = notificationIds[j];
      let nEvent = event.notifications.id(notificationId);
      if(!nEvent) {
        nEvent = event.notifications.create({ _id: notificationId });
        event.notifications.push(nEvent);
      }
      nEvent[key] = Math.trunc(Math.random() * 100 + 1);
    }
  }
  return event;
};
