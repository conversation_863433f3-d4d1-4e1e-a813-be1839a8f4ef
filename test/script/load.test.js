const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');

describe('/script/load', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  it('should return ProveSource loading script with dynamic api key', async () => {
    const apiKey = '123';
    const url = `/script/load?apiKey=${apiKey}`;
    const res = await httpUtils.noAuthRequest(server, url, httpUtils.GET);
    expect(res.text).to.include(apiKey);
    expect(res.headers['content-type']).to.include('html');
  });
});
