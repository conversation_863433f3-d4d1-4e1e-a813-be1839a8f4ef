const uuid = require('uuid/v4');
const chai = require('chai');
const signature = require('cookie-signature');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const config = require('../../config');
const serverP = require('../../server');
const testUtils = require('../testUtils');
const redis = require('../../lib/redisClient').getClient();
const { Bill } = require('../factories/BillFactory');

const AccountStub = require('../AccountStub');

describe('visitorCount', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    AccountStub.stub();
    await redis.flushallAsync();
  });

  afterEach(() => {
    AccountStub.restore();
  });

  it('should not increment when redis has x-ps-uid', async () => {
    const uid = uuid();
    const headers = { 'x-ps-uid': uid };
    const [, visitorCount] = await Promise.all([
      redis.setAsync(uid, '1'),
      Bill.getVisitorCountThisMonth(constants.accountId),
    ]);

    const res = await httpUtils.configApiGET(server, 'provesrc.com', { headers });
    expect(res).to.have.status(200);

    const newVisitorCount = await Bill.getVisitorCountThisMonth(constants.accountId);
    expect(newVisitorCount).to.be.equal(visitorCount);
  });

  it('should store x-ps-uid in redis', async () => {
    const uid = uuid();
    const headers = { 'x-ps-uid': uid };
    const res = await httpUtils.accountConfiguration(server, { headers, host: 'test.com' });
    expect(res).to.have.status(200);

    const [redisRes, ttl] = await Promise.all([redis.getAsync(uid), redis.ttlAsync(uid)]);
    expect(redisRes).to.exist;
    expect(ttl).to.be.equal(60);
  });
});
