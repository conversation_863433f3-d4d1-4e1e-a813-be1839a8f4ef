const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');

const WPEvent = require('../../app/events/models/WPEvent');
const Feed = require('../../app/account/models/Feed');

describe('/webhooks/track/wordpress', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    await Promise.all([WPEvent.remove(), Feed.remove()]);
  });

  it('should create a Wordpress event', async () => {
    const { accountId } = constants;
    const host = 'provesrc.com';
    const siteUrl = `https://${host}`;
    const data = {
      email: '<EMAIL>', firstName: 'Natan', lastName: 'Abr', ip: '*************', siteUrl,
    };
    await httpUtils.trackWordpress(server, data);

    const event = await WPEvent.findOne({ accountId });
    const {
      firstName, lastName, email, ip,
    } = data;
    expect(event.email).to.be.equal(email);
    expect(event.firstName).to.be.equal(firstName);
    expect(event.lastName).to.be.equal(lastName);
    expect(event.ip).to.be.equal(ip);
    expect(event.location.country).to.be.equal('Israel');
    expect(event.host).to.be.equal(host);
  });

  it('should create a feed event', async () => {
    const { accountId } = constants;
    const host = 'provesrc.com';
    const siteUrl = `https://${host}`;
    const data = { email: '<EMAIL>', siteUrl };
    await httpUtils.trackWordpress(server, data);

    const feed = await Feed.findOne({ accountId });
    expect(feed.message).to.be.equal('Wordpress Signup');
    expect(feed.data).to.have.keys(['host', 'email']);
  });
});
