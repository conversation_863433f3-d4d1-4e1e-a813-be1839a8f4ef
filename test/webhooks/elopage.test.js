const chai = require('chai');
const sinon = require('sinon');
const httpUtils = require('../httpUtils');
const server = require('../../server');
const NotificationFactory = require('../factories/NotificationFactory');
const { WebhookStreamEvent } = require('../factories/EventFactory').models;

const { expect } = chai;
const { Notification } = NotificationFactory;


describe('/webhooks/track elopage', () => {
  beforeEach(async () => {
    await WebhookStreamEvent.remove();
  });

  afterEach(async () => {
    sinon.restore();
  });

  it('should capture lead details', async () => {
    const notification = NotificationFactory.Stream({ autoTrack: false });
    sinon.stub(Notification, 'findOne').resolves(notification);
    const data = getData();
    const res = await httpUtils.trackWebhookData(server, notification.webhookId, data);

    expect(res).to.have.status(200);
    const event = await WebhookStreamEvent.findOne();
    expect(event.firstName).to.be.equal(data.payer.first_name);
    expect(event.location.countryCode).to.be.equal(data.payer.country_code);
    expect(event.location.country).to.be.equal(data.payer.country);
    expect(event.products[0].name).to.be.equal(data.product.name);
  });
});

function getData() {
  return {
    id: '71384717',
    fee: '0.0',
    event: 'payment.successful',
    payer: {
      zip: '40229',
      city: 'Düsseldorf',
      email: '<EMAIL>',
      phone: '',
      street: '',
      vat_no: '',
      company: '',
      country: 'Deutschland',
      last_name: 'Reimer',
      first_name: 'Alina',
      country_code: 'DE',
      street_number: '',
    },
    state: 'successful',
    action: 'payment_processed',
    amount: '0.0',
    events: [
      '',
    ],
    upsell: '',
    opt_ins: '',
    product: {
      id: '51865',
      name: 'LamiMaker KOMPLETTKURS Lash & Brow Lifting',
      slug: 'LamiMaker-Komplettkurs',
      type: 'course',
      price: '179.0',
    },
    revenue: '0.0',
    tickets: [
      {
        codes: '',
        ticket_attendees: '',
      },
    ],
    add_id_1: '',
    add_id_2: '',
    currency: 'EUR',
    order_id: '1128575',
    vat_rate: '0.0',
    error_msg: '',
    initiator: '',
    publisher: '',
    recurring: 'no',
    created_at: '17.03.2020 14:12',
    membership: '',
    product_id: '51865',
    vat_amount: '0.0',
    campaign_id: '',
    coupon_code: 'PLNTDG',
    order_token: 'GPfSXgmZ84_2S9zQHpc8',
    created_date: '2020-03-17T14:12Z',
    invoice_link: '',
    pricing_plan: {
      name: '',
    },
    success_date: '2020-03-17T14:12Z',
    success_link: 'https://elopage.com/s/Saru/payment/GPfSXgmZ84_2S9zQHpc8?',
    team_members: '',
    gift_receiver: '',
    payment_state: 'paid',
    invoice_number: '',
    payment_method: 'free',
    payments_count: '1',
    recurring_form: 'one_time',
    created_date_utc: '17.03.2020 14:12',
    credit_memo_link: '',
    success_date_utc: '17.03.2020 14:12',
    with_test_period: 'false',
    payments_schedule: '',
    with_custom_start: 'false',
    payment_session_id: '1128575',
    success_date_short: '2020-03-17',
    refunded_transfer_id: '',
    payment_session_token: 'GPfSXgmZ84_2S9zQHpc8',
    payments_count_expected: '',
    team_member_commissions: '',
  };
}
