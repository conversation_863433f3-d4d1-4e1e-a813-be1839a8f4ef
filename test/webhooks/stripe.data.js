module.exports = {
  customerCreated: {
    created: **********,
    livemode: false,
    id: 'customer.created_00000000000000',
    type: 'customer.created',
    object: 'event',
    request: null,
    pending_webhooks: 1,
    api_version: '2018-11-08',
    data: {
      object: {
        id: 'cus_00000000000000',
        object: 'customer',
        account_balance: 0,
        created: **********,
        currency: 'usd',
        default_source: null,
        delinquent: false,
        description: null,
        discount: null,
        email: '<EMAIL>',
        invoice_prefix: '976C719',
        invoice_settings: {
          custom_fields: null,
          footer: null,
        },
        livemode: false,
        metadata: {
        },
        shipping: {
          address: {
            city: 'Tel Aviv',
            country: 'IL',
            state: 'HaMerkaz',
          },
          name: '<PERSON>',
        },
        sources: {
          object: 'list',
          data: [

          ],
          has_more: false,
          total_count: 0,
          url: '/v1/customers/cus_ENXSbFh0yl012F/sources',
        },
        subscriptions: {
          object: 'list',
          data: [

          ],
          has_more: false,
          total_count: 0,
          url: '/v1/customers/cus_ENXSbFh0yl012F/subscriptions',
        },
        tax_info: null,
        tax_info_verification: null,
      },
    },
  },
  orderCreated: {
    created: **********,
    livemode: false,
    id: 'order.created_00000000000000',
    type: 'order.created',
    object: 'event',
    request: null,
    pending_webhooks: 1,
    api_version: '2018-11-08',
    data: {
      object: {
        id: 'or_00000000000000',
        object: 'order',
        amount: 1500,
        amount_returned: null,
        application: null,
        application_fee: null,
        charge: null,
        created: **********,
        currency: 'usd',
        customer: null,
        email: '<EMAIL>',
        items: [
          {
            object: 'order_item',
            amount: 1500,
            currency: 'usd',
            description: 'T-shirt',
            parent: 'sk_1DulT3Hq6qHBywiL34g1hzq1',
            quantity: null,
            type: 'sku',
          },
        ],
        livemode: false,
        metadata: {
        },
        returns: {
          object: 'list',
          data: [

          ],
          has_more: false,
          total_count: 0,
          url: '/v1/order_returns?order=or_1DunWcHq6qHBywiLHdLTb9zk',
        },
        selected_shipping_method: null,
        shipping: {
          address: {
            city: 'San Francisco',
            country: 'US',
            line1: '1234 Fake Street',
            line2: null,
            postal_code: '94102',
            state: 'ID',
          },
          carrier: null,
          name: 'Jenny Rosen',
          phone: null,
          tracking_number: null,
        },
        shipping_methods: null,
        status: 'created',
        status_transitions: {
          canceled: null,
          fulfiled: null,
          paid: null,
          returned: null,
        },
        updated: **********,
      },
    },
  },
  recipientCreated: {
    created: **********,
    livemode: false,
    id: 'recipient.created_00000000000000',
    type: 'recipient.created',
    object: 'event',
    request: null,
    pending_webhooks: 1,
    api_version: '2018-11-08',
    data: {
      object: {
        id: 'rp_00000000000000',
        object: 'recipient',
        active_account: null,
        cards: {
          object: 'list',
          data: [

          ],
          has_more: false,
          total_count: 0,
          url: '/v1/recipients/rp_1DunY2Hq6qHBywiLHo7So0KM/cards',
        },
        created: **********,
        default_card: null,
        description: 'Recipient for John Doe',
        email: '<EMAIL>',
        livemode: false,
        metadata: {
        },
        migrated_to: null,
        name: 'John Doe',
        type: 'individual',
        verified: false,
      },
    },
  },
  charge: {
    id: 'evt_1EXx6UBqFMncmISK5vYCezA2',
    object: 'event',
    api_version: '2018-11-08',
    created: **********,
    data: {
      object: {
        id: 'ch_1EXx6TBqFMncmISK3Goo3uqv',
        object: 'charge',
        amount: 243,
        amount_refunded: 0,
        application: null,
        application_fee: null,
        application_fee_amount: null,
        balance_transaction: 'txn_1EXx6UBqFMncmISKoA7vpMSW',
        billing_details: {
          address: {
            city: null,
            country: null,
            line1: '',
            line2: null,
            postal_code: '',
            state: null,
          },
          email: null,
          name: 'Harsha Boppana',
          phone: null,
        },
        captured: true,
        created: 1557350105,
        currency: 'usd',
        customer: null,
        description: "Order to Amma's Own Recipes - by Andhra Mess: \r\nOrder Details: \r\n  4 x Roti Ⓗ: $2.40\r\n    Date\r\n      Wed May 8  $0.00\r\nSubtotal: $ 2.40\r\nTax: $0.03\r\nDelivery Fee: $1.99\r\nDriver Tip: $0.00\r\nDiscount: -$ 1.99\r\nTotal: $2.43",
        destination: null,
        dispute: null,
        failure_code: null,
        failure_message: null,
        invoice: null,
        livemode: true,
        on_behalf_of: null,
        order: null,
        outcome: {
          network_status: 'approved_by_network',
          reason: null,
          risk_level: 'normal',
          seller_message: 'Payment complete.',
          type: 'authorized',
        },
        paid: true,
        payment_intent: null,
        payment_method: 'card_1EXx6KBqFMncmISKD91Ixjg5',
        payment_method_details: {
          card: {
            brand: 'discover',
            checks: {
              address_line1_check: null,
              address_postal_code_check: null,
              cvc_check: 'pass',
            },
            country: 'US',
            description: 'Consumer Credit - Rewards',
            exp_month: 2,
            exp_year: 2024,
            fingerprint: 'kOZhqjDLyHsYSMqX',
            funding: 'credit',
            last4: '6871',
            three_d_secure: null,
            wallet: null,
          },
          type: 'card',
        },
        receipt_email: null,
        receipt_number: null,
        receipt_url: 'https://pay.stripe.com/receipts/acct_1Du2fCBqFMncmISK/ch_1EXx6TBqFMncmISK3Goo3uqv/rcpt_F2188W2neJYVL4ylIyJJ8XphUjxCJGA',
        refunded: false,
        refunds: {
          object: 'list',
          data: [],
          has_more: false,
          total_count: 0,
          url: '/v1/charges/ch_1EXx6TBqFMncmISK3Goo3uqv/refunds',
        },
        review: null,
        shipping: null,
        source: {
          id: 'card_1EXx6KBqFMncmISKD91Ixjg5',
          object: 'card',
          address_city: null,
          address_country: null,
          address_line1: '',
          address_line1_check: null,
          address_line2: null,
          address_state: null,
          address_zip: '',
          address_zip_check: null,
          brand: 'Discover',
          country: 'US',
          customer: null,
          cvc_check: 'pass',
          dynamic_last4: null,
          exp_month: 2,
          exp_year: 2024,
          fingerprint: 'kOZhqjDLyHsYSMqX',
          funding: 'credit',
          last4: '6871',
          name: 'Harsha Boppana',
          tokenization_method: null,
        },
        source_transfer: null,
        statement_descriptor: null,
        status: 'succeeded',
        transfer_data: null,
        transfer_group: null,
      },
    },
    livemode: true,
    pending_webhooks: 1,
    request: {
      id: 'req_o89NGxaEFrDvK9',
      idempotency_key: null,
    },
    type: 'charge.succeeded',
  },
  onceAMonthMeals: {
    id: 'evt_FY1XaHL3uIy8k9',
    object: 'event',
    api_version: '2018-09-24',
    created: **********,
    data: {
      object: {
        id: 'cus_FY1XaXcaoBUns9',
        object: 'customer',
        account_balance: 0,
        address: null,
        balance: 0,
        created: **********,
        currency: null,
        default_source: 'card_FY1Xnu8cSyfiPU',
        delinquent: false,
        description: 'Melanie Lebel ',
        discount: null,
        email: '<EMAIL>',
        invoice_prefix: 'CCAFAB0B',
        invoice_settings: {
          custom_fields: null,
          default_payment_method: null,
          footer: null,
        },
        livemode: true,
        name: null,
        phone: null,
        preferred_locales: [],
        shipping: null,
        sources: {
          object: 'list',
          data: [
            {
              id: 'card_FY1Xnu8cSyfiPU',
              object: 'card',
              address_city: 'Kelowna',
              address_country: 'Canada',
              address_line1: '136-205 Nickel Road',
              address_line1_check: 'fail',
              address_line2: null,
              address_state: 'BC',
              address_zip: 'V1X 4E5',
              address_zip_check: 'pass',
              brand: 'Visa',
              country: 'CA',
              customer: 'cus_FY1XaXcaoBUns9',
              cvc_check: 'pass',
              dynamic_last4: null,
              exp_month: 1,
              exp_year: 2022,
              fingerprint: 'w6Y4oN45hB5J7cmh',
              funding: 'debit',
              last4: '1317',
              name: 'Melanie Lebel',
              tokenization_method: null,
            },
          ],
          has_more: false,
          total_count: 1,
          url: '/v1/customers/cus_FY1XaXcaoBUns9/sources',
        },
        subscriptions: {
          object: 'list',
          data: [],
          has_more: false,
          total_count: 0,
          url: '/v1/customers/cus_FY1XaXcaoBUns9/subscriptions',
        },
        tax_exempt: 'none',
        tax_ids: {
          object: 'list',
          data: [],
          has_more: false,
          total_count: 0,
          url: '/v1/customers/cus_FY1XaXcaoBUns9/tax_ids',
        },
        tax_info: null,
        tax_info_verification: null,
      },
    },
    livemode: true,
    pending_webhooks: 1,
    request: {
      id: 'req_Qh5D2K0xDByled',
      idempotency_key: null,
    },
    type: 'customer.created',
  },
  subscription: {
    id: 'cus_HAPR8NA97Phybb',
    object: 'customer',
    api_version: '2020-03-02',
    created: 1587884969,
    data: {
      object: {
        id: 'sub_HAPRPJgXiJo4JC',
        object: 'subscription',
        application_fee_percent: null,
        billing_cycle_anchor: **********,
        billing_thresholds: null,
        cancel_at: null,
        cancel_at_period_end: false,
        canceled_at: null,
        collection_method: 'charge_automatically',
        created: **********,
        current_period_end: **********,
        current_period_start: **********,
        customer: 'cus_HAPR8NA97Phybb',
        days_until_due: null,
        default_payment_method: null,
        default_source: null,
        default_tax_rates: [],
        discount: null,
        ended_at: null,
        items: {
          object: 'list',
          data: [
            {
              id: 'si_HAPRZsnzgrbfu6',
              object: 'subscription_item',
              billing_thresholds: null,
              created: 1587884973,
              plan: {
                id: 'subscription_plan_201256',
                object: 'plan',
                active: true,
                aggregate_usage: null,
                amount: 6900,
                amount_decimal: '6900',
                billing_scheme: 'per_unit',
                created: **********,
                currency: 'usd',
                interval: 'month',
                interval_count: 1,
                livemode: true,
                nickname: 'Breakfree Trading™ Discovery',
                product: 'prod_GlvIRhG0dzwmdC',
                tiers: null,
                tiers_mode: null,
                transform_usage: null,
                trial_period_days: 7,
                usage_type: 'licensed',
              },
              quantity: 1,
              subscription: 'sub_HAPRPJgXiJo4JC',
              tax_rates: [],
            },
          ],
          has_more: false,
          total_count: 1,
          url: '/v1/subscription_items?subscription=sub_HAPRPJgXiJo4JC',
        },
        latest_invoice: 'in_1Gc4cKBI1GhkUGQtMhFYQ6or',
        livemode: true,
        metadata: {
          kjb_offer_id: '387524',
          ip_address: '***********',
        },
        next_pending_invoice_item_invoice: null,
        pause_collection: null,
        pending_invoice_item_interval: null,
        pending_setup_intent: null,
        pending_update: null,
        plan: {
          id: 'subscription_plan_201256',
          object: 'plan',
          active: true,
          aggregate_usage: null,
          amount: 6900,
          amount_decimal: '6900',
          billing_scheme: 'per_unit',
          created: **********,
          currency: 'usd',
          interval: 'month',
          interval_count: 1,
          livemode: true,
          nickname: 'Breakfree Trading™ Discovery',
          product: 'prod_GlvIRhG0dzwmdC',
          tiers: null,
          tiers_mode: null,
          transform_usage: null,
          trial_period_days: 7,
          usage_type: 'licensed',
        },
        quantity: 1,
        schedule: null,
        start_date: **********,
        status: 'trialing',
        tax_percent: null,
        trial_end: **********,
        trial_start: **********,
      },
    },
    livemode: true,
    pending_webhooks: 4,
    request: {
      id: 'req_fOy1Be8ZLcg6fn',
      idempotency_key: null,
    },
    type: 'customer.subscription.created',
    account_balance: 0,
    address: null,
    balance: 0,
    currency: 'usd',
    default_source: null,
    delinquent: false,
    description: 'Andre Gayle',
    discount: null,
    email: '<EMAIL>',
    invoice_prefix: '********',
    invoice_settings: {
      custom_fields: null,
      default_payment_method: 'pm_1Gc4cGBI1GhkUGQtSeMbjLYO',
      footer: null,
    },
    metadata: {
      kjb_member_id: '********',
    },
    name: null,
    next_invoice_sequence: 2,
    phone: null,
    preferred_locales: [],
    shipping: null,
    sources: {
      object: 'list',
      data: [],
      has_more: false,
      total_count: 0,
      url: '/v1/customers/cus_HAPR8NA97Phybb/sources',
    },
    subscriptions: {
      object: 'list',
      data: [
        {
          id: 'sub_HAPRPJgXiJo4JC',
          object: 'subscription',
          application_fee_percent: null,
          billing: 'charge_automatically',
          billing_cycle_anchor: **********,
          billing_thresholds: null,
          cancel_at: null,
          cancel_at_period_end: false,
          canceled_at: null,
          collection_method: 'charge_automatically',
          created: **********,
          current_period_end: **********,
          current_period_start: **********,
          customer: 'cus_HAPR8NA97Phybb',
          days_until_due: null,
          default_payment_method: null,
          default_source: null,
          default_tax_rates: [],
          discount: null,
          ended_at: null,
          invoice_customer_balance_settings: {
            consume_applied_balance_on_void: true,
          },
          items: {
            object: 'list',
            data: [
              {
                id: 'si_HAPRZsnzgrbfu6',
                object: 'subscription_item',
                billing_thresholds: null,
                created: 1587884973,
                plan: {
                  id: 'subscription_plan_201256',
                  object: 'plan',
                  active: true,
                  aggregate_usage: null,
                  amount: 6900,
                  amount_decimal: '6900',
                  billing_scheme: 'per_unit',
                  created: **********,
                  currency: 'usd',
                  interval: 'month',
                  interval_count: 1,
                  livemode: true,
                  nickname: 'Breakfree Trading™ Discovery',
                  product: 'prod_GlvIRhG0dzwmdC',
                  tiers: null,
                  tiers_mode: null,
                  transform_usage: null,
                  trial_period_days: 7,
                  usage_type: 'licensed',
                },
                quantity: 1,
                subscription: 'sub_HAPRPJgXiJo4JC',
                tax_rates: [],
              },
            ],
            has_more: false,
            total_count: 1,
            url: '/v1/subscription_items?subscription=sub_HAPRPJgXiJo4JC',
          },
          latest_invoice: 'in_1Gc4cKBI1GhkUGQtMhFYQ6or',
          livemode: true,
          metadata: {
            kjb_offer_id: '387524',
            ip_address: '***********',
          },
          next_pending_invoice_item_invoice: null,
          pause_collection: null,
          pending_invoice_item_interval: null,
          pending_setup_intent: null,
          pending_update: null,
          plan: {
            id: 'subscription_plan_201256',
            object: 'plan',
            active: true,
            aggregate_usage: null,
            amount: 6900,
            amount_decimal: '6900',
            billing_scheme: 'per_unit',
            created: **********,
            currency: 'usd',
            interval: 'month',
            interval_count: 1,
            livemode: true,
            nickname: 'Breakfree Trading™ Discovery',
            product: 'prod_GlvIRhG0dzwmdC',
            tiers: null,
            tiers_mode: null,
            transform_usage: null,
            trial_period_days: 7,
            usage_type: 'licensed',
          },
          quantity: 1,
          schedule: null,
          start: **********,
          start_date: **********,
          status: 'trialing',
          tax_percent: null,
          trial_end: **********,
          trial_start: **********,
        },
      ],
      has_more: false,
      total_count: 1,
      url: '/v1/customers/cus_HAPR8NA97Phybb/subscriptions',
    },
    tax_exempt: 'none',
    tax_ids: {
      object: 'list',
      data: [],
      has_more: false,
      total_count: 0,
      url: '/v1/customers/cus_HAPR8NA97Phybb/tax_ids',
    },
    tax_info: null,
    tax_info_verification: null,
  },
};
