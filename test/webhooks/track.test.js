const chai = require('chai');
const server = require('../../server');

const { expect } = chai;
const httpUtils = require('../httpUtils');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const testUtils = require('../testUtils');

const Factory = require('../factories/NotificationFactory');
const Notification = require('../../app/notifications/models/Notification');
const WebhookEvent = require('../../app/events/models/WebhookEvent');
const WebhookStreamEvent = require('../../app/events/models/WebhookStreamEvent');
const Feed = require('../../app/account/models/Feed');
const WebhookError = require('../../app/webhooks/WebhookError');
const WebhookData = require('../../app/webhooks/WebhookData');
const testData = require('./testData');

const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;

const constants = require('../constants');

const TRACK_API = '/webhooks/track';

describe('/webhooks/track', () => {
  beforeEach(async () => {
    await Promise.all([
      Notification.remove(),
      WebhookEvent.remove(),
      WebhookStreamEvent.remove(),
      Feed.remove(),
      WebhookError.remove(),
      WebhookData.remove(),
    ]);
  });

  afterEach(() => {
    sandbox.restore();
    sinon.restore();
  });

  it('should accept GET and do nothing', async () => {
    const params = Factory.params.conversion();
    const { webhookId } = params;
    const res = await httpUtils.noAuthRequest(server, `/webhooks/track/${webhookId}`);
    expect(res).to.have.status(200);
  });

  it('should respond 200 to HEAD requests', async () => {
    const params = Factory.params.conversion();
    const { webhookId } = params;
    const res = await chai.request(server).head(`/webhooks/track/${webhookId}`);
    expect(res).to.have.status(200);
  });

  it('should track webhook with guid', () => {
    const guid = '123';
    const params = Factory.params.conversion();
    const { webhookId } = params;

    sandbox.stub(Account, 'findOne').resolves(AccountFactory.default());

    return httpUtils.consoleRequest(server, constants.NOTIFICATIONS.CREATE, httpUtils.POST).send(params).then((res) => {
      expect(res).to.have.status(200);
      const url = `${TRACK_API}/${webhookId}`;
      return httpUtils.noAuthRequest(server, url, httpUtils.POST).send({ guid });
    }).then((res) => {
      expect(res).to.have.status(200);
      expect(res.body).to.have.property('message', 'success');
      return WebhookEvent.findOne({ webhookId, guid });
    })
      .then((event) => {
        expect(event).to.have.property('guid', guid);
        expect(event.accountId.toString()).to.be.equal(constants.accountId);
      });
  });

  it('should track stream webhook with guid', async () => {
    const notification = Factory.Stream({ autoTrack: false });
    await notification.save();
    const guid = '123';
    const email = '<EMAIL>';
    const data = { email, guid };
    const res = await httpUtils.trackWebhookData(server, notification.webhookId, data);
    expect(res).to.have.status(200);
    const event = await WebhookStreamEvent.findOne();
    expect(event.guid).to.be.equal(guid);
  });

  it('should set "now" when invalid timestamp', async () => {
    const notification = Factory.Stream({ autoTrack: false });
    await notification.save();
    const data = {
      email: '<EMAIL>',
      firstName: 'Natan',
      lastName: 'Abramov',
      timestamp: 'asfaf',
    };
    const res = await httpUtils.trackWebhookData(server, notification.webhookId, data);
    expect(res).to.have.status(200);
    const event = await WebhookStreamEvent.findOne();
    expect(Date.now() - event.date).to.be.below(1000); // less than 1 second
  });

  it('should accept timestamp string', async () => {
    const notification = Factory.Stream({ autoTrack: false });
    await notification.save();
    const date = new Date(Date.now() - 3 * 86400 * 1000);
    const data = {
      email: '<EMAIL>',
      firstName: 'Natan',
      lastName: 'Abramov',
      timestamp: `${date.getTime()}`,
    };
    await httpUtils.trackWebhookData(server, notification.webhookId, data);
    const event = await WebhookStreamEvent.findOne();
    expect(event.date).to.be.deep.equal(date);
  });

  it('should track webhook', () => {
    const params = Factory.params.conversion();
    const { webhookId } = params;

    sandbox.stub(Account, 'findOne').resolves(AccountFactory.default());

    return httpUtils.consoleRequest(server, constants.NOTIFICATIONS.CREATE, httpUtils.POST).send(params).then((res) => {
      expect(res).to.have.status(200);
      const url = `${TRACK_API}/${webhookId}`;
      return httpUtils.noAuthRequest(server, url, httpUtils.POST);
    }).then((res) => {
      expect(res).to.have.status(200);
      expect(res.body).to.have.property('message', 'success');
      return WebhookEvent.findOne({ webhookId });
    })
      .then((event) => {
        expect(event).to.not.be.null;
      });
  });

  it('should not track webhook if no notification with webhook', () => {
    const params = Factory.params.conversion();
    const { webhookId } = params;

    return httpUtils.noAuthRequest(server, `${TRACK_API}/${webhookId}`, httpUtils.POST).catch((err) => {
      expect(err).to.to.have.status(400);
    });
  });

  it('should return error on stream webhook without email', async () => {
    const notification = Factory.Stream({ autoTrack: false });
    await notification.save();
    const res = await httpUtils.trackWebhook(server, notification.webhookId);
    expect(res).to.have.status(400);
  });

  it('should store webhook details on error', async () => {
    const notification = Factory.Stream({ autoTrack: false });
    await notification.save();
    const data = { key: 'value', suck: 1234, nested: { what: 1 } };
    const headers = { header1: '1234', 'x-yup': 'no' };
    const query = { hi: 'ho' };
    const res = await httpUtils.trackWebhookData(server, notification.webhookId, data, headers, query);
    expect(res).to.have.status(400);

    const werr = await WebhookError.findOne();
    expect(werr).to.exist;
    expect(werr.accountId).to.be.deep.equal(notification.accountId);
    expect(werr.webhookId).to.be.equal(notification.webhookId);
    expect(werr.reqId).to.be.equal(res.headers['x-request-id']);
    expect(werr.query).to.be.deep.equal(query);
    expect(werr.body).to.be.deep.equal(data);
    expect(werr.headers).to.include(headers);
    expect(werr.error).to.include('missing email');
  });

  it('should store webhook details', async () => {
    const notification = Factory.Stream({ autoTrack: false });
    await notification.save();
    const data = { key: 'value', suck: 1234, nested: { what: 1 } };
    const headers = { header1: '1234', 'x-yup': 'no' };
    const query = { hi: 'ho' };
    const res = await httpUtils.trackWebhookData(server, notification.webhookId, data, headers, query);
    expect(res).to.have.status(400);

    const wData = await WebhookData.findOne();
    expect(wData).to.exist;
    expect(wData.accountId).to.be.deep.equal(notification.accountId);
    expect(wData.webhookId).to.be.equal(notification.webhookId);
    expect(wData.reqId).to.be.equal(res.headers['x-request-id']);
    expect(wData.query).to.be.deep.equal(query);
    expect(wData.body).to.be.deep.equal(data);
    expect(wData.headers).to.include(headers);
  });

  it('should add product details to webhook', async () => {
    const notification = Factory.Stream({ autoTrack: false });
    await notification.save();

    const product = {
      id: 1,
      name: 'Sneakers',
      link: 'https://provesrc.com/product',
      image: 'https://provesrc.com/product.png',
    };
    const data = {
      email: '<EMAIL>', firstName: 'Natan', lastName: 'Abramov', products: [product],
    };
    data.total = 50;
    data.currency = 'USD';
    await httpUtils.trackWebhookData(server, notification.webhookId, data);

    const event = await WebhookStreamEvent.findOne();
    expect(event.products[0].toObject()).to.be.deep.equal(product);
    expect(event.total).to.be.equal(data.total);
    expect(event.currency).to.be.equal(data.currency);
  });

  it('should add product details if in root', async () => {
    const notification = Factory.Stream({ autoTrack: false });
    await notification.save();

    const product = {
      name: 'Sneakers',
      link: 'https://provesrc.com/product',
      image: 'https://provesrc.com/product.png',
    };
    const data = {
      email: '<EMAIL>',
      firstName: 'Natan',
      lastName: 'Abramov',
      productName: product.name,
      productImage: product.image,
      productLink: product.link,
    };
    data.total = 50;
    data.currency = 'USD';
    await httpUtils.trackWebhookData(server, notification.webhookId, data);

    const event = await WebhookStreamEvent.findOne();
    expect(event.products[0].toObject()).to.be.deep.equal(product);
    expect(event.total).to.be.equal(data.total);
    expect(event.currency).to.be.equal(data.currency);
  });

  it('should find email inside request body', async () => {
    const notification = Factory.Stream({ autoTrack: false });
    await notification.save();

    const email = '<EMAIL>';
    const data = { firstName: 'Natan', lastName: 'Abramov', shit: { fuck: { electronic_mail: email } } };
    await httpUtils.trackWebhookData(server, notification.webhookId, data);

    const event = await WebhookStreamEvent.findOne();
    expect(event).to.exist;
    expect(event.email).to.be.equal(email);
  });

  it('should find names with weird keys', async () => {
    const notification = Factory.Stream({ autoTrack: false });
    await notification.save();

    const email = '<EMAIL>';
    const fname = 'Natan';
    const lname = 'Abra';
    const data = { 'first name': fname, lName: lname, shit: { fuck: { electronic_mail: email } } };
    await httpUtils.trackWebhookData(server, notification.webhookId, data);

    const event = await WebhookStreamEvent.findOne();
    expect(event.firstName).to.be.equal(fname);
    expect(event.lastName).to.be.equal(lname);
  });

  it('should find name in cognito forms webhook', async () => {
    const notification = Factory.Stream({ autoTrack: false });
    await notification.save();

    const data = { Name: { First: 'Tina' }, Email: '<EMAIL>' };
    await httpUtils.trackWebhookData(server, notification.webhookId, data);

    const event = await WebhookStreamEvent.findOne();
    expect(event.firstName).to.be.equal(data.Name.First);
  });

  it('should find names nested in body (objects like customer, client, etc)', async () => {
    const notification = Factory.Stream({ autoTrack: false });
    await notification.save();

    const email = '<EMAIL>';
    const fname = 'Fucky';
    const lname = 'Sucky';
    const data = { customer: { naMe: fname, surname: lname }, shit: { fuck: { electronic_mail: email } } };
    await httpUtils.trackWebhookData(server, notification.webhookId, data);

    const event = await WebhookStreamEvent.findOne();
    expect(event.firstName).to.be.equal(fname);
    expect(event.lastName).to.be.equal(lname);
  });

  it('should save feed event for conversion webhooks', async () => {
    const notification = Factory.ConversionWebhook({ autoTrack: false });
    const res = await notification.save();

    await httpUtils.trackWebhook(server, notification.webhookId);

    const event = await Feed.findOne();
    expect(event).to.exist;
    expect(event.message).to.include('Webhook');
  });

  it('should mailchimp webhook', async () => {
    const notification = Factory.Stream({ autoTrack: false });
    await notification.save();
    const data = {
      type: 'subscribe',
      fired_at: '2019-01-18 14:34:03',
      data: {
        id: 'b3b568528f',
        email: '<EMAIL>',
        email_type: 'html',
        ip_opt: '**************',
        web_id: '87836373',
        merges: {
          EMAIL: '<EMAIL>',
          FNAME: 'ROB',
          LNAME: '',
          MMERGE3: '',
          MMERGE4: '',
          MMERGE5: '',
          MMERGE6: '',
          MMERGE7: '',
        },
        list_id: '9188088837',
      },
    };
    const headers = { 'content-type': 'application/x-www-form-urlencoded' };
    const res = await httpUtils.trackWebhookData(server, notification.webhookId, data, headers);
    expect(res).to.have.status(200);

    const event = await WebhookStreamEvent.findOne();
    expect(event.email).to.be.equal(data.data.email);
    expect(event.firstName).to.be.equal(data.data.merges.FNAME);
  });

  it('should support paypal webhook', async () => {
    const notification = Factory.Stream({ autoTrack: false });
    await notification.save();
    const email = '<EMAIL>';
    const data = {
      payer_email: email,
    };
    const res = await httpUtils.trackWebhookData(server, notification.webhookId, data);
    expect(res).to.have.status(200);

    const event = await WebhookStreamEvent.findOne();
    expect(event.email).to.be.equal(email);
  });

  it('should find geo ip location', async () => {
    const notification = Factory.Stream({ autoTrack: false });
    await notification.save();
    const data = {
      email: '<EMAIL>',
      ip: '************',
      firstName: 'Deyniel',
      lastName: 'Punales',
    };
    const res = await httpUtils.trackWebhookData(server, notification.webhookId, data);
    expect(res).to.have.status(200);
    const event = await WebhookStreamEvent.findOne();
    expect(event.location.country).to.be.equal('United States');
  });

  it('should use country sent in body', async () => {
    const notification = Factory.Stream({ autoTrack: false });
    await notification.save();
    const data = {
      email: '<EMAIL>',
      firstName: 'Florian',
      lastName: 'Stoll',
      city: 'Neu-Ulm',
      country: 'Deutschland',
      countryCode: 'DE',
      productName: 'Ladekantenschutz Mercedes Benz E-Klasse T-Modell S213 - ab 2016',
      productLink: '/index.php?a=776',
    };
    const res = await httpUtils.trackWebhookData(server, notification.webhookId, data);
    expect(res).to.have.status(200);
    const event = await WebhookStreamEvent.findOne();
    expect(event.location.country).to.be.equal(data.country);
  });

  it('should find productName in thinkific order.created webhook', async () => {
    const notification = Factory.Stream({ autoTrack: false });
    await notification.save();
    const data = {
      id: '20180126172320940835610',
      resource: 'order',
      action: 'created',
      tenant_id: '3',
      created_at: '2018-01-26T22:23:20.808Z',
      payload: {
        affiliate_referral_code: null,
        billing_name: 'Milton Assis',
        coupon: null,
        created_at: '2018-01-26T22:23:18.400Z',
        id: 19796,
        payment_type: 'one-time',
        product_id: 1,
        product_name: 'Five Dollar Course',
        status: 'Complete',
        user: {
          email: '<EMAIL>',
          first_name: 'Milton',
          id: 136832,
          last_name: 'Assis',
        },
      },
    };
    const res = await httpUtils.trackWebhookData(server, notification.webhookId, data);
    expect(res).to.have.status(200);
    const event = await WebhookStreamEvent.findOne();
    expect(event.products[0].name).to.be.equal(data.payload.product_name);
  });

  it('should work with Plug&Play webhooks', async () => {
    const data = testData.plugnplay;
    const headers = { 'content-type': 'application/x-www-form-urlencoded' };
    const notification = Factory.Stream({ autoTrack: false });
    sinon.stub(Notification, 'findOne').resolves(notification);

    const res = await httpUtils.trackWebhookData(server, notification.webhookId, data, headers);

    expect(res).to.have.status(200);
  });

  it('should identify name in hotmart webhooks', async () => {
    const data = testData.hotmart;
    const notification = Factory.Stream({ autoTrack: false });
    sinon.stub(Notification, 'findOne').resolves(notification);

    const res = await httpUtils.trackWebhookData(server, notification.webhookId, data);

    expect(res).to.have.status(200);
    const event = await WebhookStreamEvent.findOne();
    expect(event).to.exist;
    expect(event.email).to.be.equal(data.data.buyer.email);
    expect(event.firstName).to.be.equal(data.data.buyer.name);
    expect(event.products[0].name).to.be.equal(data.data.product.name);
    expect(event.products[0].id).to.be.equal(data.data.product.id);
  });

  it('should track drip webhooks', async () => {
    const data = testData.drip;
    const notification = Factory.Stream({ autoTrack: false });
    sinon.stub(Notification, 'findOne').resolves(notification);

    const res = await httpUtils.trackWebhookData(server, notification.webhookId, data);

    expect(res).to.have.status(200);
    const event = await WebhookStreamEvent.findOne();
    expect(event.email).to.be.equal(data.subscriber.email);
    expect(event.ip).to.be.equal(data.subscriber.ip_address);
    expect(event.firstName).to.be.equal(data.subscriber.custom_fields.first_name);
  });
});
