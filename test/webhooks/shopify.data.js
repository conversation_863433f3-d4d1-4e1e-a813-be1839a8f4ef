module.exports = {
  id: 820982911946154508,
  email: '<EMAIL>',
  closed_at: null,
  created_at: '2018-07-17T13:02:34-04:00',
  updated_at: '2018-07-17T13:02:34-04:00',
  number: 234,
  note: null,
  token: '123456abcd',
  gateway: null,
  test: true,
  total_price: '45.00',
  subtotal_price: '35.00',
  total_weight: 0,
  total_tax: '0.00',
  taxes_included: false,
  currency: 'ILS',
  financial_status: 'voided',
  confirmed: false,
  total_discounts: '5.00',
  total_line_items_price: '40.00',
  cart_token: null,
  buyer_accepts_marketing: true,
  name: '#9999',
  referring_site: null,
  landing_site: null,
  cancelled_at: '2018-07-17T13:02:34-04:00',
  cancel_reason: 'customer',
  total_price_usd: null,
  checkout_token: null,
  reference: null,
  user_id: null,
  location_id: null,
  source_identifier: null,
  source_url: null,
  processed_at: null,
  device_id: null,
  phone: null,
  customer_locale: 'en',
  app_id: null,
  browser_ip: null,
  landing_site_ref: null,
  order_number: 1234,
  discount_applications: [{
    type: 'manual',
    value: '5.0',
    value_type: 'fixed_amount',
    allocation_method: 'one',
    target_selection: 'explicit',
    target_type: 'line_item',
    description: 'Discount',
    title: 'Discount',
  }],
  discount_codes: [],
  note_attributes: [],
  payment_gateway_names: ['visa', 'bogus'],
  processing_method: '',
  checkout_id: null,
  source_name: 'web',
  fulfillment_status: 'pending',
  tax_lines: [],
  tags: '',
  contact_email: '<EMAIL>',
  order_status_url: 'https:\/\/checkout.shopify.com\/1262387273\/orders\/123456abcd\/authenticate?key=abcdefg',
  line_items: [{
    id: 866550311766439020,
    variant_id: null,
    title: 'Natan 1',
    quantity: 1,
    price: '20.00',
    sku: '',
    variant_title: null,
    vendor: null,
    fulfillment_service: 'manual',
    product_id: 1319085768777,
    requires_shipping: true,
    taxable: true,
    gift_card: false,
    name: 'Natan',
    variant_inventory_management: null,
    properties: [],
    product_exists: true,
    fulfillable_quantity: 1,
    grams: 0,
    total_discount: '0.00',
    fulfillment_status: null,
    discount_allocations: [],
    tax_lines: [],
  }, {
    id: 141249953214522974,
    variant_id: null,
    title: 'Natan 2',
    quantity: 1,
    price: '20.00',
    sku: '',
    variant_title: null,
    vendor: null,
    fulfillment_service: 'manual',
    product_id: *********,
    requires_shipping: true,
    taxable: true,
    gift_card: false,
    name: 'Natan',
    variant_inventory_management: null,
    properties: [],
    product_exists: true,
    fulfillable_quantity: 1,
    grams: 0,
    total_discount: '5.00',
    fulfillment_status: null,
    discount_allocations: [{
      amount: '5.00',
      discount_application_index: 0,
    }],
    tax_lines: [],
  }, {
    id: 866550311766449020,
    variant_id: null,
    title: 'Natan 3',
    quantity: 1,
    price: '20.00',
    sku: '',
    variant_title: null,
    vendor: null,
    fulfillment_service: 'manual',
    product_id: 13190851241768777,
    requires_shipping: true,
    taxable: true,
    gift_card: false,
    name: 'Natan',
    variant_inventory_management: null,
    properties: [],
    product_exists: true,
    fulfillable_quantity: 1,
    grams: 0,
    total_discount: '0.00',
    fulfillment_status: null,
    discount_allocations: [],
    tax_lines: [],
  }],
  shipping_lines: [{
    id: 271878346596884015,
    title: 'Generic Shipping',
    price: '10.00',
    code: null,
    source: 'shopify',
    phone: null,
    requested_fulfillment_service_id: null,
    delivery_category: null,
    carrier_identifier: null,
    discounted_price: '10.00',
    discount_allocations: [],
    tax_lines: [],
  }],
  billing_address: {
    first_name: 'Bob',
    address1: '123 Billing Street',
    phone: '555-555-BILL',
    city: 'Billtown',
    zip: 'K2P0B0',
    province: 'Kentucky',
    country: 'United States',
    last_name: 'Biller',
    address2: null,
    company: 'My Company',
    latitude: null,
    longitude: null,
    name: 'Bob Biller',
    country_code: 'US',
    province_code: 'KY',
  },
  shipping_address: {
    first_name: 'Steve',
    address1: '123 Shipping Street',
    phone: '555-555-SHIP',
    city: 'Shippington',
    zip: '40003',
    province: 'Kentucky',
    country: 'United States',
    last_name: 'Shipper',
    address2: null,
    company: 'Shipping Company',
    latitude: null,
    longitude: null,
    name: 'Steve Shipper',
    country_code: 'US',
    province_code: 'KY',
  },
  fulfillments: [],
  refunds: [],
  customer: {
    id: 115310627314723954,
    email: '<EMAIL>',
    accepts_marketing: false,
    created_at: null,
    updated_at: null,
    first_name: 'John',
    last_name: 'Smith',
    orders_count: 0,
    state: 'disabled',
    total_spent: '0.00',
    last_order_id: null,
    note: null,
    verified_email: true,
    multipass_identifier: null,
    tax_exempt: false,
    phone: null,
    tags: '',
    last_order_name: null,
    default_address: {
      id: 715243470612851245,
      customer_id: 115310627314723954,
      first_name: null,
      last_name: null,
      company: null,
      address1: '123 Elm St.',
      address2: null,
      city: 'Ottawa',
      province: 'Ontario',
      country: 'Canada',
      zip: 'K2H7A8',
      phone: '************',
      name: '',
      province_code: 'ON',
      country_code: 'CA',
      country_name: 'Canada',
      default: false,
    },
  },
};
