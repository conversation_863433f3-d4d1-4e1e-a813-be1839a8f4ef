const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const httpUtils = require('../httpUtils');
const constants = require('../constants');

const NFactory = require('../factories/NotificationFactory');

const { Notification } = NFactory;
const { WebhookStreamEvent } = require('../factories/EventFactory').models;
const stripeData = require('./stripe.data');

describe('/webhooks/track stripe', () => {
  let server;
  before(async () => {
    server = await require('../../server');
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    await WebhookStreamEvent.remove();
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should work with stripe customer.created webhook', async () => {
    const notif = NFactory.Stream({ autoTrack: false });
    sandbox.stub(Notification, 'findOne').resolves(notif);
    const data = stripeData.customerCreated;

    const res = await httpUtils.trackWebhookData(server, notif.webhookId, data);
    expect(res).to.have.status(200);
    const event = await WebhookStreamEvent.findOne();
    expect(event).to.exist;
    expect(event.email).to.be.equal(data.data.object.email);
    expect(event.firstName).to.be.equal(data.data.object.shipping.name);
    expect(event.location.country).to.be.equal(data.data.object.shipping.address.country);
    expect(event.location.state).to.be.equal(data.data.object.shipping.address.state);
    expect(event.location.city).to.be.equal(data.data.object.shipping.address.city);
  });

  it('should work with stripe order.created webhook', async () => {
    const notif = NFactory.Stream({ autoTrack: false });
    sandbox.stub(Notification, 'findOne').resolves(notif);
    const data = stripeData.orderCreated;

    const res = await httpUtils.trackWebhookData(server, notif.webhookId, data);
    expect(res).to.have.status(200);
    const event = await WebhookStreamEvent.findOne();
    expect(event).to.exist;
    expect(event.email).to.be.equal(data.data.object.email);
    expect(event.firstName).to.be.equal(data.data.object.shipping.name);
    expect(event.location.country).to.be.equal(data.data.object.shipping.address.country);
    expect(event.location.state).to.be.equal(data.data.object.shipping.address.state);
    expect(event.location.city).to.be.equal(data.data.object.shipping.address.city);
  });

  it('should work with stripe recipient.created webhook', async () => {
    const notif = NFactory.Stream({ autoTrack: false });
    sandbox.stub(Notification, 'findOne').resolves(notif);
    const data = stripeData.recipientCreated;

    const res = await httpUtils.trackWebhookData(server, notif.webhookId, data);
    expect(res).to.have.status(200);
    const event = await WebhookStreamEvent.findOne();
    expect(event).to.exist;
    expect(event.email).to.be.equal(data.data.object.email);
    expect(event.firstName).to.be.equal(data.data.object.name);
  });

  it('should generate a random email for stripe charge webhooks (charge event)', async () => {
    const notif = NFactory.Stream({ autoTrack: false });
    sandbox.stub(Notification, 'findOne').resolves(notif);
    const data = stripeData.charge;

    const res = await httpUtils.trackWebhookData(server, notif.webhookId, data, { 'user-agent': 'stripe' });
    expect(res).to.have.status(200);
    const event = await WebhookStreamEvent.findOne();
    expect(event.email).to.exist;
  });

  it('should find the name in sources (onceAMonthMeals)', async () => {
    const notif = NFactory.Stream({ autoTrack: false });
    sandbox.stub(Notification, 'findOne').resolves(notif);
    const data = stripeData.onceAMonthMeals;

    const res = await httpUtils.trackWebhookData(server, notif.webhookId, data);

    expect(res).to.have.status(200);
    const event = await WebhookStreamEvent.findOne();
    expect(event.firstName).to.be.equal(data.data.object.sources.data[0].name);
  });

  it('should capture subscription plan nickname', async () => {
    const notif = NFactory.Stream({ autoTrack: false });
    sandbox.stub(Notification, 'findOne').resolves(notif);
    const data = stripeData.subscription;

    const res = await httpUtils.trackWebhookData(server, notif.webhookId, data);
    expect(res).to.have.status(200);
    const event = await WebhookStreamEvent.findOne();
    expect(event.products[0].name).to.be.equal(data.data.object.plan.nickname);
  });
});
