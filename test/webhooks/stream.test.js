const httpUtils = require('../httpUtils');
const chai = require('chai');

const { expect } = chai;
const sinon = require('sinon');
const constants = require('../constants');
const config = require('../../config');
const serverP = require('../../server');
const utils = require('../testUtils');
const crypto = require('../../lib/utils/cryptoUtils');
const maxmind = require('../../lib/maxmind');

const Notification = require('../../app/notifications/models/Notification');
const WebhookStreamEvent = require('../../app/events/models/WebhookStreamEvent');
const Factory = require('../factories/NotificationFactory');

describe('/webhooks/track stream', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  const sandbox = sinon.createSandbox();
  beforeEach(async () => {
    sandbox.restore();
    await Promise.all([Notification.remove(), WebhookStreamEvent.remove()]);
  });

  it('should track and add all properties', async () => {
    sandbox.stub(config, 'getSocialProfiles').value(true);
    const email = '<EMAIL>';
    const firstName = 'Natan';
    const lastName = 'Abramov';
    const notif = Factory.StreamParams({ autoTrack: false });
    const { webhookId } = notif;
    const timestamp = Date.now() - 5000;
    sandbox.stub(Notification, 'findOne').resolves(notif);
    const opts = {
      webhookId,
      email,
      timestamp,
      ip: '***************',
      firstName,
      lastName,
      order: {
        products: [{
          name: 'Camisa',
          productURL: 'http://www.toys-woody.co.il/store/%D7%A4%D7%90%D7%96%D7%9C-%D7%A6-%D7%90%D7%A0%D7%A7%D7%99-%D7%94%D7%97%D7%99%D7%95%D7%AA',
          productImage: 'https://static.s123-cdn-static-d.com/uploads/4900116/normal_61dedc69c2ce8.jpg',
        }],
      },
    };
    await httpUtils.trackWebhookOpts(server, opts);

    const event = await WebhookStreamEvent.findOne({ webhookId, email });
    expect(event.date.getTime()).to.be.equal(timestamp);
    expect(event.ip).to.exist;
    expect(event.location).to.include.all.keys('city', 'country');
    // expect(event.picasa.author[0].name.$t).to.equal('Natan Abramov');
    expect(event.gravatar.hash).to.be.equal(crypto.md5(email));
    expect(event.firstName).to.be.equal(firstName);
    expect(event.lastName).to.be.equal(lastName);
    expect(event.products[0].link).to.be.equal(opts.order.products[0].productURL);
    expect(event.products[0].image).to.be.equal(opts.order.products[0].productImage);
    expect(event.source).to.exist;
  });

  it('should receive form data', async () => {
    const notification = Factory.ConversionWebhook({ accountId: constants.accountId });
    await notification.save();

    const { webhookId } = notification;
    await chai.request(server).post(`${constants.WEBHOOKS.TRACK}/${webhookId}`).send('email=<EMAIL>');

    await utils.sleep(200);

    const event = await WebhookStreamEvent.findOne({ webhookId });
    expect(event).to.exist;
  });

  it('should use location from req.body', async () => {
    const notification = Factory.ConversionWebhook();
    await notification.save();

    const { webhookId } = notification;
    const ip = '************'; // Israeli
    const maxmindLocation = await maxmind.geoIP(ip);
    const city = 'Yeruham';
    const country = 'Israel';
    const res = await httpUtils.trackWebhookOpts(server, {
      email: '<EMAIL>', webhookId, city, country, ip,
    });
    expect(res).to.have.status(200);

    await utils.sleep(200);

    const event = await WebhookStreamEvent.findOne({ webhookId });
    expect(event.location.city).to.be.equal(city);
    expect(event.location.country).to.be.equal(country);
    expect(event.location.countryCode).to.be.equal(maxmindLocation.countryCode);
  });

  it('should complete country and state codes from names', async () => {
    const notification = Factory.ConversionWebhook();
    await notification.save();

    const { webhookId } = notification;
    const city = 'San Fransico';
    const state = 'California';
    const stateCode = 'CA';
    const country = 'United States';
    const countryCode = 'US';
    const data = {
      email: '<EMAIL>', city, state, country,
    };

    const res = await httpUtils.trackWebhookData(server, webhookId, data);
    expect(res).to.have.status(200);

    await utils.sleep(200);

    const event = await WebhookStreamEvent.findOne({ webhookId });
    expect(event.location.city).to.be.equal(city);
    expect(event.location.countryCode).to.be.equal(countryCode);
    expect(event.location.stateCode).to.be.equal(stateCode);
  });

  it('should complete country and state names from codes', async () => {
    const notification = Factory.ConversionWebhook();
    await notification.save();

    const { webhookId } = notification;
    const city = 'San Fransico';
    const state = 'California';
    const stateCode = 'CA';
    const country = 'United States';
    const countryCode = 'US';
    const data = {
      email: '<EMAIL>', city, stateCode, countryCode,
    };

    const res = await httpUtils.trackWebhookData(server, webhookId, data);
    expect(res).to.have.status(200);

    await utils.sleep(200);

    const event = await WebhookStreamEvent.findOne({ webhookId });
    expect(event.location.city).to.be.equal(city);
    expect(event.location.country).to.be.equal(country);
    expect(event.location.state).to.be.equal(state);
  });

  it('should transform state code to state if sent in state field', async () => {
    const notification = Factory.ConversionWebhook();
    await notification.save();

    const { webhookId } = notification;
    const city = 'San Fransico';
    const state = 'California';
    const stateCode = 'CA';
    const country = 'United States';
    const countryCode = 'US';
    const data = {
      email: '<EMAIL>', city, state: stateCode, countryCode,
    };

    const res = await httpUtils.trackWebhookData(server, webhookId, data);
    expect(res).to.have.status(200);

    await utils.sleep(200);

    const event = await WebhookStreamEvent.findOne({ webhookId });
    expect(event.location.city).to.be.equal(city);
    expect(event.location.country).to.be.equal(country);
    expect(event.location.state).to.be.equal(state);
  });
});
