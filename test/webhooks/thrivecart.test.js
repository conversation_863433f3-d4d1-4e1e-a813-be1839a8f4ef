const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const httpUtils = require('../httpUtils');
const constants = require('../constants');

const NFactory = require('../factories/NotificationFactory');

const { Notification } = NFactory;
const { WebhookStreamEvent } = require('../factories/EventFactory').models;

describe('/webhooks/track thrivecart', () => {
  let server;
  before(async () => {
    server = await require('../../server');
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    await WebhookStreamEvent.remove();
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should work with thrivecart webhook', async () => {
    const notif = NFactory.Stream({ autoTrack: false });
    sandbox.stub(Notification, 'findOne').resolves(notif);
    const data = getData();

    const res = await httpUtils.trackWebhookData(server, notif.webhookId, data, getHeaders());
    expect(res).to.have.status(200);
    const event = await WebhookStreamEvent.findOne();
    expect(event).to.exist;
    expect(event.email).to.be.equal(data.payload.invitee.email);
    expect(event.firstName).to.be.equal(data.payload.invitee.name);
  });
});

function getHeaders() {
  return {
    'x-forwarded-for': '**************',
    'x-forwarded-proto': 'https',
    'x-forwarded-port': '443',
    host: 'api.provesrc.com',
    'x-amzn-trace-id': 'Root=1-5fc4e1a4-197611a96c6c3fc762c90bf4',
    'content-length': '2442',
    'user-agent': 'thrivecart/1.0',
    'content-type': 'application/x-www-form-urlencoded',
  };
}

function getData() {
  return {
    event: 'order.success',
    mode: 'live',
    mode_int: '2',
    thrivecart_account: 'amytakespictures',
    thrivecart_secret: 'YE858T467LO4',
    base_product: '7',
    base_product_name: 'Product Photography 101',
    base_product_label: 'Product Photography 101',
    base_product_owner: '110107',
    order_id: '6499899',
    invoice_id: '*********',
    order_date: '2020-11-30 12:12:19',
    order_timestamp: '**********',
    currency: 'USD',
    customer_id: '********',
    customer_identifier: 'cus_IU9XlEaUkhozG7',
    customer: {
      id: '********',
      name: 'Rebecca Pick',
      email: '<EMAIL>',
      tandc: '1',
      ip_address: '***************',
      address: {
        country: 'GB',
      },
      first_name: 'Rebecca',
      last_name: 'Pick',
      checkbox_confirmation: 'true',
    },
    order: {
      id: '6499899',
      invoice_id: '*********',
      processor: 'stripe',
      total: '0',
      total_str: '0.00',
      total_gross: '0',
      total_gross_str: '0.00',
      date: '2020-11-30 12:12:19',
      date_iso8601: '2020-11-30T12:12:19+00:00',
      date_unix: '**********',
      tracking_id: 'null',
      tax: 'null',
      charges: [
        {
          name: 'Product Photography 101',
          label: 'Product Photography 101',
          reference: '7',
          item_type: 'product',
          item_identifier: 'product_7',
          amount: '0',
          amount_str: '0.00',
          type: 'single',
          quantity: '1',
          unit_price: 'false',
          unit_price_str: '0.00',
          defer: 'false',
          amount_gross: '0',
          amount_gross_str: '0.00',
          tax_paid: '0',
          tax_paid_str: '0.00',
          payment_plan_id: '30408',
          payment_plan_name: 'One-time payment ($0.00)',
        },
      ],
    },
    transactions: {
      'product-7': 'false',
    },
    purchases: [
      'Product Photography 101',
    ],
    purchase_map: [
      'product-7',
    ],
    purchase_map_flat: 'product-7',
    accessible_purchases: [
      'Product Photography 101',
    ],
    accessible_purchase_map: [
      'product-7',
    ],
    accessible_purchase_map_flat: 'product-7',
    fulfillment: {
      url: 'https://amytakespictures.thrivecart.com/product-photography-101/confirm/631be06745386cbb066c629cd2407ef6b0d2db098d6a/',
    },
  };
}
