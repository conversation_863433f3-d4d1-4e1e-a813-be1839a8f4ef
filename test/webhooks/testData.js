module.exports.plugnplay = {
  id: '294975',
  order_id: '294975',
  contract_id: '160547',
  first: '1',
  email: '<EMAIL>',
  firstname: '<PERSON><PERSON>',
  lastname: '<PERSON>',
  country: 'BE',
  raw: '{"checkout":"proefperiode","billing_cycle":"single","nr_of_cycles":null,"firstname":"<PERSON><PERSON>","lastname":"<PERSON> de Walle","email":"<EMAIL>","country":"BE","zipcode":"8970","housenumber":"32","street":"Woestenseweg","city":"Poperinge","company":null,"payment_method":"bancontact","discount_code":null,"av":"1"}',
  date: '2019-11-09 06:42:26',
  sku: '828224',
  quantity: '1',
  price: '23.9669',
  original_price: '23.966859504132',
  invoice_number: 'WOO2019131',
  vat_amount: '5.033',
  vat_pct: '21',
  product: 'Proefperiode',
  trial_finished: '0',
  signup_token: '74a769fb-3f30-4268-8ee9-498d4a9e8522',
  ledger: '',
  order_bump: '{"active":false}',
  products: '[{"title":"Proefperiode","sku":"828224","quantity":1,"price":"23.9669","original_price":23.96685950413223,"vat_amount":5.033000000000001,"vat_pct":21}]',
  product_line_array: 'Proefperiode',
  product_quantities_array: '1',
  product_price_array: '23.966859504132',
};

module.exports.drip = {
  subscriber: {
    id: 'sf7skpkca1ueqwmeqf6u',
    status: 'active',
    email: '<EMAIL>',
    custom_fields: {
      first_name: 'Natalia',
      last_name: 'Llorens Aracil',
      prod_name: 'meditAcción',
    },
    tags: [
      'BM - Customer Meditaccion',
    ],
    time_zone: 'Europe/Berlin',
    utc_offset: 60,
    created_at: '2019-11-09T16:50:48Z',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Linux; Android 9; SM-A750FN) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.90 Mobile Safari/537.36',
    lifetime_value: 990,
    original_referrer: 'https://m.facebook.com/',
    landing_url: 'https://www.budamindset.com/clase-manifiesta-tu-esencia-m/?fbclid=IwAR0bnO5e5GKEyfQHnmHOcXMXmtnIip-S_cnV15IKQVl-0b8QsB0xtlPwSwE',
    prospect: true,
    base_lead_score: null,
    lead_score: 76,
    user_id: null,
  },
};

module.exports.hotmart = {
  data: {
    product: {
      has_co_production: false,
      name: 'PACOTE DESVINCULAÇÃO',
      id: 342266,
      ucode: '2856e9c6-af41-401d-859b-de8f821d652f',
    },
    commissions: [
      {
        source: 'MARKETPLACE',
        value: 50.2,
      },
      {
        source: 'PRODUCER',
        value: 446.8,
      },
    ],
    purchase: {
      offer: {
        code: 'f0y9yf47',
      },
      order_date: 1675111710000.0,
      original_offer_price: {
        currency_value: 'BRL',
        value: 497,
      },
      price: {
        value: 497,
      },
      checkout_country: {
        iso: 'BR',
        name: 'Brasil',
      },
      buyer_ip: '************',
      order_bump: {
        is_order_bump: false,
      },
      payment: {
        pix_qrcode: 'https://customer-local-latam.ebanx.com/pix/checkout?hash=63d82d1dede38c4658b4197b9c0667d0beb4bd943789df98',
        pix_expiration_date: 1675284511000.0,
        installments_number: 1,
        type: 'PIX',
        pix_code: '00020101021226740014br.gov.bcb.pix2552pix.ebanx.com/qr/v2/77DD32083122C3FBBB241FA605444FE65204000053039865802BR5924Launch Pad Tecnologia Se6014BELO HORIZONTE62070503***63042732',
      },
      approved_date: 1675111775000.0,
      full_price: {
        value: 497,
      },
      transaction: 'HP155216751117090',
      status: 'APPROVED',
    },
    affiliates: [
      {
        affiliate_code: '',
        name: '',
      },
    ],
    producer: {
      name: 'BE FREE DESENVOLVIMENTO PESSOAL EIRELI',
    },
    buyer: {
      name: 'Suzaine mello',
      checkout_phone: '4598131229',
      email: '<EMAIL>',
    },
  },
  id: 'f581d953-48d0-42d7-9566-1585c8f3f05c',
  creation_date: 1675111778095.0,
  event: 'PURCHASE_APPROVED',
  version: '2.0.0',
};
