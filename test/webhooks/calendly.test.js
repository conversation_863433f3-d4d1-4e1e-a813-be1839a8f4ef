const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const httpUtils = require('../httpUtils');
const constants = require('../constants');

const NFactory = require('../factories/NotificationFactory');

const { Notification } = NFactory;
const { WebhookStreamEvent } = require('../factories/EventFactory').models;

describe('/webhooks/track calendly', () => {
  let server;
  before(async () => {
    server = await require('../../server');
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    await WebhookStreamEvent.remove();
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should work with calendly webhook', async () => {
    const notif = NFactory.Stream({ autoTrack: false });
    sandbox.stub(Notification, 'findOne').resolves(notif);
    const data = getData();

    const res = await httpUtils.trackWebhookData(server, notif.webhookId, data);
    expect(res).to.have.status(200);
    const event = await WebhookStreamEvent.findOne();
    expect(event).to.exist;
    expect(event.email).to.be.equal(data.payload.invitee.email);
    expect(event.firstName).to.be.equal(data.payload.invitee.name);
  });
});

function getData() {
  return {
    event: 'invitee.created',
    payload: {
      event_type: {
        uuid: 'ECGNTWIP5Q7IM4O4',
        kind: 'One-on-One',
        slug: '30min',
        name: 'Digital Strategy Call',
        duration: 30,
        owner: {
          type: 'users',
          uuid: 'GEEEIAX52IQXKZKE',
        },
      },
      event: {
        uuid: 'DFM6DVTYUNAIUPAV',
        assigned_to: [
          'Abdul',
        ],
        extended_assigned_to: [
          {
            name: 'Abdul',
            email: '<EMAIL>',
            primary: true,
          },
        ],
        start_time: '2020-06-25T10:15:00+10:00',
        start_time_pretty: '10:15am - Thursday, June 25, 2020',
        invitee_start_time: '2020-06-25T10:15:00+10:00',
        invitee_start_time_pretty: '10:15am - Thursday, June 25, 2020',
        end_time: '2020-06-25T10:45:00+10:00',
        end_time_pretty: '10:45am - Thursday, June 25, 2020',
        invitee_end_time: '2020-06-25T10:45:00+10:00',
        invitee_end_time_pretty: '10:45am - Thursday, June 25, 2020',
        created_at: '2020-06-24T09:36:40+10:00',
        location: '+**************',
        canceled: false,
        canceler_name: null,
        cancel_reason: null,
        canceled_at: null,
      },
      invitee: {
        uuid: 'FCI56KYEJ4NQOP4D',
        first_name: null,
        last_name: null,
        name: 'Darren',
        email: '<EMAIL>',
        text_reminder_number: '+**************',
        timezone: 'Australia/Sydney',
        created_at: '2020-06-24T09:36:40+10:00',
        is_reschedule: false,
        payments: [],
        canceled: false,
        canceler_name: null,
        cancel_reason: null,
        canceled_at: null,
      },
      questions_and_answers: [
        {
          question: 'Website link',
          answer: 'purplesoft.com.au',
        },
      ],
      questions_and_responses: {
        '1_question': 'Website link',
        '1_response': 'purplesoft.com.au',
      },
      tracking: {
        utm_campaign: null,
        utm_source: null,
        utm_medium: null,
        utm_content: null,
        utm_term: null,
        salesforce_uuid: null,
      },
      old_event: null,
      old_invitee: null,
      new_event: null,
      new_invitee: null,
    },
    time: '2020-06-23T23:36:41Z',
  };
}
