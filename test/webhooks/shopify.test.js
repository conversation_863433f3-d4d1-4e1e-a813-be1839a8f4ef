const _ = require('lodash');
const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');
const ShopifyEvent = require('../../app/events/models/ShopifyEvent');
const sinon = require('sinon');
const Shopify = require('../../lib/apis/shopify');
const AccountF = require('../factories/AccountFactory');
const { SHOPIFY_EVENT_TYPES } = require('../../app/constants');
const AccountFactory = require('../factories/AccountFactory');
const NotifFactory = require('../factories/NotificationFactory');
const { sleep } = require('../testUtils');

const { Account } = AccountF;

describe('/webhooks/shopify', () => {
  const sandbox = sinon.createSandbox();
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    await ShopifyEvent.remove();
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should save order event type as pos', async () => {
    const shop = 'provesrcTest.myshopify.com';
    const domain = 'www.skull-shoes.com';
    sandbox.stub(Shopify.Shopify.prototype, 'getProducts').resolves([{
      id: '1', name: 'product1',
    }]);
    sandbox.stub(Account, 'findOne').resolves(AccountF.shopify('<EMAIL>', shop, domain));
    const params = require('./posOrder');
    const headers = { 'x-shopify-shop-domain': shop };
    const res = await httpUtils.noAuthRequest(server, '/webhooks/track/shopify', httpUtils.POST).set(headers).send(params.body);
    expect(res.status).to.equal(200);

    const event = await ShopifyEvent.findOne();
    expect(event.type).to.equal('pos');
  });

  it('should save order event type as online', async () => {
    const shop = 'provesrcTest.myshopify.com';
    const domain = 'www.skull-shoes.com';
    sandbox.stub(Shopify.Shopify.prototype, 'getProducts').resolves([{
      id: '1',
      name: 'product1',
    }]);
    sandbox.stub(Account, 'findOne').resolves(AccountF.shopify('<EMAIL>', shop, domain));
    const params = _.cloneDeep(require('./posOrder'));
    params.body.app_id = 2;
    const headers = { 'x-shopify-shop-domain': shop };
    const res = await httpUtils.noAuthRequest(server, '/webhooks/track/shopify', httpUtils.POST).set(headers).send(params.body);
    expect(res.status).to.equal(200);

    const event = await ShopifyEvent.findOne();
    expect(event.type).to.equal('online');
  });

  it('should save order event type as online when no appid ', async () => {
    const shop = 'provesrcTest.myshopify.com';
    const domain = 'www.skull-shoes.com';
    sandbox.stub(Shopify.Shopify.prototype, 'getProducts').resolves([{
      id: '1',
      name: 'product1',
    }]);
    sandbox.stub(Account, 'findOne').resolves(AccountF.shopify('<EMAIL>', shop, domain));
    const params = _.cloneDeep(require('./posOrder'));
    params.body.app_id = null;
    const headers = { 'x-shopify-shop-domain': shop };
    const res = await httpUtils.noAuthRequest(server, '/webhooks/track/shopify', httpUtils.POST).set(headers).send(params.body);
    expect(res.status).to.equal(200);

    const event = await ShopifyEvent.findOne();
    expect(event.type).to.equal('online');
  });

  it('should create event with first line item (when no product ids)', async () => {
    sandbox.stub(Shopify.Shopify.prototype, 'getProduct').resolves({
      handle: 'natan',
      image: { src: 'https://provesrc.com/natan.jpg' },
    });

    const shop = 'provesrcTest.myshopify.com';
    const domain = 'www.skull-shoes.com';
    sandbox.stub(Account, 'findOne').resolves(AccountF.shopify('<EMAIL>', shop, domain));

    const params = _.cloneDeep(require('./shopify.data'));
    for(let i = 0; i < params.line_items.length; i++) {
      params.line_items[i].product_id = null;
    }

    const endpoint = `/webhooks/track/shopify?apiKey=${constants.apiKey}`;
    const headers = { 'x-shopify-shop-domain': shop };
    const res = await httpUtils.noAuthRequest(server, endpoint, httpUtils.POST).set(headers).send(params);
    expect(res).to.have.status(200);

    const event = await ShopifyEvent.findOne();
    expect(event.products[0].name).to.be.equal(params.line_items[0].title);
  });

  it('should save a shopify event', async () => {
    const shop = 'provesrcTest.myshopify.com';
    const domain = 'www.skull-shoes.com';
    sandbox.stub(Shopify.Shopify.prototype, 'getProduct').resolves({
      handle: 'natan',
      image: { src: 'https://provesrc.com/natan.jpg' },
    });
    sandbox.stub(Account, 'findOne').resolves(AccountF.shopify('<EMAIL>', shop, domain));

    const endpoint = `/webhooks/track/shopify?apiKey=${constants.apiKey}`;
    const params = _.cloneDeep(require('./shopify.data'));
    const headers = { 'x-shopify-shop-domain': shop };
    const res = await httpUtils.noAuthRequest(server, endpoint, httpUtils.POST).set(headers).send(params);
    expect(res).to.have.status(200);

    await sleep(200);
    const event = await ShopifyEvent.findOne();
    expect(event).to.exist;
    expect(event.location.country).to.be.equal(params.billing_address.country);
    expect(event.orderId).to.be.equal(params.id);
    expect(event.shop).to.be.equal(shop);
    expect(event.domain).to.be.equal(domain);
    expect(event.type).to.be.equal(SHOPIFY_EVENT_TYPES.online);
  });

  it('should construct an email from name if no email', async () => {
    sandbox.stub(Shopify.Shopify.prototype, 'getProduct').resolves({
      handle: 'natan',
      image: { src: 'https://provesrc.com/natan.jpg' },
    });

    const shop = 'provesrcTest.myshopify.com';
    const domain = 'www.skull-shoes.com';
    sandbox.stub(Account, 'findOne').resolves(AccountF.shopify('<EMAIL>', shop, domain));

    const endpoint = `/webhooks/track/shopify?apiKey=${constants.apiKey}`;
    const params = _.clone(require('./shopify.data'));
    params.email = null;
    params.billing_address.first_name = 'David Shlomo';
    const headers = { 'x-shopify-shop-domain': shop };
    const res = await httpUtils.noAuthRequest(server, endpoint, httpUtils.POST).set(headers).send(params);
    expect(res).to.have.status(200);

    await sleep(200);
    const event = await ShopifyEvent.findOne();
    let expectedEmail = `${params.billing_address.first_name}.${params.billing_address.last_name}@from-shopify.com`;
    expectedEmail = expectedEmail.replace(/\s/g, '.');
    expect(event.email).to.be.equal(expectedEmail);
  });

  it('should find customer name in customer object', async () => {
    const shop = 'provesrcTest.myshopify.com';
    const domain = 'www.skull-shoes.com';
    sandbox.stub(Shopify.Shopify.prototype, 'getProduct').resolves({
      handle: 'natan',
      image: { src: 'https://provesrc.com/natan.jpg' },
    });
    sandbox.stub(Account, 'findOne').resolves(AccountF.shopify('<EMAIL>', shop, domain));

    const endpoint = `/webhooks/track/shopify?apiKey=${constants.apiKey}`;
    const params = _.cloneDeep(require('./shopify.data'));
    params.shipping_address = null;
    params.billing_address = null;
    const headers = { 'x-shopify-shop-domain': shop };
    const res = await httpUtils.noAuthRequest(server, endpoint, httpUtils.POST).set(headers).send(params);
    expect(res).to.have.status(200);

    await sleep(200);
    const event = await ShopifyEvent.findOne();
    expect(event).to.exist;
    expect(event.firstName).to.be.equal('John');
  });
});
