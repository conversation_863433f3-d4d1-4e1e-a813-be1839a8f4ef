const chai = require('chai');
const sinon = require('sinon');
const app = require('../../server');
const NFactory = require('../factories/NotificationFactory');
const httpUtils = require('../httpUtils');
const { WebhookStreamEvent } = require('../factories/EventFactory').models;

const { Notification } = NFactory;
const { expect } = chai;

describe('clickfunnels', () => {
  afterEach(async () => {
    sinon.restore();
  });

  describe('/funnel_webhooks/test', () => {
    function getEndpoint(webhook) {
      return `/webhooks/track/${webhook}/funnel_webhooks/test`;
    }

    it('should always return 200', async () => {
      const webhook = '8167aed58583430762a2d55caf956fd3';
      const url = getEndpoint(webhook);
      const res = await chai.request(app).post(url).send();
      expect(res).to.have.status(200);
    });

    it('should always return 200 on {root}/funnel_webhooks/test', async () => {
      const res = await chai.request(app).post('/funnel_webhooks/test').send();
      expect(res).to.have.status(200);
    });
  });

  describe('clickfunnels webhook', () => {
    it('should capture name from contact_profile', async () => {
      const notif = NFactory.Stream({ autoTrack: false });
      sinon.stub(Notification, 'findOne').resolves(notif);
      const data = getUpsellWebohokData();
      const res = await httpUtils.trackWebhookData(app, notif.webhookId, data);
      expect(res).to.have.status(200);
      const event = await WebhookStreamEvent.findOne();
      expect(event).to.exist;
      expect(event.email).to.be.equal(data.purchase.contact.email);
      expect(event.firstName).to.be.equal(data.purchase.contact.first_name || data.purchase.contact.contact_profile.first_name);
    });
  });
});

function getUpsellWebohokData() {
  return {
    purchase: {
      id: 93642653,
      products: [
        {
          id: 3881606,
          name: 'Appointments Autopilot Kit w/ Payment Plan',
          stripe_plan: 'subscription_plan_2147692075',
          amount: {
            cents: 0,
            currency_iso: 'USD',
          },
          amount_currency: 'USD',
          created_at: '2021-11-23T15:41:39.000Z',
          updated_at: '2021-11-23T15:50:04.000Z',
          subject: '',
          html_body: '<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">\r\n\r\n',
          thank_you_page_id: ********,
          stripe_cancel_after_payments: null,
          bump: false,
          cart_product_id: null,
          billing_integration: 'stripe_account-231005',
          infusionsoft_product_id: null,
          infusionsoft_subscription_id: null,
          ontraport_product_id: null,
          ontraport_payment_count: null,
          ontraport_payment_type: null,
          ontraport_unit: null,
          ontraport_gateway_id: null,
          ontraport_invoice_id: null,
          commissionable: false,
          statement_descriptor: '',
          netsuite_id: null,
          netsuite_tag: null,
          netsuite_class: null,
          description: '',
        },
      ],
      member_id: null,
      contact: {
        id: **********,
        page_id: ********,
        first_name: '',
        last_name: '',
        name: '',
        address: '',
        city: '',
        country: '',
        state: '',
        zip: '',
        email: '<EMAIL>',
        phone: '',
        webinar_at: null,
        webinar_last_time: null,
        webinar_ext: 'ZE4gv0ag',
        created_at: '2021-12-14T13:20:16.000Z',
        updated_at: '2021-12-14T13:20:16.000Z',
        ip: '***************',
        funnel_id: 10804050,
        funnel_step_id: 73015941,
        unsubscribed_at: null,
        cf_uvid: '9cd68d10303a94652cfb09619fd60475',
        cart_affiliate_id: '',
        shipping_address: '',
        shipping_city: '',
        shipping_country: '',
        shipping_state: '',
        shipping_zip: '',
        vat_number: '',
        affiliate_id: null,
        aff_sub: '',
        aff_sub2: '',
        cf_affiliate_id: null,
        contact_profile: {
          id: 865317229,
          first_name: 'Ashley',
          last_name: 'Souza',
          address: '',
          city: null,
          country: null,
          state: null,
          zip: null,
          email: '<EMAIL>',
          phone: null,
          created_at: '2021-12-14T05:05:53.000Z',
          updated_at: '2021-12-14T13:12:42.000Z',
          unsubscribed_at: null,
          cf_uvid: '9cd68d10303a94652cfb09619fd60475',
          shipping_address: '',
          shipping_country: null,
          shipping_city: null,
          shipping_state: null,
          shipping_zip: null,
          vat_number: null,
          middle_name: null,
          websites: null,
          location_general: null,
          normalized_location: null,
          deduced_location: null,
          age: null,
          gender: null,
          age_range_lower: null,
          age_range_upper: null,
          action_score: 35,
          known_ltv: '27.00',
          tags: [],
          time_zone: null,
        },
        additional_info: {
          cf_affiliate_id: '',
          time_zone: null,
          utm_source: '',
          utm_medium: '',
          utm_campaign: '',
          utm_term: '',
          utm_content: '',
          cf_uvid: '9cd68d10303a94652cfb09619fd60475',
          webinar_delay: '-63806728411359',
          purchase: {
            product_id: '3881606',
            payment_method_nonce: '',
            order_saas_url: '',
            stripe_customer_id: 'cus_Km1LuYvzZ8BM2o',
            stripe_customer_token: 'pm_1K6avSEp54HoGvMq6lgVgOyQ',
          },
          upsell: '1',
          cfpspageviewid: '1263761',
          cfpsfbeventid: '10804050-73015941-********-5092680312407605',
          cfpsuseragent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.93 Safari/537.36',
          cfpsfbp: 'fb.1.1639458201486.421569790',
        },
        time_zone: null,
        upsell: '1',
        cfpspageviewid: '1263761',
        cfpsfbeventid: '10804050-73015941-********-5092680312407605',
        cfpsuseragent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.93 Safari/537.36',
        cfpsfbp: 'fb.1.1639458201486.421569790',
      },
      funnel_id: 10804050,
      stripe_customer_token: 'pm_1K6avSEp54HoGvMq6lgVgOyQ',
      created_at: '2021-12-14T13:20:17.000Z',
      updated_at: '2021-12-14T13:20:17.000Z',
      subscription_id: null,
      charge_id: null,
      ctransreceipt: null,
      status: 'failed',
      fulfillment_status: null,
      fulfillment_id: null,
      payments_count: null,
      infusionsoft_ccid: null,
      oap_customer_id: null,
      payment_instrument_type: null,
      original_amount_cents: 9700,
      original_amount: {
        cents: 9700,
        currency_iso: 'USD',
      },
      original_amount_currency: 'USD',
      manual: false,
      error_message: 'There was an issue with your credit card.',
      nmi_customer_vault_id: null,
    },
    event: 'created',
  };
}
