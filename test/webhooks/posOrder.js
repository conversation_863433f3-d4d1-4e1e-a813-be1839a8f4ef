module.exports = {
  _id: '67331f2f4ae0106d452f76df',
  accountId: '641c015e2b347d22372779b5',
  orderId: *************.0,
  shop: {
    id: ***********.0,
    name: '<PERSON>ry<PERSON>',
    email: '<EMAIL>',
    domain: 'stryv.co',
    phone: '+**********',
    country_name: 'Singapore',
    shop_owner: '<PERSON><PERSON><PERSON>back Admin',
    plan_display_name: 'Shopify Plus',
    plan_name: 'shopify_plus',
    myshopify_domain: 'stryv-supplements.myshopify.com',
    installed: '2024-06-03T08:36:34.231Z',
    uninstalled: null,
  },
  body: {
    id: *************.0,
    admin_graphql_api_id: 'gid://shopify/Order/*************',
    app_id: 129785,
    browser_ip: '***************',
    buyer_accepts_marketing: false,
    cancel_reason: null,
    cancelled_at: null,
    cart_token: null,
    checkout_id: **************.0,
    client_details: {
      accept_language: 'en-SG,en-GB;q=0.9,en;q=0.8',
      browser_height: null,
      browser_ip: '***************',
      browser_width: null,
      session_hash: null,
      user_agent: 'Shopify POS/9.21.2/iOS/17.5.1/Apple/iPad12,1/production',
    },
    closed_at: null,
    company: null,
    confirmation_number: 'G4Q0UUCV8',
    confirmed: true,
    contact_email: '<EMAIL>',
    created_at: '2024-11-12T17:26:03+08:00',
    currency: 'SGD',
    current_subtotal_price: '199.80',
    current_subtotal_price_set: {
      shop_money: {
        amount: '199.80',
        currency_code: 'SGD',
      },
      presentment_money: {
        amount: '199.80',
        currency_code: 'SGD',
      },
    },
    current_total_additional_fees_set: null,
    current_total_discounts: '21.10',
    current_total_discounts_set: {
      shop_money: {
        amount: '21.10',
        currency_code: 'SGD',
      },
      presentment_money: {
        amount: '21.10',
        currency_code: 'SGD',
      },
    },
    current_total_duties_set: null,
    current_total_price: '199.80',
    current_total_price_set: {
      shop_money: {
        amount: '199.80',
        currency_code: 'SGD',
      },
      presentment_money: {
        amount: '199.80',
        currency_code: 'SGD',
      },
    },
    current_total_tax: '16.50',
    current_total_tax_set: {
      shop_money: {
        amount: '16.50',
        currency_code: 'SGD',
      },
      presentment_money: {
        amount: '16.50',
        currency_code: 'SGD',
      },
    },
    customer_locale: 'en',
    device_id: 4,
    discount_codes: [],
    email: '<EMAIL>',
    estimated_taxes: false,
    financial_status: 'authorized',
    fulfillment_status: 'fulfilled',
    landing_site: null,
    landing_site_ref: null,
    location_id: 75705581782.0,
    merchant_of_record_app_id: null,
    name: 'STRYV-#36036',
    note: null,
    note_attributes: [],
    number: 35036,
    order_number: 36036,
    order_status_url: 'https://stryv.co/***********/orders/6c29a2dfaa02ef6c023572d4f08570f7/authenticate?key=c7a4e36c727d67b4a0a524fa373d603c',
    original_total_additional_fees_set: null,
    original_total_duties_set: null,
    payment_gateway_names: [
      'shopify_payments',
    ],
    po_number: null,
    presentment_currency: 'SGD',
    processed_at: '2024-11-12T17:26:02+08:00',
    reference: '213dec20b0c36aa9eafbf21aa3d427a6',
    referring_site: null,
    source_identifier: '75705581782-4-3498',
    source_name: 'pos',
    source_url: null,
    subtotal_price: '199.80',
    subtotal_price_set: {
      shop_money: {
        amount: '199.80',
        currency_code: 'SGD',
      },
      presentment_money: {
        amount: '199.80',
        currency_code: 'SGD',
      },
    },
    tags: '',
    tax_exempt: false,
    tax_lines: [
      {
        price: '16.50',
        rate: 0.09,
        title: 'GST',
        price_set: {
          shop_money: {
            amount: '16.50',
            currency_code: 'SGD',
          },
          presentment_money: {
            amount: '16.50',
            currency_code: 'SGD',
          },
        },
        channel_liable: false,
      },
    ],
    taxes_included: true,
    test: false,
    total_discounts: '21.10',
    total_discounts_set: {
      shop_money: {
        amount: '21.10',
        currency_code: 'SGD',
      },
      presentment_money: {
        amount: '21.10',
        currency_code: 'SGD',
      },
    },
    total_line_items_price: '220.90',
    total_line_items_price_set: {
      shop_money: {
        amount: '220.90',
        currency_code: 'SGD',
      },
      presentment_money: {
        amount: '220.90',
        currency_code: 'SGD',
      },
    },
    total_outstanding: '0.00',
    total_price: '199.80',
    total_price_set: {
      shop_money: {
        amount: '199.80',
        currency_code: 'SGD',
      },
      presentment_money: {
        amount: '199.80',
        currency_code: 'SGD',
      },
    },
    total_shipping_price_set: {
      shop_money: {
        amount: '0.00',
        currency_code: 'SGD',
      },
      presentment_money: {
        amount: '0.00',
        currency_code: 'SGD',
      },
    },
    total_tax: '16.50',
    total_tax_set: {
      shop_money: {
        amount: '16.50',
        currency_code: 'SGD',
      },
      presentment_money: {
        amount: '16.50',
        currency_code: 'SGD',
      },
    },
    total_tip_received: '0.00',
    total_weight: 0,
    updated_at: '2024-11-12T17:26:05+08:00',
    user_id: 94139777238.0,
    billing_address: null,
    customer: {
      id: 7568060121302.0,
      email: '<EMAIL>',
      created_at: '2024-11-12T17:21:02+08:00',
      updated_at: '2024-11-12T17:26:04+08:00',
      first_name: 'Samuel',
      last_name: 'Teo',
      state: 'disabled',
      note: '',
      verified_email: true,
      multipass_identifier: null,
      tax_exempt: false,
      email_marketing_consent: {
        state: 'unsubscribed',
        opt_in_level: 'single_opt_in',
        consent_updated_at: '2024-11-12T17:21:02+08:00',
      },
      sms_marketing_consent: {
        state: 'unsubscribed',
        opt_in_level: 'single_opt_in',
        consent_updated_at: '2024-11-12T17:21:02+08:00',
        consent_collected_from: 'SHOPIFY',
      },
      tags: '',
      currency: 'SGD',
      tax_exemptions: [],
      admin_graphql_api_id: 'gid://shopify/Customer/7568060121302',
    },
    discount_applications: [
      {
        target_type: 'line_item',
        type: 'automatic',
        value: '10.0',
        value_type: 'percentage',
        allocation_method: 'each',
        target_selection: 'entitled',
        title: '10off178',
      },
    ],
    fulfillments: [
      {
        id: 5193869131990.0,
        admin_graphql_api_id: 'gid://shopify/Fulfillment/5193869131990',
        created_at: '2024-11-12T17:26:04+08:00',
        location_id: 75705581782.0,
        name: 'STRYV-#36036.1',
        order_id: *************.0,
        service: 'manual',
        shipment_status: null,
        status: 'success',
        tracking_company: null,
        tracking_number: null,
        tracking_numbers: [],
        tracking_url: null,
        tracking_urls: [],
        updated_at: '2024-11-12T17:26:04+08:00',
        line_items: [
          {
            id: 14084755456214.0,
            admin_graphql_api_id: 'gid://shopify/LineItem/14084755456214',
            attributed_staffs: [],
            current_quantity: 1,
            fulfillable_quantity: 0,
            fulfillment_service: 'manual',
            fulfillment_status: 'fulfilled',
            gift_card: false,
            grams: 0,
            name: '1 Hairdryer + 2 hair pods sample',
            price: '169.00',
            price_set: {
              shop_money: {
                amount: '169.00',
                currency_code: 'SGD',
              },
              presentment_money: {
                amount: '169.00',
                currency_code: 'SGD',
              },
            },
            product_exists: true,
            product_id: 8567196451030.0,
            properties: [],
            quantity: 1,
            requires_shipping: true,
            sku: 'BD-SVHDPD12-01',
            taxable: true,
            title: '1 Hairdryer + 2 hair pods sample',
            total_discount: '16.90',
            total_discount_set: {
              shop_money: {
                amount: '16.90',
                currency_code: 'SGD',
              },
              presentment_money: {
                amount: '16.90',
                currency_code: 'SGD',
              },
            },
            variant_id: **************.0,
            variant_inventory_management: 'shopify',
            variant_title: null,
            vendor: 'Stryv',
            tax_lines: [
              {
                channel_liable: false,
                price: '12.56',
                price_set: {
                  shop_money: {
                    amount: '12.56',
                    currency_code: 'SGD',
                  },
                  presentment_money: {
                    amount: '12.56',
                    currency_code: 'SGD',
                  },
                },
                rate: 0.09,
                title: 'GST',
              },
            ],
            duties: [],
            discount_allocations: [
              {
                amount: '16.90',
                amount_set: {
                  shop_money: {
                    amount: '16.90',
                    currency_code: 'SGD',
                  },
                  presentment_money: {
                    amount: '16.90',
                    currency_code: 'SGD',
                  },
                },
                discount_application_index: 0,
              },
            ],
          },
          {
            id: 14084755488982.0,
            admin_graphql_api_id: 'gid://shopify/LineItem/14084755488982',
            attributed_staffs: [],
            current_quantity: 1,
            fulfillable_quantity: 0,
            fulfillment_service: 'manual',
            fulfillment_status: 'fulfilled',
            gift_card: false,
            grams: 0,
            name: 'stryv MultiShave',
            price: '42.00',
            price_set: {
              shop_money: {
                amount: '42.00',
                currency_code: 'SGD',
              },
              presentment_money: {
                amount: '42.00',
                currency_code: 'SGD',
              },
            },
            product_exists: true,
            product_id: 8270459764950.0,
            properties: [],
            quantity: 1,
            requires_shipping: true,
            sku: 'SVMSBDO2-01',
            taxable: true,
            title: 'stryv MultiShave',
            total_discount: '4.20',
            total_discount_set: {
              shop_money: {
                amount: '4.20',
                currency_code: 'SGD',
              },
              presentment_money: {
                amount: '4.20',
                currency_code: 'SGD',
              },
            },
            variant_id: **************.0,
            variant_inventory_management: 'shopify',
            variant_title: null,
            vendor: 'Stryv',
            tax_lines: [
              {
                channel_liable: false,
                price: '3.12',
                price_set: {
                  shop_money: {
                    amount: '3.12',
                    currency_code: 'SGD',
                  },
                  presentment_money: {
                    amount: '3.12',
                    currency_code: 'SGD',
                  },
                },
                rate: 0.09,
                title: 'GST',
              },
            ],
            duties: [],
            discount_allocations: [
              {
                amount: '4.20',
                amount_set: {
                  shop_money: {
                    amount: '4.20',
                    currency_code: 'SGD',
                  },
                  presentment_money: {
                    amount: '4.20',
                    currency_code: 'SGD',
                  },
                },
                discount_application_index: 0,
              },
            ],
          },
          {
            id: 14084755521750.0,
            admin_graphql_api_id: 'gid://shopify/LineItem/14084755521750',
            attributed_staffs: [],
            current_quantity: 1,
            fulfillable_quantity: 0,
            fulfillment_service: 'manual',
            fulfillment_status: 'fulfilled',
            gift_card: false,
            grams: 0,
            name: 'PWP: Liver Guard (1 bottle)',
            price: '9.90',
            price_set: {
              shop_money: {
                amount: '9.90',
                currency_code: 'SGD',
              },
              presentment_money: {
                amount: '9.90',
                currency_code: 'SGD',
              },
            },
            product_exists: true,
            product_id: 8568562942166.0,
            properties: [],
            quantity: 1,
            requires_shipping: true,
            sku: 'BBLGCP30-01',
            taxable: true,
            title: 'PWP: Liver Guard (1 bottle)',
            total_discount: '0.00',
            total_discount_set: {
              shop_money: {
                amount: '0.00',
                currency_code: 'SGD',
              },
              presentment_money: {
                amount: '0.00',
                currency_code: 'SGD',
              },
            },
            variant_id: **************.0,
            variant_inventory_management: 'shopify',
            variant_title: null,
            vendor: 'Stryv',
            tax_lines: [
              {
                channel_liable: false,
                price: '0.82',
                price_set: {
                  shop_money: {
                    amount: '0.82',
                    currency_code: 'SGD',
                  },
                  presentment_money: {
                    amount: '0.82',
                    currency_code: 'SGD',
                  },
                },
                rate: 0.09,
                title: 'GST',
              },
            ],
            duties: [],
            discount_allocations: [],
          },
        ],
      },
    ],
    line_items: [
      {
        id: 14084755456214.0,
        admin_graphql_api_id: 'gid://shopify/LineItem/14084755456214',
        attributed_staffs: [],
        current_quantity: 1,
        fulfillable_quantity: 0,
        fulfillment_service: 'manual',
        fulfillment_status: 'fulfilled',
        gift_card: false,
        grams: 0,
        name: '1 Hairdryer + 2 hair pods sample',
        price: '169.00',
        price_set: {
          shop_money: {
            amount: '169.00',
            currency_code: 'SGD',
          },
          presentment_money: {
            amount: '169.00',
            currency_code: 'SGD',
          },
        },
        product_exists: true,
        product_id: 8567196451030.0,
        properties: [],
        quantity: 1,
        requires_shipping: true,
        sku: 'BD-SVHDPD12-01',
        taxable: true,
        title: '1 Hairdryer + 2 hair pods sample',
        total_discount: '16.90',
        total_discount_set: {
          shop_money: {
            amount: '16.90',
            currency_code: 'SGD',
          },
          presentment_money: {
            amount: '16.90',
            currency_code: 'SGD',
          },
        },
        variant_id: **************.0,
        variant_inventory_management: 'shopify',
        variant_title: null,
        vendor: 'Stryv',
        tax_lines: [
          {
            channel_liable: false,
            price: '12.56',
            price_set: {
              shop_money: {
                amount: '12.56',
                currency_code: 'SGD',
              },
              presentment_money: {
                amount: '12.56',
                currency_code: 'SGD',
              },
            },
            rate: 0.09,
            title: 'GST',
          },
        ],
        duties: [],
        discount_allocations: [
          {
            amount: '16.90',
            amount_set: {
              shop_money: {
                amount: '16.90',
                currency_code: 'SGD',
              },
              presentment_money: {
                amount: '16.90',
                currency_code: 'SGD',
              },
            },
            discount_application_index: 0,
          },
        ],
      },
      {
        id: 14084755488982.0,
        admin_graphql_api_id: 'gid://shopify/LineItem/14084755488982',
        attributed_staffs: [],
        current_quantity: 1,
        fulfillable_quantity: 0,
        fulfillment_service: 'manual',
        fulfillment_status: 'fulfilled',
        gift_card: false,
        grams: 0,
        name: 'stryv MultiShave',
        price: '42.00',
        price_set: {
          shop_money: {
            amount: '42.00',
            currency_code: 'SGD',
          },
          presentment_money: {
            amount: '42.00',
            currency_code: 'SGD',
          },
        },
        product_exists: true,
        product_id: 8270459764950.0,
        properties: [],
        quantity: 1,
        requires_shipping: true,
        sku: 'SVMSBDO2-01',
        taxable: true,
        title: 'stryv MultiShave',
        total_discount: '4.20',
        total_discount_set: {
          shop_money: {
            amount: '4.20',
            currency_code: 'SGD',
          },
          presentment_money: {
            amount: '4.20',
            currency_code: 'SGD',
          },
        },
        variant_id: **************.0,
        variant_inventory_management: 'shopify',
        variant_title: null,
        vendor: 'Stryv',
        tax_lines: [
          {
            channel_liable: false,
            price: '3.12',
            price_set: {
              shop_money: {
                amount: '3.12',
                currency_code: 'SGD',
              },
              presentment_money: {
                amount: '3.12',
                currency_code: 'SGD',
              },
            },
            rate: 0.09,
            title: 'GST',
          },
        ],
        duties: [],
        discount_allocations: [
          {
            amount: '4.20',
            amount_set: {
              shop_money: {
                amount: '4.20',
                currency_code: 'SGD',
              },
              presentment_money: {
                amount: '4.20',
                currency_code: 'SGD',
              },
            },
            discount_application_index: 0,
          },
        ],
      },
      {
        id: 14084755521750.0,
        admin_graphql_api_id: 'gid://shopify/LineItem/14084755521750',
        attributed_staffs: [],
        current_quantity: 1,
        fulfillable_quantity: 0,
        fulfillment_service: 'manual',
        fulfillment_status: 'fulfilled',
        gift_card: false,
        grams: 0,
        name: 'PWP: Liver Guard (1 bottle)',
        price: '9.90',
        price_set: {
          shop_money: {
            amount: '9.90',
            currency_code: 'SGD',
          },
          presentment_money: {
            amount: '9.90',
            currency_code: 'SGD',
          },
        },
        product_exists: true,
        product_id: 8568562942166.0,
        properties: [],
        quantity: 1,
        requires_shipping: true,
        sku: 'BBLGCP30-01',
        taxable: true,
        title: 'PWP: Liver Guard (1 bottle)',
        total_discount: '0.00',
        total_discount_set: {
          shop_money: {
            amount: '0.00',
            currency_code: 'SGD',
          },
          presentment_money: {
            amount: '0.00',
            currency_code: 'SGD',
          },
        },
        variant_id: **************.0,
        variant_inventory_management: 'shopify',
        variant_title: null,
        vendor: 'Stryv',
        tax_lines: [
          {
            channel_liable: false,
            price: '0.82',
            price_set: {
              shop_money: {
                amount: '0.82',
                currency_code: 'SGD',
              },
              presentment_money: {
                amount: '0.82',
                currency_code: 'SGD',
              },
            },
            rate: 0.09,
            title: 'GST',
          },
        ],
        duties: [],
        discount_allocations: [],
      },
    ],
    payment_terms: null,
    refunds: [],
    shipping_address: null,
    shipping_lines: [],
  },
  headers: {
    'x-forwarded-for': '************',
    'x-forwarded-proto': 'https',
    'x-forwarded-port': '443',
    host: 'api.provesrc.com',
    'x-amzn-trace-id': 'Root=1-67331f2f-1259fafb5cfbbdce48a94479',
    'content-length': '12652',
    'user-agent': 'Shopify-Captain-Hook',
    accept: '*/*',
    'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3',
    'content-type': 'application/json',
    'x-shopify-api-version': '2024-07',
    'x-shopify-event-id': '1dc16b19-b536-4cc1-a0b2-ea9be3d7c844',
    'x-shopify-hmac-sha256': '04FzR2pRPdUU6DtC/55sqClZxY3IzdDDTrW/HR1wmwY=',
    'x-shopify-order-id': '*************',
    'x-shopify-shop-domain': 'stryv-supplements.myshopify.com',
    'x-shopify-test': 'false',
    'x-shopify-topic': 'orders/create',
    'x-shopify-triggered-at': '2024-11-12T09:26:05.107285693Z',
    'x-shopify-webhook-id': '8405fa67-8281-4766-8022-fb9b2dd77ef7',
  },
  query: {
    apiKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************.n1i5mKGUWAvgK4USP9UoKZTXRyGM48CbBidiM81xRlo',
  },
  createdAt: '2024-11-12T09:26:07.303Z',
  updatedAt: '2024-11-12T09:26:07.303Z',
  __v: 0,
};
