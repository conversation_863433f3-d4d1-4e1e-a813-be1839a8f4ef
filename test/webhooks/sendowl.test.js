const chai = require('chai');
const sinon = require('sinon');
const httpUtils = require('../httpUtils');
const server = require('../../server');
const NotificationFactory = require('../factories/NotificationFactory');
const { WebhookStreamEvent } = require('../factories/EventFactory').models;

const { expect } = chai;
const { Notification } = NotificationFactory;


describe('/webhooks/track sendowl', () => {
  beforeEach(async () => {
    await WebhookStreamEvent.remove();
  });

  afterEach(async () => {
    sinon.restore();
  });

  it('should capture lead details', async () => {
    const notification = NotificationFactory.Stream({ autoTrack: false });
    sinon.stub(Notification, 'findOne').resolves(notification);
    const data = {
      order: {
        state: 'complete',
        gateway: 'PayPal',
        buyer_email: '<EMAIL>',
        paypal_email: '<EMAIL>',
        buyer_name: 'UNΛGΛNG Isseï',
        business_name: null,
        business_vat_number: null,
        shipping_address1: null,
        shipping_address2: null,
        shipping_city: null,
        shipping_region: null,
        shipping_postcode: null,
        shipping_country: null,
        billing_address1: null,
        billing_address2: null,
        billing_city: null,
        billing_region: null,
        billing_postcode: null,
        billing_country: 'FR',
        buyer_ip_address: '************',
        settled_currency: 'USD',
        access_allowed: true,
        dispatched_at: null,
        tag: null,
        giftee_name: null,
        giftee_email: null,
        gift_deliver_at: null,
        download_url: 'https://transactions.sendowl.com/orders/44539932/download/4af2684be793f4254032fdac5b72d419',
        order_checkout_url: null,
        subscription_management_url: null,
        eu_resolved_country: null,
        id: '0044539932',
        sendowl_order_id: 44539932,
        refunded: false,
        settled_gateway_fee: '2.68',
        settled_gross: '54.00',
        price_at_checkout: '54.00',
        eu_reverse_charge: null,
        for_subscription: false,
        payment_method: 'paypal_express',
        cart: {
          completed_checkout_at: '2019-08-05T08:13:34Z',
          cart_items: [
            {
              valid_until: '2020-08-04T14:13:34Z',
              download_attempts: 25,
              id: 56094758,
              quantity: 2,
              unit_price: '27.00',
              total_price: '54.00',
              product: {
                id: 657924,
                name: 'Echo Sound Works Sphere Complete',
                shopify_variant_id: null,
                product_type: 'bundle',
                member_types: [
                  'digital',
                  'digital',
                  'digital',
                  'digital',
                  'digital',
                  'digital',
                  'digital',
                ],
                price: '27.00',
                price_is_minimum: false,
                limit_to_single_qty_in_cart: false,
                update_message: null,
              },
            },
          ],
          errors: [],
        },
        licenses: [],
        discount_code: null,
        unsubscribe_url: 'http://transactions.sendowl.com/orders/44539932/unsubscribe/4af2684be793f4254032fdac5b72d419',
        product_update_consent_url: 'http://transactions.sendowl.com/orders/44539932/consent/4af2684be793f4254032fdac5b72d419',
        transactions: [
          {
            gateway_transaction_id: '7K5167498L5401937',
            payment_currency: 'USD',
            alternate_pay_method_note: null,
            created_at: '2019-08-05T08:13:34Z',
            payment_gross: '54.00',
            payment_gateway_fee: '2.68',
            net_price: '54.00',
            refund: false,
            card_last_4_digits: null,
            card_expires_at: null,
          },
        ],
        order_custom_checkout_fields: [],
        receiver_name: 'UNΛGΛNG Isseï',
        receiver_email: '<EMAIL>',
        gift_order: false,
        send_update_emails: true,
        next_payment_date: null,
        errors: [],
        buyer_first_name: 'UNΛGΛNG',
        receiver_first_name: 'UNΛGΛNG',
        validity_statement: 'This link may be used up to 25 times before 2020-08-04 14:13:34 UTC when it will expire.',
        buyer_address1: null,
        buyer_address2: null,
        buyer_city: null,
        buyer_region: null,
        buyer_postcode: null,
        buyer_country: null,
      },
    };
    const res = await httpUtils.trackWebhookData(server, notification.webhookId, data);

    expect(res).to.have.status(200);
    const event = await WebhookStreamEvent.findOne();
    expect(event.firstName).to.be.equal(data.order.buyer_first_name);
    expect(event.products[0].name).to.be.equal(data.order.cart.cart_items[0].product.name);
  });
});
