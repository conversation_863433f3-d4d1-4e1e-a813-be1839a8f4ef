const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');
const utils = require('../testUtils');
const urlModule = require('url');
const Chance = require('chance');

const chance = new Chance();
const maxmind = require('../../lib/maxmind');

const WooEvent = require('../../app/events/models/WooEvent').model;
const Feed = require('../../app/account/models/Feed');

describe('WooCommerce', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    await Promise.all([WooEvent.remove(), Feed.remove()]);
  });

  it('should fail when no auth', async () => {
    const res = await httpUtils.noAuthRequest(server, '/webhooks/track/woocommerce', httpUtils.POST);
    expect(res).to.have.status(401);
  });

  it('should save a WooEvent and a Feed event', async () => {
    const data = getData();

    const {
      ip, siteUrl, firstName, lastName, email, total, currency,
    } = data;
    const product = data.products[0];

    const res = await httpUtils.apiRequest(server, '/webhooks/track/woocommerce', httpUtils.POST).send(data);
    expect(res).to.have.status(200);

    const { accountId } = constants;
    const [wooEvent, feed] = await Promise.all([
      WooEvent.findOne({ accountId }),
      Feed.findOne({ accountId }),
    ]);

    const { host } = urlModule.parse(siteUrl);
    const location = await maxmind.geoIP(ip);

    expect(wooEvent.firstName).to.be.equal(firstName);
    expect(wooEvent.lastName).to.be.equal(lastName);
    expect(wooEvent.email).to.be.equal(email);
    expect(wooEvent.host).to.be.equal(host);
    expect(wooEvent.ip).to.be.equal(ip);
    expect(wooEvent.location.toObject()).to.deep.equal(location);
    expect(wooEvent.total).to.be.equal(total);
    expect(wooEvent.currency).to.be.equal(currency);
    expect(wooEvent.products[0].toObject()).to.be.deep.equal(product);

    expect(feed.message).to.be.equal('WooCommerce Order');
    expect(feed.data.store).to.be.equal(host);
    expect(feed.data.name).to.be.equal(firstName);
    expect(feed.data.products[0]).to.be.deep.equal(product);
    expect(feed.data.total).to.be.equal(total);
    expect(feed.data.currency).to.be.equal(currency);
  });

  it('should not fail when no product image', async () => {
    const data = getData();
    const product = data.products[0];
    product.image = null;
    const res = await httpUtils.apiRequest(server, '/webhooks/track/woocommerce', httpUtils.POST).send(data);
    expect(res).to.have.status(200);

    const { accountId } = constants;
    const [wooEvent, feed] = await Promise.all([
      WooEvent.findOne({ accountId }),
      Feed.findOne({ accountId }),
    ]);
    expect(wooEvent).to.exist;
    expect(feed).to.exist;
  });

  it('should replace httpss with https', async () => {
    const data = getData();
    const { image } = data.products[0];
    data.products[0].image = image.replace('https', 'httpss');

    const res = await httpUtils.apiRequest(server, '/webhooks/track/woocommerce', httpUtils.POST).send(data);
    expect(res).to.have.status(200);

    const wooEvent = await WooEvent.findOne();
    expect(wooEvent).to.exist;
    expect(wooEvent.products[0].image).to.not.include('httpss');
  });
});

function getData() {
  const email = '<EMAIL>';
  const siteUrl = 'https://shop.provesrc.com';
  const total = 52;
  const firstName = 'Natan';
  const lastName = 'Abramov';
  const currency = 'USD';
  const product = {
    id: 5,
    quantity: 1,
    price: total,
    name: 'product',
    link: 'https://provesrc.com/product',
    image: 'https://provesrc.com/image',
  };
  const ip = chance.ip();
  return {
    email, siteUrl, products: [product], ip, currency, total, firstName, lastName,
  };
}
