const chai = require('chai');
const sinon = require('sinon');
const server = require('../../server');
const config = require('../../config');
const httpUtils = require('../httpUtils');
const constants = require('../constants');

const sandbox = sinon.createSandbox();
const { expect } = chai;

const zapierService = require('../../app/zapier/zapier.service');

describe('getNotificationsList', () => {
  let app;
  beforeEach(async () => {
    app = await server;
  });

  afterEach(async () => {
    sandbox.restore();
  });

  describe('auth', () => {
    it('should fail if wrong api key', async () => {
      const opts = { apiKey: '123', headers: { 'x-zapier-secret': config.zapier.secret } };
      const res = await httpUtils.apiRequest(app, '/zapier/notifications', 'GET', opts);
      expect(res.body.error).to.include('not authorized');
      expect(res).to.have.status(401);
    });

    it('should fail if wrong secret', async () => {
      const opts = { apiKey: constants.apiKey, headers: { 'x-zapier-secret': 'wrongsecret' } };
      const res = await httpUtils.apiRequest(app, '/zapier/notifications', 'GET', opts);
      expect(res.body.error).to.include('not authorized');
      expect(res).to.have.status(401);
    });

    it('should return status 200', async () => {
      const opts = { apiKey: constants.apiKey, headers: { 'x-zapier-secret': config.zapier.secret } };
      const res = await httpUtils.apiRequest(app, '/zapier/notifications', 'GET', opts);
      expect(res).to.have.status(200);
    });
  });

  describe('getNotificationsList', () => {
    it('should return list of notifications', async () => {
      const notifications = [
        { notification: 'Notification 1', webhook: '1' },
        { notification: 'Notification 2', webhook: '2' },
      ];
      const list = sandbox.stub(zapierService, 'getNotificationsList').resolves(notifications);

      const opts = {
        apiKey: constants.apiKey,
        headers: { 'x-zapier-secret': config.zapier.secret },
      };
      const res = await httpUtils.apiRequest(app, '/zapier/notifications', 'GET', opts);

      expect(res.body).to.be.eql(notifications);
      expect(list).to.have.been.calledWith(sinon.match.string);
    });

    it('should return empty list on error', async () => {
      sandbox.stub(zapierService, 'getNotificationsList').rejects(Error('No pass'));

      const opts = {
        apiKey: constants.apiKey,
        headers: { 'x-zapier-secret': config.zapier.secret },
      };
      const res = await httpUtils.apiRequest(app, '/zapier/notifications', 'GET', opts);

      expect(res.body).to.be.eql([]);
    });
  });
});
