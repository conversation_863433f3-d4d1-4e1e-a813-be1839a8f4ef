const chai = require('chai');
const httpUtils = require('../httpUtils');
const app = require('../../server');
const constants = require('../constants');

const { expect } = chai;

describe('zapier/auth', () => {
  it('should return not authorized response (status 401) if invalid api key', async () => {
    const res = await httpUtils.apiRequest(app, '/zapier/auth', 'GET', { apiKey: '123' });
    expect(res).to.have.status(401);
  });

  it('should succesufuly response withh status 200 if api key is correct3', async () => {
    const res = await httpUtils.apiRequest(app, '/zapier/auth', 'GET', { apiKey: constants.apiKey });
    expect(res).to.have.status(200);
  });
});
