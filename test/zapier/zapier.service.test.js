const chai = require('chai');
const sinon = require('sinon');
const constants = require('../constants');
const config = require('../../config');

const { expect } = chai;
const zapierService = require('../../app/zapier/zapier.service');
const Notification = require('../../app/notifications/models/Notification');
const notifConsts = require('../../app/notifications/constants');

describe('zapier.service', () => {
  afterEach(async () => {
    sinon.restore();
  });

  describe('getNotificationsList', () => {
    it('should fail if unable to decode apiKey', async () => {
      const promise = zapierService.getNotificationsList('12345');
      return expect(promise).to.be.rejectedWith('accountId');
    });

    it('should webhook notifications for account', async () => {
      const notifications = [
        { id: '123', name: 'Notification 1', webhookId: '1' },
        { id: '123', name: 'Notification 2', webhookId: '2' },
      ];
      const find = sinon.mock(Notification).expects('find');
      find.chain('select').resolves(notifications);

      const result = await zapierService.getNotificationsList(constants.accountId);

      expect(result).to.be.eql(notifications);
      expect(find).to.have.been.calledWith(sinon.match({
        accountId: constants.accountId,
        autoTrack: false,
        'settings.platform': notifConsts.PLATFORMS.custom,
      }));
    });
  });
});
