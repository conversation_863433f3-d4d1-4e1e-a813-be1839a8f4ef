const chai = require('chai');
const httpUtils = require('../httpUtils');
const constants = require('../constants');

const { expect } = chai;
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const serverP = require('../../server');

const VisitorPing = require('../../app/notifications/models/VisitorPing');
const nFactory = require('../factories/NotificationFactory');

const { Notification } = nFactory;

const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;

describe('Live Visitors Notification', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  let account;
  beforeEach(async () => {
    account = AccountFactory.default();
    sandbox.stub(Account, 'findOne').resolves(account);
    sandbox.stub(Account.prototype, 'save').resolves();
    await VisitorPing.remove();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('/notifications/ping', () => {
    it('should count visitors in mongodb on ping', async () => {
      const psuid = '12345';
      const host = 'provesrc.com';
      await httpUtils.pingSimple(server, psuid, `api.${host}`);
      await httpUtils.ping(server, { 'x-ps-uid': psuid, origin: `api.${host}` });

      const count = await VisitorPing.count({});
      expect(count).to.be.equal(1);
      const ping = await VisitorPing.findOne({});
      expect(ping).to.exist;
      expect(ping.uuid).to.be.equal(psuid);
      expect(ping.host).to.be.equal(host);
    });
  });

  describe('/account/configuration', () => {
    let sandbox;
    beforeEach(async () => {
      sandbox = sinon.createSandbox();
    });

    afterEach(() => {
      sandbox.restore();
    });

    it('should return hasLive: false when no live notification', async () => {
      sandbox.mock(Notification)
        .expects('findOne').twice()
        .chain('cache')
        .twice()
        .chain('exec')
        .twice()
        .resolves(null);
      const res = await httpUtils.configApiGET(server, 'provesrc.com');
      expect(res.body.hasLive).to.be.false;
    });

    it('should return hasLive: true when no live notification', async () => {
      sandbox.mock(Notification)
        .expects('findOne').twice()
        .chain('cache')
        .twice()
        .chain('exec')
        .twice()
        .resolves({});
      const res = await httpUtils.configApiGET(server, 'provesrc.com');
      expect(res.body.hasLive).to.be.true;
    });
  });

  describe('/notifications/get', () => {
    beforeEach(async () => {
      await Notification.remove();
    });

    it('should live visitor notification', async () => {
      // create live visitor count
      const host = 'provesrc.com';
      const url = `https://${host}`;
      const n = nFactory.LiveVisitors({ display: url });
      sandbox.mock(Notification).expects('find').chain('cache').chain('exec')
        .yields(null, [n]);

      // send 2 pings (2 visitors)
      await Promise.all([
        httpUtils.pingSimple(server, '1', host),
        httpUtils.pingSimple(server, '2', host),
      ]);

      const res = await httpUtils.notificationsGet(server, { url });
      expect(res.body).to.have.length(1);
      const notif = res.body[0];
      expect(notif.type).to.be.equal('live');
      expect(notif.count).to.be.equal(2);
    });

    it('should check minimum to display', async () => {
      // create live visitor count
      const host = 'provesrc.com';
      const url = `https://${host}`;
      const n = nFactory.LiveVisitors({ display: url });
      n.settings.minimumToDisplay = 2;
      await n.save();

      // send 1 pings (2 visitors)
      httpUtils.pingSimple(server, '1', host);

      const res = await httpUtils.notificationsGet(server, { url });
      expect(res.body).to.have.length(0);
    });
  });
});
