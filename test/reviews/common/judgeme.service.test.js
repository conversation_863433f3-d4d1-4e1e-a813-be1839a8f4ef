const chai = require('chai');
const sinon = require('sinon');
const judgeme = require('../../../lib/apis/judgeme');
const JudgemeReview = require('../../../app/reviews/models/JudgemeReview');
const Feed = require('../../../app/account/models/Feed');
const judgemeService = require('../../../app/reviews/judgeme.service');

const { expect } = chai;

describe('judgeme.service', () => {
  afterEach(() => {
    sinon.restore();
  });

  describe('handleReviews', () => {
    it('should successfully save fetched reviews', async () => {
      const notification = {
        accountId: '123456',
        placeId: '321654',
        pageToken: 'secretPrivetApiKey',
      };
      const batchOne = [];
      const batchTwo = [];

      for(let i = 1; i < 11; i += 1) {
        batchOne.push({
          id: i,
          rating: 3,
          body: 'test text',
          reviewer: {
            name: 'test name',
            email: '<EMAIL>',
          },
          pictures: [],
          created_at: new Date(100000),
        });
        if(i <= 3) {
          batchTwo.push({
            id: i + 10,
            rating: 5,
            title: 'test title',
            reviewer: {
              name: 'test name',
              email: '<EMAIL>',
            },
            pictures: [],
            created_at: new Date(100000),
          });
        }
      }
      batchTwo[0].pictures = [{ urls: { small: 'testpic.url' } }];
      batchTwo[1].body = 'test body';
      batchTwo[2].created_at = new Date(0);
      const reviewResults = [
        {
          accountId: notification.accountId,
          placeId: notification.placeId,
          reviewId: 11,
          productId: null,
          authorName: 'test name',
          authorEmail: '<EMAIL>',
          profilePhotoUrl: 'testpic.url',
          rating: 5,
          text: 'test title',
          time: batchTwo[0].created_at,
        },
        {
          accountId: notification.accountId,
          placeId: notification.placeId,
          reviewId: 12,
          productId: null,
          authorName: 'test name',
          authorEmail: '<EMAIL>',
          profilePhotoUrl: null,
          rating: 5,
          text: 'test body',
          time: batchTwo[1].created_at,
        },
      ];
      const getReviewsStub = sinon.stub(judgeme, 'getReviews');
      getReviewsStub.onCall(0).resolves(batchOne);
      getReviewsStub.onCall(1).resolves(batchTwo);

      const find = sinon.mock(JudgemeReview)
        .expects('find')
        .chain('sort')
        .chain('limit')
        .chain('then')
        .resolves({ time: new Date(1000) });

      sinon.stub(Feed, 'saveFeed');
      const judgemeSave = sinon.stub(JudgemeReview, 'insertMany').resolves(null);
      const serviceRes = await judgemeService.handleReviews(notification, {});

      expect(serviceRes).to.be.eql({ reviews: 2, error: null });
      expect(find).to.have.been.called;
      expect(getReviewsStub).to.have.been.callCount(2);
      expect(judgemeSave).to.be.calledWith(reviewResults);
    });
  });
});
