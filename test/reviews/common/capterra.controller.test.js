const chai = require('chai');
const sinon = require('sinon');
const capterraController = require('../../../app/reviews/capterra.controller');
const Feed = require('../../../app/account/models/Feed');
const CapterraReview = require('../../../app/reviews/models/CapterraReview');
const capterra = require('../../../lib/apis/capterra');

const { expect } = chai;

describe('capterra controller', () => {
  afterEach(() => {
    sinon.restore();
  });

  describe('handleReviews', () => {
    function stubs() {
      const feedSave = sinon.stub(Feed.prototype, 'save');
      const capterraSave = sinon.stub(CapterraReview, 'insertMany');
      const capterraGetReviews = sinon.stub(capterra, 'getReviews');
      return {
        feedSave, capterraSave, capterraGetReviews,
      };
    }

    it('should save error event in Feed and not save review if no placeId found in notification (input)', async () => {
      const { feedSave, capterraSave } = stubs();
      const notification = { accountId: 123, placeId: null };

      feedSave.resolves({});
      await capterraController.handleReviews(notification);
      expect(feedSave).to.be.calledOnce;
      expect(capterraSave).to.not.be.called;
    });

    it('should save error event in Feed and not save reviews if no reviews found in capterra', async () => {
      const { capterraGetReviews, feedSave, capterraSave } = stubs();

      capterraGetReviews.resolves(null);
      feedSave.resolves();
      const notification = {
        accountId: '123456',
        placeId: '*********',
      };

      await capterraController.handleReviews(notification);

      expect(feedSave).to.be.calledOnce;
      expect(capterraSave).to.not.be.called;
    });

    it('should successfully save reviews', async () => {
      const {
        capterraSave, feedSave, capterraGetReviews,
      } = stubs();
      const notification = {
        accountId: '123456',
        placeId: '*********',
      };

      const reviews = [{
        id: 1,
        time: new Date(0),
        authorName: 'Israel',
      }, {
        id: 2,
        time: new Date(1000),
        authorName: 'Israel',
      }, {
        id: 3,
        time: new Date(100000),
        authorName: 'Israel',
      }];

      const find = sinon.mock(CapterraReview)
        .expects('find')
        .chain('sort')
        .chain('limit')
        .chain('then')
        .resolves(reviews[0]);
      capterraGetReviews.resolves(reviews);
      capterraSave.resolves(reviews);
      feedSave.resolves();

      await capterraController.handleReviews(notification);
      expect(find).to.have.been.called;
      const formattedReviews = reviews
        .filter(r => r.time > reviews[0].time)
        .map((r) => {
          r.accountId = notification.accountId;
          r.placeId = notification.placeId;
          return r;
        });
      expect(capterraSave).to.be.calledWith(formattedReviews);
    });
  });
});
