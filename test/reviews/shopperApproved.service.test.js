const chai = require('chai');
const sinon = require('sinon');
const shopperApproved = require('../../lib/apis/shopperApproved');
const ShopperApprovedReview = require('../../app/reviews/models/ShopperApprovedReview');
const Feed = require('../../app/account/models/Feed');
const shopperApprovedService = require('../../app/reviews/shopperApproved.service');

const { expect } = chai;

describe('shopperApproved.service', () => {
  afterEach(() => {
    sinon.restore();
  });

  describe('handleReviews', () => {
    it('should successfully save fetched reviews', async () => {
      const notification = {
        accountId: '123456',
        placeId: '321654',
        pageToken: 'secretPrivateApiKey',
      };
      const rawReviews = [
        {
          reviewId: 123,
          orderId: 'ord1',
          authorName: 'the name',
          authorEmail: '<EMAIL>',
          authorLocation: 'CA, United States',
          rating: 5,
          text: 'Great some thing',
          time: new Date(100000),
        },
        {
          reviewId: 456,
          orderId: 'ord2',
          authorName: 'Name',
          authorEmail: '<EMAIL>',
          authorLocation: 'North pole',
          rating: 5,
          text: 'first comment',
          time: new Date(0),
        },
      ];

      const reviewResults = [
        {
          accountId: notification.accountId,
          placeId: notification.placeId,
          reviewId: rawReviews[0].reviewId,
          authorName: rawReviews[0].authorName,
          orderId: rawReviews[0].orderId,
          authorEmail: rawReviews[0].authorEmail,
          authorLocation: rawReviews[0].authorLocation,
          rating: 5,
          text: rawReviews[0].text,
          time: rawReviews[0].time,
        },
      ];
      const getReviewsStub = sinon.stub(shopperApproved, 'getReviews').resolves(rawReviews);

      const find = sinon.mock(ShopperApprovedReview)
        .expects('find')
        .chain('sort')
        .chain('limit')
        .chain('then')
        .resolves({ time: new Date(1000) });

      sinon.stub(Feed, 'saveFeed');
      const shopperApprovedSave = sinon.stub(ShopperApprovedReview, 'insertMany').resolves(null);
      const serviceRes = await shopperApprovedService.handleReviews(notification, {});

      expect(serviceRes).to.be.eql({ reviews: 1, error: null });
      expect(find).to.have.been.called;
      expect(getReviewsStub).to.have.been.called;
      expect(shopperApprovedSave).to.be.calledWith(reviewResults);
    });
  });
});
