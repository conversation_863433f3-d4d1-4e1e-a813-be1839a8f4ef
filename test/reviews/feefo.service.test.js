const chai = require('chai');
const sinon = require('sinon');
const feefo = require('../../lib/apis/feefo');
const FeefoReview = require('../../app/reviews/models/FeefoReview');
const Feed = require('../../app/account/models/Feed');
const feefoService = require('../../app/reviews/feefo.service');

const { expect } = chai;

describe('feefo.service', () => {
  afterEach(() => {
    sinon.restore();
  });

  describe('handleReviews', () => {
    it('should successfully save fetched reviews', async () => {
      const notification = {
        accountId: '123456',
        placeId: '321654',
      };
      const rawReviews = [
        {
          reviewId: '123',
          authorName: 'the name',
          authorLocation: 'CA, United States',
          rating: 5,
          text: 'Great some thing',
          time: new Date(100000),
        },
        {
          reviewId: '456',
          authorName: 'Name',
          authorLocation: 'North pole',
          rating: 5,
          text: 'first comment',
          time: new Date(0),
        },
      ];

      const reviewResults = [
        {
          accountId: notification.accountId,
          placeId: notification.placeId,
          reviewId: rawReviews[0].reviewId,
          authorName: rawReviews[0].authorName,
          authorLocation: rawReviews[0].authorLocation,
          rating: 5,
          text: rawReviews[0].text,
          time: rawReviews[0].time,
        },
      ];
      const getReviewsStub = sinon.stub(feefo, 'getReviews').resolves(rawReviews);

      const find = sinon.mock(FeefoReview)
        .expects('find')
        .chain('sort')
        .chain('limit')
        .chain('then')
        .resolves({ time: new Date(1000) });

      sinon.stub(Feed, 'saveFeed');
      const shopperApprovedSave = sinon.stub(FeefoReview, 'insertMany').resolves(null);
      const serviceRes = await feefoService.handleReviews(notification);

      expect(serviceRes).to.be.eql({ reviews: 1, error: null });
      expect(find).to.have.been.called;
      expect(getReviewsStub).to.have.been.called;
      expect(shopperApprovedSave).to.be.calledWith(reviewResults);
    });
  });
});
