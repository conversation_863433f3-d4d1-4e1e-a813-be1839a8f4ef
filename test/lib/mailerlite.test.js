const _ = require('lodash');
const moment = require('moment');
const chai = require('chai');

const { expect } = chai;
const constants = require('../constants');
const mailerlite = require('../../lib/apis/mailerlite');
const config = require('../../config');

// If you want to test this, set 'testConfig.mailerlite.active' and add 'apikey' and 'subscribe' (url)
xdescribe('Lib - mailerlite', () => {
  it('should subscribe to mailerlite', async () => {
    await mailerlite.subscribe('<EMAIL>');
  });

  it('should add parameters to subscriber', async () => {
    const fields = [
      'created_notification',
      'updated_notification',
      'logged_in',
      'installed_code',
      'plan',
      'saw_wizard_remarketing',
      'contacted_via_drift',
      'recent_ipn',
      'expires',
      'limit',
      'daysleft',
      'missed_text',
    ];
    const data = {
      contacted_via_drift: 1,
      plan: 'Starter Monthly Plan',
      recent_ipn: 'Auth',
      limit: 1000,
      daysleft: 2,
      missed_text: 'a',
    };
    const date = new Date();
    for(let i = 0; i < fields.length; i++) {
      if(!data[fields[i]]) data[fields[i]] = date;
    }

    const res = await mailerlite.updateSubscriber('<EMAIL>', data);

    const expectedDate = moment(date).format('YYYY-MM-DD');
    for(let i = 0; i < res.body.fields.length; i++) {
      const field = res.body.fields[i];
      const { key } = field;
      const { type } = field;
      const fieldIndex = fields.indexOf(key);
      if(fieldIndex > -1) {
        fields.splice(fieldIndex, 1);
        const { value } = field;
        console.log('field', field);
        switch(type) {
        case 'DATE':
          expect(value).to.be.equal(expectedDate);
          break;

        default:
          expect(value).to.be.equal(`${data[field.key]}`);
          break;
        }
      }
    }
    expect(fields).to.be.empty;
  });
});
