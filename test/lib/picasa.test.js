const chai = require('chai');

const { expect } = chai;
const constants = require('../constants');
const picasa = require('../../lib/apis/picasa');

xdescribe('Lib - <PERSON>casa', () => {
  it('should get picasa profile', async () => {
    const email = '<EMAIL>';
    const profile = await picasa.getProfile(email);
    expect(profile).to.exist;
    expect(profile.author).to.exist;
  });

  it('should not find picasa profile', async () => {
    const email = '<EMAIL>';
    const profile = await picasa.getProfile(email);
    expect(profile).to.not.exist;
  });
});
