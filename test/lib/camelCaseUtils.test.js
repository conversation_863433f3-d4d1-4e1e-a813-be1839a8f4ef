const chai = require('chai');
const lib = require('../../lib/utils/camelCaseUtils');

const { expect } = chai;

describe('lib/utils/camelCaseUtils', () => {
  describe('toCamelCase', () => {
    it('checks function exists', () => {
      expect(lib.toCamelCase).to.be.an('function');
    });

    it('changes a string passed with space character to camelCase', () => {
      expect(lib.toCamelCase('a b')).to.equal('aB', 'doesnt return expected value');
    });

    it('returns unchanged string when no spaces in passed string', () => {
      expect(lib.toCamelCase('ab')).to.equal('ab', 'doesnt return expected value');
    });

    it('returns a string passed with multiple spaces as camelCase', () => {
      expect(lib.toCamelCase('a   b')).to.equal('aB', 'doesnt return expected value');
    });
  });

  describe('changePropertyNameToCamelCase', () => {
    it('checks function exists', () => {
      expect(lib.changePropertyNameToCamelCase).to.be.an('function', 'changePropertyNameToCamelCase not function');
    });

    it('returns an array of objects passed with _ in property keys as camelCased', () => {
      const data = [{ key_a: 'valuea' }, { key_b_c: 'valueb' }];
      const results = lib.changePropertyNameToCamelCase(data);
      expect(results).to.be.an('array', 'results not array');
      expect(results).to.have.lengthOf(2, 'results array not expected length');
      expect(results[0]).to.have.property('keyA', 'valuea', 'keyA property not as expected');
      expect(results[1]).to.have.property('keyBC', 'valueb', 'keyBC property not as expected');
    });

    it('returns an array of objects passed with no _ in property keys as is', () => {
      const data = [{ keyA: 'valuea' }, { keyBC: 'valueb' }];
      const results = lib.changePropertyNameToCamelCase(data);
      expect(results).to.be.an('array', 'results not array');
      expect(results).to.have.lengthOf(2, 'results not expected length');
      expect(results[0]).to.have.property('keyA', 'valuea', 'keyA property not as expected');
      expect(results[1]).to.have.property('keyBC', 'valueb', 'keyBC property not as expected');
    });
  });
});
