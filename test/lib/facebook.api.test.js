const chai = require('chai');
const request = require('superagent');
const config = require('../mocking/lib/facebookReviews');
const chaiAsPromised = require('chai-as-promised');
const superagentMock = require('superagent-mock')(request, config);
const nock = require('nock');
const lib = require('../../lib/apis/facebook');

chai.use(chaiAsPromised);

const { expect } = chai;

describe('lib/facebookReviews', () => {
  after(() => {
    superagentMock.unset();
  });

  describe('getApiBaseUri', () => {
    it('check function exists', () => {
      expect(lib.getApiBaseUri).to.be.an('function', 'not a function');
    });

    it('return a url for base of API', () => {
      const result = lib.getApiBaseUri();
      expect(result).to.be.an('string');
      expect(result).to.equal('https://graph.facebook.com/v3.3');
    });
  });

  describe('getRatingsApiUri', () => {
    it('check function exists', () => {
      expect(lib.getRatingsApiUri).to.be.an('function', 'not a function');
    });

    it('return a url for getRatings', () => {
      const result = lib.getRatingsApiUri('***************');
      expect(result).to.be.an('string');
      expect(result).to.equal('https://graph.facebook.com/v3.3/***************/ratings');
    });
  });

  describe('getPagesApiUrl', () => {
    it('check function exists', () => {
      expect(lib.getPagesApiUrl).to.be.an('function', 'not a function');
    });

    it('return a url for getPages', () => {
      const result = lib.getPagesApiUrl();
      expect(result).to.be.an('string');
      expect(result).to.equal('https://graph.facebook.com/v3.3/me/accounts');
    });
  });

  describe('getUserAccessTokenApiUrl', () => {
    it('check function exists', () => {
      expect(lib.getUserAccessTokenApiUrl).to.be.an('function', 'not a function');
    });

    it('return a url for getUserAccessToken', () => {
      const result = lib.getUserAccessTokenApiUrl();
      expect(result).to.be.an('string');
      expect(result).to.equal('https://graph.facebook.com/v3.3/oauth/access_token');
    });
  });

  describe('getPageAccessTokenApiUrl', () => {
    it('check function exists', () => {
      expect(lib.getPageAccessTokenApiUrl).to.be.an('function', 'not a function');
    });

    it('return a url for getPageAccessToken', () => {
      const result = lib.getPageAccessTokenApiUrl('***************');
      expect(result).to.be.an('string');
      expect(result).to.equal('https://graph.facebook.com/v3.3/***************');
    });
  });


  describe('getRatings', () => {
    it('check function exists', () => {
      expect(lib.getRatings).to.be.an('function', 'not a function');
    });

    it('gets ratings for a page', async () => {
      const results = await lib.getRatings('***************', { access_token: 'N3SMsizs6xKTpRX1Xx5VDfyIzpJmFVjKgMe201EfI8Hc1M87gCzEqQ5HEmrGooQBhUYe1BqzHW2tK5FrNmSNLsQxN1QaYqDZ1n09JRKDCifA0zDaCxQZ2ihaCFW91sTn2MCq2nJzS378giA7l6llUq0sHMxVFjCMlukJv37yIT6dmOtbZrcxCxCy4hFvXwuyQzRrXblWz' });
      expect(results).to.be.an('object', 'not an object');
      expect(results).to.have.keys(['data', 'paging']);
      expect(results.data).to.be.an('array', 'results.data not an array');
      expect(results.data).to.have.lengthOf(5, 'array not expected length');
      expect(results.data[0]).to.have.keys(['created_time', 'recommendation_type', 'review_text']);
      expect(results.data[0]).property('created_time', '2019-06-05T08:11:17+0000', 'created_time not expected value');
      expect(results.data[0]).property('recommendation_type', 'positive', 'recommendation_type not expected value');
      expect(results.data[0]).property('review_text', 'This is test post 1. Please ignore this.', 'review_text not expected value');
    });

    it('returns error if status of request not 200', () => {
      expect(lib.getRatings('926936568936850', { access_token: 'N3SMsizs6xKTpRX1Xx5VDfyIzpJmFVjKgMe201EfI8Hc1M87gCzEqQ5HEmrGooQBhUYe1BqzHW2tK5FrNmSNLsQxN1QaYqDZ1n09JRKDCifA0zDaCxQZ2ihaCFW91sTn2MCq2nJzS378giA7l6llUq0sHMxVFjCMlukJv37yIT6dmOtbZrcxCxCy4hFvXwuyQzRrXblWz' })).to.be.eventually.rejectedWith(Error);
    });

    it('returns error if status of request is 200 but no data element', () => {
      expect(lib.getRatings('216062941462599', { access_token: 'N3SMsizs6xKTpRX1Xx5VDfyIzpJmFVjKgMe201EfI8Hc1M87gCzEqQ5HEmrGooQBhUYe1BqzHW2tK5FrNmSNLsQxN1QaYqDZ1n09JRKDCifA0zDaCxQZ2ihaCFW91sTn2MCq2nJzS378giA7l6llUq0sHMxVFjCMlukJv37yIT6dmOtbZrcxCxCy4hFvXwuyQzRrXblWz' })).to.be.eventually.rejectedWith(Error);
    });
  });

  describe('getPages', () => {
    it('check function exists', () => {
      expect(lib.getPages).to.be.an('function', 'not a function');
    });

    it('gets pages and tokens for user', async () => {
      const results = await lib.getPages({
        access_token: 'IgDhFVbzkXsoFx0tomtP6hC119Ks6o36xLoFX0wTvkLvfzlJfKROLlRZDohe6DEE9Mtjtl3X1KKW5n3rsUeWUGCKY4pTMzLQ7UsgLmNBkLuV5bAYugF2A9uwotC5aW14YYqCyG17aG1nZFyAMzURSduzsPfoowjHMTdBnqAA9cutGrsPszDq4wMmMCjRagpIFK1mPUXUe',
      });
      expect(results).to.be.an('object', 'not an object');
      expect(results).to.have.keys(['data', 'paging']);
      expect(results.data).to.be.an('array', 'results.data not an array');
      expect(results.data).to.have.lengthOf(1, 'array not expected length');
      expect(results.data[0]).to.have.keys(['access_token', 'category', 'category_list', 'name', 'id', 'tasks']);
      expect(results.data[0]).property('access_token', 'TokenHere', 'access_token not expected value');
    });

    it('returns error if status of request not 200', () => {
      expect(lib.getPages({
        access_token: 'cTr4OWJqlDbC7dYMuCns0hiPfZB3ayu24UNgeclYMNoDiJSMyDfrybOcbxI1iPYrwhVS2qnSgdH65EtECoFc75d7d2Q5O5acVQr10TRaHYqZQD5s5mW7HSLU9Wn4LFfMca2TvYM3tzhf1HwunJmoY5BBzRsVm1AgthkfJtclIKEwIb5xEUvkOg0GfqtT2epnmoW5GEXhP',
      })).to.be.eventually.rejectedWith(Error);
    });

    it('returns error if status of request is 200 but no data element', () => {
      expect(lib.getPages({
        access_token: 'LTE7Ojiz9k3kfKjnQ7RodtVgQANtDu3HXfY8wWQqYAKrvO7i3XCKRBM8y1l7AcyXv3VW52yTgr9y0qdh8KV6xSCgaBJzEGVbFyHYrYpeV1TO0TKooVHcz58ImKa5KgCdU6bZdoZea95bGtbSNYfMm0XYBQncDf47QJFYREQ4yOtrYdsb86ndKSWrm7oE3cpeCVKQnVf0R',
      })).to.be.eventually.rejectedWith(Error);
    });
  });

  describe('getTokenInfo', () => {
    it('check function exists', () => {
      expect(lib.getTokenInfo).to.be.an('function', 'not a function');
    });

    it('gets tokeninfo', async () => {
      const results = await lib.getTokenInfo({
        access_token: 'uAb3MXKioQdquB2eKyVmz5F0kT8G4IZfVUIB5gSi9vImP10rQnLOOg3jiXCNg6yxLEOn8qFF5H5bjHxiKOUzcrTyVfHMmJ3WZDI0aGx9bNNwuKmHqlir4KArRLMQjp3lYUDKpAkq91dUKHOxSs3kCTYcOoLusgwPdHhfvF4Ly2ZNIuEMu5Yncg8VnnynXgxjmKQlhnsrg',
        input_token: 'EgCfit02uTr37D4Qpjbd5YDTJlh0pBx64FMSUWdeQwxz5trobQuuqaxYeLf43stKfo8gaB0p0QZOjFYvtrnYctX1oONNy5vngLJ7xEnhqfBetwRU7wf9LsOmvMFgT7if4IxIdQ7X0zvMCXOJbKfsHT4wgKGshtUUPfm4o0myoUgWFM1rr82avMEroD2F37ijOhMzxBL8b',
      });
      expect(results).to.be.an('object', 'not an object');
      expect(results).to.have.keys(['data']);
      expect(results.data).to.have.keys(['app_id', 'type', 'application', 'data_access_expires_at', 'expires_at', 'is_valid', 'scopes', 'user_id']);
    });

    it('returns error if status of request not 200', () => {
      expect(lib.getTokenInfo({
        access_token: 'LTE7Ojiz9k3kfKjnQ7RodtVgQANtDu3HXfY8wWQqYAKrvO7i3XCKRBM8y1l7AcyXv3VW52yTgr9y0qdh8KV6xSCgaBJzEGVbFyHYrYpeV1TO0TKooVHcz58ImKa5KgCdU6bZdoZea95bGtbSNYfMm0XYBQncDf47QJFYREQ4yOtrYdsb86ndKSWrm7oE3cpeCVKQnVf0R',
        input_token: 'sdaQZqklie7yifecMxhTFMVMtdLQkGUK9NnYsuy16GyJv4oDkpD64BvEwngVuIaD4Lp200yRrJIET0PWbi5OmAkpUaQyTknWJm3D8NoB0eYq1q8fLRVDHrFUF9WWwvUjJvZ3K3A2dBphOVOtVVirA2Ef5SjLrJxk9FNQlZXR0g86g8fUDqGKlUnpuOJUn79l7WEMJjp8a',
      })).to.be.eventually.rejectedWith(Error);
    });

    it('returns error if status of request is 200 but no data element', () => {
      expect(lib.getTokenInfo({
        access_token: 'WZPbWgAYYo060q0dB20FhPdq54DnSgVn9dXOmDl30RVuP0HxzqmjH6TctnL8ypoRrlHMgKlJu5IJviAps4x7SmTOjh4Fb9M3jC9HG8SV6nh5l50XEYnewjS9x9b8pYFW2uo3WrglrBkgcDLW43dfC6scgAJoeZYS8qkUeO31lMC4BZg6ZslS9Tp3bcCgj5dqE7XpLsRZq',
        input_token: 'frNzDHP3dGBsZxenKfimoTEtIDNivBX6AHI24MfALPWkOFllUBAC7nhRETCvGvAmIYqwP1AOH9rrpYtlNnu2SjQ2XaKilAYPdWghIJFc7iMZX24bKBIYhx4IBQl6Kr0G1nRzNX0gbpHht0uu6VcxtvSaWhK0fcIzzqeaBtI1XBfFbRhgAFvpqaIwkuHsZxk0ezlCWSwfMF',
      })).to.be.eventually.rejectedWith(Error);
    });
  });

  describe('getUserAccessToken', () => {
    it('check function exists', () => {
      expect(lib.getUserAccessToken).to.be.an('function', 'not a function');
    });

    it('gets long lived user access token', async () => {
      const results = await lib.getUserAccessToken({
        grant_type: 'fb_exchange_token',
        client_id: '183740247262197',
        client_secret: '501614648436422',
        fb_exchange_token: 'zEakht3c7c2lifPcjOTAoxB10RFRCteR4Vcz9INWBLezOZYReMww9cRE3mBTHSdzqWnzqRUQzOOXYgQfxBONkxziL6bSSKvnIYMBQnqs7vR4HC3spfQOzcVUCbreVgOlB0uYbEiPxPxwEtxuoJ1YnS7VEda0Kr1Hp0jjh1cCz2ZhhRDUkLjgULPBTp0bIFzRqTBJfUSmY',
      });
      expect(results).to.be.an('object', 'not an object');
      expect(results).to.have.keys(['access_token', 'token_type', 'expires_in']);
    });

    it('returns error if status of request not 200', () => {
      expect(lib.getUserAccessToken({
        grant_type: 'fb_exchange_token',
        client_id: '183740247262197',
        client_secret: '501614648436422',
        fb_exchange_token: 'WE55N1gHAUmf9PPFFEf1cEDfLL1vlZFaYugZKx9oXPmG9JK3TeGwVwgaGGftE41fWKg3K7oBRPBcvjyxKCMEVJePBYojQHpXb9gntWdkHujzR0TuHrL3iUDC61zVEr3iLJZfCpcXVgnYBHk45FcV3dQHUwDEA8ZUIpbf3SBkhvYIxtI4GgKMHhvvXKHDgFVjbd2SCbTig',
      })).to.eventually.be.rejectedWith(Error);
    });

    it('returns error if status of request is 200 but no access_token element', () => {
      expect(lib.getUserAccessToken({
        grant_type: 'fb_exchange_token',
        client_id: '183740247262197',
        client_secret: '501614648436422',
        fb_exchange_token: 'ekvfsv9caItJedSu3icNWdwExPzxwqCxLIUG9NKBlNK7yU4bB82V9HcD5T308oUK7dYdKtyUpVTYfV9AXM8OH97iAtTUVlmvBLJNAzfEese5yFpNpEPvmN13c6gYyFb5zfbGgy8g3ybk7Njzx1lY9js0ldGFSEevQ9qq265Q9tpJR0bmgYzvzfZzVC5E0D8uZ3s0wWtgz',
      })).to.eventually.be.rejectedWith(Error);
    });
  });

  describe('getPageAccessToken', () => {
    it('check function exists', () => {
      expect(lib.getPageAccessToken).to.be.an('function', 'not a function');
    });

    it('gets long lived page access token', async () => {
      const results = await lib.getPageAccessToken(913640925564999, {
        access_token: 'ekvfsv9caItJedSu3icNWdwExPzxwqCxLIUG9NKBlNK7yU4bB82V9HcD5T308oUK7dYdKtyUpVTYfV9AXM8OH97iAtTUVlmvBLJNAzfEese5yFpNpEPvmN13c6gYyFb5zfbGgy8g3ybk7Njzx1lY9js0ldGFSEevQ9qq265Q9tpJR0bmgYzvzfZzVC5E0D8uZ3s0wWtgz',
        fields: 'access_token',
      });
      expect(results).to.be.an('object', 'not an object');
      expect(results).to.have.keys(['access_token', 'id']);
    });

    it('returns error if status of request not 200', () => {
      expect(lib.getPageAccessToken(544993869929785, {
        access_token: 'Jjp6dDuAbm2HcaaMb4RvaUvEhYlw4kkcOlRYpf7gYIwV2D95kCrEJnGPTOc4n3G6vjZnT0yfFT0CXaPZPrVEixDWilo2akZFxPOacorcNeyn0tUJf8p65BjD5L6HGoG7dspvdwDoap3pqmFIXKsJMTFzdfVSe5rvPf8gTvvhN2ijwoTo9np9jt7WmYOFlWYw3KmQBVjxa',
        fields: 'access_token',
      })).to.eventually.be.rejectedWith(Error);
    });

    it('returns error if status of request is 200 but no access_token element', () => {
      expect(lib.getPageAccessToken(663231235548314, {
        access_token: 'U3s0j4JfGrsanIRSlvheWT4lduqM3lZSKTPqmgOZBPDZsaBOtxvDbTheELMvjagEfTujCJ7Tqp0RY6RRaOQP2KxZA6FeVQZfhlCrpY34mEPQU5dbHwxqJf5oviEJ8sTDomdBPNk2UroMXx7GNq9d2FV13LZbRK8dXpyD3EWRzeZLIyeRgk4ovBfcWjlcF234VBRp18R6W',
        fields: 'access_token',
      })).to.eventually.be.rejectedWith(Error);
    });
  });

  describe('getPageLikes', () => {
    it('should return null if input is url but do not have path name', async () => {
      const name = 'https://www.facebook.com/';

      nock('https://www.facebook.com/')
        .get('/pg//community/')
        .reply(404);

      nock('https://www.facebook.com/')
        .get('//')
        .reply(404);

      const res = await lib.getPageLikes(name);
      expect(res).to.be.null;
    });

    it('should throw no found error if input is full url (with id in pathname)', async () => {
      const url = 'www.facebook.com/provesource';
      const name = 'provesource';

      nock('https://www.facebook.com/')
        .get(`/pg/${name}/community/`)
        .reply(404);

      nock('https://www.facebook.com/')
        .get(`/${name}/`)
        .reply(404);

      const res = await lib.getPageLikes(url);
      expect(res.status).to.equal(404);
    });

    it('should throw no found error if input is id string', async () => {
      const name = 'provesource';

      nock('https://www.facebook.com/')
        .get(`/pg/${name}/community/`)
        .reply(404);

      nock('https://www.facebook.com/')
        .get(`/${name}/`)
        .reply(404);

      const res = await lib.getPageLikes(name);
      expect(res.status).to.equal(404);
    });
  });
});
