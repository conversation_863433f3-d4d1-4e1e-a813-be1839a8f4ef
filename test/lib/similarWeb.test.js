const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();

const constants = require('../constants');
const similarweb = require('../../lib/apis/similarWeb');

describe('similarWeb - lib (#slow)', () => {
  afterEach(() => {
    sandbox.restore();
  });

  it('should get stats for domain', async () => {
    const stats = await similarweb.getStats('provesrc.com');
    expect(stats).to.have.keys([
      'SiteName', 'IsSmall', 'Category', 'Title', 'Description', 'Engagments',
    ]);
  }).timeout(5000);

  it('should return null if not found', async () => {
    const stats = await similarweb.getStats('asdaikldja12.com');
    expect(stats).to.be.null;
  }).timeout(5000);
});
