const chai = require('chai');
const nock = require('nock');
const shopperApproved = require('../../lib/apis/shopperApproved');

const { expect } = chai;

describe('shopperApproved api lib', () => {
  describe('getReviews', () => {
    it('should return successfully reviews data', async () => {
      const siteId = '123321';
      const token = 'YOGZkWbZyyWuufV1nponX88SQg4';
      const response = {
        123: {
          review_id: 123,
          order_id: 'ord1',
          display_name: 'the name',
          email_address: '<EMAIL>',
          location: 'CA, United States',
          overall: 5,
          comments: 'Great some thing',
          review_date: Date.now(),
        },
        456: {
          review_id: 456,
          order_id: 'ord2',
          display_name: 'Name',
          email_address: '<EMAIL>',
          location: 'North pole',
          overall: null,
          initial_overall: 5,
          comments: null,
          initial_comments: 'first comment',
          review_date: Date.now(),
        },
        total_count: 2,
      };
      const reviews = [
        {
          reviewId: response[123].review_id,
          orderId: response[123].order_id,
          authorName: response[123].display_name,
          authorEmail: response[123].email_address,
          authorLocation: response[123].location,
          rating: response[123].overall,
          text: response[123].comments,
          time: new Date(response[123].review_date),
        },
        {
          reviewId: response[456].review_id,
          orderId: response[456].order_id,
          authorName: response[456].display_name,
          authorEmail: response[456].email_address,
          authorLocation: response[456].location,
          rating: response[456].initial_overall,
          text: response[456].initial_comments,
          time: new Date(response[456].review_date),
        },
      ];

      nock('https://api.shopperapproved.com')
        .get(`/reviews/${siteId}?token=${token}&rating=4,5&sort=newest&limit=100&test=false&xml=false`)
        .reply(200, response);

      const res = await shopperApproved.getReviews(siteId, token, {});

      expect(res).to.exist;
      expect(res).to.be.eql(reviews);
    });
  });
});
