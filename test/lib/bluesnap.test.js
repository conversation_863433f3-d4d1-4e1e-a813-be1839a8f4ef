const { expect } = require('chai');
const config = require('../../config');
const bluesnap = require('../../lib/apis/bluesnap').getClient(config.bluesnap.apiKey, config.bluesnap.password, config.env);
const utils = require('../testUtils');
const creditCardUtils = require('../../lib/utils/creditCardUtils');

xdescribe('Lib - BlueSnap/API (#slow)', () => {
  before(async () => {
  });

  afterEach(async () => {
  });

  it('should get subscription details', async () => {
    const subId = '13518065';
    const subscription = await bluesnap.getSubscription(subId);
    expect(subscription).to.exist;
  });

  it('should update subscription', async () => {
    const subId = '13518065';
    const contract = '2396485';
    const res = await bluesnap.switchContract(subId, contract);
    expect(res.status).to.be.equal(204);
  });

  it('should fail to update subscription', async () => {
    const subId = '13518065';
    const contract = '11111111';
    await bluesnap.switchContract(subId, contract).then(() => {
      expect.fail();
    }).catch((err) => {
      expect(err).to.exist;
    });
  });

  it('should add card to shopper and update subscription', async () => {
    const shopperId = '23791275';
    const subId = '13518065';
    const data = {
      firstName: 'John',
      lastName: 'Doe',
      ip: '**************',
      countryCode: 'US',
      cardNumber: '5425 2334 3010 99 03',
      cvv: '111',
      expMonth: '04',
      expYear: '2023',
    };
    let res = await bluesnap.addCard(shopperId, data);
    expect(res).to.have.status(204);

    const cardType = creditCardUtils.getVendor(data.cardNumber);
    const fourDigits = creditCardUtils.getFourDigits(data.cardNumber);

    res = await bluesnap.changeCard(subId, fourDigits, cardType);
    expect(res).to.have.status(204);
  }).timeout(10000);

  it('should fail adding credit card (bad card)', async () => {
    const shopperId = '23791275';
    const data = {
      firstName: 'John',
      lastName: 'Doe',
      ip: '**************',
      countryCode: 'US',
      cardNumber: '1111 2334 3010 9113',
      cvv: '111',
      expMonth: '04',
      expYear: '2023',
    };
    await bluesnap.addCard(shopperId, data).then(() => {
      expect.fail(true, null, 'add card should fail');
    }).catch((err) => {
      console.error('description:', err.data.messages.message[1].description);
      console.error(JSON.stringify(err, null, 2));
    });
  });

  it('should fail to add card when exp. date in the past', async () => {
    const shopperId = '23791275';
    const data = {
      firstName: 'John',
      lastName: 'Doe',
      ip: '**************',
      countryCode: 'US',
      cardNumber: '5425 2334 3010 9903',
      cvv: '111',
      expMonth: '12',
      expYear: '2004',
    };
    await bluesnap.addCard(shopperId, data).then(() => {
      expect.fail(true, null, 'add card should fail');
    }).catch((err) => {
      // console.error('description:', err.data.messages.message[1].description);
      console.error(JSON.stringify(err, null, 2));
    });
  });
});
