const chai = require('chai');

const { expect } = chai;
const countryCodes = require('../../lib/geo/countryCodes');
const stateCodes = require('../../lib/geo/stateCodes');

describe('names to codes', () => {
  it('should return country code from country name', async () => {
    const us = countryCodes.toCode('UniTed StatEs');
    expect(us).to.be.equal('US');
  });

  it('should return country name from country code', async () => {
    const us = countryCodes.fromCode('uS');
    expect(us).to.be.equal('United States');
  });

  it('should return state name from code', async () => {
    const az = stateCodes.toCode('AriZona');
    expect(az).to.be.equal('AZ');
  });

  it('should return state code from name', async () => {
    const gu = stateCodes.fromCode('gU');
    expect(gu).to.be.equal('Guam');
  });
});
