const chai = require('chai');

const { expect } = chai;
const constants = require('../constants');
const maxmind = require('../../lib/maxmind');

describe('Lib - maxmind', () => {
  it('should return slim result', async function () {
	  this.timeout(300);
    const result = await maxmind.geoIP('*************');
    expect(result).to.deep.include({ city: 'Haifa', country: 'Israel', countryCode: 'IL' });
  });

  it('should contain state for US IPs', async () => {
    const usIP = '**************';
    const result = await maxmind.geoIP(usIP);
    expect(result.state).to.exist;
  });

  it('should return null when no ip', async () => {
    const result = await maxmind.geoIP(null);
    expect(result).to.be.null;
  });
});
