const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();

const urlModule = require('url');
const constants = require('../constants');
const urlUtils = require('../../lib/utils/urlUtils');

describe('Lib - URLUtils', () => {
  afterEach(() => {
    sandbox.restore();
  });

  it('should clean the protocol, www, query and anchor', async () => {
    const url = 'https://www.provesrc.com/about?ref=producthunt#account';
    const clean = urlUtils.clean(url);
    expect(clean).to.be.equal('provesrc.com/about');
  });

  it('should support angular anchors', async () => {
    const url = 'https://console.provesrc.com/#/settings#account-settings';
    const clean = urlUtils.clean(url);
    expect(clean).to.be.equal('console.provesrc.com/#/settings');
  });

  it('should not remove non www subdomains', async () => {
    const url = 'https://console.provesrc.com';
    const clean = urlUtils.clean(url);
    expect(clean).to.be.equal('console.provesrc.com');
  });

  it('should encode URLs', async () => {
    const url = 'https://קלאבמד.co.il/קורסים';
    const clean = urlUtils.clean(url);
    const encoded = 'xn--4dbcg7am4c.co.il/%d7%a7%d7%95%d7%a8%d7%a1%d7%99%d7%9d';
    expect(clean).to.be.equal(encoded);
  });

  it('should remove utm params as well', async () => {
    const url = 'https://www.provesrc.com/about?utm_source=facebook';
    const clean = urlUtils.clean(url);
    expect(clean).to.be.equal('provesrc.com/about');
  });

  it('should remove www from incomplete URLs', async () => {
    const url = 'www.provesrc.com/about?utm_source=facebook';
    const clean = urlUtils.clean(url);
    expect(clean).to.be.equal('provesrc.com/about');
  });

  it('should remove https protocol from incomplete URLs', async () => {
    let url = 'https://provesrc.com/about?utm_source=facebook';
    let clean = urlUtils.clean(url);
    expect(clean).to.be.equal('provesrc.com/about');

    url = 'http://provesrc.com/about?utm_source=facebook';
    clean = urlUtils.clean(url);
    expect(clean).to.be.equal('provesrc.com/about');
  });

  it('should remove protocol and www from incomplete URLs', async () => {
    let url = 'http://www.provesrc.com/about?utm_source=facebook';
    let clean = urlUtils.clean(url);
    expect(clean).to.be.equal('provesrc.com/about');

    url = 'https://www.provesrc.com/about?utm_source=facebook';
    clean = urlUtils.clean(url);
    expect(clean).to.be.equal('provesrc.com/about');
  });

  it('should return the domain', async () => {
    let url = 'http://provesrc.com/about?utm_source=facebook';
    let domain = 'provesrc.com';
    expect(urlUtils.getDomain(url)).to.be.equal(domain);

    url = 'http://www.walla.com';
    domain = 'www.walla.com';
    expect(urlUtils.getDomain(url)).to.be.equal(domain);
  });

  describe('getPathName', () => {
    it('should return  null if input url is invalid ', async () => {
      expect(urlUtils.getPathName('//blah.blah.blah/blah')).to.be.null;
      expect(urlUtils.getPathName(' ')).to.be.null;
      expect(urlUtils.getPathName('nike')).to.be.null;
    });

    it('should return  path name of entered url ', async () => {
      const url = 'http://www.facebook.com/blah';

      expect(urlUtils.getPathName(url)).to.be.eql('/blah');
    });
  });
});
