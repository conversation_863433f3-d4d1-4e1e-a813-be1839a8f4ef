const chai = require('chai');
const strUtils = require('../../lib/utils/stringUtils');

const { expect } = chai;

describe('string utility test', () => {
  describe('get name from path', () => {
    it('should return null if invalid path input', async () => {
      expect(strUtils.getPathComponent('', 1)).to.be.null;
      expect(strUtils.getPathComponent('gedrgerger', 1)).to.be.null;
    });

    it('should return null if invalid order input', async () => {
      expect(strUtils.getPathComponent('some', 1)).to.be.null;
      expect(strUtils.getPathComponent('//some', 1)).to.be.null;
    });

    it('should return name from correct place in path', async () => {
	    expect(strUtils.getPathComponent('/some1/some2', 2)).to.be.eql('some2');
    });
  });
});
