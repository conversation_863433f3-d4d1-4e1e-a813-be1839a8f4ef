const chai = require('chai');
const request = require('superagent');
const chaiAsPromised = require('chai-as-promised');
const lib = require('../../lib/apis/stamped');

chai.use(chaiAsPromised);

const { expect } = chai;

describe('stamped - lib', () => {
  describe('getReviews', () => {
    it('should throw an error on invalid store url', () => {
      expect(lib.getReviews()).to.be.eventually.rejectedWith('store url was provided');
    });

    it('should reject if invalid apiKey', async () => {
      const promise = lib.getReviews('seguidores.com.br', 'asd');
      return expect(promise).to.be.eventually.rejectedWith('api key');
    });

    it('should reject if invalid storeUrl', async () => {
      const promise = lib.getReviews('seguidorvvves.com.br', 'ewf');
      return expect(promise).to.be.eventually.rejectedWith('store url');
    });

    it('should return reviews for provided store url and api key', async () => {
      const res = await lib.getReviews('seguidores.com.br', 'pubkey-0hNp68x545x7D87hU31FtpV760P604');
      expect(res.data).to.exist;
      expect(res.data).to.be.an('array');
    });

    it('should return reviews shopify store url only', async () => {
      const res = await lib.getReviews('myblankii.myshopify.com');
      expect(res.data).to.exist;
      expect(res.data).to.be.an('array');
    });

    it('should return an empty array for provided store url when no reviews', async () => {
      const res = await lib.getReviews('run2cure.myshopify.com', 'pubkey-22pU19e35y080P1WbVJuYQb3A742Vy');
      expect(res.data).to.exist;
      expect(res.data).to.be.an('array');
      expect(res.data).to.have.lengthOf(0);
    });
  });
});
