const chai = require('chai');
const nock = require('nock');
const instagram = require('../../lib/apis/instagram');

const { expect } = chai;

describe('instagram', () => {
  describe('getFollowers', () => {
    it('should return null if input is incorrect (url without path name)', async () => {
      const url = 'https://www.instagram.com';
      instagram.setAuth('test', 'test');

      nock('https://www.instagram.com/')
        .get('//')
        .reply(404);

      const res = await instagram.getFollowers(url);

      expect(res).to.be.null;
    });

    it('should return error (404 not found) if input is random string', async () => {
      const name = 'someRandomString';
      instagram.setAuth('test', 'test');

      nock('https://www.instagram.com/')
        .get('/')
        .reply(404);

      await expect(instagram.getFollowers(name)).to.eventually.be.rejectedWith('StatusCodeError: 404');
    });

    it('should return error if setAuth not called', async () => {
      const name = 'gal_gadot';
      instagram.setAuth(null, null);

      await expect(instagram.getFollowers(name)).to.eventually.be.rejectedWith('forgot to call setAuth with instagram user name and password');
    });
  });
});
