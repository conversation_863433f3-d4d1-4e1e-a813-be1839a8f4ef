const chai = require('chai');
const request = require('superagent');
const chaiAsPromised = require('chai-as-promised');
const lib = require('../../lib/apis/yotpo');

chai.use(chaiAsPromised);

const { expect } = chai;

describe('yotpo - lib', () => {
  describe('getReviews', () => {
    it('causes error as no options sent to method', () => {
      expect(lib.getReviews({})).to.be.eventually.rejected;
    });

    it('get not found response from yotpo for invalid app_key', async () => {
      const promise = lib.getReviews({ token: 'aabb', per_page: 15 });
      return expect(promise).to.be.eventually.rejectedWith('Not Found');
    });

    it('get empty response from yotpo for valid app_key', async () => {
      const res = await lib.getReviews({ token: 'ND7sS3YBOUoqJOJcPywQmpwo2F8Ke9HG2HD3V9Mx', per_page: 15 });
      expect(res).to.be.an('array', 'response should be an array');
      expect(res).to.have.lengthOf(0, 'reviews array should be zero length');
      expect(res).to.be.empty;
    });

    it('returns reviews for valid app_key', async () => {
      const results = await lib.getReviews({ token: 'FBtb3QzWyatQY0OYjMcwfZEFvJ4a6CVkIZLgHEf0', per_page: 15 });
      expect(results).to.be.an('array', 'response should be an array');
      expect(results).to.have.lengthOf(15, 'reviews array should be 15 length');
      expect(results).to.not.be.empty;
      expect(results[0]).to.have.property('yotpoAppId');
      expect(results[0]).to.have.property('reviewId');
      expect(results[0]).to.have.property('productId');
      expect(results[0]).to.have.property('profilePhotoUrl');
      expect(results[0]).to.have.property('authorName');
      expect(results[0]).to.have.property('rating');
      expect(results[0]).to.have.property('text');
      expect(results[0]).to.have.property('time');
    });

    it('returns reviews with 5 stars only', async () => {
      const results = await lib.getReviews({ token: 'B66LUOV862yJrPGLymFzYVvjmQlNw1uzkioS642f', per_page: 15 });
      expect(results).to.be.an('array', 'reviews should be array');
      expect(results).to.not.be.empty;
      for(let i = 0; i < results.length; i++) {
        expect(results[0]).to.have.property('yotpoAppId');
        expect(results[0]).to.have.property('reviewId');
        expect(results[0]).to.have.property('productId');
        expect(results[0]).to.have.property('profilePhotoUrl');
        expect(results[0]).to.have.property('authorName');
        expect(results[0]).to.have.property('rating').and.to.be.above(3);
        expect(results[0]).to.have.property('text');
        expect(results[0]).to.have.property('time');
      }
    });
  });
});
