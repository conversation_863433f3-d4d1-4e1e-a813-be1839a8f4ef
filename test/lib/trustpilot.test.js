const chai = require('chai');
const request = require('superagent');
const chaiAsPromised = require('chai-as-promised');
const lib = require('../../lib/apis/trustpilot');

chai.use(chaiAsPromised);

const { expect } = chai;

describe('trustpilot - lib', () => {
  describe('getReviews', () => {
    it('checks function exists', () => {
      expect(lib.getReviews).to.be.an('function', 'getReviews not function');
    });

    it('causes error as no options sent to method', () => {
      expect(lib.getReviews({})).to.be.eventually.rejected;
    });

    it('get response from trustpilot', async () => {
      const results = await lib.getReviews({ domain: 'provesrc.com' });
      expect(results).to.exist;
    });


    it('returns reviews for valid domain name', async () => {
      const results = await lib.getReviews({ domain: 'provesrc.com' });
      expect(results).to.be.an('array', 'reviews should be array');
      expect(results).to.not.be.empty;
      expect(results[0]).to.have.property('authorName');
      expect(results[0]).to.have.property('profilePhotoUrl');
      expect(results[0]).to.have.property('rating');
      expect(results[0]).to.have.property('reviewId');
      expect(results[0]).to.have.property('text');
      expect(results[0]).to.have.property('time');
      expect(results[0]).to.have.property('businessName');
      expect(results[0]).to.have.property('domain');
    });

    it('returns reviews with 5 stars only', async () => {
      const results = await lib.getReviews({ domain: 'mydeal.com.au', lowestRating: 4 });
      expect(results).to.be.an('array', 'reviews should be array');
      expect(results).to.not.be.empty;
      for(let i = 0; i < results.length; i++) {
        expect(results[i]).to.have.property('authorName');
        expect(results[i]).to.have.property('profilePhotoUrl');
        expect(results[i]).to.have.property('rating').and.to.be.above(3);
        expect(results[i]).to.have.property('reviewId');
        expect(results[i]).to.have.property('text');
        expect(results[i]).to.have.property('time');
        expect(results[i]).to.have.property('domain');
      }
    });
  });
});
