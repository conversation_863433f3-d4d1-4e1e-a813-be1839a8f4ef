const chai = require('chai');
const request = require('superagent');
const config = require('../mocking/lib/googleReviews');
const chaiAsPromised = require('chai-as-promised');
const superagentMock = require('superagent-mock')(request, config);
const lib = require('../../lib/apis/googleReviews');

chai.use(chaiAsPromised);

const { expect } = chai;

describe('index', () => {
  after(() => {
    superagentMock.unset();
  });

  describe('getApiBaseUri', () => {
    it('checks function exists', () => {
      expect(lib.getApiBaseUri).to.be.an('function', 'getApiBaseUri not function');
    });

    it('checks function returns expected type', () => {
      expect(lib.getApiBaseUri()).to.be.an('string', 'getApiBaseUri does not return string');
    });
  });

  describe('getReviews', () => {
    it('checks function exists', () => {
      expect(lib.getReviews).to.be.an('function', 'getReviews not function');
    });

    it('causes error as no options sent to method', () => {
      expect(lib.getReviews({})).to.be.eventually.rejected;
    });

    it('causes error as key provided in options is invalid', () => {
      expect(lib.getReviews({ key: '1234' })).to.be.eventually.rejected;
    });

    it('causes error as correct key provided but no placeid', () => {
      expect(lib.getReviews({ key: '5678' })).to.be.eventually.rejected;
    });

    it('causes error as status is UNKNOWN_ERROR', () => {
      expect(lib.getReviews({ key: 5678, placeid: 'A' })).to.be.eventually.rejected;
    });

    it('causes error as status is OVER_QUERY_LIMIT', () => {
      expect(lib.getReviews({ key: 5678, placeid: 'C' })).to.be.eventually.rejected;
    });

    it('causes error as status is REQUEST_DENIED', () => {
      expect(lib.getReviews({ key: 5678, placeid: 'D' })).to.be.eventually.rejected;
    });

    it('causes error as status is INVALID_REQUEST', () => {
      expect(lib.getReviews({ key: 5678, placeid: 'E' })).to.be.eventually.rejected;
    });

    it('causes error as status is NOT_FOUND', () => {
      expect(lib.getReviews({ key: 5678, placeid: 'F' })).to.be.eventually.rejected;
    });

    it('causes error as result nor reviews property included in response', () => {
      expect(lib.getReviews({ key: 5678, placeid: 'G' })).to.be.eventually.rejected;
    });

    it('causes error as result but not reviews property included in response', () => {
      expect(lib.getReviews({ key: 5678, placeid: 'H' })).to.be.eventually.rejected;
    });

    it('returns reviews for valid placeid', async () => {
      const results = await lib.getReviews({ key: '5678', placeid: 'ABCD' });
      expect(results).to.be.an('object', 'reviews should be object');
      expect(results).property('name', 'Airport', 'does not have expected name property');
      expect(results.reviews).to.be.an('array', 'reviews should be array');
      expect(results.reviews).to.have.lengthOf(5, 'reviews array should have length of 5');
      expect(results.reviews[0]).to.have.property('author_name');
      expect(results.reviews[0]).to.have.property('author_url');
      expect(results.reviews[0]).to.have.property('language');
      expect(results.reviews[0]).to.have.property('profile_photo_url');
      expect(results.reviews[0]).to.have.property('rating');
      expect(results.reviews[0]).to.have.property('relative_time_description');
      expect(results.reviews[0]).to.have.property('text');
      expect(results.reviews[0]).to.have.property('time');
    });

    it('returns no review results', async () => {
      const results = await lib.getReviews({ key: '5678', placeid: 'B' });
      expect(results).to.be.an('object');
      expect(results.reviews).to.be.an('array', 'reviews should be array');
      expect(results.reviews).to.have.lengthOf(0, 'reviews array should be zero length');
    });
  });
});
