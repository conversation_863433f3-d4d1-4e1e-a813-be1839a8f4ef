const chai = require('chai');
const request = require('superagent');
const chaiAsPromised = require('chai-as-promised');
const lib = require('../../lib/apis/reviewsio');

chai.use(chaiAsPromised);

const { expect } = chai;

describe('review.io - lib', () => {
  describe('getReviews', () => {
    it('causes error as no options sent to method', () => {
      expect(lib.getReviews({})).to.be.eventually.rejected;
    });

    it('get response from reviews.io', async () => {
      const results = await lib.getReviews({
        store: 'virtalent', min_rating: 4, order: 'desc', per_page: 10,
      });
      expect(results).to.exist;
    });

    it('returns 0 reviews for valid store name', async () => {
      const results = await lib.getReviews({
        store: 'Viyella', min_rating: 4, order: 'desc', per_page: 10,
      });
      expect(results).to.be.an('array', 'response should be an array');
      expect(results).to.have.lengthOf(0, 'reviews array should be zero length');
      expect(results).to.be.empty;
    });

    it('should be rejected with unauthorized when store does not exist', async () => {
      const promise = lib.getReviews({
        store: '34g34g34g', min_rating: 4, order: 'desc', per_page: 10,
      });
      return expect(promise).to.be.eventually.rejectedWith('Unauthorized');
    });

    it('returns reviews for valid store name', async () => {
      const results = await lib.getReviews({
        store: 'virtalent', min_rating: 4, order: 'desc', per_page: 10,
      });
      expect(results).to.be.an('array', 'response should be an object');
      expect(results).to.have.lengthOf(10, 'reviews array should be 10 length');
      expect(results).to.not.be.empty;
      expect(results[0]).to.have.property('authorName');
      expect(results[0]).to.have.property('initials');
      expect(results[0]).to.have.property('rating');
      expect(results[0]).to.have.property('reviewId');
      expect(results[0]).to.have.property('text');
      expect(results[0]).to.have.property('time');
      expect(results[0]).to.have.property('storeName');
      expect(results[0]).to.have.property('storeId');
    });

    it('returns reviews with 5 stars only', async () => {
      const results = await lib.getReviews({
        store: 'virtalent', min_rating: 4, order: 'desc', per_page: 10,
      });
      expect(results).to.be.an('array', 'reviews should be array');
      expect(results).to.not.be.empty;
      for(let i = 0; i < results.length; i++) {
        expect(results[i]).to.have.property('authorName');
        expect(results[i]).to.have.property('rating').and.to.be.above(3);
        expect(results[i]).to.have.property('authorName');
        expect(results[i]).to.have.property('initials');
        expect(results[i]).to.have.property('reviewId');
        expect(results[i]).to.have.property('text');
        expect(results[i]).to.have.property('time');
        expect(results[i]).to.have.property('storeName');
        expect(results[i]).to.have.property('storeId');
      }
    });
  });
});
