const chai = require('chai');
const nock = require('nock');
const feefo = require('../../lib/apis/feefo');

const { expect } = chai;

describe('feefo api lib', () => {
  describe('getReviews', () => {
    it('should return successfully reviews data', async () => {
      const placeId = '123321';
      const response = {
        reviews: [
          {
            service: {
              rating: { rating: 5 },
              id: 'skjdhfgjsknvk',
              title: 'great shopping service',
              created_at: Date.now(),
            },
            customer: {
              display_name: 'test name',
              display_location: 'moscow, russia',
            },
          },
          {
            products: [{
              rating: { rating: 4 },
              id: 'hdfglsiudfhl',
              review: 'Good prod and staff',
              created_at: Date.now(),
            }],
          },
          {
            service: {
              rating: { rating: 3 },
              review: 'rew',
              id: 'jhdfglkjshdflgk',
              created_at: Date.now(),
            },
          },
        ],
      };
      const reviews = [
        {
          reviewId: 'skjdhfgjsknvk',
          authorName: 'test name',
          authorLocation: 'moscow, russia',
          rating: 5,
          text: 'great shopping service',
          time: new Date(response.reviews[0].service.created_at),
        },
        {
          reviewId: 'hdfglsiudfhl',
          authorName: null,
          authorLocation: null,
          rating: 4,
          text: 'Good prod and staff',
          time: new Date(response.reviews[1].products[0].created_at),
        },
      ];

      nock('https://api.feefo.com')
        .get(`/api/10/reviews/all?merchant_identifier=${placeId}&page_size=100&page=1&sort=-created_date`)
        .reply(200, response);

      const res = await feefo.getReviews(placeId);

      expect(res).to.exist;
      expect(res).to.be.eql(reviews);
    });
  });
});
