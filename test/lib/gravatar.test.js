const chai = require('chai');

const { expect } = chai;
const constants = require('../constants');
const gravatar = require('../../lib/apis/gravatar');
const crypto = require('../../lib/utils/cryptoUtils');

describe('Lib - Gravatar', () => {
  it('should get gravatar profile', async () => {
    const email = '<EMAIL>';
    const profile = await gravatar.getProfile(email);

    const hash = crypto.md5(email);
    expect(profile).to.exist;
    expect(profile.hash).to.equal(hash);
    expect(profile.displayName).to.have.length.above(0);
  });
});
