const chai = require('chai');
const nock = require('nock');
const judgeme = require('../../lib/apis/judgeme');

const { expect } = chai;

describe('judgeme api lib', () => {
  describe('getReviews', () => {
    it('should return successfully reviews raw data', async () => {
      const page = 1;
      const placeId = 'provesource-test.myshopify.com';
      const token = 'YOGZkWbZyyWuufV1nponX88SQg4';
      const revs = [{ id: 1 }, { id: 2 }];
      const response = {
        reviews: revs,
      };

      nock('http://judge.me')
        .get(`/api/v1/reviews?shop_domain=${placeId}&api_token=${token}&per_page=10&page=${page}`)
        .reply(200, response);

      const res = await judgeme.getReviews(placeId, token, { page });

      expect(res).to.exist;
      expect(res).to.be.eql(revs);
    });
  });
});
