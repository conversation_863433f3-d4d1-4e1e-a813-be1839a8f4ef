const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const config = require('../../config');
const constants = require('../constants');

const cryptoUtils = require('../../lib/utils/cryptoUtils');
const profitWellApi = require('../../lib/apis/profitwell');

xdescribe('ProfitWell API', () => {
  afterEach(() => {
    sandbox.restore();
  });
  const accountId = '1234'; // always use this not to spam the ProfitWell data

  it('should add customer', async () => {
    const pwApi = profitWellApi.getClient(config.profitWell.apiKey);
    try {
      const res = await pwApi.createSubscription({
        accountId,
        email: '<EMAIL>',
        subId: cryptoUtils.randomString(10),
        planName: 'Starter Monthly Plan',
        value: 1900,
        interval: profitWellApi.INTERVALS.month,
      });
      expect(res).to.have.status(201);
      console.log(res.body);
    } catch(err) {
      console.error(err && err.response && err.response.body);
      throw err;
    }
  });

  it('should churn subscription', async () => {
    try {
      const pwApi = profitWellApi.getClient(config.profitWell.apiKey);
      const subId = `testSub_${cryptoUtils.randomString(5)}`;
      let res = await pwApi.createSubscription({
        accountId,
        email: '<EMAIL>',
        subId,
        planName: 'Starter Monthly Plan',
        value: 1900,
        interval: profitWellApi.INTERVALS.month,
      });
      expect(res).to.have.status(201);

      res = await pwApi.churnSubscription(subId, Date.now() + 86400 * 1000 * 10, profitWellApi.CHURN_TYPES.delinquent);
      console.log(res.body);
      expect(res).to.have.status(200);
    } catch(err) {
      console.error(err.response.body);
      throw err;
    }
  });

  it('should update subscription', async () => {
    try {
      const pwApi = profitWellApi.getClient(config.profitWell.apiKey);
      const subId = `testSub_${cryptoUtils.randomString(6)}`;
      let res = await pwApi.createSubscription({
        accountId,
        email: '<EMAIL>',
        subId,
        planName: 'Starter Monthly Plan',
        value: 1900,
        interval: profitWellApi.INTERVALS.month,
      });
      expect(res).to.have.status(201);

      res = await pwApi.updateSubscription(subId, {
        planName: 'Growth Monthly Plan',
        interval: profitWellApi.INTERVALS.month,
        value: 4900,
        startDate: Date.now(),
      });
      console.log(res.body);
      expect(res).to.have.status(200);
    } catch(err) {
      console.error(err.response.body);
      throw err;
    }
  });
});
