const { expect } = require('chai');
const sinon = require('sinon');
const redis = require('../../lib/redisClient').getClient();

const sandbox = sinon.createSandbox();

const locker = require('../../lib/redisClient/Locker').getLocker();

describe('redis locker - Lib', () => {
  beforeEach(async () => {
    await redis.flushallAsync();
  });

  afterEach(async () => {
    sandbox.restore();
    await redis.flushallAsync();
  });

  it('should create a key with TTL and return true', async () => {
    const key = 'lock';
    const locked = await locker.lock(key, 10);
    expect(locked).to.be.true;

    const ttl = await redis.ttlAsync(key);
    expect(ttl).to.be.above(0).below(11);
  });

  it('should not lock if already exists', async () => {
    const key = 'lock';
    await redis.set(key, 1);
    const locked = await locker.lock(key, 10);
    expect(locked).to.be.false;
  });

  it('should remove lock on unlock', async () => {
    const key = 'lock';
    await locker.lock(key, 10);
    const unlocked = await locker.unlock(key);
    expect(unlocked).to.be.true;
  });
});
