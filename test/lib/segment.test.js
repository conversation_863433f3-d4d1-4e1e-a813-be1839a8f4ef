const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();

const nock = require('nock');

const segment = require('../../lib/apis/segment');

describe('Segment Lib', () => {
  afterEach(() => {
    sandbox.restore();
  });

  describe('track', () => {
    it('should fail if no authorization', async () => {
      nockTrack().reply(403);
      await expect(segment.track()).to.be.rejectedWith('Forbidden');
    });

    it('should succeeded with correctauth', async () => {
      const writeKey = '12345';
      nockTrack().basicAuth({ user: writeKey }).reply(200);
      const res = await segment.track(writeKey);
      expect(res).to.have.status(200);
    });

    it('should send POST body correctly', async () => {
      const writeKey = '12345';
      const data = {
        userId: '123',
        event: 'Notification View',
        properties: {
          notificationId: 'asodijadsajsdna',
        },
        context: {
          integration: {
            name: 'provesource',
            version: '1.0.0',
          },
        },
      };
      nockTrack(data).basicAuth({ user: writeKey }).reply(200);
      const res = await segment.track(writeKey, data);
      expect(res).to.have.status(200);
    });
  });

  function nockTrack(body) {
    return nock('https://api.segment.io').post('/v1/track', body);
  }
});
