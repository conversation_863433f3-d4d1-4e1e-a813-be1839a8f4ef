const chai = require('chai');
const nock = require('nock');
const youtube = require('../../lib/apis/youtube');

const { expect } = chai;

describe('youtube', () => {
  describe('getSubscribers', () => {
    it('should return null if input is incorrect (url without path name)', async () => {
      const url = 'https://www.youtube.com';

      nock('https://www.youtube.com/')
        .get('/channel/')
        .reply(404);

      nock('https://www.youtube.com/')
        .get('/subscribe_embed?channelid=')
        .reply(404);
      const res = await youtube.getSubscribers(url);

      expect(res).to.be.null;
    });

    it('should return error (404 not found) if input is string', async () => {
      const id = 'someRandomString';

      nock('https://www.youtube.com/')
        .get(`/channel/${id}`)
        .reply(404);

      nock('https://www.youtube.com/')
        .get(`/subscribe_embed?channelid=${id}`)
        .reply(404);
      const res = await youtube.getSubscribers(id);

      expect(res.status).to.be.eql(404);
    });
    it('should return error (404 not found) if input is url with id in path name', async () => {
      const id = 'someRandomString';
      const url = `https://www.youtube.com/channel/${id}`;

      nock('https://www.youtube.com/')
        .get(`/channel/${id}`)
        .reply(404);

      nock('https://www.youtube.com/')
        .get(`/subscribe_embed?channelid=${id}`)
        .reply(404);
      const res = await youtube.getSubscribers(url);

      expect(res.status).to.be.eql(404);
    });
  });
});
