const chai = require('chai');
const lib = require('../../lib/utils/objectArrayUtils');

const { expect } = chai;

describe('lib/utils/objectArrayUtils', () => {
  describe('addKeyValueToObjectArray', () => {
    it('checks function exists', () => {
      expect(lib.addKeyValueToObjectArray).to.be.an('function', 'addKeyValueToObjectArray not a function');
    });

    it('adds a key to each object in an array of objects', () => {
      const data = [
        { a: 'B' },
        { a: 'C' },
      ];
      const results = lib.addKeyValueToObjectArray(data, 'b', 'D');

      expect(results).to.be.an('array', 'not an array');
      expect(results).to.be.lengthOf(2, 'not expected length');
      expect(results[0]).to.have.keys(['a', 'b']);
      expect(results[1]).to.have.keys(['a', 'b']);
      expect(results[0]).property('a', 'B', 'property value not as expected');
      expect(results[0]).property('b', 'D', 'property value not as expected');
      expect(results[1]).property('a', 'C', 'property value not as expected');
      expect(results[1]).property('b', 'D', 'property value not as expected');
    });
  });
});
