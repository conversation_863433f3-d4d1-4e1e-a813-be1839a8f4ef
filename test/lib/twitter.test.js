const chai = require('chai');
const nock = require('nock');
const chaiAsPromised = require('chai-as-promised');
const twitter = require('../../lib/apis/twitter');

chai.use(chaiAsPromised);

const { expect } = chai;

describe('twitter', () => {
  describe('getFollowers', () => {
    it('should return null if input is incorrect(url without path name)', async () => {
      const name = 'https://www.twitter.com/';

      nock('https://twitter.com/')
        .get(`/${name}`)
        .reply(404);

      const res = await twitter.getFollowers(name);
      expect(res).to.be.null;
    });

    it('should throw error (not found 404) if input is full url (with profile id in path name)', async () => {
      const url = 'https://www.twitter.com/provesource/';
      const name = 'provesource';

      nock('https://twitter.com/')
        .get(`/${name}`)
        .reply(404);
      await expect(twitter.getFollowers(url)).to.eventually.be.rejectedWith('Not Found');
    });

    it('should throw error (not found 404) if input is page id', async () => {
      const name = 'provesource';

      nock('https://twitter.com/')
        .get(`/${name}`)
        .reply(404);

      await expect(twitter.getFollowers(name)).to.eventually.be.rejectedWith('Not Found');
    });
  });
});
