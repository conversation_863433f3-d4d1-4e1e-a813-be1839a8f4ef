const chai = require('chai');
const sinon = require('sinon');

const { expect } = chai;
const lodashUtils = require('../../lib/utils/lodashUtils');

describe('lodashUtils', () => {
  afterEach(async () => {
    sinon.restore();
  });

  describe('get case insensitive', async () => {
    it('should support array accessors', async () => {
      const data = {
        array: [
          { name: '<PERSON><PERSON>' },
        ],
      };
      const key = 'array[0].name';
      const val = lodashUtils.geti(data, key);
      expect(val).to.be.equal(data.array[0].name);
    });
  });
});
