const chai = require('chai');
const sinon = require('sinon');

const { expect } = chai;
const objectUtils = require('../../lib/utils/objectUtils');

describe('objectUtils', () => {
  afterEach(async () => {
    sinon.restore();
  });

  describe('find', async () => {
    it('should return value for key if object', async () => {
      const firstName = 'Natan';
      const values = objectUtils.find({ firstName }, 'firstName');
      expect(values[0]).to.be.equal(firstName);
    });

    it('should return value for nested object in object', async () => {
      const firstName = 'Natan';
      const object = { nested: { what: { firstName } } };
      const values = objectUtils.find(object, 'firstName');
      expect(values[0]).to.be.equal(firstName);
    });

    it('should find value in array of objects', async () => {
      const firstName = 'Natan';
      const array = [{ nested: { what: { firstName } } }];
      const values = objectUtils.find(array, 'firstName');
      expect(values[0]).to.be.equal(firstName);
    });

    it('should find value in nested array of objects', async () => {
      const firstName = 'Natan';
      const object = {
        someKey: {
          someArray: [
            { number: 1 },
            { value: 5, firstName },
          ],
        },
      };
      const values = objectUtils.find(object, 'firstName');
      expect(values[0]).to.be.equal(firstName);
    });

    it('should find all values for a given key', async () => {
      const firstName = 'Natan';
      const object = {
        someKey: {
          someArray: [
            { number: 1 },
            { value: 5, firstName },
          ],
          firstName: 'John',
        },
      };
      const values = objectUtils.find(object, 'firstName');
      expect(values[0]).to.be.equal(firstName);
      expect(values.length).to.be.equal(2);
    });

    it('should find key ignoring case sensitivity', async () => {
      const firstName = 'Natan';
      const object = { nested: { what: { FIRSTNaME: firstName } } };
      const values = objectUtils.find(object, 'firstname', { ignoreCase: true });
      expect(values[0]).to.be.equal(firstName);
    });

    it('should find multiple keys', async () => {
      const keys = ['fname', 'firstName', 'name'];
      const object = {
        nested: {
          name: 'John',
        },
        billing: {
          creditCard: {
            address: {
              firstName: 'Natan',
            },
          },
        },
        shipping: [{
          naMe: 'Melanie',
        }],
      };
      const values = objectUtils.find(object, keys, { ignoreCase: true });
      expect(values.length).to.be.equal(3);
      expect(values).to.include.members(['Natan', 'John', 'Melanie']);
    });

    it('should return value if its an array', async () => {
      const data = {
        nested: {
          products: [1, 2, 3],
        },
      };
      const products = objectUtils.find(data, 'products');
      expect(products[0]).to.be.eql([1, 2, 3]);
    });
  });

  describe('findFirst', () => {
    it('should find multiple keys', async () => {
      const keys = ['fname', 'firstName', 'name'];
      const object = {
        nested: {
          name: 'John',
        },
        billing: {
          creditCard: {
            address: {
              firstName: 'Natan',
            },
          },
        },
        shipping: [{
          naMe: 'Melanie',
        }],
      };
      const value = objectUtils.findFirst(object, keys, { ignoreCase: true });
      expect(value).to.be.equal('John');
    });

    it('should find wix structure nested country', async () => {
      const country = 'IL';
      const order = {
        billingInfo: {
          paymentMethod: 'offline',
          paymentGatewayTransactionId: 'd54907fa-65e8-4e9e-a21e-f9a18ee77d4d',
          address: {
            fullName: {
              firstName: 'kuki',
              lastName: 'duki',
            },
            country,
            city: 'Beersheba',
            zipCode: '51351',
            email: '<EMAIL>',
            addressLine1: 'Jaja',
          },
        },
      };
      const value = objectUtils.findFirst(order, 'country');
      expect(value).to.be.equal(country);
    });
  });
});
