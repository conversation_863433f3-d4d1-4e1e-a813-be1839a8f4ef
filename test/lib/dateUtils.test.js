const chai = require('chai');

const { expect } = chai;
const dateUtils = require('../../lib/utils/dateUtils');

describe('Lib - dateUtils', () => {
  it('minuteOfTheDay', () => {
    const now = new Date();
    const expectedMinutes = (now.getUTCHours() * 60) + now.getUTCMinutes();
    expect(dateUtils.minuteOfTheDay()).to.equal(expectedMinutes);
  });

  it('todayNormalized12am', () => {
    const now = new Date();
    const expected = now.setUTCHours(0, 0, 0, 0);
    expect(dateUtils.todayNormalized12am()).to.equal(expected);
  });

  it('isToday', () => {
    const now = Date.now();
    const timestamp1 = now + (Math.random() * Date.MILLISECONDS_IN_DAY);

    let modulo;
    if(timestamp1 > now) modulo = timestamp1 % now;
    else modulo = now % timestamp1;

    expect(modulo < Date.MILLISECONDS_IN_DAY).to.equal(dateUtils.isToday(timestamp1));
  });

  describe('getCycleStartDate', () => {
    it('should return this month', async () => {
      const date = new Date();
      date.setUTCDate(date.getUTCDate() - 1);
      const retval = dateUtils.getCycleStartDate(date);
      const expected = dateUtils.normalizeTo12Am(date);

      expect(retval.getTime()).to.be.equal(expected.getTime());
    });
  });

  describe('Month utils', () => {
    it('should change month to next month when in the past', () => {
      const created = Date.now() - (86400 * 1000);
      const result = dateUtils.getSubscriptionUntilDate(created);

      const now = new Date();
      expect(result.getTime()).to.be.above(now.getTime());
      let month = now.getUTCMonth() + 1;
      if(month === 12) month = 0;
      expect(result.getUTCMonth()).to.be.equal(month);
    });

    it('should return next month when same day', async () => {
      const date = new Date();
      const result = dateUtils.getSubscriptionUntilDate(date);

      expect(result.getTime() - date.getTime()).to.be
        .below(dateUtils.MILLISECONDS_IN_DAY * 32)
        .above(dateUtils.MILLISECONDS_IN_DAY);
    });

    it('should change year and month for really past dates', () => {
      const created = new Date('2016-01-01');
      const result = dateUtils.getSubscriptionUntilDate(created);

      const now = new Date();
      expect(result.getTime()).to.be.above(now.getTime());
      let month = now.getUTCMonth() + 1;
      if(month === 12) month = 0;
      expect(result.getUTCMonth()).to.be.equal(month);
    });

    it('should only change year for past year dates but future month', () => {
      const created = new Date();
      created.setUTCFullYear(created.getUTCFullYear() - 1, created.getUTCMonth() + 1);
      const result = dateUtils.getSubscriptionUntilDate(created);

      const now = new Date();
      expect(result.getTime()).to.be.above(now.getTime());
    });
  });
});
