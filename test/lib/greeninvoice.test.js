const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();

const config = require('../../config');
const constants = require('../constants');
const GreenInvoice = require('../../lib/apis/greeninvoice');

xdescribe('GreenInvoice API (#slow)', () => {
  afterEach(() => {
    sandbox.restore();
  });

  it('should return JWT token', async () => {
    const client = GreenInvoice.getClient(config.greenInvoice.apiKey, config.greenInvoice.secret);
    const jwt = await client._getJWT();
    console.log('token:', jwt);
    expect(jwt).to.not.be.empty;
  });

  it('should get customer', async () => {
    const email = '<EMAIL>';
    const client = GreenInvoice.getClient(config.greenInvoice.api<PERSON>ey, config.greenInvoice.secret);
    const customer = await client._getCustomer({ email });
    expect(customer).to.not.be.empty;
    expect(customer.emails[0]).to.be.equal(email);
  });

  it('should get customers', async () => {
    const email = '<EMAIL>';
    const client = GreenInvoice.getClient(config.greenInvoice.apiKey, config.greenInvoice.secret);
    const customers = await client.searchCustomers({ email });
    expect(customers).to.have.length.above(1);
    expect(customers[0].emails[0]).to.be.equal(email);
    expect(customers[1].emails[0]).to.be.equal(email);
  });

  it('should get all customers by names', async () => {
    const client = GreenInvoice.getClient(config.greenInvoice.apiKey, config.greenInvoice.secret);
    const customers = await client._searchCustomersByNames(['Aaron Koo', 'heloise laight']);
    expect(customers).to.have.length(2);
  });

  it('should get invoices for customer', async () => {
    const email = '<EMAIL>';
    const client = GreenInvoice.getClient(config.greenInvoice.apiKey, config.greenInvoice.secret);
    const documents = await client.getDocumentsForCustomer(email);
    expect(documents).to.not.be.empty;
  });

  it('should get all invoices for email', async () => {
    const email = '<EMAIL>';
    const client = GreenInvoice.getClient(config.greenInvoice.apiKey, config.greenInvoice.secret);
    const documents = await client.getDocumentsForCustomer(email);
    expect(documents).to.not.be.empty;
    expect(documents).to.have.length.above(1);
  });

  it('should get invoices for customer based on name', async () => {
    const firstName = 'Aaron';
    const lastName = 'Koo';
    const client = GreenInvoice.getClient(config.greenInvoice.apiKey, config.greenInvoice.secret);
    const documents = await client.getDocumentsForCustomer({ name: `${firstName} ${lastName}` });
    expect(documents).to.not.be.empty;
  });

  it('should get invoices for customer based on names', async () => {
    const client = GreenInvoice.getClient(config.greenInvoice.apiKey, config.greenInvoice.secret);
    const names = ['Aaron Koo', 'heloise laight'];
    const [name1, name2] = names;
    const documents = await client.getDocumentsByNames(names);
    const docNames = documents.reduce((accumulator, doc) => {
      if(!accumulator.includes(doc.client.name)) {
        accumulator.push(doc.client.name);
      }
      return accumulator;
    }, []);
    expect(documents).to.have.length.above(2);
    expect(docNames).to.have.length(2);
    expect(docNames).to.include(name1);
    expect(docNames).to.include(name2);
  });

  // This just a code runner test
  it('should get invoice starting from date', async () => {
    const client = GreenInvoice.getClient(config.greenInvoice.apiKey, config.greenInvoice.secret);
    const documents = await client.searchDocuments({ clientName: 'HAPPYCAR GmbH', fromDate: '2019-05-23' });
    expect(documents.length).to.be.above(0);
  });

  it('should send email with invoice', async () => {
    const client = GreenInvoice.getClient(config.greenInvoice.apiKey, config.greenInvoice.secret);
    const res = await client.sendDocument(
      'a74677f7-88fd-4866-9767-581a9812789f',
      ['<EMAIL>'],
      'ProveSource Invoice',
    );
    console.log(res);
  });
});
