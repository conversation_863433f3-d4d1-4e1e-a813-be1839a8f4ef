const chai = require('chai');
const chaiAsPromised = require('chai-as-promised');
const nock = require('nock');
const wixService = require('../../app/wix/wix.service');
const wix = require('../../lib/apis/wix');

const exData = require('../wix/wixExData');

chai.use(chaiAsPromised);

const { expect } = chai;

describe('wix API - lib', () => {
  describe('updateEmbedScript', () => {
    it('should return null to all non JSON strings', async () => {
      const msg = 'accessToken or apiKey';
      await Promise.all([
        expect(wix.updateEmbedScript(123)).to.eventually.be.rejectedWith(msg),
        expect(wix.updateEmbedScript(null)).to.eventually.be.rejectedWith(msg),
        expect(wix.updateEmbedScript({})).to.eventually.be.rejectedWith(msg),
        expect(wix.updateEmbedScript(undefined)).to.eventually.be.rejectedWith(msg),
      ]);
    });
  });

  describe('getToken', () => {
    it('should throw error if input is not correct', async () => {
      const msg = 'invalid tokens';
      await Promise.all([
        expect(wix.getTokens(123)).to.eventually.be.rejectedWith(msg),
        expect(wix.getTokens(null)).to.eventually.be.rejectedWith(msg),
        expect(wix.getTokens(undefined)).to.eventually.be.rejectedWith(msg),
        expect(wix.getTokens({})).to.eventually.be.rejectedWith(msg),
        expect(wix.getTokens('')).to.eventually.be.rejectedWith(msg),
      ]);
    });

    it('should successfully return tokens', async () => {
      const code = '1234';
      const refresh_token = 'oath2.0 1';
      const access_token = 'oath2.0 2';
      const params = {
        grant_type: 'authorization_code',
        client_id: '0359ef26-3f2d-4c81-a7cf-e',
        client_secret: '475cc7f6-44b35b1c759c',
        code,
      };
      const newTokens = {
        access_token,
        refresh_token,
      };
      nock('https://www.wix.com')
        .post('/oauth/access', params)
        .reply(200, newTokens);

      wix.setAuth(params.client_id, params.client_secret);
      const response = await wix.getTokens(code);

      expect(response).to.be.not.empty;
      expect(response).to.be.eql({ refresh_token, access_token });
    });
  });

  describe('decodeJwt', () => {
    // wrong input tests(no string)
    it('should return null to all non JSON string', async () => {
      expect(wix.decodeJwt(123)).to.be.null;
      expect(wix.decodeJwt(null)).to.be.null;
      expect(wix.decodeJwt({})).to.be.null;
      expect(wix.decodeJwt(undefined)).to.be.null;
    });

    // bad formated input test
    it('should return null when wrong token or string entered', async () => {
      expect(wix.decodeJwt('')).to.be.null;
      expect(wix.decodeJwt('lgjsduhfgisudfhgisudyhg')).to.be.null;
      expect(wix.decodeJwt('123.123.123')).to.be.null;
    });

    it('should return null if token have invalid validation parameters', async () => {
      const token = 'eyJraWQiOiJMclNlcGtHMiIsImFsZyI6IlJTMjU2In0.eyJkYXRhIjoie1wiZGF0YVwiOlwie1xcXCJhcHBJZFxcXCI6XFxcIjQwMjRmNTRmLTMzZDUtNDc4NS1iYmYyLWRkY2I2YzU0YmU2NVxcXCJ9XCIsXCJpbnN0YW5jZUlkXCI6XCJhMWE5NmI0ZC1iYjM2LTRlNzgtYmFiZC01MGYzMzhiNzZiZGVcIixcImV2ZW50VHlwZVwiOlwiQXBwSW5zdGFsbGVkXCJ9IiwiaWF0IjoxNTg5Nzg3MjMzLCJleHAiOjE1OTMzODcyMzN9.123';

      setTimeout(async () => {
        const response = await wix.decodeJwt(token);
        expect(response).to.be.null;
      }, 100);
    });

    it('should succesfuly verify and decode JWT ', async () => {
      const token = '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
      const decodedPayload = {
        data: {
          appId: '4024f54f-33d5-4785-bbf2-ddcb6c54be65',
        },
        instanceId: 'a1a96b4d-bb36-4e78-babd-50f338b76bde',
        eventType: 'AppInstalled',
      };

      setTimeout(async () => {
        const response = await wix.decodeJwt(token);
        expect(response).to.be.eql(decodedPayload);
        expect(response).to.have.keys(['data', 'instanceId', 'eventType']);
      }, 100);
    });
  });

  describe('refreshAccessToken', () => {
    it('should response with wix error when http request failed', async () => {
      const grantType = 'refresh_token';
      const clientId = '0359ef26-71c913dce';
      const clientSecret = '475cc7f1-b2c3-48c9c';
      const refreshToken = '12345';
      const accessToken = '321';
      const params = {
        grant_type: grantType,
        client_id: clientId,
        client_secret: clientSecret,
        refresh_token: refreshToken,
      };
      const err = 'unknown failure on wix';
      nock('https://www.wix.com').post('/oauth/access', params).replyWithError(err);
      wix.setAuth(clientId, clientSecret);
      await expect(wix.refreshAccessToken(refreshToken, accessToken))
        .to.eventually.be.rejectedWith(err);
    });

    it('should response with new access and refresh tokens', async () => {
      const grantType = 'refresh_token';
      const clientId = '0359ef26-3f2d-771c913dce';
      const clientSecret = '475cc7f16-44b35b1c759c';
      const refreshToken = '12345';
      const accessToken = '321';
      const params = {
        grant_type: grantType,
        client_id: clientId,
        client_secret: clientSecret,
        refresh_token: refreshToken,
      };
      const newTokens = {
        access_token: accessToken,
        refresh_token: refreshToken,
      };
      nock('https://www.wix.com', {
        reqheaders: {
          Authorization: accessToken,
        },
      }).post('/oauth/access', params)
        .reply(200, newTokens);

      wix.setAuth(clientId, clientSecret);
      const res = await wix.refreshAccessToken(refreshToken, accessToken);

      expect(res).to.be.eql(newTokens);
    });
  });

  describe('getOrder', () => {
    it('should throw error if wix http request failed', async () => {
      const orderId = '8fe8a767-bd0f-45b1-b764-9f967999e2fc';
      const accessToken = '123.123.123';
      nock('https://www.wixapis.com', {
        reqheaders: {
          authorization: accessToken,
        },
      }).get(`/stores/v2/orders/${orderId}`)
        .replyWithError('invalid orderId');
      await expect(wix.getOrder(accessToken, orderId)).to.eventually.be.rejectedWith('orderId');
    });

    it('should response with detailed order object from wix response', async () => {
      const orderId = '8fe8a767-bd0f-45b1-b764-9f967999e2fc';
      const accessToken = '123.123.123';
      nock('https://www.wixapis.com', {
        reqheaders: {
          Authorization: accessToken,
        },
      })
        .get(`/stores/v2/orders/${orderId}`)
        .reply(200, exData.GET_ORDER);
      expect(await wix.getOrder(accessToken, orderId)).to.be.eql(exData.GET_ORDER.order);
    });
  });

  describe('getProduct', () => {
    it('should throw error if wix request failed', async () => {
      const productId = 'a60fef92-ee29-070f-a7ed-9bbc3cc1c2f4';
      const accessToken = '123.123.123';
      nock('https://www.wixapis.com', {
        reqheaders: {
          authorization: accessToken,
        },
      })
        .get(`/stores/v1/products/${productId}`)
        .replyWithError('invalid productId');
      await expect(wix.getProduct(accessToken, productId))
        .to.eventually.be.rejectedWith('productId');
    });

    it('should respond with product from wix', async () => {
      const productId = 'a60fef92-ee29-070f-a7ed-9bbc3cc1c2f4';
      const accessToken = '123.123.123';
      nock('https://www.wixapis.com', {
        reqheaders: {
          Authorization: accessToken,
        },
      })
        .get(`/stores/v1/products/${productId}`)
        .reply(200, exData.GET_PRODUCT);
      expect(await wix.getProduct(accessToken, productId)).to.be.eql(exData.GET_PRODUCT.product);
    });
  });

  describe('getCheckoutUrl', () => {
    it('should return error if input is empty or malformed ', async () => {
      const msg = 'invalid access token or bad request parameters';
      const body = { testCheckout: true };
      const accessToken = 'testString';
      await Promise.all([
        expect(wix.getCheckoutUrl(123, body)).to.eventually.be.rejectedWith(msg),
        expect(wix.getCheckoutUrl(null, body)).to.eventually.be.rejectedWith(msg),
        expect(wix.getCheckoutUrl(undefined, body)).to.eventually.be.rejectedWith(msg),
        expect(wix.getCheckoutUrl({}, body)).to.eventually.be.rejectedWith(msg),
        expect(wix.getCheckoutUrl('', body)).to.eventually.be.rejectedWith(msg),
        expect(wix.getCheckoutUrl(accessToken, null)).to.eventually.be.rejectedWith(msg),
        expect(wix.getCheckoutUrl(accessToken, undefined)).to.eventually.be.rejectedWith(msg),
      ]);
    });

    it('should reject with error if failed to get url in wix', async () => {
      const body = { testCheckout: true };
      const err = 'unknown failure on wix';
      const accessToken = '123123.12312.12312';

      nock('https://www.wixapis.com').post('/apps/v1/checkout', body).replyWithError(err);

      await expect(wix.getCheckoutUrl(accessToken, body))
        .to.eventually.be.rejectedWith(err);
    });

    it('should return successfully url from wix', async () => {
      const url = { checkoutUrl: 'some.com/thanks' };
      const body = { testCheckout: true, successUrl: url };
      const accessToken = '123123.12312.12312';

      nock('https://www.wixapis.com').post('/apps/v1/checkout', body).reply(200, url);
      const res = await wix.getCheckoutUrl(accessToken, body);

      expect(res).to.not.be.empty;
      expect(res.checkoutUrl).to.be.eql(url.checkoutUrl);
    });
  });
});
