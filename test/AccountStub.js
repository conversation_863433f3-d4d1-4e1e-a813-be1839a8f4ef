const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const AccountFactory = require('./factories/AccountFactory');

const { Account } = AccountFactory;

module.exports.stub = () => {
  const account = AccountFactory.default();
  sandbox.stub(Account, 'findOne').resolves(account);
  sandbox.stub(Account.prototype, 'save').resolves();
  return account;
};

module.exports.restore = () => {
  sandbox.restore();
};
