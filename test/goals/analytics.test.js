const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const httpUtils = require('../httpUtils');
const testUtils = require('../testUtils');

const constants = require('../constants');
const GoalAnalytics = require('../../app/goals/GoalAnalytics');
const Account = require('../../app/account/models/Account');
const AccountFactory = require('../factories/AccountFactory');

describe('/goals/analytics', () => {
  let server;
  before(async () => {
    server = await require('../../server');
    if(!server) throw new Error('server not loaded');
  });

  afterEach(async () => {
    sandbox.restore();
  });

  let account = null;
  beforeEach(async () => {
    account = AccountFactory.default();
    account.goals = [];
    sandbox.stub(Account, 'findOne').resolves(account);
    sandbox.stub(account, 'save').resolves();

    await GoalAnalytics.remove();
  });

  it('should accept segmentUserId', async () => {
    const url = 'https://provesrc.com/thank-you';
    account.goals.push({ name: 'Goal 1', url });

    const id = testUtils.ObjectId();
    const events = [{
      notificationId: id, view: true, hover: true, click: true,
    }];
    const data = { url, events, segmentUserId: '12345' };

    const res = await httpUtils.apiRequest(server, '/goals/analytics', httpUtils.POST).send(data);
    expect(res).to.have.status(200);
  });

  it('should count goal analytics and store cookie', async () => {
    const url = 'https://provesrc.com/thank-you';
    account.goals.push({ name: 'Goal 1', url });

    const id1 = testUtils.ObjectId();
    const id2 = testUtils.ObjectId();
    const id3 = testUtils.ObjectId();
    const events = [
      {
        notificationId: id1, view: true, hover: true, click: true,
      },
      {
        notificationId: id2, view: true, hover: false, click: false,
      },
      {
        notificationId: id3, view: true, hover: true, click: true,
      },
    ];

    const data = { url, events };
    const res = await httpUtils.apiRequest(server, '/goals/analytics', httpUtils.POST).send(data);
    expect(res).to.have.status(200);
    expect(res).to.have.cookie(`psgoal${account.goals[0]._id}`);

    const event = await GoalAnalytics.findOne();
    expect(event.conversions).to.be.equal(1);
    expect(event.clickConversions).to.be.equal(1);
    expect(event.views).to.be.equal(3);
    expect(event.hovers).to.be.equal(2);
    expect(event.clicks).to.be.equal(2);

    const notification = await event.notifications.find(notif => notif._id.equals(id1));
    expect(notification).to.exist;
    expect(notification.conversions).to.be.equal(1);
    expect(notification.views).to.be.equal(1);
    expect(notification.hovers).to.be.equal(1);
    expect(notification.clicks).to.be.equal(1);

    const notification2 = await event.notifications.find(notif => notif._id.equals(id2));
    expect(notification2.views).to.be.equal(1);
    expect(notification2.hovers).to.be.equal(0);
    expect(notification2.clicks).to.be.equal(0);
  });

  it('should ignore if no goal with URL', async () => {
    const data = { url: '12345' };
    const res = await httpUtils.apiRequest(server, '/goals/analytics', httpUtils.POST).send(data);
    expect(res).to.have.status(200);

    const event = await GoalAnalytics.findOne();
    expect(event).to.not.exist;
  });

  it('should not count user already completed in last 7 days (cookie)', async () => {
    const url = 'https://provesrc.com/thank-you';
    account.goals.push({ name: 'Goal 1', url });
    const goal = account.goals[0];

    const data = { url };
    const agent = httpUtils.Agent(server);
    let res = await agent.apiRequest(server, '/goals/analytics', httpUtils.POST).send(data);
    expect(res).to.have.status(200);
    expect(res).to.have.cookie(`psgoal${goal._id}`);

    res = await agent.apiRequest(server, '/goals/analytics', httpUtils.POST).send(data);
    expect(res).to.have.status(200);

    const event = await GoalAnalytics.findOne();
    expect(event.conversions).to.be.equal(1);
  });

  it('should count multiple goals if URL matches', async () => {
    const url = 'https://provesrc.com/thank-you/complete';
    account.goals.push({ name: 'Goal 1', url: 'thank' });
    account.goals.push({ name: 'Goal 2', url: 'complete' });

    const data = { url };
    const agent = httpUtils.Agent(server);
    const res = await agent.apiRequest(server, '/goals/analytics', httpUtils.POST).send(data);
    expect(res).to.have.status(200);
    expect(res).to.have.cookie(`psgoal${account.goals[0]._id}`);
    expect(res).to.have.cookie(`psgoal${account.goals[1]._id}`);

    const events = await GoalAnalytics.find();
    expect(events.length).to.be.equal(2);
    const eventGoalIds = events.map(e => e.goalId.toString());
    const accGoalIds = account.goals.map(g => g._id.toString());
    expect(eventGoalIds).to.have.members(accGoalIds);
  });

  describe('code tracking', () => {
    it('should accept code track', async () => {
      account.goals.push({ name: 'Goal 1', codeTrack: true });

      const events = [{
        notificationId: testUtils.ObjectId(), view: true, hover: true, click: true,
      }];

      const data = { events, id: account.goals[0].id };
      const res = await httpUtils.apiRequest(server, '/goals/analytics', httpUtils.POST).send(data);
      expect(res).to.have.status(200);
      expect(res).to.have.cookie(`psgoal${account.goals[0].id}`);

      const event = await GoalAnalytics.findOne();
      expect(event.conversions).to.be.equal(1);
      expect(event.clickConversions).to.be.equal(1);
      expect(event.views).to.be.equal(1);
      expect(event.hovers).to.be.equal(1);
      expect(event.clicks).to.be.equal(1);
    });

    it('should not track if codeTrack=false', async () => {
      account.goals.push({ name: 'Goal 1', codeTrack: false });
      const events = [{
        notificationId: testUtils.ObjectId(), view: true, hover: true, click: true,
      }];

      const data = { events, id: account.goals[0].id };
      const res = await httpUtils.apiRequest(server, '/goals/analytics', httpUtils.POST).send(data);
      expect(res).to.have.status(200);
      expect(res).to.not.have.cookie(`psgoal${account.goals[0].id}`);

      const event = await GoalAnalytics.findOne();
      expect(event).to.be.null;
    });
  });
});
