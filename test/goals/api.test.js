const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const moment = require('moment');
const httpUtils = require('../httpUtils');
const dateUtils = require('../../lib/utils/dateUtils');
const testUtils = require('../testUtils');

const constants = require('../constants');
const Account = require('../../app/account/models/Account');
const AccountFactory = require('../factories/AccountFactory');
const GoalAnalyticsFactory = require('../factories/GoalAnalyticsFactory');

const { GoalAnalytics } = GoalAnalyticsFactory;

describe('/goals', () => {
  let server;
  before(async () => {
    server = await require('../../server');
    if(!server) throw new Error('server not loaded');
  });

  afterEach(async () => {
    sandbox.restore();
  });

  let account = null;
  beforeEach(async () => {
    account = AccountFactory.default();
    sandbox.stub(Account, 'findOne').resolves(account);
    sandbox.stub(account, 'save').resolves();

    await Promise.all([
      GoalAnalytics.remove(),
    ]);
  });

  describe('POST', () => {
    beforeEach(async () => {
      account.goals = [];
    });

    it('should create goal', async () => {
      const name = 'Goal #1';
      const url = 'thank-you';
      const res = await httpUtils
        .consoleRequest(server, '/goals', httpUtils.POST)
        .send({ name, url, value: 10 });
      expect(res).to.have.status(201);

      const account = await Account.findOne({ _id: constants.accountId });
      expect(account.goals.length).to.be.equal(1);
      const goal = account.goals[0];
      expect(goal.name).to.be.equal(name);
      expect(goal.url).to.be.equal(url);

      expect(account.stats.goals.created).to.be.equal(1);
      const diff = Date.now() - account.stats.goals.lastCreated.getTime();
      expect(diff).to.be.below(200);
    });

    it('should not create two goals with the same name', async () => {
      const name = 'Goal #1';
      const url = 'thank-you';
      let res = await httpUtils
        .consoleRequest(server, '/goals', httpUtils.POST)
        .send({ name, url, value: 10 });
      expect(res).to.have.status(201);

      res = await httpUtils
        .consoleRequest(server, '/goals', httpUtils.POST)
        .send({ name, url, value: 10 });
      expect(res).to.have.status(400);
      expect(res.body.error).to.contain('a goal with this name exists');

      const account = await Account.findOne({ _id: constants.accountId });
      expect(account.goals.length).to.be.equal(1);
    });
  });

  describe('PUT', () => {
    let goalId = null;
    beforeEach(async () => {
      account.goals.push({ name: 'Goal 1', url: 'thanks', codeTrack: true });
      goalId = account.goals[0]._id;
    });

    it('should edit the goal name', async () => {
      const randomname = `Goal ${parseInt(1 + Math.random() * 100)}`;
      const res = await httpUtils.consoleRequest(server, `/goals/${goalId}`, httpUtils.PUT)
        .send({ name: randomname });
      expect(res).to.have.status(200);

      const account = await Account.findOne({ _id: constants.accountId });
      expect(account.goals[0].name).to.be.equal(randomname);
      expect(account.goals[0].codeTrack).to.be.true;

      expect(account.stats.goals.updated).to.be.equal(1);
      const diff = Date.now() - account.stats.goals.lastUpdated.getTime();
      expect(diff).to.be.below(100);
    });

    it('should fail if goal does not exist', async () => {
      const res = await httpUtils.consoleRequest(server, '/goals/abc', httpUtils.PUT).send({ name: '123' });
      expect(res).to.have.status(404);
      expect(res.body.error).to.be.include('not found');
    });

    it('should fail if goal with same name exists', async () => {
      const randomname = `Goal ${parseInt(1 + Math.random() * 100)}`;
      account.goals.push({ name: randomname, url: 'paka-paka' });

      const data = { name: randomname };
      const res = await httpUtils.consoleRequest(server, `/goals/${goalId}`, httpUtils.PUT).send(data);
      expect(res).to.have.status(400);
      expect(res.body.error).to.be.equal('a goal with same name exists, please choose a different name');
    });

    it('should succeed if name not changed', async () => {
      const account = await Account.findOne({ _id: constants.accountId });
      const { name } = account.goals.id(goalId);
      const res = await httpUtils.consoleRequest(server, `/goals/${goalId}`, httpUtils.PUT).send({ name });
      expect(res).to.have.status(200);

      const dbAccount = await Account.findOne({ _id: constants.accountId });
      expect(dbAccount.goals[0].name).to.be.equal(name);
    });
  });

  describe('DELETE', () => {
    let goalId = null;
    beforeEach(async () => {
      account.goals.push({ name: 'Goal 1', url: 'thanks' });
      goalId = account.goals[0]._id;
    });

    it('should delete goal', async () => {
      const res = await httpUtils.consoleRequest(server, `/goals/${goalId}`, httpUtils.DELETE);
      expect(res).to.have.status(200);

      const account = await Account.findOne({ _id: constants.accountId });
      expect(account.goals.length).to.be.equal(0);

      expect(account.stats.goals.deleted).to.be.equal(1);
      const diff = Date.now() - account.stats.goals.lastDeleted.getTime();
      expect(diff).to.be.below(100);
    });

    it('should fail if goal does not exit', async () => {
      const res = await httpUtils.consoleRequest(server, '/goals/abc', httpUtils.DELETE);
      expect(res).to.have.status(404);
      expect(res.body.error).to.be.include('not found');
    });
  });

  describe('GET', () => {
    beforeEach(async () => {
      account.goals.push({ name: 'Goal #1', url: 'thank-you' });
    });

    it('should get goals', async () => {
      const res = await httpUtils.consoleRequest(server, '/goals', httpUtils.GET);
      expect(res.body[0].name).to.be.equal(account.goals[0].name);
      expect(res.body[0].url).to.be.equal(account.goals[0].url);
      expect(res.body[0]._id).to.not.be.null;
    });

    it('should contain goal analytics', async () => {
      const goal = account.goals[0];

      const eventPromises = [];
      const data = {
        conversions: 0, clickConversions: 0, views: 0, hovers: 0, clicks: 0,
      };
      let lastDate;
      for(let i = 0; i < 10; i++) {
        const date = dateUtils.todayNormalized12am() - i * dateUtils.MILLISECONDS_IN_DAY;
        if(i === 9) lastDate = date;

        const event = GoalAnalyticsFactory.getEvent(constants.accountId, goal._id, date);
        eventPromises.push(event.save());

        data.conversions += event.conversions;
        data.views += event.views;
        data.hovers += event.hovers;
        data.clicks += event.clicks;
        data.clickConversions += event.clickConversions;
      }
      await Promise.all(eventPromises);

      const startDate = moment(lastDate).format('YYYY-MM-DD');
      const endDate = moment(Date.now()).format('YYYY-MM-DD');
      const apiurl = `/goals?startDate=${startDate}&endDate=${endDate}`;
      const res = await httpUtils.consoleRequest(server, apiurl, httpUtils.GET);
      expect(res.body[0].name).to.be.equal(account.goals[0].name);
      expect(res.body[0].url).to.be.equal(account.goals[0].url);
      expect(res.body[0]._id).to.not.be.null;
      expect(res.body[0].conversions).to.be.equal(data.conversions);
      expect(res.body[0].clickConversions).to.be.equal(data.clickConversions);
      expect(res.body[0].views).to.be.equal(data.views);
      expect(res.body[0].hovers).to.be.equal(data.hovers);
      expect(res.body[0].clicks).to.be.equal(data.clicks);
    });

    it('should contain notification goal analytics', async () => {
      const goal = account.goals[0];
      const notificationId = testUtils.ObjectId();

      const eventPromises = [];
      const data = {
        conversions: 0, views: 0, hovers: 0, clicks: 0,
      };
      let lastDate;
      for(let i = 0; i < 10; i++) {
        const date = dateUtils.todayNormalized12am() - i * dateUtils.MILLISECONDS_IN_DAY;
        if(i === 9) lastDate = date;

        const event = GoalAnalyticsFactory.getEvent(constants.accountId, goal._id, date, notificationId);
        eventPromises.push(event.save());

        data.conversions += event.notifications.id(notificationId).conversions;
        data.views += event.notifications.id(notificationId).views;
        data.hovers += event.notifications.id(notificationId).hovers;
        data.clicks += event.notifications.id(notificationId).clicks;
      }
      await Promise.all(eventPromises);

      const startDate = moment(lastDate).format('YYYY-MM-DD');
      const endDate = moment(Date.now()).format('YYYY-MM-DD');
      const apiurl = `/goals?startDate=${startDate}&endDate=${endDate}&notificationId=${notificationId}`;
      const res = await httpUtils.consoleRequest(server, apiurl, httpUtils.GET);
      expect(res.body[0].name).to.be.equal(account.goals[0].name);
      expect(res.body[0].url).to.be.equal(account.goals[0].url);
      expect(res.body[0]._id).to.not.be.null;
      expect(res.body[0].conversions).to.be.equal(data.conversions);
      expect(res.body[0].views).to.be.equal(data.views);
      expect(res.body[0].hovers).to.be.equal(data.hovers);
      expect(res.body[0].clicks).to.be.equal(data.clicks);
    });
  });
});
