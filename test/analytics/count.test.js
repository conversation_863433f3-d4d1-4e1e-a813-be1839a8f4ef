const chai = require('chai');
const httpUtils = require('../httpUtils');

const { expect } = chai;
const constants = require('../constants');
const serverP = require('../../server');
const dateUtils = require('../../lib/utils/dateUtils');

const NotificationFactory = require('../factories/NotificationFactory');
const EventFactory = require('../factories/EventFactory');
const AnalyticsEvent = require('../../app/notifications/models/AnalyticsEvent');

describe('/notifications/analytics counting', () => {
  let server;
  before(async () => {
    server = await serverP;
    if(!server) throw new Error('server not loaded');
  });

  beforeEach(async () => {
    await AnalyticsEvent.remove();
  });

  it('should count analytics for webhooks', async () => {
    const notification = NotificationFactory.ConversionWebhook({ autoTrack: false });
    await notification.save();

    const { webhookId } = notification;
    const res = await httpUtils.trackWebhookData(server, webhookId);
    expect(res).to.have.status(200);

    const accountEvent = await AnalyticsEvent.findOne({ accountId: constants.accountId, notificationId: null });
    expect(accountEvent.total.webhooks).to.be.equal(1);

    const notifEvent = await AnalyticsEvent.findOne({ accountId: constants.accountId, notificationId: notification._id });
    expect(notifEvent.total.webhooks).to.be.equal(1);
  });

  it('should count woocommerce events', async () => {
    const params = EventFactory.WooEvent({ lean: true });
    const res = await httpUtils.trackWooCommerce(server, params);
    expect(res).to.have.status(200);

    const accountEvent = await AnalyticsEvent.findOne({ accountId: constants.accountId, notificationId: null });
    expect(accountEvent.total.woocommerceOrders).to.be.equal(1);
  });
});
