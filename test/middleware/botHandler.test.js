const { expect } = require('chai');
const botHandler = require('../../middleware/botHandler');

describe('middleware bothandler', () => {
  it('should detect bot as true', () => {
    const uas = [
      'A1 Sitemap Generator/1.0 (+http://www.micro-sys.dk/products/sitemap-generator/) miggibot/2006.01.24',
      'AddThis.<NAME_EMAIL>',
      'EsperanzaBot(+http://www.esperanza.to/bot/)',
      'Googlebot/2.X (+http://www.googlebot.com/bot.html)',
      'Kemvibot/1.0 (http://kemvi.com, <EMAIL>)',
      'Mozilla/4.0_(compatible;_MSIE_5.0;_Windows_95)_TrueRobot/1.4 libwww/5.2.8',
      'NetResearchServer/2.5(loopimprovements.com/robot.html)',
      'PRCrawler/Nutch-0.9 (data mining development project; <EMAIL>)',
      'Scooter-W3-1.0',
      'Trailfire/0.7.1 (Nutch; http://lucene.apache.org/nutch/bot.html; <EMAIL>)',
    ];
    for(let i = 0; i < uas.length; i++) {
      const ua = uas[i];
      const result = botHandler.isBot(ua);
      expect(result).to.be.true;
    }
  });

  it('should return isBot false', () => {
    const uas = [
      'Airmail/237 CFNetwork/673.4 Darwin/13.2.0 (x86_64) (iMac13%2C2)',
      'Eudora/5.0.0b25 (MacOS)',
      'Mozilla/4.06 [en] (WinNT; I)',
      'Mozilla/5.0 (iOS; like Mac OS X) AppleWebKit/536.36 (KHTML, like Gecko) not Chrome/27.0.1500.95 Mobile/10B141 Safari/537.36 Bowser/0.2.1',
      'Mozilla/5.0 (Macintosh; U; PPC Mac OS X 10_5_4; en-us) AppleWebKit/525.18 (KHTML, like Gecko) Version/3.0.4 Safari/523.10',
    ];
    for(let i = 0; i < uas.length; i++) {
      const ua = uas[i];
      const result = botHandler.isBot(ua);
      expect(result).to.be.false;
    }
  });
});
