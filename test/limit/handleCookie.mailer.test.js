const { expect } = require('chai');
const sinon = require('sinon');

const sandbox = sinon.createSandbox();
const testUtils = require('../testUtils');

const redis = require('../../lib/redisClient').getClient();
const getLimitInfo = require('../../app/visitorCount/getLimitInfo');
const AccountFactory = require('../factories/AccountFactory');

const { Account } = AccountFactory;

const handleCookie = require('../../app/visitorCount/handleCookie');

describe('handleCookie', () => {
  afterEach(() => {
    sandbox.restore();
  });

  it('should send 90% limit reached email', async () => {
    sandbox.stub(redis, 'getAsync').resolves();

    const account = AccountFactory.default();
    sandbox.stub(Account, 'findOne').returns({ select: () => account });

    const limit = 1000;
    const count = limit * 0.9 - 1;
    const limitInfo = {
      fromRedis: true,
      email: '<EMAIL>',
      limit,
      expires: Date.now() + ********,
      visitorCount: count,
      cycleDate: Date.now() - 8640000,
    };
    sandbox.stub(getLimitInfo, 'get').resolves(limitInfo);

    const mockReq = {
      headers: {}, jwtData: { accountId: account.id }, locals: {}, signedCookies: [],
    };
    const mockRes = { cookie: () => {} };
    await handleCookie(mockReq, mockRes);

    expect(mailerStub).to.have.been.calledWith(sinon.match.string, account.email);
  });
});
