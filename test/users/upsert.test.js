require('../../lib/mongooseLoader');
const chai = require('chai');

const { expect } = chai;
const uuid = require('uuid/v4');
const constants = require('../constants');

const upsert = require('../../app/users/upsert');
const User = require('../../app/users/User');

describe('/users/upsert', () => {
  beforeEach(async () => {
    await User.remove();
  });

  it('should add user', async () => {
    const uid = uuid();
    const ua = '<PERSON><PERSON>er';
    const host = 'provesrc.com';
    const request = {
      signedCookies: [],
      headers: {
        'x-ps-uid': uid,
        'user-agent': ua,
        origin: host,
      },
    };
    await upsert(request);
    const user = await User.findOne();
    expect(user.uid).to.be.equal(uid);
    expect(user.userAgents).to.include(ua);
    expect(user.hosts).to.include(host);
  });

  it('should update user if already exists', async () => {
    const uid = uuid();
    await (new User({ uid, ips: ['***********'] })).save();

    const ua = 'jango';
    const ip = '***********';
    const request = {
      signedCookies: [],
      headers: {
        'x-ps-uid': uid,
        'user-agent': ua,
      },
      remoteAddress: ip,
      jwtData: { accountId: constants.accountId },
    };
    const cycleDate = new Date();
    const data = { cycleDate };
    await upsert(request, data);

    const users = await User.find();
    expect(users).to.have.lengthOf(1);
    const user = users[0];
    expect(user.userAgents).to.include(ua);
    expect(user.ips).to.include(ip);
    expect(user.accounts).to.include(constants.accountId);
    expect(user.cycleDates[constants.accountId]).to.deep.include(cycleDate);
  });
});
