require('../../lib/mongooseLoader');

const mongoose = require('mongoose');
const _ = require('lodash');
const chai = require('chai');

const { expect } = chai;
const uuid = require('uuid/v4');
const User = require('../../app/users/User');

describe('Model - User', () => {
  beforeEach(async () => {
    await User.remove();
  });

  it('should create and save a user', async () => {
    const uid = uuid();
    const host = 'provesrc.com';
    const accountId = mongoose.Types.ObjectId();
    const cycleDates = { [accountId]: new Date() };
    const ua = 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.103 Safari/537.36';
    const ip = '***********';
    const event = {
      date: new Date(),
      source: 'https://provesrc.com/contact',
      ip,
      email: '<EMAIL>',
      firstName: 'Natan',
      lastName: 'Abramov',
    };
    const params = {
      uid, hosts: [host], accounts: [accountId], userAgents: [ua], ips: [ip], events: [event],
    };
    params.cycleDates = cycleDates;
    await (new User(params).save());

    const dbUser = await User.findOne();
    const fields = _.pick(dbUser.toObject(), ['uid', 'hosts', 'accounts', 'userAgents', 'ips', 'events', 'cycleDates']);
    expect(fields).to.be.deep.equal(params);
  });
});
