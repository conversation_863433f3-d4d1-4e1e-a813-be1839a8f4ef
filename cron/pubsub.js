const redisService = require('../app/common/redis.service');
const logger = require('../lib/logger').getLogger('pubsub');

// key: redis channel, value: function to execute on message
const fnMap = {};

module.exports = {
  setupListeners() {
    redisService.getPubSub().on('message', (channel, message) => {
      const fn = fnMap[channel];
      if(fn && typeof fn === 'function') {
        logger.info({ channel, message }, 'forwarding redis message');
        fn(message);
      } else {
        logger.info({ channel, message }, 'ignoring redis message');
      }
    });

    return Promise.all([
    ]);
  },
};
