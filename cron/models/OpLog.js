const mongoose = require('mongoose');
const { errSerializer, objectUtils } = require('../../lib/utils');

const OpLog = new mongoose.Schema({
  name: { type: String, required: true },
  accountId: { type: mongoose.SchemaTypes.ObjectId },
  resourceId: { type: mongoose.SchemaTypes.ObjectId },
  resource: {},
  meta: {},
  message: { type: String },
  result: {
    type: mongoose.SchemaTypes.Mixed,
    default: null,
  },
  error: {
    type: {},
    default: {},
  },
}, { timestamps: true, collection: 'oplog' });

OpLog.index({ accountId: 1 });
OpLog.index({ resourceId: 1 });
OpLog.index({ name: 1, created: -1 });
OpLog.index({ createdAt: -1 }, { expires: '30d' });

OpLog.pre('save', function (next) {
  this.result = objectUtils.destroyCircular(this.result);
  this.error = errSerializer(this.error);
  next();
});

OpLog.methods.safeSave = function safeSave() {
  return this.save().catch(err => err);
};

module.exports = mongoose.model('OpLog', OpLog);
