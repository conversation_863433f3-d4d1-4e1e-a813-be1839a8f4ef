const mongoose = require('mongoose');
const { objectUtils, errSerializer } = require('../../lib/utils');

const CronResult = new mongoose.Schema({
  job: { type: 'String', required: true },
  result: {
    type: {},
    set(res) {
      return objectUtils.destroyCircular(res);
    },
  },
  error: {
    type: {},
    set(err) {
      return errSerializer(err);
    },
  },
}, { timestamps: true, collection: 'cronResults' });

CronResult.index({ createdAt: -1 }, { expires: '30d' });

module.exports = mongoose.model('CronResult', CronResult);
