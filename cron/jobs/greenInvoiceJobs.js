/* eslint-disable no-await-in-loop */
const _ = require('lodash');
const config = require('../../config');
const logger = require('../../lib/logger/LoggerFactory')('sendInvoices');
const greenInvoice = require('../../lib/apis/greeninvoice').getClient(
  config.greenInvoice.apiKey,
  config.greenInvoice.secret,
);
const Account = require('../../app/account/models/Account');
const emailService = require('../../app/common/mailer.service');

module.exports = {
  getAccounts,
  getLatestDocument,
  sendInvoices,
  fixAccountingKeys,
};

function getAccounts(fromDate, toDate) {
  return Account.find({
    'subscription.recentIPN': { $in: ['RECURRING', 'CHARGE'] },
    'configuration.sendInvoices': true,
    'subscription.transactionDate': {
      $gte: fromDate || Date.now() - 86400 * 1000 * 3,
      $lte: toDate || Date.now(),
    },
  });
}

async function getLatestDocument(names) {
  if(!names || !names.length) {
    return null;
  }
  const documents = await greenInvoice.getDocumentsByNames(names);
  if(documents.length > 0) {
    return documents[0];
  }
  return null;
}

async function sendInvoices() {
  const toDate = Date.now();
  const fromDate = toDate - 86400 * 1000 * 3; // last 3 days
  const accounts = await getAccounts(fromDate, toDate);
  logger.info({ emails: accounts.map(acc => acc.email) }, 'got accounts');
  const results = {
    success: 0,
    failed: 0,
    noDoc: 0,
    noEmail: 0,
    skipped: 0,
    alreadySent: 0,
    accounts: {},
  };
  for(let i = 0; i < accounts.length; i += 1) {
    const account = accounts[i];
    const { email } = account;
    const names = account.getInvoiceNames();
    const document = await getLatestDocument(names);
    logger.info({ document, email }, 'got document');
    const emails = account.getEmails();
    if(!document) {
      results.noDoc += 1;
      results.accounts[email] = 'no doc';
      logger.info({ email }, 'no document');
      continue;
    }

    if(!emails || !emails.length) {
      results.noEmail += 1;
      results.accounts[email] = 'no email';
      logger.info({ email }, 'no email');
      continue;
    }

    const lastInvoiceSent = _.get(account, 'stats.sentEmails.invoice');
    if(lastInvoiceSent && lastInvoiceSent >= fromDate) {
      results.skipped = (results.skipped + 1) || 0;
      results.accounts[email] = 'already sent';
      continue;
    }
    const res = await greenInvoice.sendDocument(document.id, emails, 'ProveSource Invoice').catch((err) => {
      logger.error({ err, emails, document: document.id }, 'failed sending document');
      results.failed += 1;
      results.accounts[email] = err.message;
    });
    if(res.ok) {
      await Account.updateOne(
        { _id: account.id },
        { $set: { 'stats.sentEmails.invoice': new Date() } },
      );
    }
    logger.info({ emails, document: document.id, res }, 'sent document');
    results.accounts[email] = document.id;
    results.success += 1;
  }
  return results;
}

/**
 * This fixes rivhit not identifying client-cards from GreenInvoice due to
 * accountingKey/ID starting with "0" and then cards are opened as "foregin software"
 * we only fix customers starting June 1st because past ones (~370) are already in rivhit
 */
async function fixAccountingKeys() {
  const startDate = new Date('2024-06-01T00:00:00.000Z');
  const results = {
    scannedClients: 0,
    startingWithZero: {
      total: 0,
      fixed: [],
    },
    duplicates: {
      total: 0,
      notFixed: [],
    },
  };
  const clients = [];
  for(let i = 1; i < Infinity; i += 1) {
    const clientsRes = await greenInvoice.searchCustomers({
      page: i,
      pageSize: 500,
    });
    if(!clientsRes.length) {
      break;
    }
    clients.push(...clientsRes);
  }
  results.scannedClients = clients.length;
  clients.forEach((client) => {
    client.creationDate = new Date(client.creationDate * 1000);
  });
  clients.sort((a, b) => b.creationDate - a.creationDate);

  const newClients = clients.filter(c => c.creationDate > startDate);
  const groupedByAccountingKey = _.groupBy(newClients, 'accountingKey');
  const duplicateAccountingKey = _.flatMap(groupedByAccountingKey, g => (g.length > 1 ? g : []));
  if(duplicateAccountingKey.length) {
    results.duplicates.total = duplicateAccountingKey.length;
    results.duplicates.notFixed = duplicateAccountingKey.map(c => _.pick(c, ['id', 'name', 'accountingkey']));
  }

  const clientsWithBadKey = clients.filter(c => c.creationDate > startDate && c.accountingKey.startsWith('0'));
  results.startingWithZero.total = clientsWithBadKey.length;
  await Promise.all(clientsWithBadKey.map(c => greenInvoice.updateClient(c.id, {
    accountingKey: `1${c.accountingKey}`,
  }).then(() => {
    results.startingWithZero.fixed.push(_.pick(c, ['id', 'name', 'accountingKey']));
  })));

  await emailService.sendAdminEmail({
    to: '<EMAIL>',
    subject: 'ProveSource GreenInvoice Fix Accounting Keys Result',
    html: `<pre>${JSON.stringify(results, null, 2)}</pre>`,
  });
  return results;
}
