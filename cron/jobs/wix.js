/* eslint-disable no-loop-func,no-await-in-loop */
const _ = require('lodash');
const moment = require('moment');
const { serializeError } = require('serialize-error');
const config = require('../../config');
const mailerService = require('../../app/common/mailer.service');
const cryptoUtils = require('../../lib/utils/cryptoUtils');
const wixService = require('../../app/wix/wix.service');
const WixSite = require('../../app/wix/models/WixSite');
const Account = require('../../app/account/models/Account');
const Review = require('../../app/notifications/models/Review');
const OpLog = require('../models/OpLog');
const logger = require('../../lib/logger').getLogger('wix.jobs');

const { reviewSources } = require('../../app/notifications/constants');

module.exports = {
  checkWixSubscriptions,
  sendWixInstallEmail,
  wixReviewsTask,
};

async function sendWixInstallEmail() {
  const BATCH_SIZE = 20;
  const results = {
    processed: 0,
    skipped: { noEmail: 0, alreadySent: 0, alreadyInstalled: 0 },
    sent: { total: 0, instances: [] },
    errors: { total: 0, instances: [] },
  };
  const wixSites = await WixSite.find({ noConnectedAccount: true });
  let batchPromises = [];
  let instanceIds = [];
  for(let i = 0; i < wixSites.length; i += 1) {
    results.processed += 1;
    const wixSite = wixSites[i];
    const email = wixSite.ownerEmail || _.get(wixSite, 'ownerInfo.email') || wixSite.email;
    if(!email) {
      results.skipped.noEmail += 1;
      continue;
    }
    const {
      instanceId, lastInstallEmail, siteName, url: siteUrl,
    } = wixSite;
    const account = await Account.findOne({ 'wix.instanceId': instanceId }).select('_id');
    if(account) {
      results.skipped.alreadyInstalled += 1;
      await WixSite.updateOne({ instanceId }, { noConnectedAccount: false, accountId: account.id });
      continue;
    }
    // check if email was sent in the last 7 days
    const isEmailSentRecently = lastInstallEmail && moment().diff(moment(lastInstallEmail), 'days') <= 7;
    if(isEmailSentRecently) {
      results.skipped.alreadySent += 1;
      continue;
    }
    const token = cryptoUtils.encrypt(JSON.stringify(instanceId), config.cryptoKeys.wixInstallEmail);
    const link = `${config.apiUrl}/wix/continue-install?token=${token}`;
    batchPromises.push(mailerService.sendWixInstallEmail({
      to: email, link, siteName, siteUrl,
    }).then(() => {
      results.sent.total += 1;
      results.sent.instances.push({ email, instanceId });
      // collect instanceIds for a "batch" db update op
      instanceIds.push(instanceId);
    }).catch((err) => {
      results.errors.total += 1;
      results.errors.instances.push({ email, instanceId, error: serializeError(err) });
    }));
    if(batchPromises.length >= BATCH_SIZE || i === wixSites.length - 1) {
      await Promise.all(batchPromises);
      await WixSite.updateMany({ instanceId: { $in: instanceIds } }, { $set: { lastInstallEmail: new Date() } });
      instanceIds = [];
      batchPromises = [];
    }
  }
  return results;
}

async function checkWixSubscriptions() {
  const results = {
    processed: 0, updated: 0, notUpdated: 0, errors: {}, failed: {}, success: [],
  };
  const accounts = await Account.find({
    'subscription.source': 'wix',
  });
  let promises = [];
  for(let i = 0; i < accounts.length; i += 1) {
    results.processed += 1;
    const account = accounts[i];
    const site = account.wix[0];
    const id = site && (site.url || site.instanceId || account.email);
    if(!site) {
      results.errors['no-site'] = (results.errors['no-site'] + 1) || 1;
      results.failed[id] = 'No instance';
      continue;
    }
    promises.push(wixService.updateBillingExpirationDate(account)
      .then(({ updated, site: updatedSite }) => {
        if(!updatedSite && !updated) {
          results.errors['no-site'] = (results.errors['no-site'] + 1) || 1;
          results.failed[id] = 'No instance';
        } else {
          const expirationDate = _.get(updatedSite, 'billing.expirationDate');
          const { isFree } = updatedSite;
          const expiration = new Date(expirationDate);
          if(!isFree && expiration < Date.now()) {
            results.failed[id] = 'Expiration in the past, but isFree=false';
          }
          if(updated) {
            results.updated += 1;
            results.success.push(id);
          } else {
            results.notUpdated += 1;
          }
        }
      })
      .catch((err) => {
        results.failed[id] = err.message;
        results.errors[err.message] = (results.errors[err.message] + 1) || 1;
      }));
    if(promises.length && promises.length % 10 === 0) {
      // eslint-disable-next-line no-await-in-loop
      await Promise.all(promises);
      promises = [];
    }
  }
  if(promises.length) {
    await Promise.all(promises);
  }
  return results;
}

async function wixReviewsTask() {
  const taskResults = {
    reviews: 0,
    success: 0,
    errors: 0,
  };

  const notifications = await Review.find({
    source: reviewSources.wix,
    stopAutoSync: { $ne: true },
  });

  if(notifications.length > 0) {
    logger.info({ notifications }, 'processing notifications');

    for(let i = 0; i < notifications.length; i += 1) {
      const notification = notifications[i];
      const oplog = new OpLog({
        name: 'WixReviews',
        accountId: notification.accountId,
        resourceId: notification._id,
        resource: notification,
      });
      const {
        error,
        reviews,
      } = await wixService.handleReviews(notification);
      if(error) {
        taskResults.errors += 1;
        oplog.error = error;
      } else {
        taskResults.reviews += reviews;
        taskResults.success += 1;
        oplog.result = { reviews };
      }
      await oplog.safeSave();
    }
  } else {
    logger.info('no notifications to process');
  }
  return taskResults;
}
