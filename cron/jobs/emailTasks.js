/* eslint-disable no-await-in-loop */
const _ = require('lodash');
const config = require('../../config');
const { sleep } = require('../../lib/utils');
const dateUtils = require('../../lib/utils/dateUtils');
const Account = require('../../app/account/models/Account');
const accountService = require('../../app/account/account.service');
const SubAccount = require('../../app/account/models/SubAccount');
const IPN = require('../../app/billing/bluesnap/IPNModel');
const emailAutomation = require('../../app/common/email.automation.service');
const mailerService = require('../../app/common/mailer.service');
const notifierService = require('../../app/common/notifier.service');
const mailshake = require('../../lib/apis/Mailshake').getClient(config.mailshake.apiKey);

module.exports = {
  updateEmailAutomationFields,
  delinquentChurnReport,
  chargeFailedReport,
  reviewRequest,
};

async function reviewRequest() {
  const results = { errors: [], sent: [] };
  const startDate = dateUtils.todayNormalized12am() - dateUtils.MILLISECONDS_IN_DAY * 3;
  const endDate = startDate + dateUtils.MILLISECONDS_IN_DAY;
  // $gte + $lt search in array of objects is broken into two queries, returning wrong results
  // must use $elemMatch
  const [wixAccounts, shopifyAccounts] = await Promise.all([
    Account.find({
      'stats.sentEmails.reviewRequest': null,
      wix: {
        $elemMatch: { installedDate: { $gte: startDate, $lt: endDate } },
      },
    }),
    Account.find({
      'stats.sentEmails.reviewRequest': null,
      shopify: {
        $elemMatch: { installed: { $gte: startDate, $lt: endDate } },
      },
    }),
  ]);

  await Promise.all(wixAccounts.map((account) => {
    const platform = 'wix';
    return accountService.sendReviewRequest({ account, platform }).then(() => {
      results.sent.push({ email: account.email, platform });
    }).catch((err) => {
      results.errors.push({ email: account.email, platform, err });
    });
  }));
  await Promise.all(shopifyAccounts.map((account) => {
    const platform = 'shopify';
    return accountService.sendReviewRequest({ account, platform }).then(() => {
      results.sent.push({ email: account.email, platform });
    }).catch((err) => {
      results.errors.push({ email: account.email, platform, err });
    });
  }));
  return results;
}

async function updateEmailAutomationFields({ query = null, accountId = null, email = null } = {}) {
  const BATCH_SIZE = 30;
  const results = { ops: 0, errors: 0, statuses: {} };

  for(let i = 0; i < 35; i += 1) {
    const findQuery = { ...query };
    if(accountId) {
      findQuery.accountId = accountId;
    }
    if(email) {
      findQuery.email = email;
    }
    const accounts = await Account.find(findQuery, {
      email: 1,
      stats: 1,
      subscription: 1,
      hosts: 1,
      onboarding: 1,
      installed: 1,
      location: 1,
      shopify: 1,
      wordpress: 1,
      wix: 1,
      bigcommerce: 1,
      thinkific: 1,
      createdAt: 1,
    }).sort({ updatedAt: -1 })
      .limit(BATCH_SIZE)
      .skip(i * BATCH_SIZE);
    const updates = [];
    accounts.forEach((account) => {
      const subscription = account.subscriptionInfo();
      let createdNotification = _.get(account, 'stats.notifications.lastCreated', null);
      if(!createdNotification && _.get(account, 'stats.createdNotifications')) {
        createdNotification = account.createdAt;
      }
      updates.push({
        email: account.email,
        fields: {
          recent_ipn: (subscription && subscription.recentIPN) || 'none',
          plan: (subscription && subscription.plan) || 'free',
          last_client_request: _.get(account, 'stats.lastClientRequest', null),
          last_impression: _.get(account, 'stats.notifications.lastImpression', null),
          logged_in: account.lastSeen || null,
          country: _.get(account, 'location.country'),
          city: _.get(account, 'location.city'),
          created_notification: createdNotification,
          installed_code: account.installed
            || _.get(account, 'onboarding.installed', null)
            || (account.hosts && account.hosts.length && new Date()),
          shopify: account.shopify && account.shopify.length ? 'true' : 'false',
          wordpress: account.wordpress && account.wordpress.length ? 'true' : 'false',
          bigcommerce: account.bigcommerce && account.bigcommerce.length ? 'true' : 'false',
          thinkific: account.thinkific && account.thinkific.length ? 'true' : 'false',
        },
      });
    });
    const response = await emailAutomation.batchUpdate(updates);
    if(response && response.body) {
      response.body.forEach((res) => {
        results.ops += 1;
        if(!results.statuses[res.status]) {
          results.statuses[res.status] = 0;
        }
        results.statuses[res.status] += 1;
        if(res.body && res.body.error) {
          results.errors += 1;
        }
      });
    }

    // Break early if we got fewer accounts than expected (end of data)
    if(accounts.length < BATCH_SIZE) {
      break;
    }

    // Sleep between batches (except for the last iteration)
    if(i < 34) {
      await sleep(30000);
    }
  }
  return results;
}

async function delinquentChurnReport({ startDate, endDate } = {}) {
  const sDate = new Date(startDate || dateUtils.todayNormalized12am() - 86400 * 1000);
  const eDate = new Date(endDate || dateUtils.todayNormalized12am());
  const ipns = await IPN.find({
    createdAt: { $gte: sDate, $lt: eDate },
    type: 'CANCELLATION',
    'data.cancelReason': 'CANCELLED_DUE_TO_UNPAID',
    // I think it's not relevant because empty means it was "no auto renewal"
    // 'data.cancelReason': { $in: ['', 'CANCELLED_DUE_TO_UNPAID'] },
  });
  const accounts = (await Account.find({ _id: { $in: ipns.map(ipn => ipn.accountId) } }))
    .filter(account => !account.isSubscriptionActive());
  if(!accounts || !accounts.length) {
    return { accounts: 0 };
  }
  const emails = [];
  let html = '<ol>';
  html += accounts.map((account) => {
    const { subscription } = account;
    let line = '<li style="margin-bottom: 5px;">';
    const email = account.email || account.parent.email;
    emails.push(email);
    line += `${email} was ${subscription.plan} ${subscription.period}`;
    if(account.parent && account.name) {
      line += ` (SubAccount ${account.name} accountId: ${account.id})`;
    } else {
      line += ` (accountId: ${account.id})`;
    }
    line += '</li>';
    return line;
  }).join(' ');
  html += '</ol>';
  // await mailshake.add({ campaignId: 861412, emails: emails.join(',') }).catch((err) => {
  //   notifierService.notifyError(err, 'failed to add delinquent emails to mailshake campaign', { emails });
  // });
  await mailerService.sendAdminEmail({
    to: ['<EMAIL>'],
    subject: `Delinquent Churn ${dateUtils.getISODate(sDate)} - ${dateUtils.getISODate(eDate)}`,
    html,
  });
  return { accounts: accounts.length };
}

async function chargeFailedReport({ startDate, endDate } = {}) {
  const sDate = new Date(startDate || dateUtils.todayNormalized12am() - 86400 * 1000);
  const eDate = new Date(endDate || dateUtils.todayNormalized12am());
  const accounts = await Account.find({
    'subscription.recentIPN': 'CC_CHARGE_FAILED',
    'subscription.created': { $gte: sDate, $lt: eDate },
  });
  if(!accounts || !accounts.length) {
    return { accounts: 0 };
  }
  const emails = [];
  let html = '<ol>';
  for(let i = 0; i < accounts.length; i += 1) {
    const account = accounts[i];
    const { email } = account;
    emails.push(email);
    if(!account.isSubscriptionActive()) {
      html += `<li style="margin-bottom: 5px;">${email}\t${dateUtils.getISODate(account.subscription.created)}</li>`;
    }
  }
  html += '</ol>';
  if(html.length < 10) {
    return { accounts: 0 };
  }
  // await mailshake.add({ campaignId: 861417, emails: emails.join(',') }).catch((err) => {
  //   notifierService.notifyError(err, 'failed to add charge failed emails to mailshake campaign', { emails });
  // });
  await mailerService.sendAdminEmail({
    to: ['<EMAIL>'],
    subject: `Charge Failed Report ${dateUtils.getISODate(sDate)} - ${dateUtils.getISODate(eDate)}`,
    html,
  });
  return { accounts: accounts.length };
}
