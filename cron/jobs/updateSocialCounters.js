/* eslint-disable no-await-in-loop */
const _ = require('lodash');
const socialNotifService = require('../../app/notifications/social.notification.service');
const socialController = require('../../app/social/social.service');
const { dateUtils, sleep } = require('../../lib/utils');
const mailerService = require('../../app/common/mailer.service');
const OpLog = require('../models/OpLog');

module.exports = {
  updateSocialCounters,
};

async function updateSocialCounters(notificationId = null) {
  const taskResults = {
    facebook: {
      processed: 0,
      success: 0,
      failed: 0,
      errors: {},
    },
    instagram: {
      processed: 0,
      success: 0,
      failed: 0,
      errors: {},
    },
    twitter: {
      processed: 0,
      success: 0,
      failed: 0,
      errors: {},
    },
    youtube: {
      processed: 0,
      success: 0,
      failed: 0,
      errors: {},
    },
  };
  const notifications = await socialNotifService.getNotifications(notificationId);

  for(let i = 0; i < notifications.length; i += 1) {
    const notification = notifications[i];
    const oplog = new OpLog({
      name: 'SocialCounterUpdate',
      accountId: notification.accountId,
      resourceId: notification.id,
      resource: notification,
    });
    try {
      const results = await socialController.updateSocialCounters(notification);
      // key name and order is important
      const keys = ['facebook', 'twitter', 'instagram', 'youtube'];
      for(let j = 0; j < keys.length; j += 1) {
        const platform = keys[j];
        const result = results[platform];
        if(result) {
          taskResults[platform].processed += 1;
          if(result instanceof Error) {
            oplog.error[platform] = result;
            taskResults[platform].failed += 1;
            if(!taskResults[platform].errors[result.message]) {
              taskResults[platform].errors[result.message] = 0;
            }
            taskResults[platform].errors[result.message] += 1;
          } else {
            oplog.result[platform] = result;
            taskResults[platform].success += 1;
          }
        }
      }
    } catch(err) {
      oplog.error = err;
    }
    oplog.safeSave();
    // facebook widget rate limit
    if(_.get(notification, 'profiles.facebook.id', null)) {
      await sleep(5 * 1000);
    }
  }
  return taskResults;
}
