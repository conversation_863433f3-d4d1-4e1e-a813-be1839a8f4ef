const LoggerFactory = require('../../lib/logger/LoggerFactory');

const logger = LoggerFactory('cron/yotpoReviews');
const lib = require('../../lib/apis/yotpo');

const YotpoReview = require('../../app/reviews/models/YotpoReview');
const Feed = require('../../app/account/models/Feed');
const OpLog = require('../models/OpLog');

const yotpoReviewsTask = async (notifications) => {
  const taskSummary = {
    reviews: 0,
    success: 0,
    errors: 0,
  };

  for(let i = 0; i < notifications.length; i++) {
    const notification = notifications[i];
    const oplog = new OpLog({
      name: 'YoptoReviews',
      accountId: notification.accountId,
      resourceId: notification._id,
      resource: notification,
    });
    const { accountId, placeId } = notification;
    const token = placeId;
    try {
      if(!placeId) {
        logger.error({ notification }, 'no placeId for notification');
        continue;
      }

      const results = await lib.getReviews({ token, per_page: 50 }).catch((err) => {
        logger.error({ err, token }, 'an error occurred fetching yotpo reviews');
      });

      if(!results || !results.length) {
        continue;
      }

      const reformatedResults = await reformatResults(results, notification);
      const reviews = await YotpoReview.insertMany(reformatedResults, { ordered: false })
        .catch((err) => {
          logger.error({ err }, 'failed to insert yotpo reviews');
        });

      reformatedResults.forEach((result) => {
        const feedData = {
          yotpoAppId: placeId,
          reviewId: result.reviewId,
          author: result.authorName,
          text: result.text,
          rating: result.rating,
          time: result.time,
          source: 'yotpo',
        };

        Feed.saveFeed(accountId, `Yotpo Review | ${feedData.author}`, feedData);
      });
      taskSummary.reviews += reformatedResults.length;
      taskSummary.success += 1;

      logger.info({ reviews, reformatedResults, notification }, 'inserted reviews for notification');
      oplog.result = { reviews };
    } catch(error) {
      oplog.error = error;
      if(error.name === 'BulkWriteError' && error.code === 11000) {
        logger.info({ error, notification }, 'attempted to insert duplicated review(s)');
      } else {
        logger.error({ error, notification }, 'an error occurred in processing a notification');
      }
      taskSummary.errors += 1;
    }
    await oplog.safeSave();
  }
  return taskSummary;
};

const reformatResults = async (results, notification) => {
  const latestReview = await YotpoReview
    .find({ accountId: notification.accountId, yotpoAppId: notification.placeId })
    .sort({ time: -1 })
    .limit(1)
    .then(reviews => reviews[0]);

  return results.map((result) => {
    if(latestReview && latestReview.time && latestReview.time.getTime() >= result.time) {
      return null;
    }
    result.accountId = notification.accountId;
    return result;
  }).filter(r => r != null);
};

module.exports = {
  yotpoReviewsTask,
  reformatResults,
};
