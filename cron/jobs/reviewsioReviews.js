const LoggerFactory = require('../../lib/logger/LoggerFactory');

const logger = LoggerFactory('cron/reviewsioReviews');
const lib = require('../../lib/apis/reviewsio');

const ReviewsioReview = require('../../app/reviews/models/ReviewsioReview');
const Feed = require('../../app/account/models/Feed');
const OpLog = require('../models/OpLog');

const reviewsioReviewsTask = async (notifications) => {
  const taskSummary = {
    reviews: 0,
    success: 0,
    errors: 0,
  };
  for(let i = 0; i < notifications.length; i++) {
    const notification = notifications[i];
    const oplog = new OpLog({
      name: 'ReviewioReviews',
      accountId: notification.accountId,
      resourceId: notification._id,
      resource: notification,
    });
    const { accountId, placeId } = notification;
    const store = notification.placeId;

    try {
      if(!placeId) {
        logger.error({ notification }, 'no placeId for notification');
        continue;
      }

      const results = await lib.getReviews({
        store, min_rating: 4, order: 'desc', per_page: 10,
      }).catch((err) => {
        logger.error({ err, store }, 'an error occurred fetching reviews');
      });

      if(!results || !results.length) {
        continue;
      }

      const reformatedResults = await reformatResults(results, notification);
      const reviews = await ReviewsioReview.insertMany(reformatedResults, { ordered: false })
        .catch((err) => {
          logger.error({ err }, 'failed to insert reviews.io reviews');
        });

      reformatedResults.forEach((result) => {
        const feedData = {
          store,
          storeName: result.storeName,
          author: result.authorName,
          text: result.text,
          rating: result.rating,
          reviewId: result.reviewId,
          time: result.time,
          source: 'reviewsio',
        };

        const msg = `Reviews.io Review | ${feedData.storeName} (${feedData.author})`;
        Feed.saveFeed(accountId, msg, feedData);
      });

      taskSummary.reviews += reformatedResults.length;
      taskSummary.success += 1;

      logger.info({ reviews, reformatedResults, notification }, 'inserted reviews for notification');
      oplog.result = { reviews };
    } catch(error) {
      oplog.error = error;
      if(error.name === 'BulkWriteError' && error.code === 11000) {
        logger.info({ error, notification }, 'attempted to insert duplicated review(s)');
      } else {
        logger.error({ error, notification }, 'an error occurred in processing a notification');
      }
      taskSummary.errors += 1;
    }
    await oplog.safeSave();
  }
  return taskSummary;
};

const reformatResults = async (results, notification) => {
  const latestReview = await ReviewsioReview
    .find({ accountId: notification.accountId, storeId: notification.placeId })
    .sort({ time: -1 })
    .limit(1)
    .then(reviews => reviews[0]);

  return results.map((result) => {
    if(latestReview && latestReview.time && latestReview.time.getTime() >= result.time) {
      return null;
    }
    result.accountId = notification.accountId;
    return result;
  }).filter(r => r != null);
};

module.exports = {
  reviewsioReviewsTask,
  reformatResults,
};
