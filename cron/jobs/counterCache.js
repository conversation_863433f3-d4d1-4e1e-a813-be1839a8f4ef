/* eslint-disable no-await-in-loop */
const config = require('../../config');
const dbLoader = require('../../lib/mongooseLoader');
const logger = require('../../lib/logger').getLogger('counter.cache');
const Notification = require('../../app/notifications/models/Notification');
const Combo = require('../../app/notifications/models/Combo');
const PageVisits = require('../../app/notifications/models/PageVisits');
const Conversion = require('../../app/notifications/models/Conversion');
const notifConstants = require('../../app/notifications/constants');
const counterProcessor = require('../../app/notifications/get/processNotification');
const eventFinder = require('../../app/notifications/get/eventFindQuery');
const CounterCache = require('../../app/notifications/models/CounterCache');

if(module === require.main) {
  dbLoader.load(config.mongodb.url).then(() => {
    cache();
  });
}

module.exports = {
  cache,
};

async function cache() {
  const results = { failed: 0, cached: 0, notCached: 0 };
  const { notificationTypes } = notifConstants;
  const counterTypes = [
    notificationTypes.pageVisits,
    notificationTypes.conversion,
    notificationTypes.combo,
  ];
  const notifications = await Notification.find({
    active: true,
    type: { $in: counterTypes },
    lastView: { $gte: Date.now() - 3600 * 1000 },
  }).cache(180);
  results.notifications = notifications.length;
  for(let i = 0; i < notifications.length; i += 1) {
    const notification = notifications[i];
    const notificationId = notification.id;
    const {
      query, sort, model, minimumDay, countToMinute,
    } = eventFinder(notification);
    await CounterCache.updateOne({ notificationId }, { inProgress: true }, { upsert: true });
    await model.find(query, null)
      .sort(sort)
      .maxTime(notifConstants.maxQueryMS)
      .hint(model.getHint())
      .limit(notifConstants.maxEvents)
      .then((events) => {
        // cache only big notifications to avoid long cache updates for small notifications
        if(!events || events.length < 50) {
          return CounterCache.remove({ notificationId });
        }
        const result = counterProcessor.processEventsForNotification(notification, events, {
          countToMinute, minimumDay,
        });
        return CounterCache.updateOne({ notificationId }, {
          inProgress: false, result,
        }, { upsert: true });
      })
      .then((cacheRes) => {
        if(cacheRes) {
          results.cached += 1;
        } else {
          results.notCached += 1;
        }
      })
      .catch((err) => {
        results.failed += 1;
        logger.error({ err, notificationId }, 'failed to process counter cache for notification');
      });
  }
  return results;
}
