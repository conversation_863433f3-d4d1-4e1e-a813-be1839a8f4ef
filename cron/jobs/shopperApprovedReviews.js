/* eslint-disable no-await-in-loop */
const shopperApprovedService = require('../../app/reviews/shopperApproved.service');
const logger = require('../../lib/logger/LoggerFactory')('cron/shopperApprovedReviewsTask');
const Review = require('../../app/notifications/models/Review');
const { reviewSources } = require('../../app/notifications/constants');
const OpLog = require('../models/OpLog');

module.exports = {
  shopperApprovedReviewsTask,
};

async function shopperApprovedReviewsTask() {
  const taskResults = {
    reviews: 0,
    success: 0,
    failed: 0,
    errors: {},
  };

  const notifications = await Review.find(
    {
      source: reviewSources.shopperapproved,
      stopAutoSync: { $ne: true },
    },
  );
  if(notifications.length > 0) {
    logger.info({ notifications }, 'processing notifications');

    for(let i = 0; i < notifications.length; i += 1) {
      const notification = notifications[i];
      const oplog = new OpLog({
        name: 'ShopperApprovedReviews',
        accountId: notification.accountId,
        resourceId: notification._id,
        resource: notification,
      });
      const { error, reviews } = await shopperApprovedService.handleReviews(notification);
      if(error) {
        taskResults.failed += 1;
        taskResults.errors[error.message] = (taskResults.errors[error.message] + 1) || 1;
        oplog.error = error;
      } else {
        taskResults.reviews += reviews;
        taskResults.success += 1;
        oplog.result = { reviews };
      }
      await oplog.safeSave();
    }
  } else {
    logger.info('no notifications to process');
  }
  return taskResults;
}
