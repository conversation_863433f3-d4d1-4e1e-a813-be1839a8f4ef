/* eslint-disable max-len */
const _ = require('lodash');
const fs = require('fs').promises;
const moment = require('moment');
const Notification = require('../../app/notifications/models/Notification');
const Account = require('../../app/account/models/Account');
const SubAccount = require('../../app/account/models/SubAccount');
const dateUtils = require('../../lib/utils/dateUtils');

async function installedNoImpressionsReport({ startDate, endDate } = {}) {
  const sDate = new Date(startDate || dateUtils.todayNormalized12am());
  const eDate = new Date(endDate || sDate.getTime() + 86400 * 1000);
  const lastImpressionDate = moment(eDate).add(-14, 'day').toDate();
  const accounts = await Account.find({
    'subscription.period': { $ne: 'lifetime' },
    'stats.lastClientRequest': { $gte: sDate, $lte: eDate },
    'stats.notifications.lastImpression': { $lte: lastImpressionDate },
  }).select('email parent name stats subscription');
  if(!accounts) {
    return { accounts: 0 };
  }
  const notifications = await Notification.aggregate([
    // eslint-disable-next-line no-underscore-dangle
    { $match: { active: true, accountId: { $in: accounts.map(account => account._id) } } },
    { $group: { _id: '$accountId', notifications: { $sum: 1 } } },
  ]);
  const rows = ['accountId, email/subaccount, plan, period, last request, last impression'];
  accounts.forEach((account) => {
    // eslint-disable-next-line no-underscore-dangle
    const accountNotifications = notifications.find(n => n._id.toString() === account.id);
    const numNotifications = accountNotifications && accountNotifications.notifications;
    if(numNotifications > 0) {
      const subscription = account.subscriptionInfo();
      const lastReqDate = dateUtils.getISODate(_.get(account, 'stats.lastClientRequest')) || 'N/A';
      const lastImpDate = dateUtils.getISODate(_.get(account, 'stats.notifications.lastImpression')) || 'N/A';
      rows.push(`${account.id}, ${account.email || account.name}, ${subscription.plan}, ${subscription.period}, ${lastReqDate}, ${lastImpDate}`);
    }
  });
  await fs.writeFile('./active-no-impressions.csv', rows.join('\n'));
  // let html = '<ol>';
  // html += accounts.map((account) => {
  //   const subaccountName = account.parent && account.name;
  //   const subscription = account.subscriptionInfo();
  //   let line = '<li style="margin-bottom: 7px;">';
  //   line += `${account.email || subaccountName.name} on ${subscription.plan} ${subscription.period} plan`;
  //   line += ` (accountId ${account.id})`;
  //   const lastReqDate = dateUtils.getISODate(_.get(account, 'stats.lastClientRequest')) || 'N/A';
  //   const lastImpDate = dateUtils.getISODate(_.get(account, 'stats.notifications.lastImpression')) || 'N/A';
  //   line += `, last request: ${lastReqDate}, last impression: ${lastImpDate}`;
  //   line += '</li>';
  //   return line;
  // }).join(' ');
  // await mailerService.sendAdminEmail({
  //   to: '<EMAIL>',
  //   subject: `Installed no notification impressions ${dateUtils.getISODate(sDate)} - ${dateUtils.getISODate(eDate)}`,
  //   html,
  // });
  return { accounts: accounts.length };
}
