const mailerService = require('../../app/common/mailer.service');
const accountService = require('../../app/account/account.service');
const Account = require('../../app/account/models/Account');
const OpLog = require('../models/OpLog');

module.exports = {
  sendSummaryEmails,
};

async function sendSummaryEmails() {
  const results = { sent: 0, failed: 0 };
  const query = await accountService.getQuerySummaryEmail();
  const batchSize = 100;
  let iteration = 0;
  let accounts;
  do {
    accounts = await Account.find(query).limit(batchSize).skip(iteration * batchSize)
      .select('-transactions -affiliate -metrics');
    const promises = [];
    for(let i = 0; i < accounts.length; i += 1) {
      const account = accounts[i];
      const oplog = new OpLog({
        name: 'summaryEmail', accountId: account.id, resource: account,
      });
      const bcc = i % 1000 === 0;
      promises.push(accountService.sendSummaryEmail(account, { bcc }).then((res) => {
        oplog.result = (res && res[0] && res[0].toJSON()) || { success: true };
        results.sent += 1;
        oplog.safeSave();
      }).catch((err) => {
        oplog.error = err;
        results.failed += 1;
        oplog.safeSave();
      }));
    }
    await Promise.all(promises);
    iteration += 1;
  } while(accounts.length === batchSize);
  return results;
}
