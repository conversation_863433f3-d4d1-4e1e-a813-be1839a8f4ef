const _ = require('lodash');
const Account = require('../../app/account/models/Account');
require('../../app/account/models/SubAccount');
const Notification = require('../../app/notifications/models/Notification');
const { dateUtils } = require('../../lib/utils');
const getLimitInfo = require('../../app/visitorCount/getLimitInfo');
const Bill = require('../../app/billing/Bill');

(async () => {
  require('../../lib/mongooseLoader').load('mongodb://localhost:8080/proofsrc-prod');
  await updateUpgradableAccounts();
  process.exit(0);
})();

async function updateUpgradableAccounts() {
  // await Account.updateMany({ 'backoffice.upgradable': true }, { 'backoffice.upgradable': false });
  let upgradableAccounts = 0;
  const query = {
    active: true,
    email: { $not: /DELETED/ },
    'subscription.recentIPN': { $in: ['CANCELLATION', null] },
    createdAt: { $lt: Date.now() - dateUtils.MILLISECONDS_IN_DAY * 30 },
    'onboarding.installed': { $ne: null },
  };
  let iteration = 0;
  let scanned = 0;
  const batch = 100;
  let accounts;
  /* eslint-disable no-await-in-loop */
  const ids = [];
  do {
    accounts = await Account.find(query)
      .select('email subscription createdAt location parent')
      .limit(batch)
      .skip(iteration * batch);
    if(iteration === 0) {
      console.log('#,created,email,id,limit,visitors,country,cancellation date');
    }
    const statsPromises = [];
    if(accounts.length === 0) {
      break;
    }
    scanned += accounts.length;
    for(let i = 0; i < accounts.length; i += 1) {
      const account = accounts[i];
      const accountId = account.id;
      const dayAgo = Date.now() - 24 * dateUtils.MILLISECONDS_IN_HOUR;
      statsPromises.push(Promise.all([
        Notification.count({ accountId, active: true, lastView: { $gt: dayAgo } }),
        getLimitInfo(accountId),
        Bill.findOne({ accountId })
          .sort({ _id: -1 })
          .skip(1)
          .limit(1),
      ]));
    }
    const promiseResults = await Promise.all(statsPromises);
    // const ids = [];
    for(let i = 0; i < promiseResults.length; i += 1) {
      const account = accounts[i];
      const [seenNotifications, limitInfo, lastBill] = promiseResults[i];
      if(seenNotifications > 0
        && limitInfo && lastBill && lastBill.total > limitInfo.limit * 1.2) {
        upgradableAccounts += 1;
        ids.push(account.id);
        const untilDate = _.get(account, 'subscription.untilDate', null);
        console.log([
          ids.length,
          dateUtils.getISODate(account.createdAt),
          account.email || account.parent.email,
          account.id,
          limitInfo.limit,
          lastBill.total,
          _.get(account, 'location.country', 'N/A'),
          (untilDate && dateUtils.getISODate(untilDate)) || 'N/A',
        ].join(','));
      }
    }
    // await Account.updateMany({ _id: { $in: ids } }, { 'backoffice.upgradable': true });
    iteration += 1;
  } while(accounts.length === batch);
  console.log('scanned', scanned);
  /* eslint-enable no-await-in-loop */
  return { upgradableAccounts };
}

module.exports = updateUpgradableAccounts;
