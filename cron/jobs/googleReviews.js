const path = require('path');
const _ = require('lodash');
const chrono = require('chrono-node');

const config = require('../../config');
const LoggerFactory = require('../../lib/logger/LoggerFactory');
const constants = require('../../app/notifications/constants');
const GoogleReview = require('../../app/reviews/models/GoogleReview');
const ReviewNotification = require('../../app/notifications/models/Review');
const Feed = require('../../app/account/models/Feed');
const lib = require('../../lib/apis/googleReviews');
const OpLog = require('../models/OpLog');

const logger = LoggerFactory('cron/tasks/reviews');

const reformatResults = async (results, placeId, accountId) => {
  const savedReviews = await GoogleReview
    .find({ accountId, placeId })
    .select('reviewId -_id')
    .lean();
  return results.map((result) => {
    const reviewId = `${accountId}-${placeId}-${result.authorName}`;
    const isReviewExist = _.some(savedReviews, elem => elem.reviewId === reviewId);
    if(isReviewExist) {
      return null;
    }
    // resize photo link to 100X100 pic
    // e.g. photo url "https://lh3.googleusercontent.com/-nRDzJbJEfI8/AAAAAAAAAAI/AAAAAAAAAAA/3WLFUIXJG5I/s40-c-rp-mo-br100/photo.jpg"
    let profilePhotoUrl;
    if(result.authorPhoto) {
      if(result.authorPhoto.indexOf('s40') > -1) {
        profilePhotoUrl = result.authorPhoto && result.authorPhoto.replace('s40', 's100');
      } else if(result.authorPhoto.indexOf('w36-h36') > -1) {
        profilePhotoUrl = result.authorPhoto && result.authorPhoto.replace('w36-h36', 'w100-h100');
      }
    }
    return {
      placeId,
      accountId,
      reviewId,
      time: (result.timeText && chrono.parseDate(result.timeText)) || new Date(),
      businessName: result.businessName,
      authorName: result.authorName,
      profilePhotoUrl,
      relativeTimeDescription: result.timeText,
      authorUrl: result.authorUrl,
      text: result.text,
      rating: result.rating,
    };
  }).filter(r => r != null);
};

const googleReviewsTask = async (notifications, { locker = null, jobName } = {}) => {
  const taskSummary = {
    notifications: notifications.length,
    reviews: 0,
    success: 0,
    noReviews: 0,
    noReviewsAfterFilter: 0,
    anomalies: {},
    failed: 0,
    errors: {},
  };

  for(let i = 0; i < notifications.length; i += 1) {
    const notificationUpdate = {};
    const notification = notifications[i];
    const { accountId, placeId, _id: notificationId } = notification;
    if(locker && jobName) {
      locker.lock(jobName, 60);
    }
    logger.info({ notificationId, idx: `${i + 1}/${notifications.length}` }, 'processing reviews notification');
    const oplog = new OpLog({
      name: 'GoogleReviews',
      accountId,
      resourceId: notification.id,
      resource: notification,
    });
    try {
      if(!placeId) {
        logger.error({ notification }, 'no placeId for notification');
        continue;
      }
      notificationUpdate['stats.lastAttempt'] = Date.now();
      const random = Math.trunc(Math.random() * config.proxies.length + 1); // add +1 for "no proxy"
      let proxy = null;
      if(random < config.proxies.length) {
        proxy = config.proxies[random];
      }
      oplog.meta = { proxy };
      // eslint-disable-next-line no-await-in-loop
      const results = await lib.getScrapedReviews(placeId, {
        executable: config.googlePlaces.executable,
        headless: config.isProduction(),
        proxy,
        screenshotFolder: path.join(__dirname, '../screenshots'),
        lowestRating: 4,
        maximumReviews: _.get(notification, 'settings.maxReviewsFetch', 30),
        captchaApiKey: config.googlePlaces.captchaToken,
      }).catch((err) => {
        throw err;
      });
      if(results.length === 0) {
        logger.info({ notification }, 'no reviews for notification returned from google');
        taskSummary.noReviews += 1;
        oplog.result = { message: 'no reviews' };
        oplog.safeSave();
        continue;
      }

      // eslint-disable-next-line no-await-in-loop
      const reformatedResults = await reformatResults(results, placeId, accountId);
      if(reformatedResults.length === 0) {
        logger.info({ notification }, 'no reviews for notification after filtering / processing');
        taskSummary.noReviewsAfterFilter += 1;
        oplog.result = { message: 'no new reviews' };
      } else {
        reformatedResults.forEach((review) => {
          Object.entries(review).some(([k, v]) => {
            if(!v) {
              taskSummary.anomalies[k] = (taskSummary.anomalies[k] += 1) || 1;
            }
            return !v;
          });
        });
        // eslint-disable-next-line no-await-in-loop,max-len
        const reviews = await GoogleReview.insertMany(reformatedResults, { ordered: false }).catch((err) => {
          logger.error({ err }, 'failed to insert google reviews');
        });
        oplog.result = [];
        reformatedResults.forEach((result) => {
          const feedData = {
            placeId,
            businessName: result.businessName,
            reviewId: result.reviewId,
            author: result.authorName,
            text: result.text,
            rating: result.rating,
            time: result.time,
            source: constants.reviewSources.google,
          };
          oplog.result.push(feedData);

          const message = `Google Review | ${feedData.businessName} (${feedData.author})`;
          Feed.saveFeed(accountId, message, feedData);
        });
        logger.info({ reviews, reformatedResults, notification }, 'inserted reviews for notification');
      }
      notificationUpdate['stats.lastFetch'] = Date.now();
      notificationUpdate['stats.consecutiveFailures'] = 0;
      taskSummary.reviews += reformatedResults.length;
      taskSummary.success += 1;
    } catch(error) {
      if(error.name === 'BulkWriteError' && error.code === 11000) {
        logger.info({ error, notification }, 'attempted to insert duplicated review(s)');
      } else {
        logger.error({ error, notification }, 'an error occurred in processing a notification');
      }
      oplog.error = error;
      notificationUpdate['stats.lastError'] = Date.now();
      const sanitizedErrorMessage = error.message.replace(/\./g, '|');
      notificationUpdate.$inc = {
        'stats.consecutiveFailures': 1,
        [`stats.errors.${sanitizedErrorMessage}`]: 1,
      };
      taskSummary.failed += 1;
      const normalizedErr = error.message.includes(' at ') ? error.message.split(' at ')[0] : error.message;
      if(!taskSummary.errors[normalizedErr]) {
        taskSummary.errors[normalizedErr] = [];
      }
      taskSummary.errors[normalizedErr].push(placeId);
    }
    ReviewNotification.updateOne({ _id: notification.id }, notificationUpdate).catch(() => {});
    oplog.safeSave();
  }
  return taskSummary;
};

module.exports = {
  reformatResults,
  googleReviewsTask,
};
