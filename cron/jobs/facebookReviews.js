const _ = require('lodash');

const LoggerFactory = require('../../lib/logger/LoggerFactory');
const lib = require('../../lib/apis/facebook');
const digitalOceanSpaces = require('../../lib/apis/digitalOceanSpaces');
const constants = require('../../app/notifications/constants');
const config = require('../../config');
const { md5 } = require('../../lib/utils/cryptoUtils');
const FacebookReview = require('../../app/reviews/models/FacebookReview');
const accountService = require('../../app/account/account.service');
const Feed = require('../../app/account/models/Feed');
const slack = require('../../lib/apis/slackNotifier');
const OpLog = require('../models/OpLog');

const logger = LoggerFactory('cron/tasks/facebookReviews');

const reformatResults = async (results, notification) => {
  const { accountId, placeId: pageId } = notification;
  const latestReview = await FacebookReview
    .find({ accountId, pageId })
    .sort({ time: -1 })
    .limit(1)
    .then(latest => latest[0]);

  return results
    .map((r) => {
      const time = new Date(r.created_time);
      const isPositive = r.recommendation_type === 'positive';

      if(latestReview && latestReview.time && latestReview.time >= time) return null;
      if(!isPositive) return null;

      const reviewer = r.reviewer || {};
      const reviewId = md5(`${accountId}-${pageId}-${r.created_time}-${r.review_text}`);
      return {
        pageId,
        accountId,
        text: r.review_text,
        rating: 5,
        authorName: reviewer.name,
        authorId: reviewer.id,
        businessName: notification.pageName,
        reviewId,
        time,
      };
    })
    .filter(r => r != null);
};

const uploadImage = async (token, reviewerId) => {
  if(!reviewerId) return Promise.reject(Error('invalid reviewerId'));

  const options = { access_token: token, type: 'large' };
  const picture = await lib.getUserPicture(reviewerId, options);

  if(!picture) {
    return Promise.reject(Error('no fb user picture'));
  }

  return digitalOceanSpaces.uploadFile({
    accessKey: config.digitalOcean.spaces.accessKey,
    secret: config.digitalOcean.spaces.secret,
    bucket: config.digitalOcean.spaces.bucket,
    body: picture,
    path: `images/${reviewerId}.jpeg`,
    contentType: 'image/jpeg',
  });
};

const facebookReviewsTask = async (notifications) => {
  const taskSummary = {
    processed: 0,
    reviews: 0,
    success: 0,
    noReviews: 0,
    noNewReviews: 0,
    totalErrors: 0,
    errors: {
      apiErrors: {},
      placeId: 0,
      insertMany: 0,
      uploadImage: 0,
    },
  };
  for(let i = 0; i < notifications.length; i += 1) {
    const notification = notifications[i];
    taskSummary.processed += 1;
    const oplog = new OpLog({
      name: 'FacebookReviews',
      accountId: notification.accountId,
      resourceId: notification.id,
      resource: notification,
    });
    try {
      if(!notification.placeId) {
        logger.error({ notification }, 'no pageId (placeId) for notification');
        taskSummary.errors.placeId += 1;
        continue;
      }

      const results = await lib.getRatings(
        notification.placeId,
        { access_token: notification.pageToken },
      );
      if(results.data.length === 0) {
        logger.info({ notification }, 'no reviews for notification returned from facebook');
        taskSummary.noReviews += 1;
        continue;
      }

      const reformatedResults = await reformatResults(results.data, notification);
      if(reformatedResults.length === 0) {
        logger.info({ notification }, 'no reviews for notification after filtering / processing');
        taskSummary.noNewReviews += 1;
        continue;
      }

      const reviews = await FacebookReview.insertMany(reformatedResults, { ordered: false })
        .catch((err) => {
          taskSummary.errors.insertMany += 1;
          logger.warn({ err }, 'insert facebook reviews failed');
        });
      const uploadPromises = [];
      reformatedResults.forEach((result) => {
        const feedData = {
          reviewId: result.reviewId,
          pageId: notification.placeId,
          page: notification.pageName,
          text: result.text,
          rating: result.rating,
          time: result.time,
          source: constants.reviewSources.facebook,
          author: result.authorName,
        };

        let message = `Facebook Review | ${notification.pageName}`;
        if(result.authorName) message += ` (${result.authorName})`;
        Feed.saveFeed(notification.accountId, message, feedData);
        uploadPromises.push(uploadImage(notification.pageToken, result.authorId).catch((err) => {
          taskSummary.errors.uploadImage += 1;
          const log = 'failed uploading facebook images or storing results';
          slack.notifyError(err, log, { result, notification });
          logger.error({ err, result, notification }, log);
        }));
      });

      await Promise.all(uploadPromises);

      oplog.result = reformatedResults;
      taskSummary.reviews += reformatedResults.length;
      taskSummary.success += 1;
      logger.info({ reviews, reformatedResults, notification }, 'inserted reviews for notification');
    } catch(error) {
      logger.error({ error, notification }, 'an error occurred in processing a notification');
      const data = {
        notification: notification.name, page: notification.pageName, pageId: notification.pageId,
      };
      data.error = _.get(error, 'response.text', error.message);
      Feed.saveFeed(notification.accountId, 'Facebook Reviews Fetch Failed', {
        ...data,
        help: 'https://help.provesrc.com/en/articles/4371499-re-authorize-facebook-page-permissions',
      });
      oplog.error = error;
      const message = _.get(error, 'response.body.error.message', error.message);
      taskSummary.errors.apiErrors[message] = (taskSummary.errors.apiErrors[message] + 1) || 1;
      taskSummary.totalErrors += 1;
    }
    oplog.safeSave();
  }
  return taskSummary;
};

module.exports = {
  reformatResults,
  facebookReviewsTask,
};
