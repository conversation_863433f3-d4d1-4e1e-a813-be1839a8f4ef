const Account = require('../../app/account/models/Account');
const thinkificService = require('../../app/thinkific/thinkific.service');
const sleep = require('../../lib/utils/sleep');

module.exports = {
  refreshTokens,
};

async function refreshTokens() {
  const results = {
    accounts: 0, sites: 0, noSites: 0, success: 0, failed: 0, errors: {},
  };
  const accounts = await Account.find({
    thinkific: { $exists: true },
    'thinkific.uninstalled': null,
  }).select('thinkific');
  results.accounts = accounts.length;
  for(let i = 0; i < accounts.length; i += 1) {
    const account = accounts[i];
    const sites = account.thinkific.filter(site => !site.uninstalled && !site.ignoreEvents);
    if(!sites.length) {
      results.noSites += 1;
      continue;
    }
    results.sites += sites.length;
    // eslint-disable-next-line no-await-in-loop
    await Promise.all(sites.map(site => thinkificService.refreshAndSaveTokens({
      account, site, force: true,
    }).then(() => {
      results.success += 1;
    }).catch((err) => {
      results.failed += 1;
      results.errors[site.subdomain] = err.message;
    })));
    await sleep(1000);
  }
  return results;
}
