const LoggerFactory = require('../../lib/logger/LoggerFactory');

const logger = LoggerFactory('cron/trustpilotReviews');
const lib = require('../../lib/apis/trustpilot');
const constants = require('../../app/notifications/constants');

const TrustpilotReview = require('../../app/reviews/models/TrustpilotReview');
const Feed = require('../../app/account/models/Feed');
const OpLog = require('../models/OpLog');

const trustpilotReviewsTask = async (notifications) => {
  const taskSummary = {
    reviews: 0,
    success: 0,
    failed: 0,
    errors: {},
  };
  for(let i = 0; i < notifications.length; i++) {
    const notification = notifications[i];
    const { accountId, placeId } = notification;
    const domain = notification.placeId;
    const oplog = new OpLog({
      name: 'TrustpilotReviews',
      accountId,
      resourceId: notification.id,
      resource: notification,
    });
    try {
      if(!placeId) {
        logger.error({ notification }, 'no placeId for notification');
        continue;
      }

      const results = await lib.getReviews({ domain, lowestRating: 4 }).catch((err) => {
        logger.error({ err, domain }, 'an error occurred fetching reviews');
      });

      if(!results || !results.length) {
        continue;
      }

      const reformatedResults = await reformatResults(results, notification);
      const reviews = await TrustpilotReview.insertMany(reformatedResults, { ordered: false })
        .catch((err) => {
          logger.error({ err }, 'failed to insert trustpilot reviews');
        });

      reformatedResults.forEach((result) => {
        const feedData = {
          domain,
          businessName: result.businessName,
          author: result.authorName,
          text: result.text,
          rating: result.rating,
          reviewId: result.reviewId,
          time: result.time,
          source: constants.reviewSources.trustpilot,
        };

        const message = `Trustpilot Review | ${feedData.businessName} (${feedData.author})`;
        Feed.saveFeed(notification.accountId, message, feedData);
      });

      oplog.result = reformatedResults;
      taskSummary.reviews += reformatedResults.length;
      taskSummary.success += 1;

      logger.info({ reviews, reformatedResults, notification }, 'inserted reviews for notification');
    } catch(error) {
      if(error.name === 'BulkWriteError' && error.code === 11000) {
        logger.info({ error, notification }, 'attempted to insert duplicated review(s)');
      } else {
        logger.error({ error, notification }, 'an error occurred in processing a notification');
      }
      oplog.error = error;
      taskSummary.failed += 1;
      taskSummary.errors[error.message] = (taskSummary.errors[error.message] + 1) || 1;
    }
    oplog.safeSave();
  }
  return taskSummary;
};

const reformatResults = async (results, notification) => {
  const latestReview = await TrustpilotReview
    .find({ accountId: notification.accountId, domain: notification.placeId })
    .sort({ time: -1 })
    .limit(1)
    .then(reviews => reviews[0]);

  return results.map((result) => {
    if(latestReview && latestReview.time && latestReview.time.getTime() >= result.time) {
      return null;
    }
    result.accountId = notification.accountId;
    result.placeId = notification.placeId;
    return result;
  }).filter(r => r != null);
};

module.exports = {
  trustpilotReviewsTask,
  reformatResults,
};
