const _ = require('lodash');
const moment = require('moment');
const capterraController = require('../../app/reviews/capterra.controller');
const logger = require('../../lib/logger/LoggerFactory')('cron/capterraReviewsTask');
const Review = require('../../app/notifications/models/Review');
const { reviewSources } = require('../../app/notifications/constants');
// const Scrapfly = require('../../lib/apis/Scrapfly');
const OpLog = require('../models/OpLog');

module.exports = {
  capterraReviewsTask,
};

async function capterraReviewsTask() {
  const taskResults = {
    reviews: 0,
    success: 0,
    failed: 0,
    errors: {},
  };

  const notifications = await Review.find({
    source: reviewSources.capterra,
    stopAutoSync: { $ne: true },
    $or: [{
      // never seen but recently created
      lastView: null, createdAt: { $gt: moment().subtract(90, 'days').toDate() },
    }, {
      // seen in the last 180 days
      lastView: { $gt: moment().subtract(180, 'days').toDate() },
    }],
  });
  if(notifications.length > 0) {
    logger.info({ notifications }, 'processing notifications');

    for(let i = 0; i < notifications.length; i += 1) {
      const notification = notifications[i];
      const oplog = new OpLog({
        name: 'CapterraReviews',
        accountId: notification.accountId,
        resourceId: notification._id,
        resource: notification,
      });
      // const scrapfly = new Scrapfly(config.scrapfly.apiKey);
      // eslint-disable-next-line no-await-in-loop
      const { error, reviews } = await capterraController.handleReviews(notification);
      if(error) {
        taskResults.failed += 1;
        taskResults.errors[error.message] = (taskResults.errors[error.message] + 1) || 1;
        oplog.error = error;
      } else {
        taskResults.reviews += reviews;
        taskResults.success += 1;
        oplog.result = { reviews };
      }
      await oplog.safeSave();
    }
  } else {
    logger.info('no notifications to process');
  }
  return taskResults;
}
