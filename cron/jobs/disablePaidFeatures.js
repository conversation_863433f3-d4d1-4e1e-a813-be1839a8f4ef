/* eslint-disable no-underscore-dangle,no-await-in-loop */
const logger = require('../../lib/logger/LoggerFactory')('cron/paid-features');
const { dateUtils } = require('../../lib/utils');

const Account = require('../../app/account/models/Account');
const Notification = require('../../app/notifications/models/Notification');

module.exports = disable;

async function disable() {
  const BATCH_SIZE = 50;
  const dateThreshold = new Date() - dateUtils.MILLISECONDS_IN_DAY * 3;
  const query = {
    $or: [{
      'subscription.untilDate': { $lt: dateThreshold },
      // saw accounts with untilDate < today && IPN=CHARGE (they should not have an account)
      // 'subscription.recentIPN': { $nin: ['RECURRING', 'CHARGE', 'CONTRACT_CHANGE'] },
    }, {
      subscription: null,
    }],
  };
  const results = { accountsChecked: 0, accountsAboveLimit: 0, deactivatedNotifications: 0 };
  let shouldRun = true;
  let i = 0;
  while(shouldRun) {
    const accounts = await Account.find(query, '_id').skip(BATCH_SIZE * i).limit(BATCH_SIZE);
    const ids = [];
    const promises = [];
    accounts.forEach((account) => {
      promises.push(Notification.find({ accountId: account.id, active: true }, '_id')
        .sort({ priority: 1 })
        .then((notifications) => {
          const nIds = notifications.map(n => n.id);
          nIds.shift();
          ids.push(...nIds);
          results.accountsAboveLimit += 1;
        }));
    });
    await Promise.all(promises);
    await Notification.updateMany({ _id: { $in: ids } }, { active: false });
    results.accountsChecked += accounts.length;
    results.deactivatedNotifications += ids.length;
    shouldRun = accounts.length === BATCH_SIZE;
    i += 1;
  }
  return results;
}
