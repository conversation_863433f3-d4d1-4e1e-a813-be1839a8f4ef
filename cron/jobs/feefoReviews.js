/* eslint-disable no-await-in-loop */
const Review = require('../../app/notifications/models/Review');
const { reviewSources } = require('../../app/notifications/constants');
const logger = require('../../lib/logger')('cron/feefoReviewTask');
const feefoService = require('../../app/reviews/feefo.service');
const OpLog = require('../models/OpLog');


module.exports = {
  feefoReviewsTask,
};

async function feefoReviewsTask() {
  const taskResults = {
    reviews: 0,
    success: 0,
    errors: 0,
  };

  const notifications = await Review.find(
    {
      source: reviewSources.feefo,
      stopAutoSync: { $ne: true },
    },
  );

  if(notifications.length > 0) {
    logger.info({ notifications }, 'processing notifications');

    for(let i = 0; i < notifications.length; i += 1) {
      const notification = notifications[i];
      const oplog = new OpLog({
        name: 'FeefoReviews',
        accountId: notification.accountId,
        resourceId: notification._id,
        resource: notification,
      });
      const {
        error, reviews,
      } = await feefoService.handleReviews(notification);
      if(error) {
        taskResults.errors += 1;
        oplog.error = error;
      } else {
        taskResults.reviews += reviews;
        taskResults.success += 1;
        oplog.result = { reviews };
      }
      await oplog.safeSave();
    }
  } else {
    logger.info('no notifications to process');
  }
  return taskResults;
}
