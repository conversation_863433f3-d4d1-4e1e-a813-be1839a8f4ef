const config = require('../../config');
const Review = require('../../app/notifications/models/Review');
const { reviewSources } = require('../../app/notifications/constants');
const logger = require('../../lib/logger/LoggerFactory')('reviews.controller');
const Scrapfly = require('../../lib/apis/Scrapfly');

const googleReviews = require('./googleReviews');
const facebookReviews = require('./facebookReviews');
const trustpilotReviews = require('./trustpilotReviews');
const yotpoReviews = require('./yotpoReviews');
const stampedReviews = require('./stampedReviews');
const reviewsioReviews = require('./reviewsioReviews');
const capterraController = require('../../app/reviews/capterra.controller');
const judgemeService = require('../../app/reviews/judgeme.service');
const feefoService = require('../../app/reviews/feefo.service');
const shapoService = require('../../app/reviews/shapo.service');
const shopperApprovedService = require('../../app/reviews/shopperApproved.service');
const wixService = require('../../app/wix/wix.service');

module.exports = {
  runReviewsTask,
  fetchReviews,
};

async function runReviewsTask(req, res) {
  const { notificationId } = req.body;
  const notification = await Review.findOne({ _id: notificationId });
  if(!notification) {
    throw new Error('notification not found');
  }
  const result = await fetchReviews(notification);
  logger.info({ result, notificationId }, 'finished reviews task');
  res.send({ result });
}

async function fetchReviews(notification) {
  let result = null;
  const scrapfly = new Scrapfly(config.scrapfly.apiKey);
  switch(notification.source) {
  default:
    throw Error('invalid source');
  case reviewSources.other:
    break;
  case reviewSources.google:
    result = await googleReviews.googleReviewsTask([notification]);
    break;
  case reviewSources.facebook:
    result = await facebookReviews.facebookReviewsTask([notification]);
    break;
  case reviewSources.trustpilot:
    result = await trustpilotReviews.trustpilotReviewsTask([notification]);
    break;
  case reviewSources.yotpo:
    result = await yotpoReviews.yotpoReviewsTask([notification]);
    break;
  case reviewSources.capterra:
    result = await capterraController.handleReviews(notification, scrapfly);
    break;
  case reviewSources.stamped:
    result = await stampedReviews.stampedReviewsTask([notification]);
    break;
  case reviewSources.reviewsio:
    result = await reviewsioReviews.reviewsioReviewsTask([notification]);
    break;
  case reviewSources.judgeme:
    result = await judgemeService.handleReviews(notification);
    break;
  case reviewSources.feefo:
    result = await feefoService.handleReviews(notification);
    break;
  case reviewSources.shopperapproved:
    result = await shopperApprovedService.handleReviews(notification, { limit: 30 });
    break;
  case reviewSources.shapo:
    result = await shapoService.handleReviews(notification);
    break;
  case reviewSources.wix:
    result = await wixService.handleReviews(notification);
    break;
  }
  return result;
}
