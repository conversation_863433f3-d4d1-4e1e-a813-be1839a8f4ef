/* eslint-disable no-await-in-loop */
const config = require('../../config');
const Account = require('../../app/account/models/Account');
const Shopify = require('../../lib/apis/shopify');
const dateUtils = require('../../lib/utils/dateUtils');
const { SHOPIFY } = require('../../app/constants');
const { TRANSACTION_TYPES } = require('../../app/billing/constants');

const triggers = require('../../lib/triggers');
const profitwellController = require('../../app/billing/profitwell.controller');
const redis = require('../../app/common/redis.service').getClient();

module.exports = {
  sendShopifyCredits,
  checkShopifyCharges,
};

async function sendShopifyCredits() {
  const results = { sent: [], errors: [] };
  const fiveDaysAgo = dateUtils.todayNormalized12am() - dateUtils.MILLISECONDS_IN_DAY * 5;
  const fiveDaysAgoEnd = fiveDaysAgo + dateUtils.MILLISECONDS_IN_DAY;
  const accounts = await Account.find({
    shopify: {
      $elemMatch: {
        installed: { $gte: fiveDaysAgo, $lt: fiveDaysAgoEnd },
      },
    },
    'stats.sentEmails.shopifyCredit': { $exists: false },
    subscription: null,
  });
  for(let i = 0; i < accounts.length; i += 1) {
    const account = accounts[i];
    const shop = account.shopify.find(store => !store.paying
        && store.installed >= fiveDaysAgo
        && store.installed < fiveDaysAgoEnd);
    if(shop) {
      await Shopify.sendCredits({
        partnerId: SHOPIFY.partnerId,
        appId: SHOPIFY.appId,
        partnerAccessToken: config.shopify.partnerAccessToken,
        shopId: shop.id,
        amount: 14.50,
        description: '50% discount for first month',
      }).then(() => Account.updateOne({ _id: account._id }, {
        $set: {
          'stats.sentEmails.shopifyCredit': Date.now(),
        },
      })).then(() => {
        results.sent.push(account.email);
      }).catch((error) => {
        results.errors.push({
          email: account.email,
          shopId: shop.id,
          error: error.message || 'Unknown error occurred while sending credits',
        });
      });
    }
  }
  return results;
}

async function checkShopifyCharges() {
  const accounts = await Account.find({
    'subscription.source': 'shopify',
  }).select('shopify email subscription');
  const now = Date.now();

  const results = {
    accounts: accounts.length,
    cancelled: [],
    updated: 0,
    reactivated: [],
    errors: [],
  };


  for(let i = 0; i < accounts.length; i += 1) {
    const account = accounts[i];
    const { email, id: accountId, subscription } = account;
    const { subscriptionId, lastChargeId } = subscription;
    const shop = account.shopify.find(s => s.chargeId === subscriptionId
      || s.chargeId === parseFloat(lastChargeId)) || null;
    if(!shop) {
      results.errors.push({
        email,
        error: 'No shop found for subscription',
      });
      continue;
    }
    const { myshopify_domain: shopifyDomain, token } = shop;
    const { untilDate } = account.subscription;
    const oldSubscription = account.subscription.toObject();
    const shopify = Shopify(shopifyDomain, token);
    const charge = await shopify.getCharges().then(charges => charges.find(c => c.status === 'active')).catch(() => {});
    let chargeUntilDate = charge ? new Date(charge.billing_on) : null;
    if(!chargeUntilDate && untilDate && untilDate < new Date('2038')) {
      chargeUntilDate = untilDate;
    }
    const GRACE_THRESHOLD = 15 * 86400 * 1000;
    const expiredGrace = chargeUntilDate && (now - chargeUntilDate) >= GRACE_THRESHOLD;

    if(account.subscription.recentIPN === TRANSACTION_TYPES.CHARGE) {
      if(charge && !expiredGrace) {
        // everything is good, update until date
        if(untilDate.getTime() !== chargeUntilDate.getTime()) {
          account.subscription.untilDate = chargeUntilDate;
          results.updated += 1;
        }
      } else {
        // no charge of expired grace, cancel sub
        account.subscription.recentIPN = TRANSACTION_TYPES.CANCELLATION;
        account.subscription.untilDate = now - 86400 * 1000;
        results.cancelled.push(account.email);
        triggers.subscriptionStateChanged(account, account.subscription.recentIPN, oldSubscription);
        profitwellController.churnSubscription(accountId, new Date(), profitwellController.CHURN_TYPES.delinquent);
      }
    } else if(charge && !expiredGrace) {
      // reactivate
      account.subscription.recentIPN = TRANSACTION_TYPES.CHARGE;
      account.subscription.untilDate = chargeUntilDate;
      results.reactivated.push(account.email);
      triggers.subscriptionStateChanged(account, account.subscription.recentIPN, oldSubscription);
      profitwellController.updateSubscription(accountId, {
        email,
        plan: `${charge.name} (Shopify)`,
        value: charge.price * 100,
        interval: profitwellController.INTERVALS.month,
      });
    }

    if(account.isModified()) {
      await account.save();
      redis.delAsync(accountId);
    }
  }
  return results;
}
