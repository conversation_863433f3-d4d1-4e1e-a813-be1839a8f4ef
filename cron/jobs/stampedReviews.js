const LoggerFactory = require('../../lib/logger/LoggerFactory');

const logger = LoggerFactory('cron/stampedReviews');
const lib = require('../../lib/apis/stamped');
const { reviewSources } = require('../../app/notifications/constants');
const StampedReview = require('../../app/reviews/models/StampedReview');
const Feed = require('../../app/account/models/Feed');
const OpLog = require('../models/OpLog');

async function parseReviews(reviewsBody, storeUrl, accountId) {
  const reviewsArray = [];
  if(reviewsBody.length === 0) return [];

  const lastReview = await StampedReview
    .find({ accountId, storeUrl })
    .sort({ time: -1 })
    .limit(1)
    .then(reviews => reviews[0]);

  reviewsBody.forEach((review) => {
    const reviewObject = {};
    reviewObject.storeUrl = storeUrl;
    reviewObject.reviewId = review.id;
    reviewObject.productId = review.productId;
    reviewObject.profilePhotoUrl = getReviewPhoto(review);
    reviewObject.authorName = review.author;
    reviewObject.rating = review.reviewRating;
    reviewObject.text = review.reviewMessage;
    reviewObject.time = new Date(review.dateCreated);
    reviewObject.accountId = accountId;
    reviewObject.productName = review.productName;

    if(!lastReview || reviewObject.time > lastReview.time.getTime()) {
      reviewsArray.push(reviewObject);
    }
  });

  return reviewsArray;
}

function getReviewPhoto(review) {
  let image;
  if(review.productImageUrl) {
    image = review.productImageUrl;
  } else if(review.productImageLargeUrl) {
    image = review.productImageLargeUrl;
  } else if(review.avatar) {
    image = review.avatar;
  } else {
    image = null;
  }
  if(image && image.startsWith('http://')) {
    image.replace('http', 'https');
  }
  return image;
}

const stampedReviewsTask = async (notifications) => {
  const taskSummary = {
    reviews: 0,
    success: 0,
    failed: 0,
    errors: {},
  };

  for(let i = 0; i < notifications.length; i++) {
    const notification = notifications[i];
    const oplog = new OpLog({
      name: 'StampedReviews',
      accountId: notification.accountId,
      resourceId: notification._id,
      resource: notification,
    });
    const { accountId, placeId, pageToken: apiKey } = notification;
    const storeUrl = placeId;
    try {
      if(!placeId) {
        logger.error({ notification }, 'no placeId (store url) for notification');
        continue;
      }

      const results = await lib.getReviews(storeUrl, apiKey).catch((err) => {
        logger.error({ err, storeUrl }, 'an error occurred fetching stamped reviews');
      });

      if(!results || !results.data.length) {
        continue;
      }

      const parsedReviews = await parseReviews(results.data, storeUrl, accountId);
      const reviews = await StampedReview.insertMany(parsedReviews,
        { ordered: false }).catch((err) => {
        logger.error({ err }, 'failed to insert stamped.io reviews');
      });

      parsedReviews.forEach((result) => {
        const feedData = {
          storeUrl,
          apiKey,
          reviewId: result.reviewId,
          productId: result.productId,
          productName: result.productName,
          author: result.authorName,
          text: result.text,
          rating: result.rating,
          time: result.time,
          source: reviewSources.stamped,
        };

        Feed.saveFeed(accountId, `Stamped Review | ${feedData.author}`, feedData);
      });
      taskSummary.reviews += parsedReviews.length;
      taskSummary.success += 1;

      logger.info({ reviews, parsedReviews, notification }, 'inserted reviews for notification');
      oplog.result = { reviews };
    } catch(error) {
      oplog.error = error;
      if(error.name === 'BulkWriteError' && error.code === 11000) {
        logger.info({ error, notification }, 'attempted to insert duplicated review(s)');
      } else {
        logger.error({ error, notification }, 'an error occurred in processing a notification');
      }
      taskSummary.failed += 1;
      taskSummary.errors[error.message] = (taskSummary.errors[error.message] + 1) || 1;
    }
    await oplog.safeSave();
  }
  return taskSummary;
};

module.exports = {
  stampedReviewsTask,
};
