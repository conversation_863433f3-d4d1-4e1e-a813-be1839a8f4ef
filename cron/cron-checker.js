const cron = require('cron');
const mailerService = require('../app/common/mailer.service');
const redisService = require('../app/common/redis.service');
const CronResult = require('./models/CronResult');

const locker = require('../lib/redisClient/Locker').getLocker(redisService.getClient());

module.exports = {
  scheduleCheckers,
};

function scheduleCheckers() {
  cron.job('0 0 * * * *', async () => {
    const lock = await locker.lock('cron-checker', 60);
    if(!lock) {
      return;
    }
    // check if there are CronResult docs from the last hour (counter-cache runs every 1-5 minutes)
    const lastResult = await CronResult.findOne({ createdAt: { $gte: Date.now() - 1000 * 60 * 60 } });
    if(!lastResult) {
      await mailerService.sendAdminEmail({
        to: '<EMAIL>',
        subject: 'cron server is down?',
        html: '<p>no cron results in db for over an hour</p>',
      });
    }
  }, null, true);
}
