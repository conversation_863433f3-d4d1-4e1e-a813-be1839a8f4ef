const _ = require('lodash');
const moment = require('moment');
const processListeners = require('../processListeners');
const config = require('../config');
const slack = require('../lib/apis/slackNotifier');
const LoggerFactory = require('../lib/logger/LoggerFactory');
const mailerService = require('../app/common/mailer.service');

const logger = LoggerFactory('cron-app', { enabled: config.logs.cron });
processListeners.listen(null, slack, logger);

// eslint-disable-next-line import/order
const cron = require('cron');
const redisService = require('../app/common/redis.service');
const dbLoader = require('../lib/mongooseLoader');
const cachegooseLoader = require('../lib/cachegooseLoader');
const doSpaces = require('../lib/apis/digitalOceanSpaces');

const { reviewSources } = require('../app/notifications/constants');
const CronResult = require('./models/CronResult');

const Review = require('../app/notifications/models/Review');
const { googleReviewsTask } = require('./jobs/googleReviews');
const { facebookReviewsTask } = require('./jobs/facebookReviews');
const { trustpilotReviewsTask } = require('./jobs/trustpilotReviews');
const { reviewsioReviewsTask } = require('./jobs/reviewsioReviews');
const { yotpoReviewsTask } = require('./jobs/yotpoReviews');
const { stampedReviewsTask } = require('./jobs/stampedReviews');
const { capterraReviewsTask } = require('./jobs/capterraReviews');
const { judgemeReviewsTask } = require('./jobs/judgemeReviews');
const { feefoReviewsTask } = require('./jobs/feefoReviews');
const { shopperApprovedReviewsTask } = require('./jobs/shopperApprovedReviews');
const { shapoReviewsTask } = require('./jobs/shapoReviews');

const { sendSummaryEmails } = require('./jobs/summaryEmail');
const emailTasks = require('./jobs/emailTasks');
const thinkificTasks = require('./jobs/thinkificTasks');

const greenInvoiceTasks = require('./jobs/greenInvoiceJobs');
const disablePaidFeatures = require('./jobs/disablePaidFeatures');
const { updateSocialCounters } = require('./jobs/updateSocialCounters');
const filesTask = require('./jobs/filesTask');
const counterCache = require('./jobs/counterCache');
const shopifyJobs = require('./jobs/shopify');
const wixJobs = require('./jobs/wix');

const locker = require('../lib/redisClient/Locker').getLocker(redisService.getClient());

const { serverMessages: webhook } = config.slack;

const CONSTANTS = {
  jobs: {
    reviews: {
      name: 'reviews',
      func: pullReviews,
    },
    googleReviews: {
      name: 'google-reviews',
      func: runGoogleReviews,
      opts: { notify: false },
    },
    invoices: {
      name: 'invoices',
      func: greenInvoiceTasks.sendInvoices,
      opts: { notify: false },
    },
    fixAccountingKeys: {
      name: 'fix-accounting-keys',
      func: greenInvoiceTasks.fixAccountingKeys,
      opts: { notify: false },
    },
    paidFeatures: {
      name: 'paid-features',
      func: disablePaidFeatures,
      opts: { notify: false },
    },
    checkShopifyCharges: {
      name: 'check-shopify-charges',
      func: shopifyJobs.checkShopifyCharges,
    },
    checkWixSubscriptions: {
      name: 'check-wix-subscriptions',
      func: wixJobs.checkWixSubscriptions,
    },
    socialCounters: {
      name: 'social-counters',
      func: updateSocialCounters,
    },
    filesTask: {
      name: 'files-task',
      func: filesTask.deleteScreenshots,
      opts: { notify: false },
    },
    summaryEmail: {
      name: 'summary-email',
      func: sendSummaryEmails,
      opts: { notify: false },
    },
    counterCache: {
      name: 'counter-cache',
      func: counterCache.cache,
      opts: { notify: false, lockSeconds: 5 * 60 },
    },
    updateEmailAutomationFields: {
      name: 'update-email-automation-fields',
      func: emailTasks.updateEmailAutomationFields,
      opts: { notify: false },
    },
    delinquentReport: {
      name: 'delinquent-report',
      func: emailTasks.delinquentChurnReport,
      opts: { notify: false },
    },
    chargeFailedReport: {
      name: 'charge-failed-report',
      func: emailTasks.chargeFailedReport,
      opts: { notify: false },
    },
    reviewRequest: {
      name: 'review-request',
      func: emailTasks.reviewRequest,
    },
    refreshThinkificTokens: {
      name: 'refresh-thinkific-tokens',
      func: thinkificTasks.refreshTokens,
      opts: { notify: false },
    },
    sendWixInstallEmails: {
      name: 'wix-install-emails',
      func: wixJobs.sendWixInstallEmail,
    },
    // sends credits to merchants regardless if they are paying or not, i.e. giving away free money
    // sendShopifyCredits: {
    //   name: 'send-shopify-credits',
    //   func: shopifyJobs.sendShopifyCredits,
    //   opts: { notify: true },
    // },
  },
  times: {
    EVERY_X_MINUTES: (minutes = 5) => `0 */${minutes} * * * *`,
    EVERY_X_HOURS: (hours = 0) => `0 */${hours} * * *`,
    EVERY_HOUR: '0 0 * * * *',
    DAILY: (hour = 0) => `0 0 ${hour} * * *`,
    WEEKLY: (day = 0, hour = 0) => `0 0 ${hour} * * ${day}`,
    MONTHLY: (day, hour) => `0 0 ${hour} ${day} * *`,
  },
};

module.exports = {
  pullReviews,
  runGoogleReviews,
  jobs: CONSTANTS.jobs,
};

(async function main() {
  if(!config.cron.schedule) {
    logger.info('cron schedules should not start');
    return;
  }
  logger.info('cron app started');
  await dbLoader.load(config.mongodb.url, config.mongodb.readPref);
  cachegooseLoader('redis', config.redis.port, config.redis.host);

  const { jobs, times } = CONSTANTS;
  cron.job(times.DAILY(), async () => {
    const dailyJobs = Object.entries(_.pick(jobs, [
      'reviews', 'googleReviews', 'invoices', 'updateEmailAutomationFields',
      'checkShopifyCharges', 'checkWixSubscriptions', 'socialCounters', 'filesTask',
      'paidFeatures', 'delinquentReport', 'chargeFailedReport', 'reviewRequest', 'sendWixInstallEmails',
    ]));
    await Promise.all(dailyJobs.map(([, v]) => lockRun(v.name, v.func, v.opts)));
  }, null, true);

  cron.job(times.EVERY_X_HOURS(12), async () => {
    const tokenJob = jobs.refreshThinkificTokens;
    await lockRun(tokenJob.name, tokenJob.func, tokenJob.opts);
  }, null, true);

  cron.job(times.WEEKLY(1, 0), async () => {
    await Promise.all([
      lockRun(jobs.summaryEmail.name, jobs.summaryEmail.func, jobs.summaryEmail.opts),
    ]);
  }, null, true);

  cron.job(times.EVERY_X_MINUTES(1), async () => {
    await lockRun(jobs.counterCache.name, jobs.counterCache.func, jobs.counterCache.opts);
  }, null, true);

  cron.job(times.MONTHLY(1, 0), async () => {
    const keysJob = jobs.fixAccountingKeys;
    await lockRun(keysJob.name, keysJob.func, keysJob.opts);
  }, null, true);
}());

async function lockRun(name, func, { notify = true, lockSeconds = 60 } = {}) {
  let result = null;
  let lock = null;
  try {
    lock = await locker.lock(name, lockSeconds);
    if(lock) {
      logger.info({ lock: name }, 'acquired job lock');
      result = await func();

      (new CronResult({ job: name, result })).save().catch(() => {});
      if(notify) {
        slack.notify(`task completed ${name}`, { result }, { webhook });
      }
    } else {
      logger.info({ lock: name }, 'failed to acquire job lock');
    }
  } catch(err) {
    (new CronResult({ job: name, result: null, error: err })).save().catch(() => {});
    slack.notifyError({ err }, `task failed ${name}`, { result });
  }
  if(lock) {
    await locker.unlock(name);
    logger.info({ lock: name }, 'released job lock');
  }
  return result;
}

async function pullReviews() {
  const [
    facebook,
    trustpilot,
    reviewsio,
    yotpo,
    stamped,
    capterra,
    judgeme,
    feefo,
    shopperApproved,
    shapo,
    wix,
  ] = await Promise.all([
    runFacebookReviews(),
    runTrustpilotReviews(),
    runReviewsIoReviews(),
    runYotpoReviews(),
    runStampedReviews(),
    capterraReviewsTask(),
    judgemeReviewsTask(),
    feefoReviewsTask(),
    shopperApprovedReviewsTask(),
    shapoReviewsTask(),
    wixJobs.wixReviewsTask(),
  ]);
  const results = {
    facebook,
    trustpilot,
    reviewsio,
    yotpo,
    stamped,
    capterra,
    judgeme,
    feefo,
    shopperApproved,
    shapo,
    wix,
  };
  const zeroReviews = [];
  if(facebook.success === 0) {
    zeroReviews.push('facebook');
  }
  if(trustpilot.success === 0) {
    zeroReviews.push('trustpilot');
  }
  if(reviewsio.success === 0) {
    zeroReviews.push('reviews.io');
  }
  if(yotpo.success === 0) {
    zeroReviews.push('yotpo');
  }
  if(stamped.success === 0) {
    zeroReviews.push('stamped');
  }
  if(capterra.success === 0) {
    zeroReviews.push('capterra');
  }
  if(judgeme.success === 0) {
    zeroReviews.push('judgeme');
  }
  if(shopperApproved.success === 0) {
    zeroReviews.push('shopperApproved');
  }
  if(feefo.success === 0) {
    zeroReviews.push('feefo');
  }
  if(shapo.success === 0) {
    zeroReviews.push('shapo');
  }
  if(wix.success === 0) {
    zeroReviews.push('wix');
  }
  if(zeroReviews.length) {
    await mailerService.sendAdminEmail({
      to: '<EMAIL>',
      subject: 'Failed Reviews Fetch',
      html: zeroReviews.map(p => `${p} failed fetching reviews`).join('<br/>'),
    });
  }
  await mailerService.sendReviewsCronSummary({ results });
  return results;
}

async function runReviewsIoReviews() {
  let taskResults = {};

  const notifications = await Review.find(
    {
      source: reviewSources.reviewsio,
      stopAutoSync: { $ne: true },
    },
    { accountId: 1, placeId: 1 },
  );
  if(notifications.length > 0) {
    logger.info({ notifications }, 'processing notifications');
    taskResults = await reviewsioReviewsTask(notifications);
  } else {
    logger.info('no notifications to process');
  }
  return taskResults;
}

async function runTrustpilotReviews() {
  let taskResults = {};
  const notifications = await Review.find(
    {
      source: reviewSources.trustpilot,
      stopAutoSync: { $ne: true },
    },
    { accountId: 1, placeId: 1 },
  );
  if(notifications.length > 0) {
    logger.info({ notifications }, 'processing notifications');
    taskResults = await trustpilotReviewsTask(notifications);
  } else {
    logger.info('no notifications to process');
  }
  return taskResults;
}

async function runStampedReviews() {
  let taskSummary = {};
  const notifications = await Review.find(
    {
      source: reviewSources.stamped,
      stopAutoSync: { $ne: true },
    },
    {
      accountId: 1, placeId: 1, pageToken: 1,
    },
  );
  if(notifications.length > 0) {
    logger.info({ notifications }, 'processing notifications');
    taskSummary = await stampedReviewsTask(notifications);
  } else {
    logger.info('no notifications to process');
  }
  return taskSummary;
}

async function runYotpoReviews() {
  let taskSummary = {};
  const notifications = await Review.find(
    {
      source: reviewSources.yotpo,
      stopAutoSync: { $ne: true },
    },
    { accountId: 1, placeId: 1 },
  );
  if(notifications.length > 0) {
    logger.info({ notifications }, 'processing notifications');
    taskSummary = await yotpoReviewsTask(notifications);
  } else {
    logger.info('no notifications to process');
  }
  return taskSummary;
}

async function runGoogleReviews() {
  let results = null;
  const notifications = await Review.find({
    active: true,
    source: reviewSources.google,
    stopAutoSync: { $ne: true },
    // fetch only for
    $or: [{
      // seen in the last 180 days
      lastView: { $gt: moment().subtract(180, 'days').toDate() },
    }, {
      // created recently and never seen
      createdAt: { $gt: moment().subtract(90, 'days').toDate() }, lastView: null,
    }, {
      // never had a fetch attempt
      'stats.lastAttempt': null,
    }],
  }, { accountId: 1, placeId: 1 }).sort({ 'stats.lastAttempt': 1 });
  if(notifications.length > 0) {
    logger.info({ notifications }, 'processing notifications');
    results = await googleReviewsTask(notifications, { locker, jobName: 'google-reviews' });
  } else {
    logger.info('no notifications to process');
  }
  await mailerService.sendReviewsCronSummary({ results, platform: 'Google' });
  return results;
}

async function runFacebookReviews() {
  let taskResults = {};
  const notifications = await Review.find({
    source: reviewSources.facebook,
    stopAutoSync: { $ne: true },
    $or: [{
      // seen in the last 180 days
      lastView: { $gt: moment().subtract(180, 'days').toDate() },
    }, {
      // created recently and never seen
      createdAt: { $gt: moment().subtract(90, 'days').toDate() }, lastView: null,
    }],
  }, {
    accountId: 1, name: 1, placeId: 1, pageToken: 1, pageName: 1,
  });
  if(notifications.length > 0) {
    logger.info({ notifications }, 'processing notifications');
    taskResults = await facebookReviewsTask(notifications);
    await doSpaces.purgeCache(
      config.digitalOcean.spaces.id,
      config.digitalOcean.spaces.apiKey,
      { files: ['images/*'] },
    ).catch((err) => {
      logger.error({ err }, 'failed to flush DO cdn cache');
      slack.notifyError(err, 'failed to flush DO cdn cache');
    });
  } else {
    logger.info('no notifications to process');
  }
  return taskResults;
}
