const express = require('express');
const bodyParser = require('body-parser');
const methodHandler = require('../middleware/methodHandler');
const headersHandler = require('../middleware/headersHandler');
const responseHandler = require('../middleware/responseHandler');
const errorHandler = require('../middleware/errorHandler');

const reviewsController = require('./jobs/reviews.controller');
const updateSocialCounters = require('./jobs/updateSocialCounters');
const accountService = require('../app/account/account.service');
const capterraReviews = require('./jobs/capterraReviews');
const shopperApprovedReviews = require('./jobs/shopperApprovedReviews');
const { sendSummaryEmails } = require('./jobs/summaryEmail');
const emailTasks = require('./jobs/emailTasks');
const mailerService = require('../app/common/mailer.service');
const cronApp = require('./app');
const wixJobs = require('./jobs/wix');
const wrapper = require('../lib/express/routeWrapper');

const { reviewSources } = require('../app/notifications/constants');

const router = express.Router();
router.use(headersHandler);
router.use(methodHandler);
router.use(bodyParser.json({ type: 'application/json' }));
router.use((req, res, next) => {
  req.locals = {};
  next();
});

router.get('/', wrapper((req, res, next) => {
  res.body = { success: true };
  next();
}));

router.post('/cron/trigger', wrapper(async (req, res, next) => {
  const { job, params } = req.body;
  let cronJob = cronApp.jobs[job];
  if(!cronJob) {
    const jobEntries = Object.entries(cronApp.jobs);
    // eslint-disable-next-line no-restricted-syntax
    for(const [, value] of jobEntries) {
      if(value.name === job) {
        cronJob = value;
        break;
      }
    }
    if(!cronJob) {
      throw makeError400('job not found');
    }
  }
  res.body = { message: `processing ${cronJob.name} will send email when done` };
  next();

  try {
    let result = null;
    if(params) {
      result = await cronJob.func(...params);
    } else {
      result = await cronJob.func();
    }
    mailerService.sendAdminEmail({
      to: '<EMAIL>',
      subject: `cron trigger result ${cronJob.name}`,
      html: JSON.stringify(result, null, 2),
    });
  } catch(err) {
    mailerService.sendAdminEmail({
      to: '<EMAIL>',
      subject: `cron trigger failed ${cronJob.name}`,
      html: JSON.stringify(err, null, 2),
    });
  }
}));

router.post('/fetch-reviews', wrapper(reviewsController.runReviewsTask));
router.get('/cron/trigger/reviews', wrapper((req, res, next) => {
  cronApp.pullReviews();
  return next();
}));

router.post('/cron/trigger/update-email-automation-fields', wrapper(async (req, res, next) => {
  const { accountId, email, query } = req.body;
  res.body = await emailTasks.updateEmailAutomationFields({ query, accountId, email });
  return next();
}));

router.get('/cron/trigger/reviews/:source', wrapper((req, res, next) => {
  switch(req.params.source) {
  default:
    break;
  case reviewSources.google:
    cronApp.runGoogleReviews();
    break;
  case reviewSources.capterra:
    capterraReviews.capterraReviewsTask();
    break;
  case reviewSources.shopperapproved:
    shopperApprovedReviews.shopperApprovedReviewsTask();
    break;
  }
  return next();
}));

router.post('/cron/fetch-social', wrapper((req, res, next) => {
  const { notificationId } = req.body;
  updateSocialCounters.updateSocialCounters(notificationId);
  return next();
}));

router.post('/cron/check-wix-subscription', wrapper(async (req, res, next) => {
  res.body = await wixJobs.checkWixSubscriptions();
  next();
}));

router.post('/account/summary-email', wrapper(async (req, res, next) => {
  const { accountId } = req.body;
  if(!accountId) {
    res.body = await sendSummaryEmails();
  } else {
    const account = await accountService.getAccount(accountId);
    if(!account) {
      throw makeError400('account not found');
    }
    res.body = await accountService.sendSummaryEmail(account, { bcc: true, ignoreLastSent: true });
  }
  return next();
}));

router.use(responseHandler);
router.use(errorHandler);

function makeError400(message) {
  const err = new Error(message);
  err.code = 400;
  return err;
}

module.exports = router;
