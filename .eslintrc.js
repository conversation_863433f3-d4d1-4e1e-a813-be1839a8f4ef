module.exports = {
  "extends": "airbnb-base",
  plugins: [
    'chai-friendly'
  ],
  "env": {
    "browser": false,
    "commonjs": false,
    "es6": true,
    "node": true,
    "mocha": true
  },
  "parserOptions": {
    "ecmaVersion": 2018,
    "ecmaFeatures": {
      "jsx": false
    },
    "sourceType": "module"
  },
  "rules": {
    "indent": ["warn", 2, {
      SwitchCase: 1,
    }],
    "no-unused-expressions": 0,
    "chai-friendly/no-unused-expressions": 2,
    "no-const-assign": "warn",
    "no-this-before-super": "warn",
    "no-undef": "warn",
    "no-unreachable": "warn",
    "no-unused-vars": "warn",
    "no-continue": 0,
    "constructor-super": "warn",
    "valid-typeof": "warn",
    "no-use-before-define": ["error", { "functions": false }],
    'no-underscore-dangle': ['error', { allow: ['_id'] }],
    "keyword-spacing": [2, {
      "overrides": {
        "if": {"after": false},
        "for": {"after": false},
        "while": {"after": false},
        "catch": {"after": false},
        "switch": {"after": false},
      }
    }],
    "linebreak-style": 0,
    "no-param-reassign": ["error", { "props": false }],
    'prefer-destructuring': ['error', {
      AssignmentExpression: {
        array: false,
        object: false,
      },
    }, {
      enforceForRenamedProperties: false,
    }],
    'max-len': ['error', {
      code: 120,
      ignoreStrings: true,
      ignoreTemplateLiterals: true,
      ignoreRegExpLiterals: true,
      ignoreUrls: true,
      ignoreTrailingComments: true,
      ignoreComments: true,
    }],
  },
  "overrides": [{
    "files": ["scripts/**/*"],
    "rules": {
      "no-console": "off"
    }
  }],
};
