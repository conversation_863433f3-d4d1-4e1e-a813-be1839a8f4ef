/* eslint-disable no-await-in-loop */
const mongoose = require('mongoose');
const logger = require('../lib/logger').getLogger('migrations');
const Invoice = require('../app/billing/Invoice');
const { SUBSCRIPTION_SOURCE } = require('../app/billing/constants');

const BATCH_SIZE = 100;

module.exports = {
  migrateInvoiceAccountId,
  migrateInvoiceChargeId,
  migrateInvoicePlatform,
};

async function migrateInvoiceAccountId() {
  const invoices = await Invoice.find();
  logger.info(`Found ${invoices.length} invoices to migrate`);

  const results = {
    total: invoices.length,
    migrated: 0,
    failed: 0,
    errors: [],
  };

  const operations = [];
  for(let i = 0; i < invoices.length; i += 1) {
    const invoice = invoices[i];
    // Convert string to ObjectId
    const accountOid = new mongoose.Types.ObjectId(invoice.accountId);
    operations.push({
      updateOne: {
        filter: { _id: invoice._id },
        update: { $set: { accountId: accountOid } },
      },
    });

    if(operations.length >= BATCH_SIZE || i === invoices.length - 1) {
      logger.info('Processing batch for accountId migration');
      await Invoice.bulkWrite(operations, { ordered: false }).then(() => {
        results.migrated += operations.length;
      }).catch((error) => {
        results.failed += operations.length;
        results.errors.push({
          invoiceId: invoice._id,
          error: error.message,
        });
        logger.error({ error }, 'Failed to process batch for accountId migration');
      });
      operations.length = 0;
    }
  }

  return results;
}

async function migrateInvoiceChargeId() {
  // migrate only invoice with chargeId starting with in_ (stripe invoice id)
  const invoices = await Invoice.find({ chargeId: /^in_/ });
  logger.info(`Found ${invoices.length} invoices to migrate chargeId`);

  const results = {
    total: invoices.length,
    migrated: 0,
    failed: 0,
    errors: [],
  };

  const operations = [];
  for(let i = 0; i < invoices.length; i += 1) {
    const invoice = invoices[i];
    operations.push({
      updateOne: {
        filter: { _id: invoice._id },
        update: {
          $set: {
            invoiceId: invoice.chargeId,
            chargeId: invoice.charge.charge,
          },
        },
      },
    });

    if(operations.length >= BATCH_SIZE || i === invoices.length - 1) {
      logger.info('Processing batch for chargeId migration');
      // Process all operations in the current batch
      await Invoice.bulkWrite(operations, { ordered: false }).then(() => {
        results.migrated += operations.length;
      }).catch((error) => {
        results.failed += operations.length;
        results.errors.push({
          invoiceId: invoice._id,
          error: error.message,
        });
        logger.error({ error }, 'Failed to process batch for chargeId migration');
      });
      operations.length = 0;
    }
  }

  return results;
}

async function migrateInvoicePlatform() {
  const invoices = await Invoice.find();
  logger.info(`Found ${invoices.length} invoices to migrate platform`);

  const results = {
    total: invoices.length,
    migrated: 0,
    failed: 0,
    errors: [],
  };

  const operations = [];
  for(let i = 0; i < invoices.length; i += 1) {
    const invoice = invoices[i];
    let platform;
    if(invoice.chargeId && /^\d+$/.test(invoice.chargeId)) {
      platform = SUBSCRIPTION_SOURCE.bluesnap;
    } else {
      platform = SUBSCRIPTION_SOURCE.stripe;
    }

    operations.push({
      updateOne: {
        filter: { _id: invoice._id },
        update: { $set: { platform } },
      },
    });

    if(operations.length >= BATCH_SIZE || i === invoices.length - 1) {
      logger.info('Processing batch for platform migration');
      await Invoice.bulkWrite(operations, { ordered: false }).then(() => {
        results.migrated += operations.length;
      }).catch((error) => {
        results.failed += operations.length;
        results.errors.push({
          invoiceId: invoice._id,
          error: error.message,
        });
        logger.error({ error }, 'Failed to process batch for platform migration');
      });
      operations.length = 0;
    }
  }

  return results;
}
