const _ = require('lodash');
const Notification = require('../app/notifications/models/Notification');

async function migrateNotificationThemes() {
  const BATCH_SIZE = 100;
  let batchPromises = [];
  const notifications = await Notification.find();
  const results = {
    total: notifications.length,
    migrated: 0,
    failed: 0,
    errors: [],
  };
  for(let i = 0; i < notifications.length; i += 1) {
    const notification = notifications[i];
    const titleColor = _.get(notification, 'settings.title.color', '#7825F3');
    batchPromises.push(Notification.updateOne(
      { _id: notification._id },
      { $set: { 'settings.theme.titleColor': titleColor } },
    ).then(() => {
      results.migrated += 1;
    }).catch((err) => {
      results.failed += 1;
      results.errors.push(err);
    }));
    if((batchPromises.length >= BATCH_SIZE) || (i === notifications.length - 1)) {
      await Promise.all(batchPromises);
      batchPromises = [];
    }
  }
  await Notification.updateMany({}, { $unset: { 'settings.theme.title': 1, design: 1 } }, { strict: false });
  return results;
}

module.exports = {
  migrateNotificationThemes,
};
