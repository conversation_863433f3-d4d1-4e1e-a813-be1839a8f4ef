const { serializeError } = require('serialize-error');
const logger = require('../lib/logger').getLogger('migrations');
const notifierService = require('../app/common/notifier.service');
const accountMigration = require('./account.migrations');
const invoiceMigration = require('./invoice.migrations');
const notificationMigration = require('./notification.migrations');
const Migration = require('./Migration');

module.exports = {
  run,
};

async function run() {
  await runSingleMigration({
    name: 'update-shopify-installed',
    description: 'update all account.shopify.installed = null to account.createdAt',
    migrationFn: accountMigration.updateShopifyInstalled,
  });

  await runSingleMigration({
    name: 'update-forwarding-webhook',
    description: 'update account.configuration.forwardingWebhook from string to object with enabled and url fields',
    migrationFn: accountMigration.updateForwardingWebhook,
  });

  await runSingleMigration({
    name: 'migrate-invoice-account-id',
    description: 'convert Invoice.accountId from String to ObjectId',
    migrationFn: invoiceMigration.migrateInvoiceAccountId,
  });

  await runSingleMigration({
    name: 'migrate-invoice-charge-id',
    description: 'copy Invoice.chargeId to invoiceId and update chargeId structure',
    migrationFn: invoiceMigration.migrateInvoiceChargeId,
  });

  await runSingleMigration({
    name: 'migrate-invoice-platform',
    description: 'set Invoice.platform based on chargeId pattern',
    migrationFn: invoiceMigration.migrateInvoicePlatform,
  });

  await runSingleMigration({
    name: 'migrate-notification-theme',
    description: 'remove title object from theme and deisgn object from root, update titleColor',
    migrationFn: notificationMigration.migrateNotificationThemes,
  });
}

async function runSingleMigration({ name, description, migrationFn }) {
  let migration = await Migration.findOne({ name });
  if(migration) {
    if(migration.status === 'completed') {
      logger.info(`${name} db migration already completed`);
      return null;
    }
    if(migration.status === 'pending') {
      // Check if the pending migration is older than 24 hours
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      if(migration.updatedAt > oneDayAgo) {
        logger.info(`${name} db migration still running (pending)`);
        return null;
      }
      logger.info(`${name} db migration pending for more than 24 hours, re-running...`);
    }
  }
  if(!migration) {
    migration = await Migration.create({ name });
  }

  logger.info(`running db migration ${name}: ${description}`);
  return migrationFn().then((result) => {
    migration.status = 'completed';
    migration.results = result;
    logger.info({ result }, `${name} db migration completed`);
  }).catch((err) => {
    migration.status = 'failed';
    migration.error = serializeError(err);
    logger.error({ err }, '{$name} db migration failed');
    notifierService.notifyError(err, `${name} db migration failed`, { description });
    throw err;
  }).finally(async () => {
    await migration.save();
  });
}
