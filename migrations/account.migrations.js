/* eslint-disable no-await-in-loop,no-loop-func */
const Account = require('../app/account/models/Account');
const logger = require('../lib/logger').getLogger('migrations');

module.exports = {
  updateShopifyInstalled,
  updateForwardingWebhook,
};

async function updateShopifyInstalled() {
  const results = { migrated: 0, errors: [] };
  const BATCH_SIZE = 20;
  let batchPromises = [];
  const accounts = await Account.find({ 'shopify.installed': null });
  for(let i = 0; i < accounts.length; i += 1) {
    const account = accounts[i];
    account.shopify.forEach((shop) => {
      if(!shop.installed) {
        shop.installed = account.createdAt;
      }
    });
    if(account.isModified()) {
      batchPromises.push(account.save().catch((err) => {
        results.errors.push(err);
      }));
    }
    if((batchPromises.length >= BATCH_SIZE) || (i === accounts.length - 1)) {
      results.migrated += batchPromises.length;
      await Promise.all(batchPromises);
      batchPromises = [];
    }
  }
  return results; // Return the results object
}

async function updateForwardingWebhook() {
  const results = {
    total: 0,
    migrated: 0,
    failed: 0,
    errors: [],
  };

  const accounts = await Account.find({ 'configuration.forwardingWebhook': { $exists: true } })
    .select('configuration.forwardingWebhook')
    .lean();
  results.total = accounts.length;
  logger.info(`Found ${accounts.length} accounts to migrate forwardingWebhook`);

  const BATCH_SIZE = 100;
  const operations = [];

  for(let i = 0; i < accounts.length; i += 1) {
    const account = accounts[i];
    const currentWebhook = account.configuration.forwardingWebhook;

    operations.push({
      updateOne: {
        filter: { _id: account._id },
        update: {
          $set: {
            'configuration.forwardingWebhook': {
              enabled: !!currentWebhook,
              url: currentWebhook || null,
            },
          },
        },
      },
    });

    if(operations.length >= BATCH_SIZE || i === accounts.length - 1) {
      logger.info('Processing batch for forwardingWebhook migration');
      await Account.bulkWrite(operations, { ordered: false }).then(() => {
        results.migrated += operations.length;
      }).catch((error) => {
        results.failed += operations.length;
        results.errors.push({
          accountId: account._id,
          error: error.message,
        });
        logger.error({ error }, 'Failed to process batch for forwardingWebhook migration');
      });
      operations.length = 0;
    }
  }

  return results;
}
