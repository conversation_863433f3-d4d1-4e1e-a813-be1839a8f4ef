const mongoose = require('mongoose');

const migrationSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true,
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed'],
    default: 'pending',
  },
  results: {},
  error: {},
}, {
  timestamps: true,
  versionKey: false,
});

const Migration = mongoose.model('Migration', migrationSchema);

module.exports = Migration;
