# Change Log
All changes will be documented in this file.
This project *tries* to follow the [Semantic Versioning](http://semver.org) style.

## [6.69.x] - 2025-06-11
### Update
- shopify subscription management to use actual until date and auto-cancel subscriptions on frozen/cancelled shops

## [6.68.x] - 2025-05-29
### Add
- future invoices update API
- bluesnap cancel subscription API

### Update
- backoffice ps_admin login to set the req.session.email as well (to display in dashboard)

### Fix
- creating duplicate notificaitons on shopify reinstall
- update notification doesn't check if name already exists

## [6.67.x] - 2025-04-02
### Add
- stripe payment processor

## [6.66.x] - 2025-03-20
### Add
- analytics to notification list
- goal analytics includes notification analytics breakdown

## [6.65.x] - 2025-03-12
### Fix
- ESLint issues project wide

## [6.64.x] - 2025-01-16
### Change
- shopify products API from REST to GraphQL (completely different API)

## [6.63.x] - 2024-12-23
### Change
- puppeteer version update from v12 to v23, hopefully this will fix some of the issues on production ubuntu servers

## [6.62.x] - 2024-12-10
### Add
- support for product, location and name dynamic variables {{product.name}}, {{productName}}, {{name}}, {{location.country}}

## [6.61.x] - 2024-11-14
### Add
- shopify event type support (all/online/pos)

## [6.60.x] - 2024-09-11
### Add
- review request email automation for shopify and wix

## [6.59.x] - 2024-08-28
### Add
- support for wix reviews, subscription payments and form submissions
- invoices pagination support

## [6.58.x] - 2024-08-15
### Change
- migrate wix stores to wix ecommerce APIs

### Add
- support for wix restaurants, reservations and bookings that are now reliant on wix ecommerce webhooks/APIs

## [6.57.x] - 2024-07-17
### Fix/Change
- feed search index/query

## [6.56.x] - 2024-07-02
### Change
- account filters to filter in /get notification functions instead of not saving the event completely (this allows users to regret but still have the events)

## [6.55.x] - 2024-06-20
### Add
- account level configuration to filter names, emails, IPs (stream, reviews, notifications/get)
- invoice generate custom address, city, zip
- stream notifications countries filter
- shapo as a review source

### Update
- facebook to v20

## [6.54.0] - 2024-06-05
### Change
- invoices to be generated via API instead of BlueSnap IPN

## [6.53.0] - 2024-03-26
### Add
- form submission forwarding webhook

## [6.52.0] - 2023-11-30
### Add
- option to include/exclude countries in stream notifications
- `stopAutoSync` admin flag to reviews notifications 
- fetching images from thinkific /products API in addition to regular way

### Fix
- capterra reviews scrape

## [6.51.0] - 2023-09-26
### Add
- stamped review display based on `productId` and not just `reviewId` so a single `provesrc.display` call can show multiple reviews
- STREAM ONLY localization parameter support in `getWithName`

### Fix
- stamped API not working when apiKey query param is added

## [6.50.0] - 2022-08-26
### Add
- custom review webhook support

## [6.49.x] - 2022-06-12
### Add
- custom/other reviews support, e.g. from bbb.com

### Change
- starter plan pricing to $29/month and increase plan size to 20k + branding/whitelabel included

## [6.48.x] - 2022-01-23
### Add
- option to filter countries in stream notification

### Change
- notification response object to be slimmer and not include all kinds of server side settings

## [6.47.x] - 2021-12-15
### Add
- paypal as a payment option

### Fix
- Green invoices not sent sometimes when doc is produced close to 2am 

## [6.46.x] - 2021-10-03
### Add
- free plan

### Remove
- free trial

## [6.45.x] - 2021-07-13
### Add
- basic plan

## [6.44.x] - 2021-06-28
### Change
- social counter facebook to use official API (facebook login, etc) instead of scraping 

## [6.43.x] - 2021-05-31
### Add
- thinkific integration
- facebook conversion API calls on signup and purchase
- wordpress/woocommerce multisite support by not parsing host from event.siteUrl (multisite is usually on a path not a subdomain)

### Replace
- hideOnMobile toggle with mobileDesign select (hide, small, big)

## [6.42.x] - 2021-01-13
### Add
- blocked domains configuration to block domains that copy website content including ProveSource code

### Remove
- freemium plan

## [6.41.x] - 2020-12-23
### Add
- backoffice upgradable accounts endpoint

## [6.40.x] - 2020-11-25
### Add
- shopper approved reviews source
- feefo reviews source

## [6.39.x] - 2020-11-24
### Add
- visitor type targeting rule (all, new, returning)

## [6.38.x] - 2020-11-05
### Add
- Judge.me reviews

## [6.37.x] - 2020-10-14
### Add
- weekly summary emails

## [6.36.x] - 2020-10-04
### Add
- BigCommerce integration APIs
- PayPal IPN item_name as productName
- webhook recursive search for fields

### Change
- Wix app integration to max 1 site per account

## [6.35.x] - 2020-08-19
### Add
- sub acounts feature
- review shuffling so it doesn't show the latest/same reviews all the time

## [6.34.x] - 2020-08-05
### Add
- wix billing
- google reviews scraping proxy rotation

## [6.33.x] - 2020-07-19
### Add
- url suggestions feature
- notification last viewed field
- support for social media @ handlers (instagram, twitter)
- PayPal IPN router to create invoices in (Purple/ProveSource) GreenInvoice based on the money sender (AdsKeeper, etc)

### Fix
- when user changes email, mailerlite is not updated 

## [6.32.x] - 2020-07-15
### Change
- separate app and cron to different deployment/servers (due to headless chrome resource requirements and instability)
- google reviews fetching to use scraping with headless chrome puppeteer

## [6.31.x] - 2020-06-29
### Add
- new wordpress APIs `wp/setup` and `wp/uninstall` for initial setup (order import) and storing details about sites

## [6.30.x] - 2020-06-16
### Add
- zapier APIs `/zapier/notifications` and `/zapier/auth`

## [6.28.x] - 2020-05-06\
### Add
- support for `text` in social counter notification
- support for full URLs in social counter profiles
- getting started checklist data to onboarding

### Fix
- social counter notification not supporting profiles with dots
- emails showing up as user's name (reduct everything after "@" in `getName`)
- instagram login wall using a private API with login

### Replace
- `mailerlite` lib calls with `emailAutomation.service`

## [6.27.x] - 2020-04-22
### Add
- Track all forms option in Form Submissions
- Social Counter notification
- product link to notification even if "hide product details" is ON (productName = '')
- capterra reviews

## [6.26.x] - 2020-04-19
### Fix
- filter and hide products are separate, based on customers' use cases 

## [6.25.x] - 2020-03-25
### Add
- clickfunnels webhook test endpoint support `${webhookURL}/funnel_webhooks/test`
- trustpilot empty review text fallback to review title text
- ipn handler email lookup if no merchant transaction id

### Fix
- when hide product is ON, filter should be ignored
- product filtering causing Form Submissions to not work
- name not displayed when prefixed with whitespace
- shopify order webhooks using apiKey instead of shop domain
- profitwell updates not always working (changed mechanism to query for existing subscription and update it)

## [6.24.x] - 2020-03-05
### Add
- cron that checks shopify charges and subscriptions
- revenue aggregations (`ChargesDaily`, `ChargesMonthly`)
- elopage webhooks support

## [6.23.x] - 2020-02-20
### Add
- display URLs exclude
- invoice sort by date

### Remove
- APM, SPM and Sentry (possibly cause memory leaks)

## [6.22.x] - 2020-02-16
### Add
- cache to POST /notifications/get based on accountId + url

### Fix
- wix events customer name
- if product filter has no match event should be hidden

## [6.21.x] - 2020-01-27
### Add
- goal value
- goal hover & engagement conversions
- stream notification product filter include/exclude

## [6.20.x] - 2020-01-26
### Add
- show conversions from visitor's country first

## [6.19.x] - 2020-01-26
### Add
- country flag support

## [6.18.x] - 2020-01-20
### Add
- feed search and export

## [6.17.x] - 2020-01-13
### Add
- wix integration

## [6.16.x] - 2020-01-09
### Add
- support for `guid` in page visits notification (per URL, e.g. specific page views)

## [6.15.x] - 2019-12-30
### Add
- stamped reviews (apiKey support)
- delete reviews support
- `installed` date to shopify documents
- support for changing goal code tracking in PUT /goals API

### Fix
- reviews task `latestReview` query to use `find.sort.limit` instead of `findOne`

### Change
- prices
- reviews to be saved per account

### Remove
- shopify shop from removedShops on shopify installation
- `identifier` field from Google/FB reviews (replaced by `reviewId`)

## [6.14.x] - 2019-11-28
### Add
- redis caching on counter notification process results
- error 403 in configuration API for non active accounts

### Change
- shopify auto created notification to display only on the specific domain
- Yotpo api endpoint to fetch all reviews instead of site reviews

### Fix
- wrong invoices shown to customers due to GreenInvoice client name fuzzy search
- CHARGEBACK ipn having untilDate in the future
- notification priority duplicates (being 0)
- counter notification cache ignoring result validity (minimum events)
- counter notification bad cache when using guid (guid not included)
- duplicate facebook reviews in feed (pull request #5)

## [6.13.x] - 2019-11-10
### Add
- shopify webhooks product variant images support

### Fix
- facebook reviews images not being uploaded (due to missing `catch` on `insertMany` which dropped the whole procedure)
- goal analytics not measured if URL fits multiple goals

## [6.12.x] - 2019-10-27
### Add
- `/account/cancel-reason` API to store cancellation reasons

### Fix
- event queries with urls not escaped correctly for regex search

### Change
- affiliate association only if first time visit and not from Google

### Re-add
- facebook reviews filtering based on time of last review in db

## [6.11.x] - 2019-09-16
### Add
- no events indicator to notifications list
- mini feed for notifications
- shopify `shop/update` webhook listener for closed stores
- ProfitWell delinquent churn on cancellation without reason
- ThriveCart native webhook support
- Map Icon
- showMap icon disable procedure to notification disable CRON

### Fix
- shopify cancellation API (was sending `GET` instead of `DELETE`)
- mulitple shopify stores on same account, now ignores uninstalled stores
- Fix guid param didn't work for custom stream events

### Change
- move shops from account.shopify to account.removedShops on app/uninstall

### Remove
- shopify coupon limitations
- gravatar profile pictures from Stream notifications

## [6.10.x] - 2019-08-18
### Add
- Add goal tracking via code support
- Add email/name generation for Shopify webhook on POS orders (that are missing customer details)

## [6.9.x] - 2019-08-04
### Add
- Add Magento 1 support (webhook + notifications/get)
- Add configuration fields for custom form tracking
- Add support for sendowl webhooks
- Add DO spaces cdn cache purge after reviews cron
- Add ProveSource Ads leads api + save in Airtable
- Add shopify uninstall email from sendgrid

### Fix
- Fix shopify free plan more than 1 active notification

## [6.8.x] - 2019-07-31
### Add
- Add Yotpo reviews

## [6.7.x] - 2019-07-11
### Add
- Add facebook reviews
- Add webhook field find string type validation (e.g. `{name: {key: 'value'}}` should not be captured)
- Add `GET /account/zapier?apiKey` endpoint for Zapier API key auth
- Add `POST /account/uninstall` for integrating with

## [6.6.x] - 2019-06-13
### Add
- Add `reviews.io` integration
- Add `segment.io` event source integration
- Add `GET/PUT  /account/integrations` API
- Add `HEAD /webhooks/track` support (ThriveCart checks webhook exists with HEAD, dumbasses...)
- Add `account.loginType` field that can be either `email`, `google` or `shopify`
- Add `disableShopifyPaywall` flag
- Add support for `guid` in Shopify notification `getWithName` (uses `product.id`)

### Change
- Ignore affiliate referrer if the user came through a paid channel some time
- Change shopify restrictions to restrict only Shopify created accounts,
non-bluesnap accounts and accounts without the `disableShopifyPaywall` flag

## [6.5.x] - 2019-06-11
### Add
- Add trustpilot reviews support (cron, lib, create, get)

## [6.4.x] - 2019-06-05
### Add
- Add `sendInvoices` cron task based on `Account.configuration.sendInvoices<boolean>`

## [6.3.x] - 2019-06-04
### Add
- Add first party cookie support with `x-ps-first` header (see `cookieUtils`)
- Add `x-ps-first` to `Access-Control-Expose-Headers`

### Change
- Change reviews processor to not use `accountId` in query (same `placeId` multiple `accounts`)

## [6.2.x] - 2019-06-04
### Add
- Add reviews notification with Google reviews integration
- Add reviews cron locking, slack notification
- Add reviews cron to pm2 ecosystem configuration

### Change
- Change reviews cron schedule to be on 12am and 12pm

## [6.0.x] - 2019-05-27
### Add
- Add slack notification when free user tries to activate more than 1 notification

### Change
- Change free tier limits to allow only one active notification
- Change emails to be sent to all associated emails of the account (plan limit, charge failure, cancellation)
- Change maxmimum body size to `10mb` from default `100kb`

### Fix
- Fix invalid regex created (passing validation and then screwed in normalization), e.g. `provesrc.com\/`
- Fix sending emails to duplicate emails (sendgrid email)

## [5.5.x] - 2019-05-23
### Add
- Add company and customer names to account document on IPN
- Add `Account.emails` field to store all emails related with account (subscription, email changes)
- Add green invoice document lookup by names

### Fix
- Fix green invoice having `null` customers (failed with `TypeError`)

## [5.4.x] - 2019-05-16
### Add
- `Event.prototype.getHint()` function to get the best query index for each event model
- Add random email to stripe webhook events that have no email

### Change
- Change event queries to use `shortUrl` fields (capped to 900 characters) and indexed (~10x speed)
- Change forgot password mailer to sendgrid

### Fix
- Fix wrong sort key used with `webhookEvent` on `processNotification`
- Fix plan limit email templates (no impressions <> no notifications) + fix analytics query

### Remove
- Remove `url` indexes (slow)

## [5.3.x] - 2019-05-14
### Add
- Add `shortUrl` to url based events
- Add `event` collection queries time limitation (`maxTimeMS`)
- Add `account.affiliate.payout` tracking

### Change
- Change notification update to also update discriminator keys

### Fix
- Fix stream `maxConversions` capped to maximum of 10 in `processStream`
- Fix notification update (`POST /create`) not saving discriminator properties
- Fix affiliate MRR cacluation on negative IPNs

## [5.0.x] - 2019-05-07
### Add
- Add Elastic APM monitoring
- Add `hideProduct` setting for Stream notifications
- Add notification position `Bottom Center`

### Fix
- Fix google accounts not being activated if registered manually and then logging-in with Google

## [4.20.x] - 2019-05-05
### Add
- Add support for `notification.priority`
- Add `stats.notifications.priorityChanged` counter
- Add 90% limit reached email
- Add caching on GET `/account/configuration` Notification lookups
- Add `cycleEndDate` to `GET /billing/info` to show users when their plan resets

### Change
- Change active link to work on custom platform without value (whole notification clickable)

### Fix
- Fix plan limit reached email until date (for accounts with untilDate in 2038)

## [4.19.x] - 2019-05-01
### Add
- Add `/account/change-email` APIs

## [4.18.x] - 2019-05-01
### Add
- Add `cachegoose` and caching on `/notifications/get` (counting notifications)

## [4.17.x] - 2019-04-30
### Add
- (REVERTED) Add accounts data access layer cached on redis in `configuration` APIs

### Change
- Change `DELETE /events` to support request body (since encoded emails don't work)

### Update
- Update plan limit reached email template to include 14-day money back guarantee

## [4.16.x] - 2019-04-24
### Add
- Add `Info` notification support
- Add mailchimp webhook firstname/lastname support

## [4.15.x] - 2019-04-18
### Add
- Add account notifications create/update/delete stats
- Add plan limit reached email with SendGrid instead of Mailerlite
- Add goals stats to account

### Change
- Change product notifications to show the most expensive product
- Change shopify setup to create either stream or pagevisits notification, never both

### Fix
- Fix getting all products on Shopify order / webhook
- Fix notifications saved without track URL
- Fix account.installed not identifying localhost installations
- Fix shopify upgrades/downgrades not recorded correctly in ProfitWell

## [4.14.x] - 2019-04-16
### Add
- Add Goals feature
- Add js GET /configuration to decode url query param (base64)
- shopify webhook feed event

## [4.13.x] - 2019-04-07
### Add
- Add shopify page visits notification on setup
- Add not empty validation for Track URL in `/notifications/create`
- Add subscription charge failure reason to email
- Add subscription charge failure days left until cancellation
- Add custom branding plan support for shopify accounts
- Add bluesnap error description to /account/upgrade

### Fix
- Fix failed upgrade resetting account limit (i.e. non `CHARGE`/`RECURRING` IPNs resetting account limit)
- Fix subscription charge failure email not sent when no card
- Fix cancellation email to handle past/future tenses
- Fix subscription charge failure email sent right before cancellation (checking value of IPN `daysTillCancelDate`)

### Change
- Change subscription charge failure extension to 14 days
- Change GET `/onboarding` to not check for notifications and added db projection for speed

### Remove
- Remove all `users` collection related operations

## [4.12.x] - 2019-03-31
### Add
- Add image and localization to onboarding create step

### Fix
- Fix getting documents from Green Invoice (uses IPN `invoiceEmail`)
- Fix getting documents when customer has multiple clients in GreenInvoice (bffwear)

## [4.11.x] - 2019-03-28
### Add
- Add `GET /billing/documents` API to retrieve all documents from GreenInvoice

## [4.10.x] - 2019-03-28
### Add
- Add email activation

### Change
- Move signup trigger (mailerlite automations) to after email activation
- Improve `POST /notifications/create` error messages

## [4.9.x] - 2019-03-25
### Add
- Shop select for platforms (woo, wp, shopify, magento)

## [4.8.x] - 2019-03-14
### Add
- Add support for `whitelabel` in configuration API
- Add `specialWhitelabel` option to let free/starter plan have a whitelabel
- Add support for picking data from `products` array in webhooks

## [4.7.x] - 2019-03-12
### Add
- Add subscription charge failure automatic email
- Add support for `dashbordDebugMode` for customer debugging

## [4.6.x] - 2019-02-24
### Add
- Add `/account/updateCard` support
- Add request information to server error slack alerts
- Add SimilarWeb stats to signup slack alert when website is not small `IsSmall`
- Add similarWeb stats and red dot on installed and onboarding alerts
- Add gzip compression middleware
- Add support for Amex, Diners and Union Pay cards in `/updateCard` API
- Add WooCommerce plugin bug cover-up (replace httpss with https in product.image)
- Add support for Thinkific `order.created` webhook
- Add account configuration tracker toggle for mixpanel
- Add webhooks track `productName` search for Thinkific (`payload.course.name`)
- Add 10 days of subscription time when charge fails

### Change
- Change mongodb read preference to be secondary preferred (to avoid bottle necks)
- Ignore cloudflare.works in installed notifications (except cloudflare preview account)
- Change Shopify `GET /orders` to retrieve orders with `status=any` (previously only `open`)

### Fix
- Fix display URL matching to use normalized or clean (if not absolute)
- Fix mongodb secondary connection not working (read preference not existing)
- IPN related issues (failed upgrade - CONTRACT_CHANGE), wrong metrics stored

### Disable
- Disable storing user information in mongodb

## [4.5.x] - 2019-01-17
### Add
- `DELETE /events/delete` API
- stripe webhooks support `customer.created`, `order.created`, `recipient.created`
- mailchimp webhook `ip_signup` capture
- `googleAnalytics` tracker ON by default for new accounts
- `onboarding_complete_date` field to mailerlite

### Change
- shopify notification is not created automatically if orders=0
- ignore domains with `googleusercontent`, 'doubleads' and 'translator'
- `GET /webhooks/track` response to reflect the user must use POST
- ProfitWell churn date to today

### Fix
- emails not found in `form-urlencoded` requests
- clean events time limit not working
- www not removed from protocol-less URLs in url clean
- jwt server error in `/account/install`
- webhooks location overwritten by `undefined` values in `location` object
- `hideOwnConversions` not working with `getWithName`
- bluesnap upgrade error exception (circular dependency)
- `country` forced from `countryCode` in `/webhook/track`

## [4.4.x] - 2019-01-16
### Add
- support for `additionalLimit` Account property that adds limit only if paying customer
- support for `affiliate.commissionRate` to Account and IPNs
- support for Hotmart webhooks
- support for webhook `query string` data instead of `POST body`
- webhook error collection to collect information about webhooks
- webhook data collection to see data formats sent
- webhook GET 200 response (mailchimp status check is GET <webhook>)

## [4.3.x] - 2019-01-15
### Add
- support for Shopify coupons
- Shopify staff automatic 50% off

## [4.2.x] - 2019-01-01
### Add
- ProfitWell integration on `/ipn` calls
- `backgroundColor` from `Stream` title theme
- slack alert when user deactivates all notifications
- redis configuration option

### Fix
- non-full URLs are not stored lowercase
- SPA Hashed URLs not cleaned of query params in hash
- trailing slashes in contains/regex expressions
- mixed letter case in contains/regex (enforce lowercase)
- undefined in clean URL (`let retval; retval += 'string'`)

## [4.1.x] - 2018-12-26
### Add
- `accountId, date` compound index to clean events

### Fix
- wrong `date` key used in events find for webhook search
- webhooks not created on non-stream webhook events
- wrong key used in sort for clean events

### Remove
- webhook validation on `/notifications/create`

## [4.0.x] - 2018-12-23
### Add
- absolute/clean URLs track/display support
- `recentIPN` to `/billing/info`
- `settings.hideLocation` to Notification schema

### Fix
- edge case when shopify product doesn't exist
- wrong hint used for platform stream events

### Remove
- `visitor` analytics event type

## [3.8.x] - 2018-12-12
### Add
- onboarding APIs
- `onboarding.created` on shopify setup
- mailerlite `created_notification` on shopify trigger
- onboarding access by `ps_admin`
- support for email-less shopify and woocommerce events
- slack alerts for redis connection error

### Fix
- shopify install when email exists but user not logged in
- slack notifications for `onboarding complete`
- onboarding `pageVisits` default image
- webhook stream event require product `link` (should not)
- mailerlite automation fields
- IPN `account not found` on charge declined
- onboarding Stream notification missing `webhookId`

### Change
- GET `/account/configuration` API to use async/await
- `VisitorPing` expiry to 10 minutes
- GET `/account/configuration` use `req.headers.origin` to determine the installed website

## [3.7.x] - 2018-12-10
### Add
- shopify (real) domain to slack alert
- criteria to `possible double charge` alert
- `hideExactTimeStream` to notification schema
- zero impression field to mailerlite on plan limit trigger

### Change
- plan limit getter to ignore `customLimit` when plan's limit is higher

### Fix
- shopify properties not sent to mailerlite on user creation from Shopify
(race condition / subscribe request didn't complete before update subscriber)
- shopify order import

## [3.6.x] - 2018-12-06
### Add
- shopify notification on first time setup (reduce churn)
- domain, name and notification name to mailerlite and slack alerts

## [3.5.x] - 2018-12-02
### Add
- hints to event queries
- check if shopify is already installed (when clicking on Shopify app link it runs through the whole oauth phase)
- analytics info to plan limit reached slack notification
- mailerlite `shopify` field `true/false`
- mailerlite `shopify_unistalled` date field

### Change
- events query limit from 50 to 100
- events query default time limit from 7 to 14 days

### Fix
- multiple shops charge confirm
- plan limit remarketing slack notification

### Remove
- stream events default time limit

## [3.4.x] - 2018-11-29
### Fix
- redis limit data not refreshed on shopify charge

## [3.3.x] - 2018-11-22
### Add
- search for `firstName` and `lastName` and other name variations inside webhook data (optimized for common cases)
- search for `email` inside webhook data (common cases + extendible)
- events (emails, names, etc) to users collection

### Remove
- webhook email regex lookup force 1 email (just take the first one)

### Fix
- shopify orders import failure

## [3.2.x] - 2018-11-19
### Add
- query params tracking (utm, etc)
- google analytics clientId
- plan limit remarketing

### Change
- notification priority, stream is last

### Change
- notification priority, stream is last

## [3.1.x] - 2018-11-11
### Add
- `trackers` and `googleAnalytics` support to configuration

## [3.0.x] - 2018-11-09
### Add
- full shopify support

## [2.5.x] - 2018-11-04
### Add
- engaged visitors to analytics
- `cta`, `mobileTop` to notification settings
- `untilDate` to free subscription details in `/billing/info`

## [2.4.x] - 2018-10-28
### Add
- support for `hideOwnConversions`
- visitors to analytics
- missing Starter No Brand Yearly contract

### Fix
- CLTV and MRR metrics
- `psuid` generated twice for same user (if not set on first request)

### Replace
- `lastLogin` with `lastSeen` and update on `/notifications/list`

## [2.3.x] - 2018-10-25
### Add
- support for `ps_admin` cookie

## [2.2.x] - 2018-10-25
### Add
- account subscription metrics

## [2.1.x] - 2018-10-24
### Add
- support for `Someone` alternatives

## [2.0.x] - 2018-10-22
### Add
- live visitor notification count, including domain-less URLs support (localhost, IP, etc)

### Fix
- plan limit reached showing wrong number if user upgraded (should not add to email)
- location not returned when no `countryCode / country`
- Combo message `undefined`

## [1.20.x] - 2018-10-21
### Add
- wordpress user signups notification (+webhook)

## [1.19.x] - 2018-10-16
### Add
- support for email lookups in webhook (including regex through-out request body)
- webhook event source (+ zapier specific title)

### Fix
- subscription not cancelled for LTD refunds (due to `untilDate`)
- bot filtering

### Change
- email search algorithm no longer fails when finding more than 1 email

## [1.18.x] - 2018-10-15
### Add
- configuration `branding` properties get/set
- webhooks track map state <> stateCode and country <> countryCode

## [1.17.x] - 2018-10-07
### Add
- analytics: all types of conversions, webhooks, woocommerce
- analytics by date API
- `ps_uid` to `streamEvents`
- support for seconds timestamp in webhooks track
- support for account `customLimit`

### Fix
- slack double charge notification

## [1.16.x] - 2018-09-13
### Add
- full magento 2 support
- timestamps to `Event` models

### Fix
- billing cycle dates not calculated correctly on 31st of month
- shopify webhooks failing (add debugging logs)

## [1.15.x] - 2018-09-12
### Add
- alert when possible double charge occurs (IPN)

## [1.14.x] - 2018-08-30
### Add
- support for product in webhook root data
- support for string timestamp in webhook track data

### Fix
- Track expression must not be empty error when using webhook

## [1.13.x] - 2018-08-29
### Add
- support for shopify billing API

## [1.12.x] - 2018-08-23
### Add
- support `products`, `total` and `currency` properties in webhook

### Remove
- host check on shopify/woocommerce events

## [1.11.x] - 2018-08-22
### Add
- track webhook for stream must include `email` parameter
- `Unlimited` plan

### Change
- visitor counting mechanism (add redis key to check if already counted in last 20 seconds)

## [1.10.x] - 2018-08-14
### Add
- affiliate program

### Change
- redis expire date to be billing cycle start date + 1 month (instead of untilDate, which is different for LTD)

## [1.9.x] - 2018-08-08
### Add
- `magento 2` webhooks support
- bot filter in visitor count
- cookie reset when subscription has changed / updated
- support for `state` and `stateCode` in webhook track
- `host` to `event` find / update queries (use indexes)
- `Gorilla` and `Da Boss` plans

### Change
- stream location to return `stateCode` and `state` only if in USA
- `untilDate` not changed on refund or cancellation
- visitor count mechanism to be on `/account/configuration`

## [1.8.x] - 2018-07-30
### Add
- shopify support
- `sessionShowOnce` settings property
- `/leads/preview` to track latest previews

### Change
- slack channel for notification related events
- `host` for installed notification now taken from query param and fallback to `origin` header

## [1.7.x] - 2018-07-12
### Add
- `anonymize` setting to stream notifications
- `randomize` to account settings
- support for `Stream` notifications in `getWithName`

### Fix
- regex expressions being normalized

## [1.6.x] - 2018-07-05
### Add
- `analytics` APIs
- bypass for visitor limit with `ps_debug`
- `referrer` and `source` to account documents

### Fix
- fake timestamps in the future for `trackForm` API
- multiple stream notifications events are not sorted by time

## [1.5.x] - 2018-07-04
### Add
- users that selected a plan to "plan selected" group in mailerlite

## [1.4.x] - 2018-06-28
### Add
- Automatic upgrade emails using 'plan limit reached' `trigger`
- threshold test to form events timestamp (up 5 days in past)
- regex validation in `/notifications/create`

### Fix
- billing date being first day of month

## [1.3.x] - 2018-06-25
### Add
- `state` and `stateCode` to ip location
- slack notification about upgrade intents
- slack notifications about critical errors
- name to `feed` data

## [1.2.x] - 2018-06-19
### Add
- Free plan instead of trial

### Change
- Slack notification format

### Fix
- woocommerce track doesn't accept null product image

## [1.1.x] - 2018-06-13
### Add
- a trigger for remarketing sent
- `x-forwarded-host` header to debug loading in iframes
- `recent_ipn` to mailerLite (to find cancelled accounts)
- notification state change trigger
- email to notification state change trigger

### Change
- `installed` property on account to `Date`

## [1.0.x] - 2018-06-12
### Add
- WooCommerce dedicated webhook and integration
- mailerLite integration
- action triggers:
    - `presignup`
    - `signup`
    - `login`
    - `install`
    - `subscriptionChange`
    - `notificationCreated`
    - `notificationUpdated`
- `subscriptionActive` to `/account/configuration` GET response
- custom location support to `/webhooks/track`

## [0.21.x] - 2018-06-07
### Add
- `firstName` and `lastName` properties to `/track*` APIs
- stream visitor name prioritizes `firstName` and `lastName` from request data
- `email` to subscription details (sometimes it's different)

## [0.20.x] - 2018-06-01
### Add
- regex support to url match type

### Remove
- random factor from notification processing

### Change
- mailchimp integration to mailerlite

## [0.19.x] - 2018-05-17
### Add
- Account activity feed, includes webhook and form events
- `ps_signup` cookie track conversions and goals
- `timeLimit` support to `Stream` notifications
- slack notifier
- `/leads/presignup`
- `webhook` url-encoded form data support

### Fix
- edge case when there are pages with a single view and no minimum to display so the result is valid.
    Added a random factor to the number of events to process.
- URL encoding on /notifications/get
- Combo `minimumDay` not normalized

### Change
- gravatar name search (do not use `displayName` which is a username)

## [0.18.x] - 2018-05-10
### Add
- stream notification
- `lastLogin` date to account documents
- `ps_login` cookie to identify an account owner
- `remarketing{preview, wizard}` to `/account/configuration`
- stream event saving on signup (google and regular)
- http headers filtering in `httpLogger`
- `ip` support to webhook tracking and PS signups stream

### Change
- `ps_session` expiry to 180 days

### Fix
- redis ttl
- stream notifications event sorting
- stream notifications email filtering
- `/account/configuration` property filtering
- `null` initials with gravatar profile

### Remove
- webhook email track ip location (it will be webhook server ip)

## [0.17.x] - 2018-05-06
### Add
- localization support
- `Titan` plan support

### Fix
- events not tracked if no trial or subscription

### Change
- monster plan limit

## [0.16.x]
### Add
- CloudFlare hooks support

### Fix
- notification edit changing notification active status

## [0.15.x]
### Add
- combo notification
- email to team when new signup (google included)

### Fix
- notifications not returned when no events today and no time limit

## [0.14.x] - 2018-04-25
### Add
- notification's active state handlers

## [0.13.x] - 2018-04-23
### Add track / display URLs `contains` support

## [0.12] - 2018-04-22
### Add
- support for `settings.allTimeEvents` on notifications
- support for aggregating multiple events on the same day
- wrong minutes returned when there are days with no events in the middle (ex. events: today ... 5 days ago)
- `total` to all bill models and auto migrate existing bills
- `visitorsMonth` and `visitorsLimit` to `/billing/info`

## [0.11.x] - 2018-04-19
### Add
- `/account/forgot` for password reset link to email
- `/account/reset` for password resetting from email link

### Fix
- Max random factor (always selected 1)
- Max random factor can now count the whole event counts

## [0.10.8] - 2018-04-18
### Fix
- Cookies not being reset when upgrading a plan (querying redis constantly)

## [0.10.7] - 2018-04-18
### Fix
- passwords being overwritten on IPNs (account.save)
- whitelabel missing from /account/configuration schema

## [0.10.6] - 2018-04-18
### Fix
- `account.configuration.whitelabel` not set on lifetime deal addon IPN

## [0.10.5] - 2018-04-18
### Fix
- reading properties of null subscription

## [0.10.4] - 2018-04-18
### Add
- support for lifetime deal addon and whitelabel

## [0.10.3] - 2018-04-16
### Add
- lifetime deal visitor limits to enum

## [0.10.2] - 2018-04-16
### Add
- lifetime deals support `/billing/ipn`

## [0.10.1] - 2018-04-16
### Modify
- method of checking if trial is active that combines `created` with `untilDate`

## [0.10.0] - 2018-04-15
### Add
- visitor limits to accounts, when no subscription, or passed number of visitors
- `noSubscription` value to redis to avoid querying mongo when an account is not subscribed yet

### Fix
- tests related to psCookies

## [0.9.1] - 2018-04-12
### Fix
- Jackpot negative minutes edge case

## [0.9.0] - 2018-04-12
### Add
- `hideExactTime` to request schema and notification model

## [0.8.2] - 2018-04-11
### Disable
- mongo/redis limit lookups until we launch limits

## [0.8.1] - 2018-04-11
### Fix
- fyntax error when no `account.subscription || account.subscription.plan`

## [0.8.0] - 2018-04-11 (ProveSource Migration)
### Add
- `/billing/info` returns trial details
- `/billing/ipn` adds more data to `Account` model
- BlueSnap contracts and plan handling for limits (future implementation)
- `/account/mailto` link generator (mailto developer)
- `/account/install` generated page to show a developer how to install

### Modify
- Transform all urls to lowercase (events, notifications)
- `/webhooks/track/:id` now checks for notification existance

### Fix
- Jackpot error, when `minimumDays` and number of events is not the same

## [0.7.5] - 2018-04-05
### Modify
- the minimum number for the random factor to be 1 (so when there's little events there's still a random factor)

## [0.7.4] - 2018-04-04
### Fix
- Fix edge case when number of events in db is less that `minimumDays`

## [0.7.0-0.7.3] - 2018-04-03
### Add
- subscription data to account model
- `/billing/info` API for retrieving subscription and billing data for account

### Fix
- notification max randomness factor must be at least 5
- notification title backgroundColor key typo
- params.displayURLs is null and trying to access its length value

## [0.6.5-0.6.7] - 2018-03-27
### Fix
- notification and event find queries based on notification `autoTrack` and `manuallyShow`
- `PageVisits` notifications not returned because `manuallyShowNotifications` in query

## [0.6.1-0.6.4] - 2018-03-27
### Add
- `/account/isInstalled` API
- `/billing/ipn` API to record IPNs from Bluesnap
- auth type `noAuth`

### Modify
- body parser configuration to router level
- ipn body parser is now `urlencoded`
- ipn model encapuslates IPN data in a subdocument

## [0.6.0] - 2018-03-25
### Add
- support for `trackURL` as array
- `/notifications/shouldAutoTrack` API
- `/events/trackForm` API for the `autoTrack` feature
- random factor results for all cases (when `minimumToDisplay` exists or not)

### Fix
- `/notifications/get` returning count=0 sometimes for conversion notifications
- `timeLimit` not being enforced on `/notification/get {name}`

## [0.5.9] - 2018-03-16
### Add
- Enable entire notification to be a clickable link

## [0.5.8] - 2018-03-15
### Fix
- Error when hideExactNumber.max value is null

## [0.5.7] - 2018-03-13
### Add
- Support for hiding the exact number of events schema

## [0.5.6] - 2018-03-09
### Modify
- Logs `reqId` to `req.id` to preserve the format from `httpLogger`

## [0.5.5] - 2018-03-09
### Add
- `reqId` attached to all logs using `continuation-local-storage`
- `req.remoteAddress` now uses proxy headers from nginx if available
- `X-Request-ID` to response headers

## [0.5.1] - 2018-03-08
### Add
- Add internal info/data to `ErrorFactory` constructor and update error results to have more data
- `schemaHandler` add request params to error internal data to be logged

### Fix
- `/account/configuration` returns empty object when account has no configuration

### Modify
- `/notifications/create` accepts an empty `image` string

## [0.5.0] - 2018-03-08
### Add
- `/notifications/get` timeLimit support
- `/notifications/get {with url}` returns also `conversion` notifications with `displayURLs`
- `trust proxy` config (should show us real IP in logs)
- `/notification/create` supports updating notification by passing `_id`
- `/account/configuration` error when account doesn't exist

### Modify
- `/account/configuration` GET auth priority

## [0.4.0] - 2018-03-05
### Modify
- `/webhooks/track` does not require auth or `accountId`
- `WebhookEvent` no longer includes `accountId`
- `/notifications/get` with name does not search for events with `accountId`

## [0.3.0] - 2018-03-04
### Modify
- `/webhooks/track` support tracking webhooks without `guid`
- `/notifications/get` `guid` is now optional (webhook can have a `null` guid, like a generic webhook)

## [0.2.0] - 2018-03-04
### Modify
- `/notifications/get` replace cookie with `unique` POST param

### Fix
- `/account/configuration` authorizations based on HTTP method
- `/events/track` with url cleanUrl not being the same as a basic URL (normalization required)

## [0.1.1] - 2018-03-01
### Fix
- Cookie not being set on JSClient.

## [0.1.0] - 2018-03-01
### Modify
- endpoint 'authType' OR 'noAuth' configuration is now mandatory

### Fix
- endpoint '/account/configuration' responding with 401

## [0.0.3] - 2018-02-28
### Add
- tests for notification 'get' API
- pm2 configuration for clustering

### Fix
- All notification 'get' errors (schema, models, TypeError)

## [0.0.2] - 2018-02-26
### Fix
- notification 'get' mobile schema screwed all validations

## [0.0.1] - 2018-02-26
Initial Release
### Add
- APIs:
	- account:
		- Signup
		- Login
		- Google Login
	- events:
	    - track (mobile, webhook, url)
	- notifications:
	    - create
	    - get (mobile, by id, by url)
	    - list
	    - delete
    - mobile
        - track
    - webhooks
        - generate
        - track
- Middlewares: auth, error, response, headers
