module.exports = {
  apps: [{
    name: 'cron-server',
    script: './cron-server.js',
    watch: false, // This restarts the server when a change to file is made, we use 'reload' instead
    env: {
      NODE_ENV: 'production',
    },
    instances: 0,
    exec_mode: 'cluster',
    restart_delay: 10000, // 10 seconds between launches for alerts
    node_args: '--max_old_space_size=1536',
  }, {
    name: 'cron-app',
    script: 'cron/app.js',
    watch: false,
    env: {
      NODE_ENV: 'production',
      TYPE: 'cron',
    },
  }],
};
