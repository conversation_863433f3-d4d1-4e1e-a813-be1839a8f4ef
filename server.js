/* eslint-disable global-require */
const PORT = process.env.PORT || 3000;
const express = require('express');
const consolidate = require('consolidate');
const { version } = require('./package.json');
const logger = require('./lib/logger')('server.js');
const migrationRunner = require('./migrations/migrations.runner');

let databaseLoader = null;
if(process.env.NODE_ENV !== 'test') {
  setupProcessListeners();
  databaseLoader = require('./lib/mongooseLoader');
} else {
  databaseLoader = require('./lib/mongooseLoader/testLoader');
}

const config = require('./config');
const cachegooseLoader = require('./lib/cachegooseLoader');
const middlewares = require('./middleware/middlewareLoader');
const slack = require('./lib/apis/slackNotifier');
const cronChecker = require('./cron/cron-checker');

let server;

module.exports = (function () {
  const info = { version, PORT, env: process.env.NODE_ENV || 'development' };

  const app = express();

  app.set('trust proxy', 1);
  app.disable('x-powered-by');

  app.engine('html', consolidate.handlebars);
  app.set('view engine', 'html');
  app.set('views', './app/templates');

  middlewares.load(app);
  server = app.listen(PORT);
  server.keepAliveTimeout = 61 * 1000;
  server.headersTimeout = 62 * 1000;
  cachegooseLoader('redis', config.redis.port, config.redis.host);
  databaseLoader.load(config.dbUrl, config.mongodb.readPref).then(() => {
    logger.info({ info }, 'server up');
    return migrationRunner.run().then(() => {
      logger.info('Migrations completed successfully');
    }).catch((err) => {
      logger.error({ err }, 'Migration script failed');
      slack.notifyError(err, 'Migration script failed');
    });
  }).catch((err) => {
    logger.error({ err, info, critical: true }, 'database connection failed');
    slack.notifyError(err, 'database connection load failed');
    cleanShutdown(1);
  });
  cronChecker.scheduleCheckers();
  return app;
}());

function setupProcessListeners() {
  process.on('error', (error) => {
    logger.error({ error, critical: true }, 'process error, shut down');
    slack.notifyError(error, 'process error, shut down');
    cleanShutdown(1);
  });

  process.on('unhandledRejection', (err, p) => {
    logger.error({ err, critical: true }, 'unhandled promise rejection');
    slack.notifyError(err, 'unhandled promise rejection');
  });

  process.on('uncaughtException', (err) => {
    logger.error({ err, critical: true }, 'uncaught exception, shut down');

    if(err && err.code) {
      const bind = (typeof port === 'string' ? 'Pipe ' : 'Port ') + PORT;
      switch(err.code) {
      case 'EACCES':
        logger.error({ error: err, bind, critical: true }, 'bind requires elevated privileges');
        return process.exit(1);

      case 'EADDRINUSE':
        logger.error({ error: err, bind, critical: true }, 'bind is already in use');
        return process.exit(1);
      default:
        break;
      }
    }

    slack.notifyError(err, 'uncaught exception (shutdown)');
    return cleanShutdown(1);
  });

  process.on('SIGINT', () => {
    logger.info('server received kill signal, graceful shut down');
    cleanShutdown();
  });

  process.on('warning', (warning) => {
    logger.warn(warning, 'process warning');
    slack.notifyError(warning, 'process warning', { webhook: config.slack.processWarnings });
  });
}

function cleanShutdown(code) {
  const critical = !!(code && code !== 0);
  if(server) {
    server.close(() => {
      logger.info({ critical }, 'server closed remaining connections');
    });

    setTimeout(() => {
      logger.error({ critical }, 'server could not close connections, force shut down');
    }, 1000);

    setTimeout(() => {
      process.exit(code || 0);
    }, 1100);
  } else {
    logger.info({ critical }, 'nothing to cleanup');
    setTimeout(() => {
      process.exit(code || 0);
    }, 1100);
  }
}
