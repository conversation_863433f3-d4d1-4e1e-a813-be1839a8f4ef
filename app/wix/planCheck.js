/* eslint-disable max-len */
const authTypes = require('../../middleware/authTypes');
const logger = require('../../lib/logger')('wixPlanCheck');
// const Account = require('../account/models/Account');
// const ErrorFactory = require('../../lib/errors/ErrorFactory');
const notifierService = require('../common/notifier.service');
const wixService = require('./wix.service');
const WixData = require('./models/WixData');
// const subscriptionService = require('../billing/subscription.service');
// const config = require('../../config');
// const { TRANSACTION_TYPES, SUBSCRIPTION_SOURCE } = require('../billing/constants');
// const profitWellController = require('../billing/profitwell.controller');
// const redis = require('../../lib/redisClient').getClient();
// const triggers = require('../../lib/triggers');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
  },
  async handle(req, res, next) {
    const { accountId } = req.locals;
    logger.info({ accountId }, 'plan check');

    try {
      const { account, site } = await wixService.getBillingInfo(accountId);
      if(!site || !site.billing) {
        return next();
      }

      const { packageName: plan, billingCycle } = site.billing;
      if(account.isSubscriptionActive()
        && account.subscription.period === billingCycle
        && account.subscription.plan === plan) {
        return next();
      }

      (new WixData({
        eventType: 'BillingInfo',
        instanceId: account.wix[0].instanceId,
        data: site.billing,
      })).save().catch((err) => {
        logger.error({ err }, 'failed to save wix data');
      });

      // const subscription = subscriptionService.createSubscription(
      //   account.id, SUBSCRIPTION_SOURCE.wix, plan, billingCycle,
      // );
      // await profitWellController.updateSubscription(account.id, {
      //   email: account.email,
      //   plan: subscription.contractName,
      //   value: subscriptionService.getPlanPrice(plan) * 0.7 * 100,
      //   interval: profitWellController.getInterval(billingCycle),
      //   startDate: Date.now(),
      // }).catch((err) => {
      //   logger.error({ err }, 'failed to handle profitwell');
      // });

      // redis.delAsync(account.id);

      // triggers.subscriptionStateChanged(
      //   account,
      //   TRANSACTION_TYPES.CHARGE,
      //   account.subscription,
      // );

      // account.subscription = subscription;
      // await account.save();
      //
      // const message = `wix plan purchased ${account.email}: ${plan}`;
      // notifierService.notify(message, null, config.slack.wix);

      return next();
    } catch(err) {
      const msg = 'failed to check wix billing';
      logger.error({ accountId, err }, msg);
      notifierService.notifyError(err, msg, { accountId });
      return next(err);
    }
  },
};
