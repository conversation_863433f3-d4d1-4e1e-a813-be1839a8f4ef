const mongoose = require('mongoose');

const WixData = new mongoose.Schema({
  eventType: { type: String, required: true },
  instanceId: { type: String, required: true },
  token: { type: String },
  data: Object,
}, { timestamps: true, collection: 'wixData' });

WixData.index({ createdAt: -1 }, {
  expires: '30d',
  partialFilterExpression: {
    eventType: { $eq: 'com.wix.ecommerce.orders.api.v2.OrderEvent' },
  },
});

module.exports = mongoose.model('WixData', WixData);
