const mongoose = require('mongoose');
const StreamEvent = require('../../events/models/StreamEvent');
const product = require('../../events/models/Product');
const { WIX_EVENT_TYPES, WIX_CATALOG } = require('../../constants');

const Product = new mongoose.Schema(product, { _id: false });
const WixEventObj = Object.assign({
  type: { type: String, enum: Object.values(WIX_EVENT_TYPES) },
  orderId: String,
  instanceId: String,
  products: [Product],
  status: { type: String },
  currency: String,
  total: Number,
  domain: String,
}, StreamEvent.schema.obj);

const WixEvent = new mongoose.Schema(WixEventObj, {
  timestamps: true,
  collection: 'wixEvents',
});
WixEvent.index({ accountId: 1, date: -1 });
WixEvent.index({ accountId: 1, orderId: 1 }, { unique: true });
WixEvent.methods = Object.assign({}, StreamEvent.schema.methods);
WixEvent.statics.getHint = function getHint() {
  return { accountId: 1, date: -1 };
};

function getEventName(type) {
  switch(type) {
  default:
    return 'Wix Event';
  case WIX_EVENT_TYPES.storeOrder:
    return 'Wix Store Order';
  case WIX_EVENT_TYPES.restaurantOrder:
    return 'Wix Restaurant Order';
  case WIX_EVENT_TYPES.reservation:
    return 'Wix Reservation';
  case WIX_EVENT_TYPES.booking:
    return 'Wix Booking';
  case WIX_EVENT_TYPES.subscription:
    return 'Wix Subscription';
  case WIX_EVENT_TYPES.form:
    return 'Wix Form Submission';
  }
}

WixEvent.methods.getFeedEventName = function getName() {
  return getEventName(this.type);
};

WixEvent.statics.getEventName = getEventName;
WixEvent.statics.getEventType = (appId) => {
  switch(appId) {
  default:
  case WIX_CATALOG.stores:
    return WIX_EVENT_TYPES.storeOrder;
  case WIX_CATALOG.restaurants:
    return WIX_EVENT_TYPES.restaurantOrder;
  case WIX_CATALOG.bookings:
    return WIX_EVENT_TYPES.booking;
  case WIX_CATALOG.reservations:
    return WIX_EVENT_TYPES.reservation;
    // case WIX_CATALOG.PRICING_PLANS:
    //   return WIX_EVENT_TYPES.subscription;
  }
};


module.exports = mongoose.model('WixEvent', WixEvent);
