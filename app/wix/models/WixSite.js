const mongoose = require('mongoose');

const Wix = require('../../account/models/Wix');

const WixSite = Wix.clone();
WixSite.add({
  noConnectedAccount: { type: Boolean },
  accountId: { type: mongoose.SchemaTypes.ObjectId, ref: 'Account', default: null },
  lastInstallEmail: { type: Date },
});
WixSite.set('timestamps', true);
WixSite.set('collection', 'wixSites');
WixSite.index({ instanceId: 1 }, { unique: true });

module.exports = mongoose.model('WixSite', WixSite);
