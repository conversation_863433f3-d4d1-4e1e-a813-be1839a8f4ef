/* eslint-disable prefer-destructuring */
const _ = require('lodash');
const authTypes = require('../../middleware/authTypes');
const config = require('../../config');
const wixService = require('../wix/wix.service');
const accountService = require('../account/account.service');
const notifier = require('../common/notifier.service');
const cryptoUtils = require('../../lib/utils/cryptoUtils');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const { PLATFORMS } = require('../notifications/constants');
const analyticsService = require('../common/analytics.service');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {
      token: { type: 'string', required: true },
    },
  },
  async handle(req, res, next) {
    const { token } = req.query;
    const decrypted = cryptoUtils.safeDecrypt(token, config.cryptoKeys.wixInstallEmail);
    const instanceId = decrypted && JSON.parse(decrypted);
    if(!instanceId) {
      throw ErrorFactory('invalid token', 400, { token });
    }
    return continueInstall(req, res, next, { instanceId, state: 'continue-install' });
  },
  continueInstall,
};

// This function is a helper called from the route handle
// may also be used from other endpoints where instanceId is definite
async function continueInstall(req, res, next, { instanceId, state }) {
  let wixSite;
  let account;
  const accountId = req.session && req.session.accountId; // logged in
  const notifyData = { accountId, instanceId, state };
  try {
    wixSite = await wixService.updateSite(instanceId).then(({ site }) => site); // gets/creates wixSite doc
    const email = wixSite.ownerEmail || _.get(wixSite, 'ownerInfo.email');
    account = await wixService.getConnectedAccount(instanceId); // gets the account which the wixSite is connected to
    notifyData.account = _.pick(account, ['id', 'email']);
    notifyData.email = email;
    // if wix site is already connected to an account
    if(account) {
      if(!accountId) {
        // not logged in and wix site is connected
        notifier.notifyWix('tried to connect already connected site (not logged in)', notifyData);
        return res.render('text-box', {
          title: 'Wix site already connected',
          text: connectedNotLoggedInTemplate(wixSite.siteName),
        });
      }
      if(accountId !== account.id) {
        // wix site already connected to a different account
        notifier.notifyWix('tried to connect already connected site (different account)', notifyData);
        return res.render('text-box', {
          title: 'Wix site already connected',
          text: connectedAnotherAccountTemplate(wixSite.siteName),
        });
      }
      // logged in and session account id matched wix account id then redirect to dashboard
      notifier.notifyWix('tried to connect site again', notifyData);
      return res.render('text-box', {
        title: 'Wix site already connected to your account',
        text: connectedYourAccountTemplate(wixSite.siteName),
      });
    }

    const completeParams = {
      source: PLATFORMS.wix,
      ...(wixSite.url && {
        website: wixSite.url,
        domain: wixSite.url,
      }),
    };
    if(accountId) {
      // if wix site is not connected and the user is logged in, install the site
      account = await accountService.getAccount(accountId);
      if(account && account.canInstallWixSite()) {
        const { orders, forms, subscriptions } = await wixService.integrateSite(account, wixSite, { state });
        completeParams.events = orders + forms + subscriptions;
        analyticsService.trackInstalledApp(account.id, completeParams);
        return res.redirect(`${config.consoleUrl}/#/integration-complete?${new URLSearchParams(completeParams).toString()}`);
      }
    } else {
      // if wix site is not connected and the user is not logged in
      account = await accountService.getAccountByEmail(email);
      notifyData.account = _.pick(account, ['id', 'email']);
      // install if owner email exists and email verified by wix
      if(account && _.get(wixSite, 'ownerInfo.emailStatus', '').startsWith('VERIFIED')) {
        // if account can add site
        if(account.canInstallWixSite()) {
          const { orders, forms, subscriptions } = await wixService.integrateSite(account, wixSite, { state });
          req.session.accountId = account.id;
          req.session.apiKey = account.apiKey;
          req.session.email = account.email;
          completeParams.events = orders + forms + subscriptions;
          analyticsService.trackInstalledApp(account.id, completeParams);
          return res.redirect(`${config.consoleUrl}/#/integration-complete?${new URLSearchParams(completeParams).toString()}`);
        }
      }
    }
    // all other cases send to signup/login with a ps_wix cookie, install will occure on first login
    notifier.notifyWix('continue install needs to signup', notifyData);
    res.cookie('ps_wix', instanceId, config.getCookieConfig());
    return res.render('text-box', { title: 'Signup Required', text: getSignupTemplate() });
  } catch(err) {
    notifier.notifyError(err, 'wix continue install failed', notifyData);
    return res.render('text-box', { title: 'Wix App Install Failed', text: getFailedTemplate() });
  }
}

function connectedYourAccountTemplate(siteName) {
  let html = `Wix site <strong>${siteName}</strong> is already connected to your account<br>`;
  html += `<a href="${config.consoleUrl}" class="ps-button mt-20">Go to Dashboard</a>`;
  return html;
}

function connectedNotLoggedInTemplate(siteName) {
  let html = `Wix site <strong>${siteName}</strong> is already connected, but you are not logged in<br>`;
  html += `<a href="${config.consoleUrl}" class="ps-button mt-20">Login</a>`;
  return html;
}

function connectedAnotherAccountTemplate(siteName) {
  let html = `Wix site <strong>${siteName}</strong> is already connected to another account<br>`;
  html += `<a href="${config.consoleUrl}" class="ps-button mt-20">Go back</a>`;
  return html;
}

function getSignupTemplate() {
  let html = 'To complete the Wix app installation, login or create a new account in ProveSource<br>';
  html += 'Either you already have another wix site connected to your account or your email is not verified<br>';
  html += `<a href="${config.consoleUrl}/#/signup" class="ps-button mt-20 w-100">Signup</a><br>`;
  html += `<a href="${config.consoleUrl}" class="ps-button mt-5 w-100">Login</a>`;
  return html;
}

function getFailedTemplate() {
  let html = 'Please contact our support at <a href="mailto:<EMAIL>"><EMAIL></a> or via our live chat';
  html += `<a href="${config.consoleUrl}" class="ps-button mt-20">Go to Dashboard</a>`;
  return html;
}
