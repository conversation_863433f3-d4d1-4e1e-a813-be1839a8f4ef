const authTypes = require('../../middleware/authTypes');
const { PAID_PLANS, PERIODS_ARRAY } = require('../account/plansEnum');
const notifierService = require('../common/notifier.service');
const logger = require('../../lib/logger')('wixCharge');
const wixService = require('./wix.service');


module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    properties: {
      plan: { enum: PAID_PLANS, required: true },
      period: { enum: PERIODS_ARRAY, required: true },
    },
  },
  async handle(req, res, next) {
    const { accountId, email } = req.locals;
    const { plan, period } = req.query;
    try {
      const checkoutUrl = await wixService.getCheckoutUrl(accountId, { plan, period });
      res.body = { checkoutUrl };
      return next();
    } catch(err) {
      const msg = 'Failed to get wix checkout page';
      const data = {
        accountId, email, plan, period,
      };
      logger.error(data, msg);
      notifierService.notifyError(err, msg, data);
      return next(err);
    }
  },
};
