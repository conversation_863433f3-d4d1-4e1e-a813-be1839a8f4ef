const authTypes = require('../../middleware/authTypes');
const config = require('../../config');
const logger = require('../../lib/logger')('wix install');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {
      token: { type: 'string', required: true },
    },
  },
  handle(req, res, next) {
    const { token } = req.query;
    logger.info('new wix app install');
    let url = `https://www.wix.com/app-oauth-installation/consent?appId=${config.wix.appId}&redirectUrl=${config.wix.redirectUrl}`;
    if(token) {
      url += `&token=${token}`;
    } else {
      url += '&state=dashboard';
    }
    res.redirect(url);
  },
};
