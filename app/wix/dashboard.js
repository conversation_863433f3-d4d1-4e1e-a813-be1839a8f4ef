const config = require('../../config');
const authTypes = require('../../middleware/authTypes');
const wixService = require('./wix.service');
const notifierService = require('../common/notifier.service');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const wixContinueInstall = require('../wix/continueInstall');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {},
  },
  async handle(req, res, next) {
    const { query } = req;
    const payload = wixService.decodeSignedInstance(query.instance);
    if(!payload || !payload.instanceId) {
      const err = ErrorFactory('Invalid wix instanceId parameter');
      notifierService.notifyError(err, 'wix/dashboard no instanceId', { query, payload });
      return next(err);
    }
    const { instanceId } = payload;
    const account = await wixService.getConnectedAccount(instanceId);
    if(!account) {
      return wixContinueInstall.continueInstall(req, res, next, { instanceId, state: 'dashboard' });
    }
    req.session.accountId = account.id;
    req.session.apiKey = account.apiKey;
    req.session.email = account.email;
    req.session.name = account.name;
    return res.redirect(config.consoleUrl);
  },
};
