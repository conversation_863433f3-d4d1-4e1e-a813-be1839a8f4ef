const authTypes = require('../../middleware/authTypes');
const wixService = require('./wix.service');
const notifier = require('../common/notifier.service');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'string',
  },
  async handle(req, res, next) {
    const { body } = req;
    wixService.handleWebhook(body).catch((err) => {
      let data = body;
      if(!err.message.includes('malformed JWT')) {
        data = wixService.decodeJWT(body);
      }
      notifier.notifyError(err, 'failed to handle wix webhook', data);
    });
    return next();
  },
};
