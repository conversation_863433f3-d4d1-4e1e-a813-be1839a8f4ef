const _ = require('lodash');
const { URL } = require('url');
const chance = require('chance').Chance();
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const wix = require('../../lib/apis/wix');
const { objectUtils, urlUtils, emailUtils } = require('../../lib/utils');
const logger = require('../../lib/logger')('wixService');
const Feed = require('../account/models/Feed');
const config = require('../../config');
const redis = require('../common/redis.service').getClient();
const countryCodes = require('../../lib/geo/countryCodes');
const Account = require('../account/models/Account');
const { WixEvent, WixSite, WixData } = require('./models');
const accountService = require('../account/account.service');
const Stream = require('../notifications/models/Stream');
const PageVisits = require('../notifications/models/PageVisits');
const notifConsts = require('../notifications/constants');
const notifier = require('../common/notifier.service');
const { WIX_CATALOG, WIX_EVENT_TYPES } = require('../../app/constants');
const { firstNameKeys, lastNameKeys, emailKeys } = require('../webhooks/constants');
const { PERIODS, WIX_PLANS, ORDERED_PLANS } = require('../account/plansEnum');
const {
  SUBSCRIPTION_SOURCE,
  TRANSACTION_TYPES,
} = require('../billing/constants');
const subscriptionService = require('../billing/subscription.service');
const profitWellController = require('../billing/profitwell.controller');
const triggers = require('../../lib/triggers');
const notifierService = require('../common/notifier.service');
const mailerService = require('../common/mailer.service');
const trackerService = require('../common/tracker.service');
const cacheService = require('../common/cache.service');
const WixReview = require('../reviews/models/WixReview');
const constants = require('../notifications/constants');
const analyticsService = require('../common/analytics.service');
const appConfigService = require('../common/appConfig.service');
const { PLATFORMS } = require('../notifications/constants');

wix.setAuth(config.wix.appId, config.wix.secret, config.wix.publicKey);

module.exports = {
  decodeJWT: verifyPayload,
  decodeSignedInstance,
  getAccount,
  getRemovedAccount,
  saveSite,
  updateSite,
  getConnectedAccount,
  integrateSite,
  integrateNewAccount,
  integrateExistingAccount,
  importOrders,
  importPricingPlanOrders,
  importSubscriptionOrders: importPricingPlanOrders,
  importFormSubmissions,
  saveOrder,
  getCheckoutUrl,
  getBillingInfo,
  updateBillingExpirationDate,
  refreshAccessToken,
  handleWebhook,
  handleReviews,
  handleSiteUpdate,
  getAppInstance,
  getSiteProperties,
};

async function getAccount(accountId, instanceId) {
  const [wixAccount, sessionAccount] = await Promise.all([
    Account.findOne({ 'wix.instanceId': instanceId }),
    (accountId && Account.findById(accountId)) || null,
  ]);
  if(wixAccount) {
    return wixAccount;
  }
  if(sessionAccount && sessionAccount.canInstallWixSite()) {
    return sessionAccount;
  }
  return null;
}

async function getConnectedAccount(instanceId) {
  return Account.findOne({ 'wix.instanceId': instanceId });
}

async function getRemovedAccount(instanceId) {
  const account = await Account.findOne({ 'removedWix.instanceId': instanceId });
  if(account && account.canInstallWixSite()) {
    return account;
  }
  return null;
}

async function getAppInstance(accessToken) {
  const appInstance = await wix.getAppInstance(accessToken);
  return {
    instanceId: appInstance.instance.instanceId,
    isFree: appInstance.instance.isFree,
    appVersion: appInstance.instance.appVersion,
    billing: appInstance.instance.billing,
    availablePlans: appInstance.instance.availablePlans,
    url: appInstance.site.url,
    description: appInstance.site.description,
    apps: appInstance.site.installedWixApps,
    siteName: appInstance.site.siteDisplayName,
    ownerEmail: appInstance.site.ownerEmail || _.get(appInstance, 'site.ownerInfo.email'),
    ownerInfo: appInstance.site.ownerInfo,
    siteId: appInstance.site.siteId,
  };
}

async function getSiteProperties(accessToken) {
  const properties = await wix.getSiteProperties(accessToken);
  return {
    email: properties.email,
    businessName: properties.businessName,
    categories: properties.categories
      && [properties.categories.primary, properties.categories.secondary],
    country: properties.address && properties.address.country,
    city: properties.address && properties.address.city,
    phone: properties.phone,
  };
}

async function saveSite(instanceId, code) {
  const {
    access_token: accessToken, refresh_token: refreshToken,
  } = await wix.getTokens(code).catch((err) => {
    throw ErrorFactory('wix get tokens failed on install', 500, err);
  });
  const [appInstance = {}, properties = {}] = await Promise.all([
    getAppInstance(accessToken).catch((err) => {
      const msg = 'Failed to update wix app instance';
      notifier.notifyError(err, msg, { instanceId });
      logger.error({ err, instanceId }, msg);
    }),
    getSiteProperties(accessToken).catch((err) => {
      const msg = 'Failed to update wix site properties';
      notifier.notifyError(err, msg, { instanceId });
      logger.error({ err, instanceId }, msg);
    }),
  ]);
  const info = {
    accessToken, refreshToken, ...appInstance, ...properties,
  };
  return WixSite.findOneAndUpdate({ instanceId }, info, {
    upsert: true, new: true,
  });
}

async function updateSite(instanceId) {
  const site = await WixSite.findOne({ instanceId });
  const { accessToken, refreshToken } = await refreshAccessToken(site, instanceId).catch((err) => {
    notifier.notifyError(err, 'failed to refresh wix access tokens', { instanceId, site });
    throw err;
  });
  const [appInstance = {}, properties = {}] = await Promise.all([
    getAppInstance(accessToken).catch((err) => {
      const msg = 'failed to update wix app instance';
      notifier.notifyError(err, msg, { instanceId });
      logger.error({ err, instanceId }, msg);
    }),
    getSiteProperties(accessToken).catch((err) => {
      const msg = 'failed to update wix site properties';
      notifier.notifyError(err, msg, { instanceId });
      logger.error({ err, instanceId }, msg);
    }),
  ]);
  const info = {
    accessToken, refreshToken, ...appInstance, ...properties,
  };
  const updatedSite = WixSite.findOneAndUpdate({ instanceId }, info, {
    upsert: true, new: true,
  });
  return { site: updatedSite, newData: info };
}

async function integrateSite(account, site, { state = 'wix auth' }) {
  const hasSite = account.getWixSite(site.instanceId);
  if(hasSite) {
    return null;
  }
  const data = await connectSite(account, site, { step: state });
  await account.save();
  return data;
}

async function integrateNewAccount(account, instanceId) {
  const [wixSite, accountWithSite] = await Promise.all([
    WixSite.findOne({ instanceId }),
    Account.findOne({ 'wix.instanceId': instanceId }),
  ]);
  if(!wixSite) {
    throw ErrorFactory('Wix site not found', 500);
  }
  if(accountWithSite) {
    const msg = 'Wix site already installed on another account';
    throw ErrorFactory(msg, 500, { account, accountWithSite });
  }
  return connectSite(account, wixSite, { step: 'signup' });
}

async function integrateExistingAccount(account, instanceId) {
  const wixSite = await WixSite.findOne({ instanceId });
  if(!wixSite) {
    throw ErrorFactory('Wix site not found', 500);
  }
  const installedSite = account.getWixSite(instanceId);
  if(!installedSite && !account.canInstallWixSite()) {
    throw ErrorFactory('Account already has another wix site integrated', 500);
  }
  return connectSite(account, wixSite, { step: 'login' });
}

async function connectSite(account, site, { step } = {}) {
  try {
    const { instanceId } = site;
    const { id: accountId, apiKey } = account;

    const { refreshToken, accessToken } = await refreshAccessToken(site);
    Object.assign(site, { refreshToken, accessToken });
    if(site.noConnectedAccount || !site.accountId) {
      site.noConnectedAccount = false;
      site.accountId = accountId;
    }
    await site.save();
    const embedResult = await wix.updateEmbedScript(accessToken, apiKey);

    let orders = 0;
    let forms = 0;
    let subscriptions = 0;
    if(site.apps && site.apps.some(app => /(store|order|booking|reservation|restaurant)/i.test(app))) {
      const orderEvents = await importOrders({
        accountId,
        accessToken,
        instanceId,
      });
      orders = orderEvents.length || 0;
    }
    if(site.apps && site.apps.some(app => app.includes('form'))) {
      const formSubmissionEvents = await importFormSubmissions({
        accountId,
        accessToken,
        instanceId,
      });
      forms = formSubmissionEvents.length || 0;
    }
    if(site.apps && site.apps.some(app => app.includes('plan'))) {
      const pricingPlanEvents = await importPricingPlanOrders({
        accountId,
        accessToken,
        instanceId,
      });
      subscriptions = pricingPlanEvents.length || 0;
    }
    const domain = urlUtils.getDomain(site.url, false, { includeSubdomain: false });
    await addNotification(account, instanceId, domain);

    const { reinstall } = account.connectWixSite(site.toObject());
    const message = `Wix App ${reinstall ? 'Re-Installed' : 'Installed'} (step: ${step})`;
    Feed.saveFeed(accountId, message, {
      instanceId, orders, forms, subscriptions,
    });
    notifier.notifyWix(`${message} ${account.email}`, {
      orders,
      forms,
      subscriptions,
      instanceId,
      email: account.email,
      site: _.pick(site, ['url', 'apps', 'email', 'businessName']),
    });
    return {
      embedResult, orders, forms, subscriptions,
    };
  } catch(err) {
    const msg = 'Failed connecting wix site to account';
    throw ErrorFactory(msg, 500, { err, account });
  }
}

async function importOrders({ accountId, accessToken, instanceId }) {
  const orders = await wix.getOrders(accessToken, {
    query: { paging: { limit: 30 }, sort: '[{ "dateCreated": "desc" }]' },
  }).catch((err) => {
    notifierService.notifyError(err, 'failed to fetch wix orders', { accountId, instanceId });
  });
  if(!orders || !orders.length) {
    return [];
  }
  return Promise.all(orders.map(order => saveOrder({
    accountId, accessToken, instanceId, order,
  }))).catch((err) => {
    notifierService.notifyError(err, 'failed to import wix orders', { accountId, instanceId, orders });
  });
}

async function importFormSubmissions({ accountId, accessToken, instanceId }) {
  const submissions = await wix.getFormSubmissions(accessToken).catch((err) => {
    notifierService.notifyError(err, 'failed to fetch wix form submissions', { accountId, instanceId });
  });
  if(!submissions || !submissions.length) {
    return [];
  }
  return Promise.all(submissions.map(submission => saveFormSubmission({
    accountId, accessToken, instanceId, submission,
  }))).catch((err) => {
    notifierService.notifyError(err, 'failed to import wix form submissions', { accountId, instanceId, submissions });
  });
}

async function importPricingPlanOrders({ accountId, accessToken, instanceId }) {
  const pricingPlanOrders = await wix.getPricingPlanOrders(accessToken).catch((err) => {
    notifierService.notifyError(err, 'failed to fetch wix pricing plan orders', { accountId, instanceId });
  });
  if(!pricingPlanOrders || !pricingPlanOrders.length) {
    return [];
  }
  return Promise.all(pricingPlanOrders.map(order => savePricingPlanOrder({
    accountId, accessToken, instanceId, order,
  }))).catch((err) => {
    notifierService.notifyError(err, 'failed to import wix pricing plan orders', { accountId, instanceId, pricingPlanOrders });
  });
}

async function handleWebhook(payload) {
  const decodedPayload = verifyPayload(payload);
  if(!decodedPayload) {
    throw ErrorFactory('payload is a malformed JWT');
  }
  WixData.create(decodedPayload).catch((err) => {
    notifierService.notifyError(err, 'failed to save wix data', { decodedPayload, payload });
  });
  const { instanceId, data, eventType } = decodedPayload;
  let accountQuery = { 'wix.instanceId': instanceId };
  if(eventType === 'AppRemoved' || eventType === 'PaidPlanAutoRenewalCancelled') {
    accountQuery = {
      $or: [{ 'wix.instanceId': instanceId }, { 'removedWix.instanceId': instanceId }],
    };
  }
  // eslint-disable-next-line prefer-const
  let [account, wixSite] = await Promise.all([
    Account.findOne(accountQuery),
    WixSite.findOne({ instanceId }),
  ]);
  if(!wixSite) {
    const { site: newSite } = await updateSite(instanceId);
    wixSite = newSite;
  }
  if(!account) {
    if(!wixSite.noConnectedAccount) {
      await WixSite.updateOne({ instanceId }, { $set: { noConnectedAccount: true, accountId: null } });
    }
    if(await appConfigService.getValue({ key: 'slackErrors.wixWebhooks', defaultValue: true })) {
      notifierService.notifyError(new Error('no wix account'), `no wix account, wix/${eventType} webhook failed`, { instanceId, decodedPayload });
    }
    return;
  }
  if(wixSite.noConnectedAccount || !wixSite.accountId) {
    await WixSite.updateOne({ instanceId }, { $set: { noConnectedAccount: false, accountId: account.id } });
  }
  const accountSite = account.getWixSite(instanceId, false);
  const ignoreWebhookEvents = _.get(accountSite, 'configuration.ignoreWebhookEvents', []);
  if(ignoreWebhookEvents.length && ignoreWebhookEvents.includes(eventType)) {
    return;
  }

  try {
    switch(eventType) {
      case 'AppInstalled':
        logger.info({ decodedPayload }, 'wix installed webhook');
        break;
      case 'AppRemoved':
        await handleAppRemovedWebhook(decodedPayload, account);
        break;
      case 'PaidPlanPurchased':
        await handlePlanPurchaseWebhook(decodedPayload, account);
        break;
      case 'PaidPlanChanged':
        await handlePlanChangeWebhook(decodedPayload, account);
        break;
      case 'PaidPlanAutoRenewalCancelled':
        await handlePlanCancelWebhook(decodedPayload, account);
        break;
      case 'SitePropertiesUpdated':
        await handleSiteUpdate(decodedPayload, account);
        break;

      case 'wix.ecom.v1.order_created': // old wix stores webhook
      case 'com.wix.ecommerce.orders.api.v2.OrderEvent': // store, booking, restaurant order + reservation
        await handleOrderWebhook({ instanceId, data, account });
        break;
      case 'wix.pricing_plans.v2.order_purchased':
      case 'wix.pricing_plans.v2.order_created':
        await handlePricingPlanWebhook(decodedPayload, account);
        break;
      case 'com.wixpress.formbuilder.api.v1.FormSubmittedEvent':
      case 'wix.forms.v4.submission_created':
        await handleFormWebhook(decodedPayload, account);
        break;
      default:
      // notifierService.notify('not supported wix webhook event type', decodedPayload);
        break;
    }
  } catch(err) {
    logger.error({ err }, `wix/${eventType} webhook failed`);
    notifierService.notifyError(err, `wix/${eventType} webhook failed`, { instanceId, decodedPayload, ...(!decodedPayload && { payload }) });
  }
}

async function handleOrderWebhook({ instanceId, data, account }) {
  const orderId = data.entityId || data.orderId;
  const wixSite = account.getWixSite(instanceId, false);
  const { refreshToken, accessToken } = await refreshAccessToken(wixSite, instanceId);
  Object.assign(wixSite, { accessToken, refreshToken });
  await Promise.all([
    Account.updateOne({ _id: account.id, 'wix.instanceId': instanceId }, {
      'wix.$.accessToken': accessToken, 'wix.$.refreshToken': refreshToken,
    }),
    WixSite.updateOne({ instanceId }, { accessToken, refreshToken }),
  ]);
  const order = await wix.getOrder(accessToken, orderId).catch((err) => {
    notifierService.notifyError(err, 'failed to get wix ecommerce order', { instanceId, data });
    return data.createdEvent;
  });
  return saveOrder({
    accountId: account.id, accessToken, instanceId, order, orderId,
  }).catch((err) => {
    notifier.notifyError(err, 'failed to save wix order', {
      accountId: account.id, payload: data, order,
    });
  });
}

async function handlePricingPlanWebhook({ instanceId, data }, account) {
  const orderId = data.entityId;
  const wixSite = account.getWixSite(instanceId, false);
  const { refreshToken, accessToken } = await refreshAccessToken(wixSite, instanceId);
  Object.assign(wixSite, { accessToken, refreshToken });
  await Promise.all([
    Account.updateOne({ _id: account.id, 'wix.instanceId': instanceId }, {
      'wix.$.accessToken': accessToken, 'wix.$.refreshToken': refreshToken,
    }),
    WixSite.updateOne({ instanceId }, { accessToken, refreshToken }),
  ]);
  const order = await wix.getPricingPlanOrder(accessToken, orderId).catch((err) => {
    notifierService.notifyError(err, 'failed to get wix subscription order', { instanceId, data });
  });
  return savePricingPlanOrder({
    accountId: account.id, accessToken, instanceId, order, orderId,
  }).catch((err) => {
    notifier.notifyError(err, 'failed to save wix subscription', {
      accountId: account.id, payload: data, order,
    });
  });
}

async function handleFormWebhook({ instanceId, data }, account) {
  const submissionId = data.entityId || data.orderId || new Date(data.submissionTime).getTime();
  const wixSite = account.getWixSite(instanceId, false);
  const { refreshToken, accessToken } = await refreshAccessToken(wixSite, instanceId);
  Object.assign(wixSite, { accessToken, refreshToken });
  await Promise.all([
    Account.updateOne({ _id: account.id, 'wix.instanceId': instanceId }, {
      'wix.$.accessToken': accessToken, 'wix.$.refreshToken': refreshToken,
    }),
    WixSite.updateOne({ instanceId }, { accessToken, refreshToken }),
  ]);
  const submission = _.get(data, 'createdEvent.entity', null) || data;
  return saveFormSubmission({
    accountId: account.id, instanceId, submission, submissionId,
  }).catch((err) => {
    notifier.notifyError(err, 'failed to save wix form submission', {
      accountId: account.id, payload: data, submission,
    });
  });
}

async function handleAppRemovedWebhook(payload, account) {
  const { instanceId } = payload;
  let wixSite;
  const installedSite = await account.getWixSite(instanceId, false);
  const removedSite = await account.getWixSite(instanceId, true);
  if(removedSite) {
    removedSite.uninstalledDate = Date.now();
    wixSite = removedSite;
  } else if(installedSite) {
    installedSite.uninstalledDate = Date.now();
    account.removedWix.push(installedSite);
    account.wix.remove(installedSite);
    wixSite = installedSite;
  }

  const source = _.get(account, 'subscription.source', null);
  if(source === SUBSCRIPTION_SOURCE.wix) {
    const previousIPN = account.subscription.recentIPN;
    account.subscription = subscriptionService.cancelSubscription(account.subscription);
    await profitWellController.churnSubscription(account.id, account.subscription.untilDate);
    if(!previousIPN.includes('CANCEL')) {
      triggers.subscriptionStateChanged(account, account.subscription.recentIPN, account.subscription, { cancelReason: 'wix app removed' });
      Feed.saveFeed(account.id, 'Wix Plan Cancelled', { instanceId });
    }
  }
  await account.save();
  await redis.delAsync(account.id);
  Feed.saveFeed(account.id, 'Wix App Removed', { instanceId });
  notifierService.notifyWix(`Wix uninstalled ${account.email}`, payload);
  mailerService.sendUninstall({
    to: account.email,
    platform: 'Wix site',
    domain: wixSite && wixSite.url,
    category: 'wix-uninstall',
  });
}

async function handlePlanChangeWebhook(payload, account) {
  const { data } = payload || {};
  const {
    previousVendorProductId: previousPlan,
    vendorProductId: plan,
    previousCycle,
    cycle,
  } = data;

  if(!plan || !previousPlan) {
    throw ErrorFactory('invalid plan');
  }
  if(plan === previousPlan && previousCycle === cycle) {
    throw ErrorFactory('plan has not changed');
  }
  if(![PERIODS.MONTHLY, PERIODS.YEARLY].includes(cycle.toLowerCase())) {
    throw ErrorFactory('invalid plan cycle');
  }
  if(!account) {
    throw ErrorFactory('Account not found');
  }
  if(!account.subscription) {
    throw ErrorFactory('account has no subscription');
  }
  if(account.subscription.source !== SUBSCRIPTION_SOURCE.wix) {
    throw ErrorFactory('account subscription is not wix');
  }

  const message = `wix plan updated ${account.email}: ${plan}`;
  account.subscription = subscriptionService.updateSubscription(account.subscription, {
    plan, period: cycle,
  });
  let value = subscriptionService.getPlanPrice(plan) * 0.8 * 100;
  if(PERIODS.YEARLY.includes(cycle.toLowerCase())) {
    value *= 12 * 0.84;
  }
  triggers.subscriptionStateChanged(
    account,
    TRANSACTION_TYPES.CONTRACT_CHANGE,
    account.subscription,
    { newContractName: plan },
  );
  notifierService.notify(message, payload, config.slack.wix);
  return Promise.all([
    account.save(),
    profitWellController.updateSubscription(account.id, {
      email: account.email,
      plan: account.subscription.contractName,
      value,
      interval: profitWellController.getInterval(cycle),
      startDate: Date.now(),
    }),
    redis.delAsync(account.id),
  ]);
}

async function handlePlanPurchaseWebhook(payload, account) {
  const { vendorProductId: plan, cycle, expiresOn } = payload.data;
  if(!plan) {
    throw ErrorFactory('invalid plan');
  }

  if(![PERIODS.MONTHLY, PERIODS.YEARLY].includes(cycle.toLowerCase())) {
    throw ErrorFactory('invalid plan cycle');
  }

  account.subscription = subscriptionService.createSubscription(
    account.id, SUBSCRIPTION_SOURCE.wix, plan, cycle, expiresOn,
  );
  analyticsService.trackPurchasedEvent(account.id, {
    plan, paymentPlatform: SUBSCRIPTION_SOURCE.wix,
  });
  await account.save();

  const { email } = account;
  let value = subscriptionService.getPlanPrice(plan) * 0.8;
  if(PERIODS.YEARLY.includes(cycle.toLowerCase())) {
    value *= 12 * 0.84;
  }
  await profitWellController.updateSubscription(account.id, {
    email,
    plan: account.subscription && account.subscription.contractName,
    value: value * 100,
    interval: profitWellController.getInterval(cycle),
    startDate: Date.now(),
  });

  redis.delAsync(account.id);

  triggers.subscriptionStateChanged(account, TRANSACTION_TYPES.CHARGE);
  trackerService.purchase({ id: `${account.id}:purchase`, email, value });

  const message = `wix plan purchased ${email}: ${plan}`;
  notifierService.notify(message, null, config.slack.wix);
}

async function handlePlanCancelWebhook(payload, account) {
  const { instanceId } = payload;
  const previousIPN = account.subscription && account.subscription.recentIPN;
  account.subscription = subscriptionService.cancelSubscription(account.subscription);
  await account.save();
  await redis.delAsync(account.id);

  await profitWellController.churnSubscription(account.id, account.subscription.untilDate);
  if(!previousIPN.includes('CANCEL')) {
    triggers.subscriptionStateChanged(account, account.subscription.recentIPN);
    Feed.saveFeed(account.id, 'Wix Plan Cancelled', { instanceId });
    const message = `wix plan renewal cancelled ${account.email}`;
    notifierService.notify(message, payload, config.slack.wix);
  }
}

async function saveOrder({
  accountId, accessToken, instanceId, order, orderId,
}) {
  const dbEvent = await WixEvent.findOne({ accountId, orderId: orderId || order.id }).select('_id');
  if(dbEvent) {
    return null;
  }
  const appId = _.get(order, 'lineItems[0].catalogReference.appId');
  const eventType = WixEvent.getEventType(appId);
  let products = [];
  if(appId !== WIX_CATALOG.stores) {
    products = mapLineItemsToProducts(order.lineItems);
  } else {
    // eslint-disable-next-line max-len
    const productIds = _.map(order.lineItems, item => _.get(item, 'catalogReference.catalogItemId'))
      .filter(i => i);
    const wixProducts = await wix.getProducts(accessToken, productIds).catch((err) => {
      notifier.notifyError(err, 'failed to fetch products for order', { productIds, instanceId });
      logger.error({ err, productIds, order }, 'failed to get products for order');
    });
    products = _.map(wixProducts, p => getProduct(p, 1));
  }
  const wixEvent = makeWixEvent(accountId, instanceId, order, products, eventType);
  await wixEvent.save().catch((err) => {
    notifier.notifyError(err, 'failed to save wix order', { accountId, order });
  });
  const feedData = _.omit(wixEvent && wixEvent.toObject({ versionKey: false }), ['_id', 'accountId', 'type', 'createdAt', 'updatedAt']);
  Feed.saveFeed(accountId, WixEvent.getEventName(eventType), feedData);
  return wixEvent;
}

async function savePricingPlanOrder({
  accountId, accessToken, instanceId, order, orderId,
}) {
  const dbEvent = await WixEvent.findOne({ accountId, orderId: orderId || order.id }).select('_id');
  if(dbEvent) {
    return null;
  }
  const contactId = _.get(order, 'buyer.contactId', null);
  const contact = await wix.getContact(accessToken, contactId).catch((err) => {
    notifierService.notifyError(err, 'failed to fetch contact from wix subscription order', { accountId, instanceId, order });
  });
  const eventType = WIX_EVENT_TYPES.subscription;
  const firstName = _.get(contact, 'info.name.first', null);
  const lastName = _.get(contact, 'info.name.last', null);
  let email = _.get(contact, 'primaryInfo.email', null);
  if(!email) {
    if(firstName || lastName) {
      email = `${firstName}_${lastName}@from-wix.com`.replace(/\s/g, '.');
    } else {
      email = chance.email({ domain: 'from-wix.com' });
    }
  }
  const wixEvent = await WixEvent.create({
    orderId: order.id,
    type: eventType,
    status: order.status,
    instanceId,
    firstName,
    lastName,
    email,
    accountId,
    date: objectUtils.findFirst(order, 'dateCreated')
      || objectUtils.findFirst(order, 'createdDate')
      || Date.now(),
    currency: _.get(order, 'priceDetails.currency', null),
    products: [
      {
        id: order.id,
        name: _.get(order, 'planName', null),
        price: _.get(order, 'planPrice', 0),
      },
    ],
    location: {
      country: _.get(contact, 'info.addresses.items[0].address.countryFullname', null),
      countryCode: _.get(contact, 'info.addresses.items[0].address.country', null),
      city: _.get(contact, 'info.addresses.items[0].address.city', null),
    },
  }).catch((err) => {
    notifier.notifyError(err, 'failed to save wix subscription', { accountId, order });
  });
  const feedData = _.omit(wixEvent && wixEvent.toObject({ versionKey: false }), ['_id', 'accountId', 'type', 'createdAt', 'updatedAt']);
  Feed.saveFeed(accountId, WixEvent.getEventName(eventType), feedData);
  return wixEvent;
}

async function handleSiteUpdate({ instanceId }, account) {
  const { newData } = await updateSite(instanceId);
  const wixSite = account.getWixSite(instanceId);
  return Account.updateOne({ _id: account._id, 'wix.instanceId': instanceId }, {
    $set: { 'wix.$': { ...wixSite.toObject(), ...newData } },
  });
}

async function saveFormSubmission({
  accountId, instanceId, submission, submissionId,
}) {
  const dbEvent = await WixEvent.findOne({ accountId, orderId: submissionId || submission.id }).select('_id');
  if(dbEvent) {
    return null;
  }
  const eventType = WIX_EVENT_TYPES.form;
  // eslint-disable-next-line max-len
  let data = _.get(submission, 'submissions', null);
  if(!data && submission.submissionData && _.isArray(submission.submissionData)) {
    data = submission.submissionData.reduce((acc, { fieldName, fieldValue }) => {
      acc[fieldName.toLowerCase()] = fieldValue;
      return acc;
    }, {});
  }
  const firstName = objectUtils.findFirst(data, firstNameKeys, { includes: true });
  const lastName = objectUtils.findFirst(data, lastNameKeys, { includes: true });
  let email = objectUtils.findFirst(data, emailKeys, { includes: true })
    || emailUtils.getMatchesInJson(data);
  if(!email) {
    if(firstName || lastName) {
      email = `${firstName}_${lastName}@from-wix.com`.replace(/\s/g, '.');
    } else {
      email = chance.email({ domain: 'from-wix.com' });
    }
  }
  const wixEvent = await WixEvent.create({
    orderId: submission.id || new Date(submission.submissionTime).getTime(),
    type: eventType,
    status: submission.status,
    instanceId,
    firstName: firstName || null,
    lastName: lastName || null,
    email: email || null,
    accountId,
    date: objectUtils.findFirst(data, 'dateCreated')
      || objectUtils.findFirst(data, 'createdDate')
      || objectUtils.findFirst(data, 'submissionTime')
      || Date.now(),
  }).catch((err) => {
    notifier.notifyError(err, 'failed to save wix form submission', { accountId, submission });
  });
  const feedData = _.omit(wixEvent && wixEvent.toObject({ versionKey: false }), ['_id', 'accountId', 'type', 'createdAt', 'updatedAt']);
  Feed.saveFeed(accountId, WixEvent.getEventName(eventType), feedData);
  return wixEvent;
}

function verifyPayload(str) {
  return wix.decodeJwt(str);
}

function decodeSignedInstance(instance) {
  try {
    return wix.decodeSignedInstance(instance);
  } catch(err) {
    return null;
  }
}

async function getBillingInfo(accountId) {
  const account = await Account.findById(accountId);
  if(!account) {
    throw ErrorFactory('Account not found');
  }
  if(!account.wix || !account.wix.length) {
    throw ErrorFactory('Account is not integrated with a wix site');
  }
  const wixSite = account.wix[0];
  const { instanceId } = wixSite;
  const { refreshToken, accessToken } = await refreshAccessToken(wixSite);
  const appInstance = await getAppInstance(accessToken);

  const [updatedAccount, site] = await Promise.all([
    Account.findOneAndUpdate({ _id: accountId, 'wix.instanceId': instanceId }, {
      $set: {
        'wix.$': {
          ...wixSite.toObject(), ...appInstance, accessToken, refreshToken,
        },
      },
    }, { new: true }),
    WixSite.findOneAndUpdate({ instanceId }, {
      accessToken, refreshToken, ...appInstance,
    }, { new: true }),
  ]);

  return { account: updatedAccount, site };
}

async function getCheckoutUrl(accountId, { plan, period }) {
  const account = await Account.findById(accountId);
  if(!account.wix || !account.wix.length) {
    throw ErrorFactory('Account is not integrated with a wix site', 500);
  }
  if(![PERIODS.MONTHLY, PERIODS.YEARLY].includes(period.toLowerCase())) {
    throw ErrorFactory('Invalid period', 500);
  }
  if(!Object.values(WIX_PLANS).includes(plan)) {
    throw ErrorFactory('Invalid plan', 500);
  }
  if(account.isSubscriptionActive()) {
    const { period: subPeriod, plan: subPlan } = account.subscription;
    if(subPeriod === PERIODS.YEARLY && period === PERIODS.MONTHLY) {
      throw ErrorFactory('Unable to downgrade from yearly to monthly', 500);
    }
    if(subPeriod === period && subPlan === plan) {
      throw ErrorFactory('You are already subscribed to this plan', 500);
    }
    if(ORDERED_PLANS.indexOf(subPlan) > ORDERED_PLANS.indexOf(plan)) {
      throw ErrorFactory('Unable to downgrade to a smaller plan', 500);
    }
  }

  const wixSite = account.wix[0];
  const { instanceId } = wixSite;
  const { refreshToken, accessToken } = await refreshAccessToken(wixSite);
  const appInstance = await getAppInstance(accessToken);

  await Promise.all([
    Account.updateOne({ _id: accountId, 'wix.instanceId': instanceId }, {
      $set: {
        'wix.$': {
          ...wixSite.toObject(), ...appInstance, accessToken, refreshToken,
        },
      },
    }),
    WixSite.updateOne({ instanceId }, { accessToken, refreshToken, ...appInstance }),
  ]);

  if(!account.isSubscriptionActive() && !wixSite.isFree) {
    throw ErrorFactory('You already have an active ProveSource subscription on wix, please sort your billing issues on wix', 500);
  }

  const params = {
    testCheckout: config.env !== 'prod',
    billingCycle: (period || PERIODS.MONTHLY).toUpperCase(),
    successUrl: config.wix.thankYou,
    productId: plan,
  };
  return wix.getCheckout(accessToken, params).then((res) => {
    const { body, headers } = res;
    (new WixData({
      eventType: 'checkout', instanceId, token: accessToken, data: { headers, body, params },
    })).save().catch(() => {});
    return body && body.checkoutUrl;
  }).catch((err) => {
    throw ErrorFactory('Failed to get checkout page from wix', 500, { err });
  });
}

async function updateBillingExpirationDate(account) {
  const site = _.get(account, 'wix[0]');
  if(!site) {
    return { updated: false, instance: null };
  }
  let updated = false;
  const { accessToken, refreshToken } = await refreshAccessToken(site);
  const instance = await getAppInstance(accessToken);
  const { isFree } = instance;
  Object.assign(site, instance, { accessToken, refreshToken });
  account.markModified('wix');
  const expirationDate = _.get(instance, 'billing.expirationDate');
  if(account.subscription) {
    const expDate = new Date(expirationDate);
    const untilDate = new Date(account.subscription.untilDate);
    const now = Date.now();
    if(expirationDate && untilDate.getTime() !== expDate.getTime()) {
      updated = true;
      account.subscription.untilDate = new Date(expDate.getTime() + 86400 * 1000);
    }
    if(isFree && untilDate > now) {
      updated = true;
      account.subscription.untilDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
    }
    if(!isFree && expDate < now) {
      updated = true;
      const graceDate = new Date(expirationDate);
      graceDate.setDate(graceDate.getDate() + 14);
      account.subscription.untilDate = graceDate;
    }
  }
  if(updated) {
    await redis.delAsync(account.id);
  }
  await account.save();
  return { updated, site };
}

async function refreshAccessToken(site, instanceId = null) {
  const { refreshToken, accessToken } = site || {};
  const instId = (site && site.instanceId) || instanceId;
  if(refreshToken && accessToken) {
    const tokens = await wix.refreshAccessToken(refreshToken, accessToken);
    return { accessToken: tokens.access_token, refreshToken: tokens.refresh_token };
  }
  if(instId) {
    const newToken = await wix.createAccessToken(instId);
    return { accessToken: newToken, refreshToken: null };
  }
  throw new Error('no wix site or instanceId to get token');
}

// region helpers

function resizeImage(url) {
  const newSize = '128';
  const urlObj = new URL(url);
  let path = urlObj.pathname;
  path = path.replace(/w_\d+/, `w_${newSize}`);
  path = path.replace(/h_\d+/, `h_${newSize}`);
  // path = path.replace(/q_\d+/, `q_${newSize}`);
  urlObj.pathname = path;
  return urlObj.toString();
}

function mapLineItemsToProducts(lineItems) {
  return _.map(lineItems, (item) => {
    const imageUrl = _.get(item, 'image.url');
    let formattedImageUrl = null;
    if(imageUrl) {
      formattedImageUrl = resizeImage(imageUrl);
    }
    return {
      id: item.id,
      name: _.get(item, 'productName.original', null),
      price: _.get(item, 'price.amount', 0),
      image: formattedImageUrl,
      quantity: 1,
    };
  });
}


async function addNotification(account, instanceId, domain) {
  const accountId = account.id;
  try {
    const hasWixNotification = await accountService.hasWixNotification(accountId);
    if(hasWixNotification) {
      return null;
    }
    const active = await accountService.shouldNotificationBeActive(account);
    const stream = makeStream(accountId, active);
    const pageVisits = makePageVisits(accountId, domain, false);
    accountService.incrementNotificationCreateStats(account);
    return Promise.all([stream.save(), pageVisits.save()]);
  } catch(err) {
    const msg = 'failed to create wix notification';
    notifier.notifyError(err, msg, { accountId, instanceId });
    logger.error({ err, accountId, instanceId }, msg);
    return null;
  }
}

function getProduct(product, quantity) {
  const link = new URL(
    _.get(product, 'productPageUrl.path'),
    _.get(product, 'productPageUrl.base'),
  );
  const imageUrl = _.get(product, 'media.mainMedia.image.url');
  let formattedImageUrl = null;
  if(imageUrl) {
    formattedImageUrl = resizeImage(imageUrl);
  }
  return {
    id: product.id,
    name: _.get(product, 'name', null),
    price: _.get(product, 'priceData.price', 0),
    link,
    image: formattedImageUrl,
    quantity,
  };
}

function makeWixEvent(accountId, instanceId, order, products, eventType) {
  const countryCode = objectUtils.findFirst(order, 'country');
  const country = countryCodes.fromCode(countryCode);
  const firstName = objectUtils.findFirst(order, 'firstName');
  const lastName = objectUtils.findFirst(order, 'lastName');
  let email = objectUtils.findFirst(order, 'email');
  if(!email || !email.length) {
    if(firstName || lastName) {
      email = `${firstName}_${lastName}@from-wix.com`.replace(/\s/g, '.');
    } else {
      email = chance.email({ domain: 'from-wix.com' });
    }
  }
  let total = objectUtils.findFirst(order, 'total') || 0;
  if(total && total.amount) {
    total = total.amount;
  }
  return new WixEvent({
    orderId: order.id,
    type: eventType,
    status: order.status,
    instanceId,
    firstName,
    lastName,
    email,
    accountId,
    date: objectUtils.findFirst(order, 'dateCreated')
      || objectUtils.findFirst(order, 'createdDate')
      || Date.now(),
    products,
    currency: objectUtils.findFirst(order, 'currency'),
    total,
    location: {
      country,
      countryCode,
      city: objectUtils.findFirst(order, 'city'),
    },
  });
}

function makeStream(accountId, active) {
  return new Stream({
    accountId,
    active,
    name: 'Recent Wix Events',
    message: 'purchased',
    someoneAlternatives: ['Someone Great', 'A Happy Customer'],
    autoTrack: false,
    urlTypes: {
      track: notifConsts.URL_TYPES.contains,
      display: notifConsts.URL_TYPES.all,
      trackAbs: false,
      displayAbs: false,
    },
    settings: {
      platform: notifConsts.PLATFORMS.wix,
      displayHold: 8,
      position: notifConsts.positionTypes[0],
      hideExactTimeStream: {
        active: true,
        unit: notifConsts.timeUnits.Hours,
        value: 48,
      },
      eventType: WIX_EVENT_TYPES.all,
    },
  });
}

function makePageVisits(accountId, domain, active) {
  return new PageVisits({
    accountId,
    active,
    urlTypes: {
      track: notifConsts.URL_TYPES.contains,
      display: notifConsts.URL_TYPES.contains,
      trackAbs: false,
      displayAbs: false,
    },
    localization: 'en',
    autoTrack: true,
    trackURL: [domain],
    displayURLs: [domain],
    type: notifConsts.notificationTypes.pageVisits,
    name: 'Website Page Views (All Website)',
    message: 'have visited this website',
    refer: 'Awesome People',
  });
}

async function handleReviews(notification) {
  const { accountId, placeId: instanceId } = notification;
  const result = { reviews: 0, error: null };
  try {
    if(!instanceId) {
      throw new Error('no placeId/instanceId in notification');
    }
    const account = await Account.findOne({ _id: accountId, 'wix.instanceId': notification.placeId });
    const wixSite = account.getWixSite(instanceId);
    if(!wixSite) {
      throw new Error('site is not connected to the account');
    }
    const { refreshToken, accessToken } = await refreshAccessToken(wixSite);
    await Promise.all([
      Account.updateOne({ _id: accountId, 'wix.instanceId': instanceId }, {
        $set: { 'wix.$.accessToken': accessToken, 'wix.$.refreshToken': refreshToken },
      }),
      WixSite.updateOne({ instanceId: wixSite.instancesId }, { accessToken, refreshToken }),
    ]);
    const rawReviews = await wix.getReviews(accessToken, 4);
    const formatedReviews = await formatReviews(rawReviews, notification);
    const reviews = await WixReview
      .insertMany(formatedReviews.filter(review => !review.filtered), { ordered: false })
      .catch((err) => {
        logger.error({ err }, 'failed to write some wix reviews');
      });

    await addToFeed(formatedReviews, accountId);
    logger.info({ reviews, notification }, 'inserted reviews for notification from wix');

    result.reviews = formatedReviews.length;
  } catch(err) {
    logger.error({ notification, err }, 'failed to fetch wix reviews');
    result.error = err;
  }
  return result;
}

async function formatReviews(reviews, notification) {
  const { accountId, placeId: instanceId } = notification;
  const latestReview = await WixReview
    .find({ accountId, placeId: instanceId })
    .sort({ time: -1 })
    .limit(1)
    .then(items => items[0]);

  const latest = _.get(latestReview, 'time', new Date(0).getTime());
  return reviews.filter((review) => {
    if(review && review.date) {
      return new Date(review.date) > latest;
    }
    return true;
  }).map(review => ({
    reviewId: review.id,
    rating: review.content.rating,
    text: review.content.body,
    authorName: review.author.authorName,
    time: review.reviewDate,
    accountId: notification.accountId,
    placeId: instanceId,
  }));
}

async function addToFeed(reviews, accountId) {
  reviews.forEach((result) => {
    const feedData = {
      // placeId: result.placeId,
      author: result.authorName,
      text: result.text,
      rating: result.rating,
      reviewId: result.reviewId,
      time: result.time,
      source: constants.reviewSources.wix,
    };
    const author = result.authorName ? result.authorName : 'Someone';
    const message = `Wix Review | ${author}`;
    Feed.saveFeed(accountId, message, feedData);
  });
}

// endregion
