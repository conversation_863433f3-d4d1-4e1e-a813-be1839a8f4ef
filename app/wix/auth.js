/* eslint-disable prefer-destructuring */
const authTypes = require('../../middleware/authTypes');
const config = require('../../config');
const logger = require('../../lib/logger')('wixAppAuth');
const wixService = require('../wix/wix.service');
const { PLATFORMS } = require('../notifications/constants');
const accountService = require('../account/account.service');
const notifier = require('../common/notifier.service');
const trackerService = require('../common/tracker.service');
const triggers = require('../../lib/triggers/index');
const analyticsService = require('../common/analytics.service');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {
      code: { type: 'string', required: true },
      instanceId: { type: 'string', required: true },
    },
  },
  async handle(req, res, next) {
    const { code, instanceId } = req.query;
    let account = null;
    let sessionAccount = null;
    try {
      const wixSite = code
        ? await wixService.saveSite(instanceId, code)
        : await wixService.updateSite(instanceId).then(({ site }) => site);
      const email = wixSite.ownerEmail || wixSite.email;
      const promises = await Promise.all([
        wixService.getAccount(req.session.accountId, instanceId),
        wixService.getRemovedAccount(instanceId),
        accountService.getAccountByEmail(email),
        accountService.getAccount(req.session.accountId),
      ]);
      account = promises[0];
      const removedAccount = promises[1];
      const emailAccount = promises[2];
      sessionAccount = promises[3];
      if(!account) {
        if(email && !emailAccount) {
          account = await accountService.makeAccount({
            email,
            active: true,
            loginType: PLATFORMS.wix,
            source: PLATFORMS.wix,
            ip: req.remoteAddress,
            cookies: req.cookies,
          });
          res.cookie('ps_signup', 'true', config.cookies.ps_signup);

          const ip = req.remoteAddress;
          const userAgent = req.headers['user-agent'];
          triggers.signup(account, { ip, platform: PLATFORMS.wix });
          trackerService.signup({
            id: account.id, email, ip, ua: userAgent,
          });
          analyticsService.trackSignupEvent(account.id, {
            loginType: PLATFORMS.wix, $email: account.email,
          });
        } else if(removedAccount) {
          account = removedAccount;
        } else {
          const siteInfo = wixSite && wixSite.toObject();
          res.cookie('ps_wix', instanceId, config.getCookieConfig());
          if(req.session.accountId) {
            notifier.notifyWix('tried to connect multiple sites', {
              siteInfo, sessionAccount,
            });
            return res.render('text-box', {
              title: 'New Account Required',
              text: getMultipleSitesErrorTemplate(),
            });
          }
          notifier.notifyWix('wix install, signup required', {
            siteInfo, sessionAccount,
          });
          return res.render('text-box', {
            title: 'Signup Required',
            text: getSignupTemplate(),
          });
        }
      }

      const data = await wixService.integrateSite(account, wixSite, { state: 'wix-auth' });
      res.cookie('ps_wix', '', config.getCookieConfig());
      req.session.accountId = account.id;
      req.session.apiKey = account.apiKey;
      req.session.email = account.email;
      const params = {
        source: PLATFORMS.wix,
        ...(wixSite.url && {
          website: wixSite.url,
          domain: wixSite.url,
        }),
      };
      if(data) {
        const { orders, forms, subscriptions } = data;
        params.events = orders + forms + subscriptions;
      }
      analyticsService.trackInstalledApp(account.id, params);
      return res.redirect(`${config.consoleUrl}/#/integration-complete?${new URLSearchParams(params).toString()}`);
    } catch(err) {
      notifier.notifyError(err, 'wix auth failed', {
        instanceId, accountId: req.session.accountId, account, sessionAccount,
      });
      logger.error({ err }, 'wix auth failed');
      return res.render('text-box', {
        title: 'Wix App Install Failed',
        text: 'Please contact our support at <a href="mailto:<EMAIL>"><EMAIL></a> or via our live chat',
      });
    }
  },
};

function getMultipleSitesErrorTemplate() {
  let html = 'You can only connect one wix site per account<br>';
  html += '<ol id="wix-new-account" class="align-left">';
  html += '<li>Logout from the <a href="https://console.provesrc.com" target="_blank">ProveSource dashboard</a> (top right -> click email -> logout)</li>';
  html += '<li>Signup a <a href="https://console.provesrc.com/#/signup" target="_blank">new account</a> and activate it';
  html += '<li>If the app was not automatically connected your store, re-install the app</li>';
  html += '</ol>';
  return html;
}

function getSignupTemplate() {
  let html = 'You need to login or create an account in ProveSource before moving forward<br>';
  html += '<ol id="wix-new-account" class="align-left">';
  html += '<li>Signup a <a href="https://console.provesrc.com/#/signup">new account</a> and activate it';
  html += '<li>Or, <a href="https://console.provesrc.com">login here</a></li>';
  html += '</ol>';
  return html;
}
