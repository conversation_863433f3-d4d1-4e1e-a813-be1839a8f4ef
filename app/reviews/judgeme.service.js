/* eslint-disable no-await-in-loop, max-len */
const _ = require('lodash');
const judgeme = require('../../lib/apis/judgeme');
const logger = require('../../lib/logger/LoggerFactory')('judgemeReviews.service');
const JudgemeReview = require('./models/JudgemeReview');
const Feed = require('../account/models/Feed');
const constants = require('../notifications/constants');
const OpLog = require('../../cron/models/OpLog');

module.exports = {
  handleReviews,
};

async function handleReviews(notification) {
  const { accountId } = notification;
  const result = { reviews: 0, error: null };
  const oplog = new OpLog({
    name: 'JudgemeReviews',
    accountId,
    resourceId: notification.id,
    resource: notification,
  });
  try {
    if(!notification.placeId) {
      throw new Error('no placeId in notification');
    }

    const rawReviews = await judgeme.getReviews(notification.placeId, notification.pageToken, {
      minRating: 4, numReviews: _.get(notification, 'settings.maxReviewsFetch', 30),
    });
    if(!rawReviews.length) {
      throw new Error('no reviews found');
    }
    const formatedReviews = await formatReviews(rawReviews, notification);

    const reviews = await JudgemeReview
      .insertMany(formatedReviews, { ordered: false })
      .catch((err) => {
        logger.error({ err }, 'failed to write some judgeme reviews');
      });

    await addToFeed(formatedReviews, accountId);
    logger.info({ reviews, notification }, 'inserted reviews for notification from judgeme');

    oplog.result = reviews;
    result.reviews = formatedReviews.length;
  } catch(err) {
    oplog.error = err;
    logger.error({ notification, err }, 'failed to fetch judge.me reviews');
    result.error = err;
  }
  oplog.safeSave();
  return result;
}

async function formatReviews(reviews, notification) {
  const latestReview = await JudgemeReview
    .find({ accountId: notification.accountId, placeId: notification.placeId })
    .sort({ time: -1 })
    .limit(1)
    .then(items => items[0]);

  const latest = _.get(latestReview, 'time', new Date(0).getTime());
  return reviews
    .filter(item => latest < new Date(item.created_at))
    .map(item => ({
      accountId: notification.accountId,
      placeId: notification.placeId,
      reviewId: `${notification.placeId}-${item.id}`,
      productId: item.product_external_id,
      productHandle: item.product_handle,
      authorName: _.get(item, 'reviewer.name'),
      authorEmail: _.get(item, 'reviewer.email'),
      profilePhotoUrl: _.get(item, 'pictures[0].urls.small'),
      rating: item.rating,
      title: item.title,
      text: item.body || item.title || '',
      time: item.created_at,
      verified: item.verified,
    }));
}

async function addToFeed(reviews, accountId) {
  reviews.forEach((result) => {
    const feedData = {
      placeId: result.placeId,
      author: result.authorName,
      authorEmail: result.authorEmail,
      text: result.text,
      rating: result.rating,
      reviewId: result.reviewId,
      time: result.time,
      source: constants.reviewSources.judgeme,
    };
    const author = result.authorName || 'Someone';
    const message = `Judge.me Review | ${author}`;
    Feed.saveFeed(accountId, message, feedData);
  });
}
