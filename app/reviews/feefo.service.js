const _ = require('lodash');
const feefo = require('../../lib/apis/feefo');
const FeefoReview = require('../reviews/models/FeefoReview');
const logger = require('../../lib/logger/LoggerFactory')('feefoReviews.service');
const Feed = require('../account/models/Feed');
const constants = require('../notifications/constants');

module.exports = {
  handleReviews,
};

async function handleReviews(notification) {
  const { accountId } = notification;
  const result = { reviews: 0, error: null };
  try {
    if(!notification.placeId) {
      throw new Error('no placeId in notification');
    }

    const rawReviews = await feefo.getReviews(notification.placeId, {
      minRating: 4, numReviews: _.get(notification, 'settings.maxReviewsFetch', 30),
    });

    const formatedReviews = await formatReviews(rawReviews, notification);

    // Up to 30 reviews are saved
    if(formatedReviews.length > 30) formatedReviews.length = 30;

    const reviews = await FeefoReview
      .insertMany(formatedReviews, { ordered: false })
      .catch((err) => {
        logger.error({ err }, 'failed to write some feefo reviews');
      });

    await addToFeed(formatedReviews, accountId);
    logger.info({ reviews, notification }, 'inserted reviews for notification from feefo');

    result.reviews = formatedReviews.length;
  } catch(err) {
    logger.error({ notification, err }, 'failed to fetch feefo reviews');
    result.error = err;
  }
  return result;
}

async function formatReviews(reviews, notification) {
  const latestReview = await FeefoReview
    .find({ accountId: notification.accountId, placeId: notification.placeId })
    .sort({ time: -1 })
    .limit(1)
    .then(items => items[0]);

  const latest = _.get(latestReview, 'time', new Date(0).getTime());
  return reviews
    .filter((item) => {
      const review = item.service || (item.products && item.products[0]);
      if(review && review.created_at) {
        return new Date(review.created_at) > latest;
      }
      return true;
    })
    .map((item) => {
      const review = item.service || (item.products && item.products[0]);
      return {
        reviewId: review.id,
        rating: _.get(review, 'rating.rating'),
        text: review.review || review.title,
        authorName: _.get(item, 'customer.display_name', null),
        authorLocation: _.get(item, 'customer.display_location', null),
        time: new Date(review.created_at),
        accountId: notification.accountId,
        placeId: notification.placeId,
      };
    });
}

async function addToFeed(reviews, accountId) {
  reviews.forEach((result) => {
    const feedData = {
      placeId: result.placeId,
      author: result.authorName,
      text: result.text,
      rating: result.rating,
      reviewId: result.reviewId,
      time: result.time,
      source: constants.reviewSources.feefo,
    };
    const author = result.authorName ? result.authorName : 'Someone';
    const message = `Feefo Review | ${author}`;
    Feed.saveFeed(accountId, message, feedData);
  });
}
