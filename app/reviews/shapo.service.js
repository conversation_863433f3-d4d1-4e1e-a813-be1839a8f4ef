const _ = require('lodash');
const qs = require('qs');
const shapo = require('../../lib/apis/shapo');
const ShapoReview = require('../reviews/models/ShapoReview');
const logger = require('../../lib/logger/LoggerFactory')('shapoReviews.service');
const Feed = require('../account/models/Feed');
const constants = require('../notifications/constants');
const Account = require('../account/models/Account');
const cacheService = require('../common/cache.service');

module.exports = {
  handleReviews,
};


async function handleReviews(notification) {
  const {
    accountId, placeId: apiKey, pageToken: queryStr,
  } = notification;
  const result = { reviews: 0, error: null };
  try {
    if(!apiKey) {
      throw new Error('no apiKey/placeId in notification');
    }
    const query = qs.parse(queryStr);
    const rawReviews = await shapo.getReviews(apiKey, { rating: 4, ...query });
    const formatedReviews = await formatReviews(rawReviews, notification);
    const reviews = await ShapoReview.insertMany(formatedReviews, { ordered: false }).catch((err) => {
      logger.error({ err }, 'failed to write some shapo reviews');
    });

    await addToFeed(formatedReviews, accountId, queryStr);
    logger.info({ reviews, notification }, 'inserted reviews for notification from shapo');

    result.reviews = formatedReviews.length;
  } catch(err) {
    logger.error({ notification, err }, 'failed to fetch shapo reviews');
    Feed.saveFeed(notification.accountId, 'Shapo Reviews Fetch Failed', {
      notification: notification.name,
      query: queryStr,
      error: _.get(err, 'response.text', err.message),
    });
    result.error = err;
  }
  return result;
}

async function formatReviews(reviews, notification) {
  const placeId = [notification.placeId, notification.pageToken].join('');
  const latestReview = await ShapoReview
    .find({ accountId: notification.accountId, placeId })
    .sort({ time: -1 })
    .limit(1)
    .then(items => items[0]);

  const latest = _.get(latestReview, 'time', new Date(0).getTime());
  return reviews.filter((review) => {
    if(review && review.date) {
      return new Date(review.date) > latest;
    }
    return true;
  }).map(review => ({
    reviewId: review._id,
    rating: review.rating,
    text: review.message,
    authorName: review.name,
    time: review.date,
    accountId: notification.accountId,
    placeId,
    profilePhotoUrl: review.profileImage,
  }));
}

async function addToFeed(reviews, accountId, query) {
  reviews.forEach((result) => {
    const feedData = {
      // placeId: result.placeId,
      author: result.authorName,
      text: result.text,
      rating: result.rating,
      reviewId: result.reviewId,
      time: result.time,
      source: constants.reviewSources.shapo,
      query,
    };
    const author = result.authorName ? result.authorName : 'Someone';
    const message = `Shapo Review | ${author}`;
    Feed.saveFeed(accountId, message, feedData);
  });
}
