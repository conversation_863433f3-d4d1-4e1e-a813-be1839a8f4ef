const mongoose = require('mongoose');
const _ = require('lodash');
const constants = require('../../notifications/constants');

const stampedReview = new mongoose.Schema({
  accountId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'Account',
    index: true,
  },
  reviewId: {
    type: Number,
    required: true,
  },
  storeUrl: {
    type: String,
    required: true,
  },
  active: { type: Boolean, default: true },
  productId: String,
  profilePhotoUrl: String,
  authorName: String,
  rating: Number,
  text: String,
  time: { type: Date, index: true },
}, { timestamps: true, collection: 'stampedReviews' });

stampedReview.index({ accountId: 1, reviewId: 1 }, { unique: true });
stampedReview.index({
  accountId: 1, storeUrl: 1, active: 1, time: -1,
});
stampedReview.index({ accountId: 1, productId: 1, time: -1 });

stampedReview.methods.getData = function () {
  const data = _.pick(this, [
    'id',
    'authorName',
    'profilePhotoUrl',
    'rating',
    'text',
    'time',
  ]);
  data.source = constants.reviewSources.stamped;
  return data;
};

module.exports = mongoose.model('StampedReview', stampedReview);
