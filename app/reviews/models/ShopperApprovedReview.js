const mongoose = require('mongoose');
const _ = require('lodash');
const constants = require('../../notifications/constants');

const shopperApprovedReview = new mongoose.Schema({
  accountId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'Account',
    index: true,
  },
  reviewId: {
    type: String,
    required: true,
  },
  orderId: String,
  placeId: String,
  authorName: String,
  authorEmail: String,
  authorLocation: String,
  profilePhotoUrl: { type: String, default: null },
  active: { type: Boolean, default: true },
  rating: Number,
  text: String,
  time: { type: Date, index: true },
}, { timestamps: true, collection: 'shopperApprovedReviews' });

shopperApprovedReview.index({ accountId: 1, reviewId: 1 }, { unique: true });
shopperApprovedReview.index({
  accountId: 1, placeId: 1, time: -1,
});

shopperApprovedReview.methods.getData = function () {
  const data = _.pick(this, [
    'id',
    'authorName',
    'profilePhotoUrl',
    'rating',
    'text',
    'time',
  ]);
  data.source = constants.reviewSources.shopperapproved;
  return data;
};

module.exports = mongoose.model('ShopperApprovedReview', shopperApprovedReview);
