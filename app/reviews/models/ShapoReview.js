const mongoose = require('mongoose');
const _ = require('lodash');
const constants = require('../../notifications/constants');

const shapoReview = new mongoose.Schema({
  accountId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'Account',
  },
  reviewId: {
    type: String,
    required: true,
  },
  placeId: { type: String, required: true },
  authorName: { type: String },
  profilePhotoUrl: { type: String, default: null },
  active: { type: Boolean, default: true },
  rating: { type: Number },
  text: { type: String },
  time: { type: Date },
}, { timestamps: true, collection: 'shapoReviews' });

shapoReview.index({ accountId: 1, placeId: 1, reviewId: 1 }, { unique: true });
shapoReview.index({ accountId: 1, placeId: 1, time: -1 });

shapoReview.methods.getData = function getData() {
  const data = _.pick(this, [
    'id',
    'authorName',
    'profilePhotoUrl',
    'rating',
    'text',
    'time',
  ]);
  data.source = constants.reviewSources.shapo;
  return data;
};

module.exports = mongoose.model('ShapoReview', shapoReview);
