const mongoose = require('mongoose');
const _ = require('lodash');
const constants = require('../../notifications/constants');

const trustpilotReview = new mongoose.Schema({
  accountId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'Account',
    index: true,
  },
  reviewId: {
    type: String,
    required: true,
  },
  domain: {
    type: String,
    required: true,
  },
  active: { type: Boolean, default: true },
  businessName: String,
  authorName: String,
  profilePhotoUrl: String,
  rating: Number,
  text: String,
  time: { type: Date, index: true },
}, { timestamps: true, collection: 'trustpilotReviews' });

trustpilotReview.index({ accountId: 1, reviewId: 1, domain: 1 }, { unique: true });
trustpilotReview.index({
  accountId: 1, domain: 1, active: 1, time: -1,
});

trustpilotReview.methods.getData = function () {
  const data = _.pick(this, [
    'id',
    'authorName',
    // 'profilePhotoUrl',
    'rating',
    'text',
    'time',
  ]);
  /**
   * not sure if this is mandatory, maybe they can identify calls to their image domain from user domains
   * and that's how they found ProveSource?
   * Possible solution: re-upload images to our own CDN and use that
   */
  data.profilePhotoUrl = constants.defaultReviewImage;
  data.source = constants.reviewSources.trustpilot;
  return data;
};

module.exports = mongoose.model('TrustpilotReview', trustpilotReview);
