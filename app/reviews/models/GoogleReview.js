const mongoose = require('mongoose');
const _ = require('lodash');
const constants = require('../../notifications/constants');

const googleReview = new mongoose.Schema({
  accountId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'Account',
    index: true,
  },
  reviewId: {
    type: String,
    required: true,
    unique: true,
  },
  placeId: {
    type: String,
    required: true,
  },
  active: { type: Boolean, default: true },
  businessName: String,
  authorName: String,
  authorUrl: String,
  language: String,
  profilePhotoUrl: String,
  rating: Number,
  relativeTimeDescription: String,
  text: String,
  time: { type: Date, index: true },
}, { timestamps: true });

googleReview.index({
  accountId: 1, placeId: 1, active: 1, time: -1,
});

googleReview.methods.getData = function getData() {
  const data = _.pick(this, [
    'id',
    'authorName',
    'profilePhotoUrl',
    'rating',
    'relativeTimeDescription',
    'text',
    'time',
  ]);
  data.source = constants.reviewSources.google;
  return data;
};

/** @class Review */
const GoogleReview = mongoose.model('GoogleReview', googleReview);
module.exports = GoogleReview;
