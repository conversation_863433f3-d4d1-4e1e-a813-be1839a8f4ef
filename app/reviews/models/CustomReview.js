const mongoose = require('mongoose');
const _ = require('lodash');
const constants = require('../../notifications/constants');

const customReview = new mongoose.Schema({
  accountId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'Account',
    index: true,
  },
  reviewId: { type: String },
  guid: { type: String },
  placeId: { type: String, required: true },
  active: { type: Boolean, default: true },
  profilePhotoUrl: String,
  authorName: String,
  rating: Number,
  text: String,
  time: { type: Date, index: true },
}, { timestamps: true, collection: 'customReviews' });

customReview.index({ accountId: 1, placeId: 1 });

customReview.methods.getData = function getData() {
  const data = _.pick(this, [
    'id',
    'authorName',
    'profilePhotoUrl',
    'rating',
    'text',
    'time',
  ]);
  data.source = constants.reviewSources.other;
  return data;
};

module.exports = mongoose.model('CustomReview', customReview);
