const mongoose = require('mongoose');
const _ = require('lodash');
const constants = require('../../notifications/constants');

const reviewsioReview = new mongoose.Schema({
  accountId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'Account',
    index: true,
  },
  reviewId: {
    type: Number,
    required: true,
  },
  storeId: {
    type: String,
    required: true,
  },
  active: { type: Boolean, default: true },
  storeName: String,
  authorName: String,
  initials: String,
  rating: Number,
  text: String,
  time: { type: Date, index: true },
}, { timestamps: true, collection: 'reviewsioReviews' });

reviewsioReview.index({ accountId: 1, reviewId: 1 }, { unique: true });
reviewsioReview.index({
  accountId: 1, storeId: 1, active: 1, time: -1,
});

reviewsioReview.methods.getData = function () {
  const data = _.pick(this, [
    'id',
    'authorName',
    'initials',
    'rating',
    'text',
    'time',
  ]);
  data.source = constants.reviewSources.reviewsio;
  return data;
};

module.exports = mongoose.model('ReviewsioReview', reviewsioReview);
