const mongoose = require('mongoose');
const _ = require('lodash');
const constants = require('../../notifications/constants');

const judgemeReview = new mongoose.Schema({
  accountId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'Account',
    index: true,
  },
  reviewId: {
    type: String,
    required: true,
  },
  productId: String,
  productHandle: String,
  placeId: {
    type: String,
    require: true,
  },
  authorName: String,
  authorEmail: String,
  profilePhotoUrl: String,
  active: { type: Boolean, default: true },
  rating: Number,
  title: String,
  text: String,
  time: { type: Date, index: true },
  verified: String,
}, { timestamps: true, collection: 'judgemeReviews' });

judgemeReview.index({ accountId: 1, reviewId: 1 }, { unique: true });
judgemeReview.index({
  accountId: 1, placeId: 1, time: -1,
});

judgemeReview.methods.getData = function () {
  const data = _.pick(this, [
    'id',
    'authorName',
    'profilePhotoUrl',
    'rating',
    'title',
    'text',
    'time',
  ]);
  if(!data.authorName && data.title && data.title !== data.text) {
    data.authorName = data.title;
  }
  data.source = constants.reviewSources.judgeme;
  return data;
};

module.exports = mongoose.model('JudgemeReview', judgemeReview);
