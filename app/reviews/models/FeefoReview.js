const mongoose = require('mongoose');
const _ = require('lodash');
const constants = require('../../notifications/constants');

const feefoReview = new mongoose.Schema({
  accountId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'Account',
    index: true,
  },
  reviewId: {
    type: String,
    required: true,
  },
  placeId: String,
  authorName: String,
  authorLocation: String,
  profilePhotoUrl: { type: String, default: null },
  active: { type: Boolean, default: true },
  rating: Number,
  text: String,
  time: { type: Date, index: true },
}, { timestamps: true, collection: 'feefoReviews' });

feefoReview.index({ accountId: 1, reviewId: 1 }, { unique: true });
feefoReview.index({
  accountId: 1, placeId: 1, time: -1,
});

feefoReview.methods.getData = function () {
  const data = _.pick(this, [
    'id',
    'authorName',
    'profilePhotoUrl',
    'rating',
    'text',
    'time',
  ]);
  data.source = constants.reviewSources.feefo;
  return data;
};

module.exports = mongoose.model('FeefoReview', feefoReview);
