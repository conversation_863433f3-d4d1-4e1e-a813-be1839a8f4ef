/* eslint-disable global-require */
const FacebookReview = require('./FacebookReview');
const GoogleReview = require('./GoogleReview');
const ReviewsioReview = require('./ReviewsioReview');
const TrustpilotReview = require('./TrustpilotReview');
const YotpoReview = require('./YotpoReview');
const StampedReview = require('./StampedReview');
const CapterraReview = require('./CapterraReview');
const JudgemeReview = require('./JudgemeReview');
const FeefoReview = require('./FeefoReview');
const ShopperApprovedReview = require('./ShopperApprovedReview');
const ShapoReview = require('./ShapoReview');
const WixReview = require('./WixReview');

module.exports = {
  FacebookReview,
  GoogleReview,
  ReviewsioReview,
  TrustpilotReview,
  YotpoReview,
  StampedReview,
  CapterraReview,
  JudgemeReview,
  FeefoReview,
  ShopperApprovedReview,
  ShapoReview,
  WixReview,
  CustomReview: require('./CustomReview'),
};
