const mongoose = require('mongoose');
const _ = require('lodash');
const constants = require('../../notifications/constants');

const capterraReview = new mongoose.Schema({
  accountId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'Account',
    index: true,
  },
  reviewId: {
    type: String,
    required: true,
  },
  productId: {
    type: String,
    required: true,
  },
  placeId: String,
  authorName: String,
  authorTitle: String,
  profilePhotoUrl: String,
  active: { type: Boolean, default: true },
  rating: Number,
  text: String,
  time: { type: Date, index: true },
}, { timestamps: true, collection: 'capterraReviews' });

capterraReview.index({ accountId: 1, reviewId: 1 }, { unique: true });
capterraReview.index({
  accountId: 1, productId: 1, active: 1, time: -1,
});

capterraReview.methods.getData = function () {
  const data = _.pick(this, [
    'id',
    'authorName',
    'profilePhotoUrl',
    'rating',
    'text',
    'time',
  ]);
  data.source = constants.reviewSources.capterra;
  return data;
};

module.exports = mongoose.model('CapterraReview', capterraReview);
