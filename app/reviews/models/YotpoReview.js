const mongoose = require('mongoose');
const _ = require('lodash');
const constants = require('../../notifications/constants');

const yotpoReview = new mongoose.Schema({
  accountId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'Account',
    index: true,
  },
  reviewId: {
    type: Number,
    required: true,
  },
  yotpoAppId: {
    type: String,
    required: true,
  },
  active: { type: Boolean, default: true },
  productId: String,
  profilePhotoUrl: String,
  authorName: String,
  rating: Number,
  text: String,
  time: { type: Date, index: true },
}, { timestamps: true, collection: 'yotpoReviews' });

yotpoReview.index({ accountId: 1, reviewId: 1 }, { unique: true });
yotpoReview.index({
  accountId: 1, yotpoAppId: 1, active: 1, time: -1,
});

yotpoReview.methods.getData = function () {
  const data = _.pick(this, [
    'id',
    'authorName',
    'profilePhotoUrl',
    'rating',
    'text',
    'time',
  ]);
  data.source = constants.reviewSources.yotpo;
  return data;
};

module.exports = mongoose.model('YotpoReview', yotpoReview);
