const _ = require('lodash');
const mongoose = require('mongoose');
const constants = require('../../notifications/constants');

const facebookReview = new mongoose.Schema({
  accountId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'Account',
    index: true,
  },
  reviewId: {
    type: String,
    required: true,
    unique: true,
  },
  pageId: {
    type: String,
    required: true,
  },
  active: { type: Boolean, default: true },
  time: {
    type: Date,
    index: true,
  },
  rating: Number,
  authorName: String,
  authorId: String,
  profilePhotoUrl: String,
  text: String,
  businessName: String, // pageName

}, { timestamps: true, collection: 'facebookReviews' });

facebookReview.index({
  accountId: 1, pageId: 1, active: 1, time: -1,
});

facebookReview.methods.getData = function getData(endpoint, bucket) {
  const data = _.pick(this, [
    'id',
    'authorName',
    'profilePhotoUrl',
    'rating',
    'text',
    'time',
  ]);
  if(this.authorId && endpoint && bucket) {
    data.profilePhotoUrl = `https://${bucket}.${endpoint}/images/${this.authorId}.jpeg`;
  }
  data.source = constants.reviewSources.facebook;
  return data;
};

/** @class Review */
const FacebookReview = mongoose.model('FacebookReview', facebookReview);
module.exports = FacebookReview;
