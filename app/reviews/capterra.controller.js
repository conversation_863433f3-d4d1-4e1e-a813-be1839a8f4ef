const _ = require('lodash');
const capterra = require('../../lib/apis/capterra');
const logger = require('../../lib/logger/LoggerFactory')('capterraReviews.controller');
const Feed = require('../account/models/Feed');
const CapterraReview = require('./models/CapterraReview');
const constants = require('../notifications/constants');
const strUtil = require('../../lib/utils/stringUtils');
const config = require('../../config');

module.exports = {
  handleReviews,
  formatReviews,
  addToFeed,
};

async function handleReviews(notification) {
  const { accountId } = notification;
  const result = { reviews: 0, error: null };
  try {
    if(!notification.placeId) {
      throw new Error('no placeId in notification');
    }

    const rawReviews = await capterra.getReviews(
      notification.placeId,
      { lowestRating: 4, token: config.scrape_do.token },
    );
    if(!rawReviews || !rawReviews.length) {
      throw new Error('no reviews found');
    }

    const formatedReviews = await formatReviews(rawReviews, notification);

    const reviews = await CapterraReview
      .insertMany(formatedReviews, { ordered: false })
      .catch((err) => {
        logger.error({ err }, 'failed to write some capterra reviews');
      });

    await addToFeed(formatedReviews, accountId);

    logger.info({ reviews, notification }, 'inserted reviews for notification from capterra');

    result.reviews = formatedReviews.length;
  } catch(err) {
    logger.error({ notification, err }, 'failed to fetch capterra reviews');
    result.error = err;
  }
  return result;
}

async function formatReviews(reviews, notification) {
  const latestReview = await CapterraReview
    .find({
      accountId: notification.accountId,
      productId: { $in: [notification.placeId, capterra.getProductId(notification.placeId)] },
    })
    .sort({ time: -1 })
    .limit(1)
    .then(items => items[0]);

  const latest = _.get(latestReview, 'time', new Date(0).getTime());
  return reviews
    .filter(item => latest < item.time)
    .map((item) => {
      item.accountId = notification.accountId;
      item.productId = item.productId || capterra.getProductId(notification.placeId);
      item.placeId = notification.placeId;
      item.authorName = getAuthorName(item.authorName);
      return item;
    });
}

async function addToFeed(reviews, accountId) {
  reviews.forEach((result) => {
    const feedData = {
      productId: result.placeId,
      author: result.authorName,
      authorTitle: result.authorTitle,
      text: result.text,
      rating: result.rating,
      reviewId: result.reviewId,
      time: result.time,
      source: constants.reviewSources.capterra,
    };
    const author = result.authorName ? result.authorName : 'Someone';
    const message = `Capterra Review | ${author}`;
    Feed.saveFeed(accountId, message, feedData);
  });
}

function getAuthorName(name) {
  return (strUtil.isEmptyString(name) || name === 'Verified Reviewer') ? null : name;
}
