const Facebook = require('../reviews/models/FacebookReview');
const Google = require('../reviews/models/GoogleReview');
const Reviewsio = require('../reviews/models/ReviewsioReview');
const Trustpilot = require('../reviews/models/TrustpilotReview');
const Yotpo = require('../reviews/models/YotpoReview');
const Stamped = require('../reviews/models/StampedReview');
const Capterra = require('../reviews/models/CapterraReview');
const Judgeme = require('./models/JudgemeReview');
const Feefo = require('./models/FeefoReview');
const ShopperApproved = require('../reviews/models/ShopperApprovedReview');
const Shapo = require('../reviews/models/ShapoReview');
const Wix = require('../reviews/models/WixReview');
const CustomReview = require('../reviews/models/CustomReview');
const constants = require('../notifications/constants');

module.exports = {
  eventNames: {
    FacebookReview: 'Facebook Review',
    GoogleReview: 'Google Review',
    ReviewsioReview: 'Review.io Review',
    TrustpilotReview: 'Trustpilot Review',
    YotpoReview: 'Yotpo Review',
    StampedReview: 'Stamped Review',
    CapterraReview: 'Capterra Review',
    JudgemeReview: 'Judge.me Review',
    FeefoReview: 'Feefo Review',
    ShopperApprovedReview: 'Shopper Approved Review',
    ShapoReview: 'Shapo Review',
    WixReview: 'Wix Review',
    CustomReview: 'Custom Review',
  },
};

module.exports.getReviewModel = (source) => {
  switch(source) {
  default:
    return null;
  case constants.reviewSources.other:
    return CustomReview;
  case constants.reviewSources.facebook:
    return Facebook;
  case constants.reviewSources.google:
    return Google;
  case constants.reviewSources.reviewsio:
    return Reviewsio;
  case constants.reviewSources.trustpilot:
    return Trustpilot;
  case constants.reviewSources.yotpo:
    return Yotpo;
  case constants.reviewSources.stamped:
    return Stamped;
  case constants.reviewSources.capterra:
    return Capterra;
  case constants.reviewSources.shopperapproved:
    return ShopperApproved;
  case constants.reviewSources.judgeme:
    return Judgeme;
  case constants.reviewSources.feefo:
    return Feefo;
  case constants.reviewSources.shapo:
    return Shapo;
  case constants.reviewSources.wix:
    return Wix;
  }
};
