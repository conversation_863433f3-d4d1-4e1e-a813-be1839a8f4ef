/* eslint-disable max-len */
const _ = require('lodash');
const shopperApproved = require('../../lib/apis/shopperApproved');
const logger = require('../../lib/logger/LoggerFactory')('shopperApprovedReviews.service');
const Feed = require('../account/models/Feed');
const ShopperApprovedReview = require('./models/ShopperApprovedReview');
const constants = require('../notifications/constants');

module.exports = {
  handleReviews,
};

async function handleReviews(notification) {
  const { accountId } = notification;
  const result = { reviews: 0, error: null };
  try {
    if(!notification.placeId) {
      throw new Error('no placeId in notification');
    }

    const rawReviews = await shopperApproved.getReviews(notification.placeId, notification.pageToken, {
      limit: _.get(notification, 'settings.maxReviewsFetch', 30), lowestRating: 4,
    });
    if(!rawReviews || !_.isObject(rawReviews)) {
      throw new Error('no reviews found');
    }

    const formatedReviews = await formatReviews(rawReviews, notification);

    const reviews = await ShopperApprovedReview
      .insertMany(formatedReviews, { ordered: false })
      .catch((err) => {
        logger.error({ err }, 'failed to write some shopperApproved reviews');
      });

    await addToFeed(formatedReviews, accountId);
    logger.info({ reviews, notification }, 'inserted reviews for notification from Shopper Approved');

    result.reviews = formatedReviews.length;
  } catch(err) {
    logger.error({ notification, err }, 'failed to fetch shopperApproved reviews');
    result.error = err;
  }
  return result;
}

async function formatReviews(rawReviews, notification) {
  const latestReview = await ShopperApprovedReview
    .find({
      accountId: notification.accountId,
      placeId: notification.placeId,
    })
    .sort({ time: -1 })
    .limit(1)
    .then(items => items[0]);

  const latest = _.get(latestReview, 'time', new Date(0).getTime());
  const reviews = [];
  Object.entries(rawReviews).forEach(([key, value]) => {
    const date = new Date(value.review_date);
    if(date > latest && value.review_id && key === value.review_id.toString()) {
      reviews.push({
        accountId: notification.accountId,
        placeId: notification.placeId,
        reviewId: value.review_id,
        orderId: value.order_id,
        authorName: value.display_name,
        authorEmail: value.email_address,
        authorLocation: value.location,
        rating: value.overall || value.initial_overall,
        text: value.comments || value.initial_comments,
        time: date,
      });
    }
  });
  return reviews;
}

function addToFeed(reviews, accountId) {
  const promises = reviews.map((result) => {
    const feedData = {
      siteId: result.placeId,
      author: result.authorName,
      text: result.text,
      rating: result.rating,
      reviewId: result.reviewId,
      time: result.time,
      source: constants.reviewSources.shopperapproved,
    };
    const author = result.authorName ? result.authorName : 'Someone';
    const message = `Shopper Approved Review | ${author}`;
    return Feed.saveFeed(accountId, message, feedData);
  });
  return Promise.all(promises);
}
