const authTypes = require('../../middleware/authTypes');
const slack = require('../../lib/apis/slackNotifier');
const Account = require('../account/models/Account');
const config = require('../../config');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {
    shop_id: { type: 'integer' },
    shop_domain: { type: 'string' },
  },
  async handle(req, res, next) {
    const shop = req.headers['x-shopify-shop-domain'] || null;
    const { shop_id: shopId } = req.body;
    try {
      const account = await Account.findOne({
        $or: [{ 'removedShops.id': shopId }, { 'shopify.id': shopId }],
      });
      if(!account) {
        const data = { payload: req.body, shop };
        slack.notifyError(Error('no account with shopId'),
          `failed to redact shop ${shop}`,
          { data, webhook: config.slack.shopify });
        return next();
      }
      const subscription = account.subscriptionInfo();
      const { email } = account;
      const msg = `shop redact ${shop} by ${email} (${subscription.plan})`;
      slack.notify(msg, req.body, { webhook: config.slack.shopify });
      return next();
    } catch(err) {
      slack.notifyError(err, `failed to redact shop ${shop}`, { webhook: config.slack.shopify });
      return next(err);
    }
  },
};
