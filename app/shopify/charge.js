const _ = require('lodash');
const authTypes = require('../../middleware/authTypes');
const config = require('../../config');
const Account = require('../account/models/Account');
const Shopify = require('../../lib/apis/shopify');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const { PAID_PLANS, PRICES, PLANS } = require('../account/plansEnum');
const notifierService = require('../common/notifier.service');
const Feed = require('../account/models/Feed');
const coupons = require('./coupons');
const shopifyService = require('./shopify.service');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {
      plan: { enum: PAID_PLANS, required: true },
      coupon: { type: 'string' },
    },
  },
  async handle(req, res, next) {
    const { accountId, email } = req.session;
    const { plan } = req.query;

    try {
      const account = await Account.findOne({ _id: accountId });

      const shop = account.getActiveShop();
      if(!shop) {
        throw ErrorFactory('account is not using shopify or has no active shop');
      }

      let price = PRICES[plan];
      let planName = plan.charAt(0).toUpperCase() + plan.slice(1);
      if(plan === PLANS.STARTER_BRANDING) {
        planName = 'Starter Custom Branding';
      }

      const name = `${planName} Monthly Plan`;
      const isStaff = account.isShopifyStaff();
      if(!isStaff && account.isNewShop() && plan !== PLANS.STARTER && plan !== PLANS.BASIC) {
        // w0tm2v-bu.myshopify.com bought unlimited and he had 30 orders... so check is not relevant
        // const orders = await ShopifyEvent.count({ accountId });
        // if(orders < 30) {
        throw ErrorFactory('New shops can only upgrade to the starter plan');
        // }
      }

      let couponCode = req.query.coupon;
      if(isStaff && !couponCode) couponCode = 'WeLoveShopify50';
      if(couponCode && couponCode.length) {
        const coupon = coupons[couponCode];
        if(!coupon) {
          throw ErrorFactory('coupon is invalid or has expired', 400, { couponCode });
        } else if(_.isNumber(coupon.fullPricefactor)) {
          price *= coupon.fullPricefactor;
        }
      }

      const shopify = Shopify(shop.myshopify_domain, shop.token);
      const test = (config.env !== 'prod');
      const charge = await shopify
        .charge(name, price, config.shopify.confirm, test)
        .catch((err) => {
          shopifyService.saveEvent(req, accountId, 'charge-failed', shop.myshopify_domain, err);
          throw ErrorFactory('failed to create Shopify charge', { err });
        });

      shop.chargeId = charge.id;
      shop.ps_plan = plan;
      shop.price = price;
      await account.save().catch((err) => {
        throw ErrorFactory('failed to save Shopify charge id', { msg: err.message });
      });

      shopifyService.saveEvent(req, accountId, 'charge', shop.myshopify_domain);
      notifierService.notifyShopify(`shopify charge begin ${email}: ${plan}`);

      res.body = { url: charge.confirmation_url };
      next();
    } catch(err) {
      notifierService.notifyError(err, `charge failed ${email}`, { accountId, email, plan });
      Feed.saveFeed(accountId, `Shopify charge failed (${plan})`, { error: err.message });
      next(err);
    }
  },
};
