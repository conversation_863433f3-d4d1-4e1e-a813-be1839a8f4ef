/* eslint-disable consistent-return,prefer-destructuring */
const _ = require('lodash');
const { SUBSCRIPTION_SOURCE } = require('../billing/constants');
const authTypes = require('../../middleware/authTypes');
const config = require('../../config');
const logger = require('../../lib/logger')('shopify/cancel');
const redis = require('../common/redis.service').getClient();

const ErrorFactory = require('../../lib/errors/ErrorFactory');
const Account = require('../account/models/Account');
const Shopify = require('../../lib/apis/shopify');
const slack = require('../../lib/apis/slackNotifier');
const triggers = require('../../lib/triggers');
const dateUtils = require('../../lib/utils/dateUtils');
const shopifyService = require('./shopify.service');

const profitwellController = require('../billing/profitwell.controller');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {},
  },
  async handle(req, res, next) {
    const { accountId } = req.locals;
    let { email } = req.session;

    try {
      const account = await Account.findOne({ _id: accountId });
      if(!account) {
        // noinspection ExceptionCaughtLocallyJS
        throw ErrorFactory.AccountNotFound(accountId);
      }
      if(!account.shopify || !account.shopify.length) {
        // noinspection ExceptionCaughtLocallyJS
        throw ErrorFactory('account not paying via shopify');
      }

      email = account.email;
      const shop = account.getActiveShop();
      const chargeId = _.get(account, 'subscription.lastChargeId') || shop.chargeId;
      if(!chargeId || _.get(account, 'subscription.source') !== SUBSCRIPTION_SOURCE.shopify) {
        // noinspection ExceptionCaughtLocallyJS
        throw ErrorFactory('subscription is not managed on shopify', 500);
      }

      const shopify = Shopify(shop.myshopify_domain, shop.token);
      const result = await shopify.cancelCharge(chargeId).catch((err) => {
        throw ErrorFactory('failed to cancel shopify charge', 500, { message: err.message });
      });

      if(result.statusCode !== 200) {
        // noinspection ExceptionCaughtLocallyJS
        throw ErrorFactory('failed to cancel shopify charge (bad status)', 500, { result });
      }

      account.shopify.forEach((store) => {
        if(store.chargeId === shop.chargeId) {
          store.paying = false;
        }
      });
      account.subscription.recentIPN = 'CANCELLATION';
      account.subscription.untilDate = dateUtils.normalize(Date.now() - 86400 * 1000);
      await account.save();
      await redis.delAsync(accountId);

      profitwellController.churnSubscription(account.id, new Date());

      const message = `shopify subscription cancelled ${email}: (${account.subscription.plan})`;
      slack.notify(message, null, { webhook: config.slack.shopify });
      triggers.subscriptionStateChanged(account, 'CANCELLATION');

      shopifyService.saveEvent(req, accountId, 'cancel', shop.myshopify_domain);
      res.body = { message: 'Shopify subscription cancelled' };
      next();
    } catch(err) {
      logger.error({ critical: true, err, accountId }, 'failed to cancel subscription');
      slack.notifyError(err, `shopify cancel subscription failed ${email}`, {
        data: { accountId },
        webhook: config.slack.shopify,
      });

      next(err);
    }
  },
};
