const mongoose = require('mongoose');

/**
 * @class ShopifyLog
 * @constructor
 */
const ShopifyLog = new mongoose.Schema({
  accountId: { type: mongoose.SchemaTypes.ObjectId, index: true },
  shop: { type: String, index: true },
  event: { type: String },
  error: { type: mongoose.SchemaTypes.Mixed },
  body: Object,
  query: Object,
  headers: Object,
}, { collection: 'shopifyLog', timestamps: true });

ShopifyLog.index({ accountId: 1, event: 1, createdAt: -1 });
// Add TTL index for shop-update events that expires after 30 days
ShopifyLog.index({ createdAt: 1 }, {
  expireAfterSeconds: 7 * 24 * 60 * 60, // 7 days in seconds
  partialFilterExpression: { event: 'shop-update' },
});

/** @class ShopifyLog */
const shopifyLogModel = mongoose.model('ShopifyLog', ShopifyLog);
module.exports = shopifyLogModel;
