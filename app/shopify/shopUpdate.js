/* eslint-disable no-underscore-dangle,max-len */
const _ = require('lodash');
const authTypes = require('../../middleware/authTypes');
const logger = require('../../lib/logger')('shopify.shopUpdate');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const notifier = require('../common/notifier.service');

const Account = require('../account/models/Account');
const shopifyService = require('./shopify.service');
const profitwellController = require('../billing/profitwell.controller');
const { PLANS, PRICES } = require('../account/plansEnum');
const { SHOPIFY } = require('../constants');


module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {},
  },
  async handle(req, res, next) {
    let accountId = null;
    let email = null;
    const shopifyDomain = req.headers['x-shopify-shop-domain'] || null;

    try {
      const { id } = req.body;
      if(!id) {
        throw ErrorFactory('no shop id provided');
      }
      const account = await Account.findOne({ 'shopify.id': id });
      if(!account) {
        throw ErrorFactory(`no account associated with shop ${shopifyDomain}`);
      }

      email = account.email;
      accountId = account.id;
      const shopData = { ...req.body };
      shopData.shopCreated = shopData.created_at;
      shopData.shopUpdated = shopData.updated_at;
      shopifyService.saveSite(shopData);
      const shop = account.getShop(id);
      const oldPlan = shop.plan_name;
      const subId = parseFloat(_.get(account, 'subscription.lastChargeId') || _.get(account, 'subscription.subscriptionId'));

      const badStatus = Object.values(SHOPIFY.negativeStatus);
      if(badStatus.includes(oldPlan) && !badStatus.includes(shopData.plan_name)) {
        notifier.notifyShopify(`shop re-opened ${shopifyDomain} ${email}`);
        if(account.isSubscriptionActive() && subId && subId === shop.chargeId) {
          const price = shop.price || PRICES[shop.ps_plan] || PRICES[PLANS.STARTER];
          const plan = shop.ps_plan || PLANS.STARTER;
          let planName = plan.charAt(0).toUpperCase() + plan.slice(1);
          if(plan === PLANS.STARTER_BRANDING) {
            planName = 'Starter Custom Branding';
          }
          notifier.notifyShopify(`should re-activate subscription ${shopifyDomain} ${email}`, shopData);
          profitwellController.updateSubscription(accountId, {
            email,
            plan: `${planName} Monthly (Shopify)`,
            value: price * 100,
            interval: profitwellController.INTERVALS.month,
          });
        }
      } else if(!badStatus.includes(oldPlan) && badStatus.includes(shopData.plan_name)) {
        notifier.notifyShopify(`shop inactive ${shopifyDomain} ${email}`);

        if(account.isSubscriptionActive() && subId && subId === shop.chargeId) {
          notifier.notifyShopify(`should cancel subscription ${shopifyDomain} ${email}`, shopData);
          profitwellController.churnSubscription(account.id, new Date(), profitwellController.CHURN_TYPES.delinquent);
          shop.paying = false;
        }
      }

      shop.plan_name = shopData.plan_name;
      shop.plan_display_name = shopData.plan_display_name;

      await account.save();

      shopifyService.saveEvent(req, accountId, 'shop-update', shopifyDomain);
    } catch(err) {
      shopifyService.saveEvent(req, accountId, 'shop-update-failed', shopifyDomain, err.message);
      logger.error({ err, shop: shopifyDomain, critical: true }, 'shop update webhook failed');
      notifier.notifyError(err, `shop update webhook failed ${shopifyDomain} ${email}`, _.pick(req, ['headers', 'body']));
    }
    res.body = { message: 'success' };
    return next();
  },
};
