const _ = require('lodash');
const authTypes = require('../../middleware/authTypes');
const config = require('../../config');
const logger = require('../../lib/logger')('shopify/confirm');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const redis = require('../common/redis.service').getClient();

const Account = require('../account/models/Account');
const Shopify = require('../../lib/apis/shopify');
const { PERIODS, PRICES, PLANS } = require('../account/plansEnum');
const slack = require('../../lib/apis/slackNotifier');
const triggers = require('../../lib/triggers');
const Feed = require('../account/models/Feed');
const shopifyService = require('./shopify.service');
const trackerService = require('../common/tracker.service');
const analyticsService = require('../common/analytics.service');

const profitWellController = require('../billing/profitwell.controller');
const { SUBSCRIPTION_SOURCE } = require('../billing/constants');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    properties: {
      charge_id: { type: 'string', required: true },
    },
  },
  async handle(req, res, next) {
    const chargeId = req.query.charge_id;
    const { accountId } = req.session;
    const { email } = req.session;
    const ip = req.remoteAddress;
    const userAgent = req.headers['user-agent'];

    try {
      const account = await Account.findOne({ 'shopify.chargeId': chargeId });
      if(!account) throw ErrorFactory('charge id not found');

      const shop = account.getActiveShop();
      const plan = shop.ps_plan;
      const value = shop.price || PRICES[plan] || PRICES[PLANS.STARTER];
      const shopify = Shopify(shop.myshopify_domain, shop.token);
      const charge = await shopify.getCharge(chargeId).catch((err) => {
        shopifyService.saveEvent(req, accountId, 'confirm-no-charge', shop.myshopify_domain);
        throw ErrorFactory('failed to get charge details from Shopify', err);
      });

      if(!charge) {
        throw ErrorFactory('empty charge details from Shopify');
      }

      if(charge.status !== 'active') {
        shopifyService.saveEvent(req, accountId, 'confirm-not-accepted', shop.myshopify_domain);
        slack.notify('shopify charge not accepted', { email: account.email }, { webhook: config.slack.shopify });
        return res.redirect(`${config.consoleUrl}/shopify-decline?shop=${shop.myshopify_domain}`);
      }

      if(plan === PLANS.STARTER_BRANDING) {
        if(account.configuration) {
          account.configuration.whitelabel = true;
        } else {
          account.configuration = { whitelabel: true };
        }
      }

      account.shopify.forEach((store) => {
        if(store.chargeId === chargeId) {
          store.paying = true;
        }
      });

      const previousSub = account.isSubscriptionActive() && account.subscription;
      account.subscription = makeSubscription(charge, plan, previousSub);
      await account.save().catch((err) => {
        throw ErrorFactory('failed to save account on shopify charge', err);
      });
      if(!account.email.includes('@shopify.com')) {
        analyticsService.trackPurchasedEvent(account.id, {
          plan, paymentPlatform: SUBSCRIPTION_SOURCE.shopify,
        });
        updateProfitWell(account, charge, value * 100);
        trackerService.purchase({
          id: `${account.id}:purchase`, email, ua: userAgent, ip, value,
        });
      }

      shopifyService.saveEvent(req, accountId, 'confirm', shop.myshopify_domain);
      redis.delAsync(accountId);
      triggers.subscriptionStateChanged(account, 'CHARGE', previousSub);
      slack.notify(`shopify charge confirm ${email}: ${plan}`, null, { webhook: config.slack.shopify });

      return res.redirect(config.thankYou);
    } catch(err) {
      logger.error({
        err, chargeId, accountId, email, critical: true,
      }, 'shopify charge confirm failed');
      slack.notifyError(err, `shopify charge confirm failed ${email} #${chargeId}`);
      Feed.saveFeed(accountId, `Shopify charge confirm failed #${chargeId}`, { chargeId });
      next(err);
    }
    return next();
  },
};


function makeSubscription(charge, plan, previousSub) {
  const subscriptionId = (previousSub && previousSub.subscriptionId) || charge.id;
  return {
    created: (previousSub && previousSub.created) || Date.now(),
    contractName: charge.name,
    subscriptionId,
    lastChargeId: charge.id,
    plan,
    period: PERIODS.MONTHLY,
    source: 'shopify',
    recentIPN: 'CHARGE',
    untilDate: new Date(charge.billing_on),
    transactionDate: Date.now(),
  };
}

async function updateProfitWell(account, charge, value) {
  const plan = `${charge.name} (Shopify)`;
  const interval = profitWellController.INTERVALS.month;

  const params = {
    email: account.email,
    plan,
    value,
    interval,
  };
  return profitWellController.updateSubscription(account.id, params);
}
