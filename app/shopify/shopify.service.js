const _ = require('lodash');
const { serializeError } = require('serialize-error');
const logger = require('../../lib/logger')('shopify.service');
const notifierService = require('../common/notifier.service');
const ShopifyLog = require('./ShopifyLog');
const ShopifySite = require('./ShopifySite');

module.exports = {
  saveEvent,
  saveSite,
};

function saveEvent(req, accountId, event, shop, err = null) {
  const error = (err instanceof Error && serializeError(err)) || (err && err.message) || err;
  (new ShopifyLog({
    accountId,
    shop,
    event,
    error,
    body: req.body,
    headers: req.headers,
    query: req.query,
  })).save().catch((saveErr) => {
    const msg = 'failed to save shopify log event';
    logger.error({ err: saveErr }, msg);
    notifierService.notifyError(saveErr, msg, { accountId, event, shop });
  });
}

function saveSite(site) {
  if(!site || !site.id) {
    return Promise.resolve(null);
  }
  return ShopifySite
    .updateOne({ id: site.id }, { ...site, installed: Date.now() }, { upsert: true })
    .catch((err) => {
      notifierService.notifyError(err, 'failed to save shopify site', _.omit(site, 'token'));
    });
}
