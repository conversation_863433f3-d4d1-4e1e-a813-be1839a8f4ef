const authTypes = require('../../middleware/authTypes');
const config = require('../../config');
const logger = require('../../lib/logger')('shopify/uninstall');
const redis = require('../common/redis.service').getClient();

const Account = require('../account/models/Account');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const Feed = require('../account/models/Feed');
const emailAutomationService = require('../common/email.automation.service');
const dateUtils = require('../../lib/utils/dateUtils');
const triggers = require('../../lib/triggers');
const ShopifyEvent = require('../events/models/ShopifyEvent');
const emailService = require('../common/mailer.service');
const notifier = require('../common/notifier.service');
const profitwellController = require('../billing/profitwell.controller');
const shopifyService = require('./shopify.service');
const shopifySite = require('./ShopifySite');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {},
  },
  async handle(req, res, next) {
    let accountId = null;
    let email = null;
    const shop = req.headers['x-shopify-shop-domain'] || null;

    try {
      const { id } = req.body;
      if(!id) throw ErrorFactory('no shop id provided');

      const account = await Account.findOne({ 'shopify.id': id });
      if(!account) throw ErrorFactory(`no account associated with shop ${shop}`);

      // eslint-disable-next-line prefer-destructuring
      email = account.email;
      accountId = account.id;
      const shopItem = account.getShop(shop);
      shopItem.uninstalled = Date.now();
      shopItem.paying = false;
      account.shopify.remove(shopItem);
      account.removedShops.push(shopItem);

      let message = `shopify uninstalled by ${account.email} from ${shop}`;
      const subscription = account.subscription || {};
      const shopifySub = subscription.source === 'shopify';
      const noShops = account.shopify.length === 0;
      const sameChargeId = shopItem.chargeId === parseFloat(subscription.subscriptionId);
      if(account.subscription && shopifySub && (noShops || sameChargeId)) {
        redis.delAsync(accountId);
        account.subscription.recentIPN = 'CANCELLATION';
        account.subscription.untilDate = dateUtils.normalize(Date.now() - 86400 * 1000);
        triggers.subscriptionStateChanged(account, 'CANCELLATION');
        message += ` (${account.subscription.plan})`;

        profitwellController.churnSubscription(account.id, new Date());
      }

      const [, , numOrders] = await Promise.all([
        account.save(),
        shopifySite.updateOne({ accountId }, { accountId: null }),
        ShopifyEvent.count({ accountId }).catch((err) => {}),
      ]);

      if(accountId && shop) {
        Feed.saveFeed(accountId, `shopify uninstalled from ${shop}`);
      }
      notifier.notify(message, { orders: numOrders || 0 }, config.slack.shopify);
      emailAutomationService.updateSubscription(email, { shopify_uninstalled: new Date() });
      emailService.sendUninstall({
        to: email, domain: shopItem.domain, platform: 'Shopify store', category: 'shopify-uninstall',
      });
      shopifyService.saveEvent(req, accountId, 'uninstall', shop);
    } catch(err) {
      shopifyService.saveEvent(req, accountId, 'uninstall-failed', shop, err.message);
      logger.error({ err, shop, critical: true }, 'uninstall webhook failed');
      notifier.notifyError(err, `shopify uninstalled failed by ${email} from ${shop}`);
    }

    res.body = { message: 'success' };
    next();
  },
};
