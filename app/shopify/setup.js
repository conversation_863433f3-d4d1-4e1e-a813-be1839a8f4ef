const _ = require('lodash');
const crypto = require('crypto');
const querystring = require('querystring');
const cheerio = require('cheerio');

const config = require('../../config');
const authTypes = require('../../middleware/authTypes');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const logger = require('../../lib/logger')('shopifySetup');
const triggers = require('../../lib/triggers');
const notifConsts = require('../notifications/constants');

const { PLATFORMS } = notifConsts;

const Account = require('../account/models/Account');
const accountService = require('../account/account.service');
const Shopify = require('../../lib/apis/shopify');
const shopifyWebhook = require('../../app/webhooks/shopify');
const notifierService = require('../common/notifier.service');
const Feed = require('../account/models/Feed');
const Notification = require('../notifications/models/Notification');
const Stream = require('../notifications/models/Stream');
const PageVisits = require('../notifications/models/PageVisits');
const shopifyService = require('./shopify.service');
const trackerService = require('../common/tracker.service');
const analyticsService = require('../common/analytics.service');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    additionalProperties: true,
    properties: {
      shop: { type: 'string', required: true },
      hmac: { type: 'string', required: true },
      code: { type: 'string', required: true },
      timestamp: { type: 'string', required: true },
    },
  },
  async handle(req, res, next) {
    // this endpoint is called when the user installs first time or opens the app from Shopify admin
    const { shop, code } = req.query;
    let accountId;
    try {
      const apiSecret = config.shopify.secret;
      const hashEquals = checkHash(req.query, apiSecret);
      if(!hashEquals) throw ErrorFactory('request origin not confirmed');

      const token = await Shopify.getToken(shop, config.shopify.apiKey, apiSecret, code);
      const shopify = Shopify(shop, token);
      const shopData = await shopify.getShop();
      shopData.token = token;
      shopData.shopCreated = shopData.created_at;
      shopData.shopUpdated = shopData.updated_at;
      const { email: shopEmail } = shopData;
      const { account, alreadyInstalled, createdAccount } = await getAccount(req, res, shopData) || {};
      accountId = account && account.id;
      shopData.accountId = accountId;
      shopifyService.saveSite(shopData);
      if(!account) {
        // can't create or find account, redirect to login
        // TODO: keep shopify data until login or signup? Would require a cookie
        const message = 'Account with email found, please login';
        notifierService.notifyError(new Error(message), `Shopify ${message}`, {
          locals: req.locals, email: shopEmail, shop: _.omit(shopData, 'token'),
        });
        return res.redirect(`${config.consoleUrl}/#/login?email=${shopEmail}&error=${message}`);
      }
      if(createdAccount) {
        const ip = req.remoteAddress;
        const userAgent = req.headers['user-agent'];
        trackerService.signup({
          id: accountId, email: account.email, ip, ua: userAgent,
        });
      }
      let redirectUrl = config.consoleUrl;
      if(!alreadyInstalled) {
        const { apiKey } = account;
        const [orders] = await Promise.all([
          pullOrders(shopify),
          addScript(shopify, account.apiKey),
          addWebhooks(shopify, apiKey),
        ]);

        const notifyData = {};
        if(orders && orders.length) {
          notifyData.orders = orders.length;
        } else {
          notifyData.orders = 0;
        }
        if(shop !== shopData.domain) {
          notifyData.domain = shopData.domain;
        }

        let message = `shopify installed by ${account.email} in ${shop}`;
        if(shopData.plan_name === 'affiliate') {
          message += ' (development shop)';
        } else {
          message += ` (${shopData.plan_name})`;
        }
        Feed.saveFeed(accountId, `Shopify installed ${shop}`);

        if(orders.length) {
          await Promise.all(orders.map(order => shopifyWebhook.saveEvent(accountId, shopData, order))).catch((err) => {
            logger.error({ critical: true, err }, 'shopify failed to import old orders');
            notifierService.notifyError(err, `shopify orders import failed ${shop}`, { accountId, orders });
          });
        }
        notifyData.streamActive = orders.length > 0;
        const [streamNotif, pageVisitsNotif] = [
          getShopifyNotification(accountId, shopData.domain, notifyData.streamActive),
          getPageVisitsNotification(accountId, shopData.domain, !notifyData.streamActive),
        ];
        await Promise.all([
          Notification.findOne({ accountId, name: streamNotif.name }).then(n => !n && streamNotif.save()),
          Notification.findOne({ accountId, name: pageVisitsNotif.name }).then(n => !n && pageVisitsNotif.save()),
        ]);
        const params = {
          source: PLATFORMS.shopify,
          events: (orders && orders.length) || 0,
          website: `https://${shopData.domain}` || `https://${shopData.myshopify_domain}`,
          domain: shopData.domain,
        };
        analyticsService.trackInstalledApp(accountId, params);
        redirectUrl = `${config.consoleUrl}/#/integration-complete?${new URLSearchParams(params).toString()}`;
        logger.info({ shop }, message);
        notifierService.notify(message, notifyData, config.slack.shopify);
      }
      shopifyService.saveEvent(req, accountId, 'setup', shop);
      return res.redirect(redirectUrl);
    } catch(err) {
      let errorMessage = err.message || 'Unknown error';
      // Parse HTML error responses inline
      const htmlResponse = _.get(err, 'response.text');
      let cleanHtml = null;
      if(htmlResponse && typeof htmlResponse === 'string') {
        try {
          const $ = cheerio.load(htmlResponse);
          // Remove all <svg> and <style> tags in a single line
          cleanHtml = $('svg,style').remove().end().html();
          let parsedError = $('h3:contains("What happened?")').next('.content--desc').text().trim();
          if(!parsedError) {
            parsedError = $('head title').text().trim();
          }
          if(parsedError) {
            errorMessage = parsedError;
          }
        } catch(e) {
          // do nothing
        }
      }
      notifierService.notifyError(err, `shopify integration failed ${shop}: ${errorMessage}`, { accountId, cleanHtml });
      shopifyService.saveEvent(req, accountId, 'setup-failed', shop, err);
      if(accountId) {
        Feed.saveFeed(accountId, `Shopify integration failed ${shop}`);
      }
      return next(err);
    }
  },
  units: {
    getAccount,
    getShopifyNotification,
  },
};

async function getAccount(req, res, shop) {
  let account = null;
  let createdAccount = false;
  const { email } = shop;
  const shopDomain = shop.myshopify_domain;
  const accountId = (req.session && req.session.accountId) || null;

  const [emailAccount, shopAccount, sessionAccount, removedShopAccount] = await Promise.all([
    Account.findOne({ email }),
    Account.findOne({ 'shopify.id': shop.id }),
    Account.findOne({ _id: accountId }),
    Account.findOne({ 'removedShops.id': shop.id }),
  ]);

  if(shopAccount) {
    account = shopAccount;
    logger.info({ email: account.email, shopDomain }, 'found account with shop, auto login');
  } else if(sessionAccount) {
    account = sessionAccount;
    logger.info({ email: account.email, shopDomain }, 'user logged in, adding shop to account');
  } else if(emailAccount) {
    logger.error({ email, shopDomain }, 'user not logged in and email exists');
    if(removedShopAccount && removedShopAccount.id === emailAccount.id) {
      // if user not logged in and account with removed shop exists -> auto login there
      // i.e. user can still connect the shop to a new/another account but can also auto login to previously created account
      account = removedShopAccount;
    } else {
      return null;
    }
  } else {
    logger.info({ email, shopDomain }, 'creating account');
    account = await accountService.makeAccount({
      email: shop.email,
      active: true,
      loginType: PLATFORMS.shopify,
      ip: req.remoteAddress,
      cookies: req.cookies,
    });
    createdAccount = true;
    triggers.signup(account, {
      platform: 'Shopify', shopDomain, domain: shop.domain, ip: req.remoteAddress,
    });
    analyticsService.trackSignupEvent(account.id, {
      loginType: PLATFORMS.shopify, $email: account.email,
    });
    // already first party (on provesrc domain)
    res.cookie('ps_signup', 'true', config.cookies.ps_signup);
  }
  req.session.accountId = account.id.toString();
  req.session.email = account.email;
  req.session.apiKey = account.apiKey;

  const hasGoal = account.goals && account.goals.find(g => g.url === 'thank_you');
  if(!hasGoal) {
    account.goals.push({ name: 'Purchases', url: 'thank_you' });
    _.set(account, 'stats.goals.created', _.get(account, 'stats.goals.created', 0) + 1);
    _.set(account, 'stats.goals.lastCreated', Date.now());
  }

  let alreadyInstalled = false;
  const accShop = account.getShop(shopDomain);
  if(accShop && accShop.token === shop.token) {
    alreadyInstalled = true;
  }
  if(createdAccount) {
    accountService.incrementNotificationCreateStats(account);
  }
  if(account.addShopifyShop(shop) && !createdAccount) {
    triggers.installedScript(account, shopDomain, { shopify: true, installed: false });
  }

  account.active = true;
  // Update the mobile z-index to prevent notifications from blocking the checkout button in Shopify's cart slider on mobile devices
  const zIndex = _.get(account, 'configuration.zindex.mobile');
  if(zIndex === 9999) { // default didn't change so we can change it
    account.configuration.zindex.mobile = 20;
  }
  await account.save();

  return { account, alreadyInstalled, createdAccount };
}

function checkHash(query, apiSecret) {
  const data = querystring.stringify(_.omit(query, ['hmac']));
  const hash = Buffer.from(query.hmac, 'utf-8');
  const generatedHash = Buffer.from(crypto.createHmac('sha256', apiSecret).update(data).digest('hex'), 'utf-8');
  try {
    return crypto.timingSafeEqual(generatedHash, hash);
  } catch(err) {
    throw ErrorFactory('failed to authorize shopify setup');
  }
}

function addScript(shopifyLib, apiKey) {
  const src = `${config.shopify.scriptUrl}?apiKey=${apiKey}`;
  return shopifyLib.getScript(src).then((res) => {
    if(!res) return shopifyLib.addScript(src);
    return null;
  }).then((res) => {
    if(!res) logger.info('script was already added');
    else if(res.statusCode === 201) logger.info({ result: res.body }, 'added script to shop');
    else throw ErrorFactory('add script status != 201');
  }).catch((err) => {
    logger.error({ critical: true, err }, 'failed to add script tag');
    throw ErrorFactory('failed to add script tag to shop');
  });
}

function addWebhooks(shopifyLib, apiKey) {
  const uninstallUrl = config.shopify.webhooks.uninstall;
  const updateUrl = config.shopify.webhooks.update;
  const trackUrl = `${config.shopify.webhooks.track}?apiKey=${apiKey}`;
  const subscriptionUpdateUrl = config.shopify.webhooks.subscriptionUpdate;
  return Promise.all([
    shopifyLib.getWebhook(Shopify.topics.order_create, trackUrl),
    shopifyLib.getWebhook(Shopify.topics.app_uninstall, uninstallUrl),
    shopifyLib.getWebhook(Shopify.topics.shop_update, updateUrl),
    shopifyLib.getWebhook(Shopify.topics.subscription_update, subscriptionUpdateUrl),
  ]).catch((err) => {
    logger.error({ critical: true, err }, 'failed to get webhooks for shop');
    throw ErrorFactory('failed to get webhooks for shop');
  }).then((results) => {
    const promises = [];
    if(!results[0]) {
      logger.info({ shop: shopifyLib.shop }, 'no webhook for order/create');
      promises.push(shopifyLib.addWebhook(Shopify.topics.order_create, trackUrl));
    }
    if(!results[1]) {
      logger.info({ shop: shopifyLib.shop }, 'no webhook for app/uninstall');
      promises.push(shopifyLib.addWebhook(Shopify.topics.app_uninstall, uninstallUrl));
    }
    if(!results[2]) {
      logger.info({ shop: shopifyLib.shop }, 'no webhook for shop/update');
      promises.push(shopifyLib.addWebhook(Shopify.topics.shop_update, updateUrl));
    }
    if(!results[3]) {
      logger.info({ shop: shopifyLib.shop }, 'no webhook for app_subscriptions/update');
      promises.push(shopifyLib.addWebhook(Shopify.topics.subscription_update, subscriptionUpdateUrl));
    }
    return Promise.all(promises);
  }).then((results) => {
    if(!results.length) logger.info('webhooks were already added');
    else logger.info({ results }, 'webhooks added');
    return results;
  })
    .catch((err) => {
      logger.error({ critical: true, err }, 'failed to add webhooks to shop');
      throw ErrorFactory('failed to add webhooks to shop', 500, { err });
    });
}

function pullOrders(shopifyLib) {
  return shopifyLib.getOrders({ limit: 30, status: 'any' }).then((orders) => {
    logger.info('got orders');
    return orders;
  }).catch((err) => {
    logger.error({ critical: true, err }, 'failed to pull latest orders');
    throw ErrorFactory('failed to get last orders from shop');
  });
}

function getShopifyNotification(accountId, fullDomain, active) {
  let domain = fullDomain;
  if(fullDomain.includes('www.')) {
    domain = fullDomain.replace('www.', '');
  }
  return new Stream({
    accountId,
    active,
    name: `Recent Purchases - ${fullDomain}`,
    message: 'purchased',
    someoneAlternatives: ['Someone Great', 'A Happy Customer'],
    autoTrack: false,
    urlTypes: {
      track: notifConsts.URL_TYPES.contains,
      display: notifConsts.URL_TYPES.contains,
      trackAbs: false,
      displayAbs: false,
    },
    trackURL: [domain],
    displayURLs: [domain],
    settings: {
      platform: notifConsts.PLATFORMS.shopify,
      displayHold: 8,
      position: notifConsts.positionTypes[0],
      hideExactTimeStream: {
        active: true,
        unit: notifConsts.timeUnits.Hours,
        value: 48,
      },
    },
  });
}

function getPageVisitsNotification(accountId, fullDomain, active) {
  let domain = fullDomain;
  if(fullDomain.includes('www.')) {
    domain = fullDomain.replace('www.', '');
  }
  return new PageVisits({
    accountId,
    active,
    urlTypes: {
      track: notifConsts.URL_TYPES.contains,
      display: notifConsts.URL_TYPES.contains,
      trackAbs: false,
      displayAbs: false,
    },
    localization: 'en',
    autoTrack: true,
    trackURL: [domain],
    displayURLs: [domain],
    type: notifConsts.notificationTypes.pageVisits,
    name: `Page Views - ${domain}`,
    message: 'have visited our store',
    refer: 'buyers',
  });
}
