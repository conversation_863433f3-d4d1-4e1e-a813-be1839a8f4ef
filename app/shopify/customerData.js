const authTypes = require('../../middleware/authTypes');
const mailer = require('../../lib/mailer');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {},
  handle(req, res, next) {
    const obj = { body: req.body, headers: req.headers };
    const template = JSON.stringify(obj, null, 2);
    mailer.sendEmail('<EMAIL>', 'shopify get customer data request', template);
    next();
  },
};
