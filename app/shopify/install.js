const authTypes = require('../../middleware/authTypes');
const config = require('../../config');
const shopifyService = require('./shopify.service');
const { SHOPIFY } = require('../constants');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {
      shop: { type: 'string', required: true },
    },
  },
  handle(req, res, next) {
    // this endpoint is called when the user installs first time or opens the app from Shopify admin
    const { shop } = req.query;
    shopifyService.saveEvent(req, null, 'install', shop);
    const apiKey = `client_id=${config.shopify.apiKey}`;
    const scope = `scope=${SHOPIFY.scopes}`;
    const redirect = `redirect_uri=${config.shopify.redirect}`;
    const url = `https://${shop}/admin/oauth/authorize?${apiKey}&${scope}&${redirect}`;
    res.redirect(url);
  },
};
