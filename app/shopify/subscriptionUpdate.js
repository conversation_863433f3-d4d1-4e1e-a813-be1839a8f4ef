/* eslint-disable no-underscore-dangle,max-len */
const authTypes = require('../../middleware/authTypes');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const notifier = require('../common/notifier.service');
const Account = require('../account/models/Account');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {},
  },
  async handle(req, res, next) {
    const shop = req.headers['x-shopify-shop-domain'] || null;
    const { id, myshopify_domain } = req.body;
    if(!id) throw ErrorFactory('no shop id provided');

    const account = await Account.findOne({ 'shopify.id': id });
    if(!account) {
      notifier.notifyError(new Error('no account found'), 'shopify subscription update webhook failed', { shop, ...req.body });
    } else {
      notifier.notifyShopify(`${myshopify_domain} shopify subscription updated ${account.email}`, req.body);
    }
    return next();
  },
};
