/* eslint-disable global-require */
module.exports = {
  '/install': require('./install'),
  '/setup': require('./setup'),
  '/redactCustomer': require('./redactCustomer'),
  '/redactShop': require('./redactShop'),
  '/customerData': require('./customerData'),
  '/charge': require('./charge'),
  '/confirm': require('./confirm'),
  '/uninstall': require('./uninstall'),
  '/cancel': require('./cancel'),
  '/shop-update': require('./shopUpdate'),
  '/subscription-update': require('./subscriptionUpdate'),
};
