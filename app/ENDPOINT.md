# Endpoint Module Structure

## Root Properties
- `config`
- `schema`
- `handle` AND/OR `handleGET` AND/OR `handlePOST`

### `config` Properties
- `{boolean} noAuth` (deprecated): set to `true` does not require authorization (use `authType` instead)
- `{array} methods`: The methods allowed on this endpoint, for example `['POST']`
- `{string | object} authType`: The authorization checks to be done (generic or per method),
    available options found in `common/authTypes`
- `{boolean} keepRawBody` - should keep raw `<PERSON><PERSON>er` body under `req.rawBody`.
- `{boolean} checkBot` - adds `req.locals.isBot`
- (TODO: implement) `{array} [middlewares]`: Middlewares specific to this endpoint, executed before `handle`, method signature `function(req, res, next)`

### `authType`
- authType for the whole endpoint (all methods):
    ```js
    authType: authTypes.console
    ```
- authType per HTTP method:
    ```js
    authType: {
        POST: authType.api
        GET: authType.console
    }
    ```
- multiple authTypes per method:
    ```js
    authType: {
        POST: [authType.api, authType.console],
        GET: authType.console
    }
    ```

### `schema`
This is a json schema descriptor (lookup npm json-schema)
- schema definition for the whole endpoint:
    ```js
    schema: {
        type: 'object',
        properties: {
            key: {type: 'string'}
        }
    }
    ```

- schema defintion per http method:
    ```js
    schema: {
        POST: {...},
        GET: {...}
    }
    ```


### `handle`, `handleGET`, `handlePOST`
The actual function that handles the request with signature `function(req, res, next)`
Can be any combination of generic handle, GET and POST handles.
**At least one must be provided**.
```js
//examples
async function handle(req, res, next){}
function handleGET(req, res, next){}
function handlePOST(req, res, next){}
```
