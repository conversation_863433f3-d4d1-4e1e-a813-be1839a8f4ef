const path = require('path');
const authTypes = require('../../middleware/authTypes');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {},
  },
  async handle(req, res, next) {
    const loggedIn = !!req.session.accountId;
    if(loggedIn) {
      const { redirect_uri: redirectUrl, state } = req.query;
      res.redirect(`${redirectUrl}?state=${state}&code=${req.session.accountId}`);
    } else {
      res.render('text-box', {
        title: 'Login Required',
        text: 'Please <a href="https://console.provesrc.com">login here</a> '
              + 'and then re-connect <a href="https://zapier.com/app/dashboard">Zapier</a>',
      });
    }
  },
};
