const Notification = require('../notifications/models/Notification');
const NOTIF_CONSTS = require('../notifications/constants');

async function getNotificationsList(accountId) {
  if(!accountId) return new Error('no accountId');

  const notifications = await Notification.find({
    accountId,
    autoTrack: false,
    'settings.platform': NOTIF_CONSTS.PLATFORMS.custom,
  }).select('name webhookId');
  return notifications.map(n => ({
    id: n.id,
    name: n.name,
    webhookId: n.webhookId,
  }));
}

module.exports = {
  getNotificationsList,
};
