const authTypes = require('../../middleware/authTypes');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const zapierService = require('./zapier.service');
const config = require('../../config');
const logger = require('../../lib/logger/LoggerFactory')('zapier.controller');
const slack = require('../../lib/apis/slackNotifier');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.api,
  },
  schema: {
    GET: {
      type: 'object',
      additionalProperties: false,
      properties: {},
    },
  },
  async handle(req, res, next) {
    const secret = req.headers['x-zapier-secret'];
    if(secret !== config.zapier.secret) {
      return next(ErrorFactory('request not authorized', 401));
    }

    const { accountId } = req.jwtData;
    const notifications = await zapierService
      .getNotificationsList(accountId)
      .catch((err) => {
        logger.info({ err, accountId }, 'failed to get notifications list');
        slack.notifyError(err, 'zapier failed to get notifications list', { accountId });
      });

    res.body = notifications || [];
    return next();
  },
};
