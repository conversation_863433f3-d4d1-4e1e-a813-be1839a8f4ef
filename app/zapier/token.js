const authTypes = require('../../middleware/authTypes');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const config = require('../../config');
const Account = require('../account/models/Account');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {},
  },
  async handle(req, res, next) {
    const { code: accountId, client_secret: secret } = req.body;
    if(secret !== config.zapier.secret) {
      return next(ErrorFactory('invalid secret'));
    }

    const account = await Account.findOne({ _id: accountId }).select('apiKey email').lean();
    if(!account) {
      return next(ErrorFactory('account not found'));
    }
    res.body = {
      access_token: account.apiKey,
      token_type: 'bearer',
      email: account.email,
      apiKey: account.apiKey,
    };
    return next();
  },
};
