const url = require('url');
const User = require('./User');
const logger = require('../../lib/logger')('users/upsert');

module.exports = async function upsert(params) {
  if(!params || !params.uid || !params.email) return;

  const { uid } = params;
  const find = { uid };

  const event = {
    id: params.id,
    date: params.date || Date.now(),
    source: params.source,
    ip: params.ip,
    email: params.email,
    firstName: params.firstName,
    lastName: params.lastName,
  };
  const $addToSet = { events: event };

  try {
    await User.update(find, { $addToSet }, { upsert: true, w: 0 }).exec();
  } catch(err) {
    logger.error({ err }, 'failed to upsert user');
  }
};
