const url = require('url');
const User = require('./User');
const logger = require('../../lib/logger')('users/upsert');

module.exports = async function upsert(req, data) {
  if(!data) data = {};

  const uid = data.uid || req.signedCookies.psuid || req.headers['x-ps-uid'];
  const find = { uid };

  const $addToSet = {};
  const ua = req.headers['user-agent'];
  if(ua) $addToSet.userAgents = ua;

  const origin = req.headers.origin || req.hostname;
  if(origin) $addToSet.hosts = url.parse(origin).host || origin;

  const ip = req.remoteAddress;
  if(ip) $addToSet.ips = ip;

  if(req.jwtData && req.jwtData.accountId) {
    const { accountId } = req.jwtData;
    $addToSet.accounts = accountId;
    if(data.cycleDate) $addToSet[`cycleDates.${accountId}`] = data.cycleDate;
  }

  try {
    await User.update(find, { $addToSet }, { upsert: true, w: 0 }).exec();
  } catch(err) {
    logger.error({ err }, 'failed to upsert user');
  }
};
