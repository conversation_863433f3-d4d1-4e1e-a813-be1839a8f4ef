const mongoose = require('mongoose');

const User = new mongoose.Schema({
  uid: { type: String, required: true, unique: true },
  hosts: [String],
  accounts: [mongoose.SchemaTypes.ObjectId],
  cycleDates: {},
  userAgents: [String],
  ips: [String],
  events: [
    new mongoose.Schema({
      id: String,
      date: Date,
      source: String,
      ip: String,
      email: String,
      firstName: String,
      lastName: String,
    }, { _id: false }),
  ],

}, { timestamps: true });

module.exports = mongoose.model('User', User);
