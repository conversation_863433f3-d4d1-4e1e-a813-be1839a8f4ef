const uuid = require('uuid/v4');
const logger = require('../../lib/logger/index')('configuration');
const config = require('../../config/index');
const redis = require('../common/redis.service').getClient();
const triggers = require('../../lib/triggers');

const Bill = require('../billing/Bill');
const AnalyticsEvent = require('../notifications/models/AnalyticsEvent');

const getLimitInfo = require('./getLimitInfo');
const saveLimitInfo = require('./saveToRedis');
const dateUtils = require('../../lib/utils/dateUtils');
const cookieUtils = require('../../lib/utils/cookieUtils');
const accountService = require('../account/account.service');
// const upsertUser = require('../users/upsert');

module.exports = async function handleCookie(req, res, { timestamp = null } = {}) {
  const { isBot } = req.locals;
  if(req.locals.debug || isBot) {
    return true;
  }
  let psuid = cookieUtils.getCookie(req, 'psuid');
  if(isBot) {
    if(!psuid) {
      cookieUtils.setCookie(req, res, cookieUtils.makeCookie(
        'psuid',
        `${uuid()}-bot`,
        config.psuidCookie,
      ));
    }
    return false;
  }

  const { accountId } = req.jwtData;
  const cookieKey = `ps${accountId}`;
  const cookieValue = decodeURI(cookieUtils.getCookie(req, cookieKey));
  const cookieTimestamp = parseInt(cookieValue.split('|')[1], 10);
  const cookieShouldGet = cookieValue.split('|')[0] === 'true';
  if(timestamp && timestamp === cookieTimestamp) {
    return cookieShouldGet;
  }

  const clientUid = req.headers['x-ps-uid'];
  const [limitInfo, redisPsuid] = await Promise.all([
    getLimitInfo.get(accountId),
    redis.getAsync(clientUid),
  ]);

  if(!limitInfo) return false;

  if(!psuid) {
    psuid = redisPsuid || uuid();
    cookieUtils.setCookie(req, res, cookieUtils.makeCookie('psuid', psuid, config.psuidCookie));
  }

  const shouldCountVisitor = redisPsuid == null;
  if(shouldCountVisitor) {
    await redis.setexAsync(clientUid, 60, psuid);
    // upsertUser(req, {uid: psuid, cycleDate: new Date(limitInfo.cycleDate)});
  }

  if(!limitInfo.fromRedis) saveLimitInfo(accountId, limitInfo);

  if(limitInfo.cycleDate !== cookieTimestamp) {
    if(cookieTimestamp) {
      logger.info({ accountId }, 'cookie expired, treat as new visitor');
    } else {
      logger.info({ accountId }, 'no cookie, new visitor');
    }

    let visitors = limitInfo.visitorCount;
    if(shouldCountVisitor) {
      Bill.incrementTotal(accountId, limitInfo.limit, limitInfo.cycleDate);
      redis.hincrbyAsync(accountId, 'visitorCount', 1);
      AnalyticsEvent.incVisitors(accountId);
      visitors += 1;

      if(visitors === limitInfo.limit) {
        const daysleft = dateUtils.daysBetween(Date.now(), limitInfo.expires);
        accountService.sendPlanLimit(accountId, { limit: limitInfo.limit });
        triggers.planLimitReached(accountId, {
          expires: limitInfo.expires, daysleft, limit: limitInfo.limit,
        });
      } else if(visitors === limitInfo.limit * 0.9) {
        accountService.send90Limit(accountId, {
          limit: limitInfo.limit, cycleEndDate: limitInfo.expires,
        });
        triggers.reached90Percent(accountId, limitInfo.limit);
      }
    }
    const shouldGetNotifications = visitors <= limitInfo.limit;

    const options = Object.assign(config.psacctsCookie, { expires: limitInfo.expires });
    cookieUtils.setCookie(req, res, cookieUtils.makeCookie(
      cookieKey,
      `${shouldGetNotifications}|${limitInfo.cycleDate}`,
      options,
    ));

    return shouldGetNotifications;
  }
  logger.info({ accountId, psuid, cookieValue }, 'user already has cookie');

  return cookieShouldGet;
};
