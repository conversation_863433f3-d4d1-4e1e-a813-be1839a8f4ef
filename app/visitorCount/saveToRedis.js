const logger = require('../../lib/logger/index')('visitorCountSave');
const redis = require('../common/redis.service').getClient();

const SECONDS_IN_24_HOURS = 24 * 60 * 60;

module.exports = function saveLimitInfo(accountId, limitInfo) {
  let ttl = (limitInfo.expires - Date.now()) / 1000;
  logger.info({ accountId, ttl, limitInfo }, 'saving limit info to redis');

  redis.hmsetAsync(accountId,
    'email', limitInfo.email,
    'limit', limitInfo.limit,
    'visitorCount', limitInfo.visitorCount,
    'expires', limitInfo.expires,
    'cycleDate', limitInfo.cycleDate,
    'createdKey', new Date());

  if(ttl < 0 || ttl > SECONDS_IN_24_HOURS) ttl = SECONDS_IN_24_HOURS;
  redis.expire(accountId, Math.floor(ttl));
};
