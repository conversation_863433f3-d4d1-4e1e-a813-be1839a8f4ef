const redis = require('../common/redis.service').getClient();
const Account = require('../account/models/Account');
const Bill = require('../billing/Bill');

module.exports = getLimitInfo;
module.exports.get = getLimitInfo;

async function getLimitInfo(accountId) {
  let [limit, expires, visitorCount, cycleDate, email] = await redis.hmgetAsync(
    accountId, 'limit', 'expires', 'visitorCount', 'cycleDate', 'email',
  );

  let fromRedis = true;
  if(!limit || !expires || !visitorCount || !cycleDate) {
    fromRedis = false;

    const account = await Account.findOne({ _id: accountId });
    if(!account) {
      return null;
    }

    const numVisitors = await Bill.getVisitorCountThisMonth(
      accountId, account.getBillingCycleDate(),
    );

    limit = account.getPlanLimit();
    expires = account.getBillingCycleEndDate();
    visitorCount = numVisitors;
    email = account.email;
    cycleDate = account.getBillingCycleDate().getTime();
  } else {
    limit = parseInt(limit, 10);
    cycleDate = parseInt(cycleDate, 10);
    visitorCount = parseInt(visitorCount, 10);
    expires = new Date(expires);
  }

  const limitInfo = {
    fromRedis,
    email,
    limit,
    expires,
    visitorCount,
    cycleDate,
  };
  return limitInfo;
}
