/* eslint-disable global-require */
const config = require('../config');

module.exports = {
  '/robots.txt': require('./common/robots.txt'),

  '/goals': require('./goals/api'),
  '/goals/analytics': require('./goals/analytics'),
  '/goals/:id(\\w{24})': require('./goals/api'),
  '/funnel_webhooks/test': require('./webhooks/clickfunnelsTest'),

  '/wp/uninstall': require('./wordpress/uninstall'),
  '/wp/setup': require('./wordpress/setup'),
  '/wp/state': require('./wordpress/state'),

  '/suggestions': require('./common/urlSuggestions/suggestion'),

  '/paypal-ipn-router': require('./paypal/paypal-ipn.router'),
  '/paypal/webhook': require('./paypal/paypalWebhookHandler'),
  ...(config.env !== 'prod' && { '/paypal/pay': require('./paypal/paypalPage') }),

  '/message': require('./messages/messages'),

  '/thinkific/install': require('./thinkific/start-install'),
  '/thinkific/setup': require('./thinkific/setup'),
  '/thinkific/webhook/order': require('./thinkific/orderWebhook'),
  '/thinkific/webhook/user': require('./thinkific/userWebhook'),
  '/thinkific/webhook/enrollment': require('./thinkific/enrollmentWebhook'),
  '/thinkific/webhook/uninstall': require('./thinkific/uninstallWebhook'),

  '/backoffice/login': require('./backoffice/login'),
  '/backoffice/switch-account': require('./backoffice/switchAccount'),
  '/backoffice/upgradable': require('./backoffice/upgradable-accounts'),
  '/backoffice/find-account': require('./backoffice/findAccount'),
  '/backoffice/account': require('./backoffice/getAccount'),
  '/backoffice/case-study-accounts': require('./backoffice/caseStudyAccounts'),
  '/backoffice/cancelled-unpaid': require('./backoffice/cancelledAccounts'),
  '/backoffice/activate-account': require('./backoffice/activateAccount'),
  '/backoffice/reset-limit': require('./backoffice/resetLimit'),
  '/backoffice/update-configuration': require('./backoffice/updateConfig'),
  '/backoffice/account/delete': require('./backoffice/deleteAccount'),
  '/backoffice/account/fetch-reviews': require('./backoffice/fetchReviews'),
  '/backoffice/account/update-invoice': require('./backoffice/updateInvoice'),
  '/backoffice/affiliate': require('./backoffice/affiliateCommission'),

  '/reviews/:id': require('./webhooks/saveReview'),

  '/billing/ipn': require('./billing/bluesnap/ipn'),
  '/billing/info': require('./billing/info'),
  '/billing/documents': require('./billing/documents'),

  '/billing/checkout-url': require('./billing/getCheckoutUrl'),
  '/billing/reactivate': require('./billing/reactivateSubscription'),
  '/billing/cancel': require('./billing/cancelSubscription'),
  '/billing/update-payment-method-redirect': require('./billing/redirectToPaymentUpdate'),
  '/billing/update-invoice': require('./billing/updateInvoice'),
  '/stripe/success': require('./billing/stripe/stripeCheckoutSuccess'),
  '/stripe/cancel': require('./billing/stripe/stripeCheckoutCancel'),
  '/stripe/webhook': require('./billing/stripe/stripeWebhook'),
  '/stripe/portal': require('./billing/stripe/stripePortal'),
};
