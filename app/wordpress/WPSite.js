const mongoose = require('mongoose');

/**
* @class WPSite
*/
const WPSite = new mongoose.Schema({
  accountId: { type: mongoose.SchemaTypes.ObjectId, index: true },
  email: { type: String },
  siteUrl: { type: String },
  siteName: { type: String },
  description: { type: String },
  woocommerce: { type: Boolean },
  pluginVersion: { type: String },
  wpVersion: { type: String },
  wooVersion: { type: String },
  lastEvent: { type: String },
}, { timestamps: { createdAt: 'date' }, collection: 'wpSites' });

WPSite.index({ siteUrl: 1, email: 1 });

/**
* @type {WPSite}
*/
module.exports = mongoose.model('WPSite', WPSite);
