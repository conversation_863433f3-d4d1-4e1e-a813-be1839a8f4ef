const { RateLimiterRedis } = require('rate-limiter-flexible');
const authTypes = require('../../middleware/authTypes');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const Account = require('../account/models/Account');
const Feed = require('../account/models/Feed');

const notifier = require('../common/notifier.service');
const urlUtils = require('../../lib/utils/urlUtils');
const { makeEvent } = require('../webhooks/woocommerce');
const notificationService = require('../notifications/notification.service');
const accountService = require('../account/account.service');
const WPSite = require('./WPSite');
const WebhookData = require('../webhooks/WebhookData');
const redisService = require('../common/redis.service');
const WooEvent = require('../events/models/WooEvent').model;

const rateLimiter = new RateLimiterRedis({
  storeClient: redisService.getClient(),
  blockDuration: 60 * 5,
  duration: 60 * 5,
  points: 5,
  keyPrefix: 'wp-setup',
});

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.api,
    maxPayload: '1mb',
  },
  schema: {
    type: 'object',
    properties: {
      secret: { type: 'string' },
      email: { type: 'string' },
      siteUrl: { type: 'string' },
      siteName: { type: 'string' },
      description: { type: 'string' },
      woocommerce: { type: 'boolean' },
      selectedEvents: {
        type: 'array',
        maxItems: 10,
        items: { type: 'string', maxLength: 50 },
      },
      orders: { type: 'array' },
    },
  },
  async handle(req, res, next) {
    const {
      secret, email, siteUrl, siteName, description, woocommerce, orders, selectedEvents,
    } = req.body;
    if(secret !== 'simple-secret') {
      throw ErrorFactory('unauthorized', 401);
    }
    const pluginVersion = req.headers['x-plugin-version'];
    const wpVersion = req.headers['x-wp-version'];
    const wooVersion = req.headers['x-woo-version'];
    const ratelimitKey = `${siteUrl}|${siteName}|wp${wpVersion}|wo${wooVersion}|p${pluginVersion}`;
    await rateLimiter.consume(ratelimitKey, 1).catch((err) => {
      notifier.notifyError(err, 'wordpress rate limit reached', { headers: req.headers, body: req.body });
      throw ErrorFactory('too many wordpress import/setup attempts', 429, { siteName, siteUrl, email });
    });

    const { accountId } = req.jwtData;
    const update = {
      ...(accountId && { accountId }),
      pluginVersion,
      wooVersion,
      wpVersion,
      woocommerce,
      description,
      ...(selectedEvents && { selectedEvents }),
    };
    WebhookData.saveData({
      accountId,
      source: 'wordpress/setup',
      headers: req.headers,
      body: req.body,
      query: req.query,
    });
    await WPSite.updateOne({ email, siteUrl }, update, { upsert: true });

    const account = await Account.findOne({ _id: accountId });
    if(!account) {
      throw ErrorFactory('account not found');
    }

    const domain = urlUtils.getDomain(siteUrl, true);
    const addedWebsite = account.addWordpress({
      email,
      siteUrl,
      siteName,
      description,
      domain,
      woocommerce,
      pluginVersion,
      wpVersion,
      wooVersion,
      selectedEvents,
    });

    let addedEvents = 0;
    if(orders && orders.length > 0) {
      const events = await Promise.all(orders.map(order => makeEvent({
        accountId, ...order, pluginVersion,
      })));
      await Promise.all(events.map(async (event) => {
        const { orderId } = event;
        const dbOrder = await WooEvent.findOne({ accountId, host: siteUrl, orderId });
        if(dbOrder) {
          return null;
        }
        addedEvents += 1;
        const feedData = {
          orderId,
          email: event.email,
          date: event.date,
          name: event.firstName,
          store: event.host,
          products: event.products.toObject(),
          location: event.location.toObject(),
          ip: event.ip,
        };
        return event.save().then(() => {
          Feed.saveFeed(accountId, 'WooCommerce Order', feedData);
        }).catch((err) => {
          Feed.saveFeed(accountId, 'WooCommerce Order Import Failed', feedData);
          notifier.notifyError(err, 'WooCommerce order import failed', event);
        });
      }));
    }
    if(addedWebsite) {
      if(addedEvents) {
        await notificationService.createWooNotification({ accountId, domain, active: true });
        accountService.incrementNotificationCreateStats(account);
        res.body = {
          successMessage: `ProveSource plugin setup completed, ${addedEvents} orders imported`,
        };
      }
      let message = 'Wordpress plugin installed';
      const feedData = { siteUrl, siteName };
      if(woocommerce) {
        message = 'WooCommerce plugin installed';
        feedData.importedOrders = orders.length;
      }
      Feed.saveFeed(accountId, message, feedData);
      notifier.notifyWpInstall(siteUrl, woocommerce, email);
    }
    await account.save();
    next();
  },
};
