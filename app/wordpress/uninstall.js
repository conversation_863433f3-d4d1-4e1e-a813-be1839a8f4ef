const authTypes = require('../../middleware/authTypes');
const authHandler = require('../../middleware/authHandler');
const notifier = require('../common/notifier.service');
const emailService = require('../common/mailer.service');
const Account = require('../account/models/Account');
const Feed = require('../account/models/Feed');
const WPSite = require('./WPSite');
const WebhookData = require('../webhooks/WebhookData');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {
      siteUrl: { type: 'string' },
      email: { type: 'string' },
      woocommerce: { type: 'boolean' },
    },
  },
  async handle(req, res, next) {
    const { siteUrl, email } = req.body;
    const { accountId } = authHandler.decodeJwt(req.headers.authorization) || {};
    WebhookData.saveData({
      accountId,
      source: 'wordpress/uninstall',
      headers: req.headers,
      body: req.body,
      query: req.query,
    });
    const account = accountId && await Account.findOne({ _id: accountId });
    if(account) {
      const wpSite = account.removeWordpress(siteUrl);
      if(wpSite) {
        await account.save();
        const message = wpSite.woocommerce
          ? 'WooCommerce plugin uninstalled'
          : 'Wordpress plugin uninstalled';
        Feed.saveFeed(accountId, message, { siteUrl });
      }
      emailService.sendUninstall({
        to: email, domain: siteUrl, platform: 'Wordpress site', category: 'wp-uninstall',
      });
    }
    notifier.notifyWpUninstall(siteUrl, email);

    WPSite.updateOne({ siteUrl, email }, { lastEvent: 'uninstalled' }, { upsert: true })
      .catch(() => {});
    next();
  },
};
