// const config = require('../../config');
const authTypes = require('../../middleware/authTypes');
const authHandler = require('../../middleware/authHandler');
// const notifier = require('../common/notifier.service');
// const mailer = require('../shopify/mailer');
const Account = require('../account/models/Account');
const Feed = require('../account/models/Feed');
const WPSite = require('./WPSite');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const WebhookData = require('../webhooks/WebhookData');
const urlUtils = require('../../lib/utils/urlUtils');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {
      email: { type: 'string' },
      siteUrl: { type: 'string' },
      siteName: { type: 'string' },
      description: { type: 'string' },
      woocommerce: { type: 'boolean' },
      event: { type: 'string' },
    },
  },
  async handle(req, res, next) {
    const { accountId } = authHandler.decodeJwt(req.headers.authorization) || {};
    WebhookData.saveData({
      accountId,
      source: 'wordpress/state',
      headers: req.headers,
      body: req.body,
      query: req.query,
    });
    const account = await Account.findOne({ _id: accountId });
    if(!account) {
      throw ErrorFactory('account not found');
    }
    const {
      siteUrl, email, woocommerce, event, description, siteName,
    } = req.body;
    const pluginVersion = req.headers['x-plugin-version'];
    const wooVersion = req.headers['x-woo-version'];
    const wpVersion = req.headers['x-wp-version'];
    const domain = urlUtils.getDomain(siteUrl, true);
    const added = account.addWordpress({
      email,
      siteUrl,
      siteName,
      description,
      domain,
      woocommerce,
      pluginVersion,
      wpVersion,
      wooVersion,
    });
    if(added) {
      Feed.saveFeed(accountId, `${woocommerce ? 'WooCommerce' : 'Wordpress'} plugin re-installed`, { siteUrl, siteName });
    }
    const update = {
      ...(accountId && { accountId }),
      pluginVersion,
      wooVersion,
      wpVersion,
      woocommerce,
      lastEvent: event,
      description,
    };
    await WPSite.updateOne({ siteUrl, email }, update, { upsert: true });
    next();
  },
};
