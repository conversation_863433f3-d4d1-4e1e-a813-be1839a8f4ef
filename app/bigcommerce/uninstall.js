const authTypes = require('../../middleware/authTypes');
const bigcommerceService = require('./bigcommerce.service');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const Account = require('../account/models/Account');
const notifier = require('../common/notifier.service');
const Feed = require('../account/models/Feed');
const logger = require('../../lib/logger')('bigcommerce/uninstall');
const emailService = require('../common/mailer.service');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {
      signed_payload: { type: 'string', required: true },
    },
  },
  // eslint-disable-next-line consistent-return
  async handle(req, res, next) {
    const payload = req.query.signed_payload;
    let accountId;

    try {
      const verified = await bigcommerceService.verify(payload).catch((err) => {
        throw ErrorFactory('Failed to verify bigcommerce request', 400, err);
      });

      const storeHash = verified.store_hash;
      bigcommerceService.saveData(storeHash, 'appUninstalled', req, { verified });

      const account = await Account.findOne({ 'bigcommerce.storeHash': storeHash });
      if(!account) {
        return next(ErrorFactory('Failed to find account using storeHash', 400));
      }
      accountId = account.id;

      const store = account.getBigcommerceStore(storeHash, false);

      store.uninstalledDate = Date.now();
      account.removedBigcommerce.push(store);
      account.bigcommerce.remove(store);
      await account.save();

      Feed.saveFeed(accountId, 'Bigcommerce App Removed', {
        domain: store.domain, bigCommerceDomain: store.bigCommerceDomain,
      });
      emailService.sendUninstall({
        to: account.email, platform: 'BigCommerce store', domain: store.domain, category: 'bigcommerce-uninstall',
      });
      notifier.notifyBigCommerce(`BigCommerce Uninstalled ${account.email}`, store);

      return next();
    } catch(err) {
      const message = 'bigcommerce uninstall failed';
      notifier.notifyError(err, message, { body: req.query });
      logger.error({ err }, message);
      return next(err);
    }
  },
};
