/* eslint-disable max-len */
const authTypes = require('../../../middleware/authTypes');
const bigcommerceService = require('../bigcommerce.service');
const Account = require('../../account/models/Account');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');
const Feed = require('../../account/models/Feed');
const notifier = require('../../common/notifier.service');
const logger = require('../../../lib/logger')('bigcommerce/orderCreatedWebhook');
const Bigcommerce = require('../../../lib/apis/bigcommerce');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {
      producer: { type: 'string', required: true },
      data: { type: 'object', required: true },
    },
  },
  async handle(req, res, next) {
    const [, storeHash] = req.body.producer.split('/');
    let accountId;

    try {
      const account = await Account.findOne({ 'bigcommerce.storeHash': storeHash });
      if(!account) {
        return next(ErrorFactory('failed to find account using storeHash', 400, { storeHash }));
      }
      accountId = account.id;
      const store = account.getBigcommerceStore(storeHash, false);

      const bigcommerce = Bigcommerce(storeHash, store.accessToken);
      const order = await bigcommerce.getOrderById(req.body.data.id);
      const event = await bigcommerceService.saveOrderEvent(accountId, {
        storeHash, accessToken: store.accessToken, order, domain: store.domain,
      });
      Feed.saveFeed(accountId, 'BigCommerce Order', event && event.toObject());
      bigcommerceService.saveData(storeHash, 'orderCreated', { accountId, ...req }, { order });

      res.body = { message: 'success' };
      return next();
    } catch(err) {
      bigcommerceService.saveData(storeHash, 'orderCreateFailed', { accountId, ...req });
      const message = 'bigcommerce order created webhook failed';
      notifier.notifyError(err, message, { body: req.body });
      logger.error({ err }, message);
      return next(err);
    }
  },
};
