const authTypes = require('../../../middleware/authTypes');
const bigcommerceService = require('../bigcommerce.service');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {
      producer: { type: 'string', required: true },
    },
  },
  async handle(req, res, next) {
    const storeHash = req.body.producer.split('/')[1];
    await bigcommerceService.saveData(storeHash, 'uninstalledWebhook', req);
    return next();
  },
};
