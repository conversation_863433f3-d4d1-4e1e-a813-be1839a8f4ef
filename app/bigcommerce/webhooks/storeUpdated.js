const authTypes = require('../../../middleware/authTypes');
const Account = require('../../account/models/Account');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');
const bigcommerceService = require('../bigcommerce.service');
const notifier = require('../../common/notifier.service');
const logger = require('../../../lib/logger')('bigcommerce/storeUpdateWebhook');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {
      producer: { type: 'string', required: true },
    },
  },
  async handle(req, res, next) {
    let accountId = null;
    const [, storeHash] = req.body.producer.split('/');

    try {
      const account = await Account.findOne({ 'bigcommerce.storeHash': storeHash });
      if(!account) {
        return next(ErrorFactory('failed to find account using storeHash', 400, { storeHash }));
      }
      accountId = account.id;
      const store = account.getBigcommerceStore(storeHash, false);
      const storeInfo = await bigcommerceService.getStoreInfo(storeHash, store.accessToken);
      bigcommerceService.saveData(storeHash, 'storeUpdate', req, { old: store, new: storeInfo });
      Object.assign(store, storeInfo);

      await account.save();

      return next();
    } catch(err) {
      bigcommerceService.saveData(storeHash, 'storeUpdateFailed', req);
      const message = 'bigcommerce store updated webhook failed';
      notifier.notifyError(err, message, { body: req.body });
      logger.error({ err }, message);
      return next(err);
    }
  },
};
