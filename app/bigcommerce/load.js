const authTypes = require('../../middleware/authTypes');
const bigcommerceService = require('./bigcommerce.service');
const config = require('../../config');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const Account = require('../account/models/Account');
const notifier = require('../common/notifier.service');
const logger = require('../../lib/logger')('bigcommerce/load');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {
      signed_payload: { type: 'string', required: true },
    },
  },
  // eslint-disable-next-line consistent-return
  async handle(req, res, next) {
    let payload;
    let accountId = null;

    try {
      payload = req.query.signed_payload;
      const verified = await bigcommerceService.verify(payload);
      const storeHash = verified.store_hash;

      bigcommerceService.saveData(storeHash, 'appLoad', req);
      const account = await Account.findOne({ 'bigcommerce.storeHash': storeHash });
      if(!account) {
        throw ErrorFactory('Failed to find account associated with store', 500, { storeHash });
      }
      const bcStore = account.getBigcommerceStore(storeHash);
      const info = await bigcommerceService.getStoreInfo(storeHash, bcStore.accessToken);
      Object.assign(bcStore, info);
      await Promise.all([
        bigcommerceService.saveSite(storeHash, info),
        account.save(),
      ]);
      accountId = account.id;

      req.session.accountId = accountId.toString();
      req.session.email = account.email;
      req.session.apiKey = account.apiKey;

      if(bigcommerceService.hasStencil(info)) {
        res.redirect(config.consoleUrl);
      } else {
        res.redirect(`${config.consoleUrl}/bigcommerce-blueprint-warning.html`);
      }
    } catch(err) {
      const message = 'bigcommerce load failed';
      notifier.notifyError(err, message, { body: req.query });
      logger.error({ err }, message);
      return next(err);
    }
  },
};
