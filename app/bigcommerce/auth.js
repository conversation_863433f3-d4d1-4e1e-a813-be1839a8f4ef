const authTypes = require('../../middleware/authTypes');
const bigcommerceService = require('./bigcommerce.service');
const config = require('../../config');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const logger = require('../../lib/logger')('bigcommerce/auth');
const notifierService = require('../common/notifier.service');
const notifier = require('../common/notifier.service');
const Feed = require('../account/models/Feed');
const accountService = require('../account/account.service');
const triggers = require('../../lib/triggers');
const trackerService = require('../common/tracker.service');
const { PLATFORMS } = require('../notifications/constants');
const analyticsService = require('../common/analytics.service');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {
      code: { type: 'string', required: true },
      context: { type: 'string', required: true },
      scope: { type: 'string', required: true },
    },
  },
  importOrders,
  // eslint-disable-next-line consistent-return
  async handle(req, res, next) {
    let accountId = null;
    const storeHash = req.query && req.query.context && req.query.context.split('/')[1];

    try {
      const authRes = await bigcommerceService.authorize(req.query).catch((err) => {
        throw ErrorFactory('Failed to authorize bigcommerce app install request', 400, err);
      });

      bigcommerceService.saveData(storeHash, 'appInstalled', req);
      const {
        access_token: accessToken, user, // context, scope,
      } = authRes;
      const { id: userId, email: userEmail } = user;
      const store = await bigcommerceService.makeStore({
        storeHash, accessToken, userId, email: userEmail,
      });
      bigcommerceService.saveSite(storeHash, store);

      let account = await bigcommerceService.getAccount({
        sessionAccountId: req.session && req.session.accountId,
        storeEmail: userEmail,
        storeHash,
      }).catch((err) => {
        if(err.message.includes('email')) {
          throw ErrorFactory('Account with same email exists');
        }
        throw ErrorFactory(`Unexpected error: ${err.message}`, 500, err);
      });

      if(!account) {
        const ip = req.remoteAddress;
        const userAgent = req.headers['user-agent'];
        account = await accountService.makeAccount({
          email: userEmail,
          active: true,
          ip,
          cookies: req.cookies,
          loginType: PLATFORMS.bigcommerce,
          source: PLATFORMS.bigcommerce,
        });
        analyticsService.trackSignupEvent(account.id, {
          loginType: PLATFORMS.bigcommerce, $email: account.email,
        });
        triggers.signup(account, { platform: PLATFORMS.bigcommerce });
        trackerService.signup({
          id: account.id, email: userEmail, ip, ua: userAgent,
        });
        res.cookie('ps_signup', 'true', config.cookies.ps_signup);
      }

      let accountStore = account.getBigcommerceStore(storeHash, false);
      if(!accountStore) {
        accountStore = account.addBigcommerceStore(store);
        triggers.installedScript(account, store.domain);
      } else {
        accountStore.accessToken = accessToken;
      }
      account.active = true;
      accountId = account.id;
      accountService.addGoal(account, 'BigCommerce Purchases', 'checkout/order-confirmation');
      accountService.incrementNotificationCreateStats(account);
      await account.save();

      const active = await accountService.shouldNotificationBeActive(account);
      const { domain } = store;

      const [orders] = await Promise.all([
        bigcommerceService.getLastOrders(storeHash, accessToken),
        bigcommerceService.setScript(storeHash, accessToken, account.apiKey),
        bigcommerceService.subscribeWebhooks(storeHash, accessToken),
        bigcommerceService.getBigcommerceNotification(accountId, domain, active)
          .save().catch(() => {}),
        bigcommerceService.getPageVisitsNotification(accountId, domain, false)
          .save().catch(() => {}),
      ]);

      if(orders.length) {
        await importOrders({
          accessToken,
          accountId,
          storeHash,
          domain,
          orders,
        });
      }

      Feed.saveFeed(accountId, 'BigCommerce App Installed', {
        domain,
        store: storeHash,
        bigCommerceDomain: store.bigcommerceDomain,
        orders: orders.length,
      });
      notifierService.notifyBigCommerce(`BigCommerce Installed ${account.email}`, {
        store, orders: orders.length,
      });

      req.session.accountId = account.id.toString();
      req.session.email = account.email;
      req.session.apiKey = account.apiKey;

      const params = {
        source: PLATFORMS.bigcommerce,
        events: (orders && orders.length) || 0,
        website: accountStore.secureUrl || accountStore.bigCommerceDomain,
        domain: accountStore.secureUrl || accountStore.bigCommerceDomain,
      };
      analyticsService.trackInstalledApp(accountId, params);
      if(bigcommerceService.hasStencil(store)) {
        res.redirect(`${config.consoleUrl}/#/integration-complete?${new URLSearchParams(params).toString()}`);
      } else {
        res.redirect(`${config.consoleUrl}/bigcommerce-blueprint-warning.html`);
      }
    } catch(err) {
      if(storeHash) {
        bigcommerceService.saveData(storeHash, 'appInstallFailed', req);
      }
      if(accountId) {
        Feed.saveFeed(accountId, 'BigCommerce App Install Failed', { error: err.message });
      }

      const message = 'bigcommerce setup failed';
      notifier.notifyError(err, message, { query: req.query, accountId });
      notifier.notifyBigCommerce(message, { query: req.query, accountId, err });
      logger.error({ critical: true, err }, message);

      if(err instanceof ErrorFactory.Error) {
        return next(err);
      }
      return next(ErrorFactory('Failed to install bigcommerce app', 400, err));
    }
  },
};

async function importOrders({
  accountId, accessToken, storeHash, domain, orders,
}) {
  const events = await Promise.all(orders.map(order => bigcommerceService.saveOrderEvent(accountId, {
    storeHash, accessToken, order, domain,
  }).catch((err) => {
    notifier.notifyError(err, 'failed to import bigCommerce order', { accountId, storeHash, order });
  })));
  if(events && events.length) {
    events.forEach((event) => {
      Feed.saveFeed(accountId, 'BigCommerce Order', event && event.toObject());
    });
  }
  return events;
}
