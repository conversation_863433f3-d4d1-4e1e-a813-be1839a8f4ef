/* eslint-disable max-len,no-await-in-loop */
const _ = require('lodash');
const chance = require('chance').Chance();
const BigcommerceData = require('./models/BigcommerceData');
const BigcommerceEvent = require('./models/BigcommerceEvent');
const BigCommerceSite = require('./models/BigCommerceSite');
const logger = require('../../lib/logger')('bigcommerce/service');
const config = require('../../config');
const maxmind = require('../../lib/maxmind');
const strUtils = require('../../lib/utils/stringUtils');
const cryptoUtils = require('../../lib/utils/cryptoUtils');
const Account = require('../account/models/Account');
const Bigcommerce = require('../../lib/apis/bigcommerce');
const Stream = require('../notifications/models/Stream');
const PageVisits = require('../notifications/models/PageVisits');
const stateCodes = require('../../lib/geo/stateCodes');
const {
  PLATFORMS, URL_TYPES, positionTypes, timeUnits, notificationTypes,
} = require('../notifications/constants');

module.exports = {
  saveData,
  authorize,
  verify,
  saveSite,
  getAccount,
  makeStore,
  hasStencil,
  setScript,
  getStoreInfo,
  subscribeWebhooks,
  saveOrderEvent,
  getProductInfo,
  getLastOrders,
  getBigcommerceNotification,
  getPageVisitsNotification,
};

function saveData(storeHash, event, {
  accountId, body, query, headers,
} = {}, extras = null) {
  return new BigcommerceData({
    accountId, storeHash, event, body, query, headers, extras,
  }).save().catch((err) => {
    logger.error({ err }, 'failed to save bigcommerce data');
  });
}

function saveSite(storeHash, site) {
  if(!storeHash || !site) {
    return Promise.reject(new Error('cannot save BigCommerceSite'));
  }
  return BigCommerceSite
    .updateOne({ storeHash }, site, { upsert: true })
    .catch(() => {});
}

async function authorize(authData) {
  return Bigcommerce().authorize(authData);
}

async function verify(payload) {
  return Bigcommerce().verify(payload);
}

async function makeStore({
  storeHash, accessToken, userId, email,
}) {
  const storeInfo = await getStoreInfo(storeHash, accessToken);
  return {
    accessToken,
    ownerId: userId,
    ownerEmail: email,
    ...storeInfo,
    installedDate: Date.now(),
  };
}

async function getAccount({ sessionAccountId, storeEmail, storeHash }) {
  const [emailAccount, storeAccount, sessionAccount, removedStoreAccount] = await Promise.all([
    Account.findOne({ email: storeEmail }),
    Account.findOne({ 'bigcommerce.storeHash': storeHash }),
    Account.findById(sessionAccountId),
    Account.findOne({ 'removedBigcommerce.storeHash': storeHash }),
  ]);

  if(storeAccount) {
    return storeAccount;
  }
  if(sessionAccount) {
    return sessionAccount;
  }
  if(removedStoreAccount) {
    return removedStoreAccount;
  }
  if(emailAccount) {
    throw new Error('account with email exists');
  }
  return null;
}

function hasStencil(store) {
  return store && store.features && store.features.stencil_enabled;
}

// eslint-disable-next-line consistent-return
async function setScript(storeHash, accessToken, apiKey) {
  const allScripts = await Bigcommerce(storeHash, accessToken).getAllScripts();

  const installedScript = allScripts.data.find(script => script.name === 'ProveSource');
  if(installedScript) {
    logger.info('script was already added');
    return;
  }

  const src = config.bigcommerce.scriptSrc;
  const body = {
    name: 'ProveSource Social Proof Script',
    description: 'This script loads ProveSource social proof to show notifications of recent sales, reviews and more',
    kind: 'script_tag',
    html: `<script>!function(o,i){window.provesrc&&window.console&&console.error&&console.error("ProveSource is included twice in this page."),provesrc=window.provesrc={dq:[],display:function(){this.dq.push(arguments)}},o._provesrcAsyncInit=function(){provesrc.init({apiKey:"${apiKey}",v:"0.0.4"})};var r=i.createElement("script");r.type="text/javascript",r.async=!0,r["ch"+"ar"+"set"]="UTF-8",r.src="${src}";var e=i.getElementsByTagName("script")[0];e.parentNode.insertBefore(r,e)}(window,document);</script>`,
    consent_category: 'functional',
    visibility: 'all_pages',
    location: 'head',
    load_method: 'default',
    auto_uninstall: true,
  };

  await Bigcommerce(storeHash, accessToken).setScript(body);
  logger.info({ storeHash }, 'Provesource script installed');
}

async function subscribeWebhooks(storeHash, accessToken) {
  const subscriptions = [];
  const bigcommerceAPI = Bigcommerce(storeHash, accessToken);
  const subscribedHooks = await bigcommerceAPI.getAllWebhooks();


  // order created webhook
  const orderCreated = subscribedHooks.find(hook => hook.destination === config.bigcommerce.webhooks.orderCreate);
  if(!orderCreated) {
    const orderCreatedBody = makeSubscriptionBody(Bigcommerce.WEBHOOK_SCOPES.orderCreated,
      config.bigcommerce.webhooks.orderCreate);
    subscriptions.push(bigcommerceAPI.subscribeWebhook(orderCreatedBody));
  }

  // store updated webhook
  const storeUpdated = subscribedHooks.find(hook => hook.destination === config.bigcommerce.webhooks.storeUpdate);
  if(!storeUpdated) {
    const storeUpdatedBody = makeSubscriptionBody(Bigcommerce.WEBHOOK_SCOPES.storeUpdated,
      config.bigcommerce.webhooks.storeUpdate);
    subscriptions.push(bigcommerceAPI.subscribeWebhook(storeUpdatedBody));
  }

  // store is cancelled or app uninstalled webhook
  const uninstalled = subscribedHooks.find(hook => hook.destination === config.bigcommerce.webhooks.appUninstall);
  if(!uninstalled) {
    const uninstalledBody = makeSubscriptionBody(Bigcommerce.WEBHOOK_SCOPES.uninstalled,
      config.bigcommerce.webhooks.appUninstall);
    subscriptions.push(bigcommerceAPI.subscribeWebhook(uninstalledBody));
  }

  const subscribed = await Promise.all(subscriptions);
  logger.info({ subscribed }, 'subscribed to webhooks');
}

async function saveOrderEvent(accountId, {
  storeHash, accessToken, order, domain,
}) {
  const dbEvent = await BigcommerceEvent.findOne({
    accountId, store: storeHash, orderId: order.id,
  });
  if(dbEvent) {
    return null;
  }

  const bigcommerceAPI = Bigcommerce(storeHash, accessToken);
  const ip = order.ip_address || null;

  let { city } = order.billing_address;
  if(!city || strUtils.hasNumbers(city)) {
    city = null;
  }
  let location = {
    country: order.billing_address.country || order.geoip_country || null,
    countryCode: order.billing_address.country_iso2 || order.geoip_country_iso2 || null,
    state: order.billing_address.state || null,
    stateCode: stateCodes.toCode(order.billing_address.state) || null,
    city,
  };

  const [customer, productsRaw] = await Promise.all([
    bigcommerceAPI.getCustomer(order.customer_id).catch(() => {}),
    bigcommerceAPI.getOrderProducts(order.id).catch(() => {}),
  ]);

  const firstName = _.get(customer, 'first_name') || _.get(order, 'billing_address.first_name') || _.get(customer, 'company');
  const lastName = _.get(customer, 'last_name') || _.get(order, 'billing_address.last_name');
  const company = _.get(customer, 'company') || _.get(order, 'billing_address.company');
  let email = _.get(customer, 'email') || _.get(order, 'billing_address.email');
  if(!email || !email.length) {
    if(firstName || lastName) {
      email = `${firstName}_${lastName}@from-bigcommerce.com`.replace(/\s/g, '.');
    } else {
      email = chance.email({ domain: 'from-bigcommerce.com' });
    }
  }

  if(ip && (!location.country || !city)) {
    location = await maxmind.geoIP(ip).catch(() => {});
  }

  const products = [];
  for(let i = 0; i < productsRaw.length; i += 1) {
    const product = productsRaw[i];
    const { product_id: productId, variant_id: variantId } = product;
    let image = null;
    let link = null;
    if(productId) {
      if(variantId) {
        const variantData = await bigcommerceAPI.getProductVariant(productId, variantId).catch(() => {});
        if(variantData && variantData.data && variantData.data.image_url) {
          image = variantData.data.image_url;
        }
      }
      if(!image) {
        const images = await bigcommerceAPI.getProductImages(productId).catch(() => {});
        if(images && images.data && images.data.length > 0) {
          const imageData = images.data.find(pic => pic.is_thumbnail);
          if(imageData) {
            image = imageData.url_thumbnail;
          } else {
            image = images.data[0].url_thumbnail;
          }
        }
      }
      const linkRaw = await bigcommerceAPI.getProductById(productId).catch(() => {});
      link = `https://${domain}${_.get(linkRaw, 'data.custom_url.url')}`;
    }

    products.push({
      id: productId,
      variant: variantId,
      quantity: product.quantity,
      price: product.total_inc_tax,
      name: product.name,
      link,
      image,
    });
  }

  return (new BigcommerceEvent({
    accountId,
    orderId: order.id,
    store: storeHash,
    domain,
    email,
    firstName,
    lastName,
    company,
    ip,
    location,
    total: order.total_inc_tax || 0,
    currency: order.currency_code || order.default_currency_code || null,
    products,
    date: order.date_created || order.date_modified || Date.now(),
  })).save();
}

async function getProductInfo({
  domain, storeHash, accessToken, productId,
}) {
  const bigcommerceAPI = Bigcommerce(storeHash, accessToken);
  const [product, images] = await Promise.all([
    bigcommerceAPI.getProductById(productId),
    bigcommerceAPI.getProductImages(productId),
  ]);
  const link = `https://${domain}${product.data.custom_url.url}`;
  let image = null;
  if(images.data && images.data.length > 0) {
    const imageData = images.data.find(pic => pic.is_thumbnail);
    if(imageData) {
      image = imageData.url_thumbnail;
    } else {
      image = images.data[0].url_thumbnail;
    }
  }
  return {
    id: productId,
    name: product.data.name,
    // price: product.total_inc_tax
    link,
    image,
  };
}

async function getStoreInfo(storeHash, accessToken) {
  const info = await Bigcommerce(storeHash, accessToken).getStoreInfo();

  return {
    storeHash,
    domain: info.domain,
    status: info.status,
    storeId: info.store_id,
    bigCommerceDomain: info.control_panel_base_url,
    secureUrl: info.secure_url || info.secure_URL,
    adminEmail: info.admin_email,
    orderEmail: info.order_email,
    storeName: info.name,
    firstName: info.first_name,
    lastName: info.last_name,
    phone: info.phone,
    country: info.country,
    countryCode: info.country_code,
    address: info.address,
    planName: info.plan_name,
    planLevel: info.plan_level,
    isTrial: info.plan_is_trial,
    logo: info.logo.url,
    language: info.language,
    currency: info.currency,
    industry: info.industry,
    features: info.features,
  };
}

async function getLastOrders(storeHash, accessToken, amount = 30) {
  return Bigcommerce(storeHash, accessToken).getLastOrders(amount);
}

// region helpers

function getBigcommerceNotification(accountId, fullDomain, active) {
  let domain = fullDomain;
  if(fullDomain.includes('www.')) {
    domain = fullDomain.replace('www.', '');
  }
  return new Stream({
    accountId,
    active,
    name: `Recent Purchases - ${domain}`,
    message: 'purchased',
    someoneAlternatives: ['Someone Great', 'A Happy Customer'],
    autoTrack: false,
    urlTypes: {
      track: URL_TYPES.contains,
      display: URL_TYPES.contains,
      trackAbs: false,
      displayAbs: false,
    },
    trackURL: [domain],
    displayURLs: [domain],
    settings: {
      platform: PLATFORMS.bigcommerce,
      displayHold: 8,
      position: positionTypes[0],
      hideExactTimeStream: {
        active: true,
        unit: timeUnits.Hours,
        value: 48,
      },
    },
  });
}

function getPageVisitsNotification(accountId, domain, active) {
  return new PageVisits({
    accountId,
    active,
    urlTypes: {
      track: URL_TYPES.all,
      display: URL_TYPES.all,
      trackAbs: false,
      displayAbs: false,
    },
    localization: 'en',
    autoTrack: true,
    trackURL: [domain],
    displayURLs: [domain],
    settings: { theme: { title: { color: '#7825F3' } } },
    type: notificationTypes.pageVisits,
    name: 'Website Page Views (All Website)',
    message: 'have visited our store',
    refer: 'buyers',
  });
}

function makeSubscriptionBody(scope, path) {
  return {
    scope,
    destination: path,
    is_active: true,
  };
}

// endregion
