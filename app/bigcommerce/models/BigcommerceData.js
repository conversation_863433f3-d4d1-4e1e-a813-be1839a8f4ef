const mongoose = require('mongoose');

const BigcommerceData = new mongoose.Schema({
  accountId: { type: mongoose.SchemaTypes.ObjectId, ref: 'Account' },
  storeHash: { type: String, required: true },
  event: { type: String, required: true },
  body: { type: Object },
  query: { type: Object },
  headers: { type: Object },
  extras: { type: Object },
}, { timestamps: true, collection: 'bigcommerceData' });

BigcommerceData.index({ storeHash: 1, createdAt: -1 });
BigcommerceData.index({ createdAt: -1 }, { expires: '30d' });

module.exports = mongoose.model('BigcommerceData', BigcommerceData);
