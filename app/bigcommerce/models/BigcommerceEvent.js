const mongoose = require('mongoose');
const StreamEvent = require('../../events/models/StreamEvent');
const product = require('../../events/models/Product');

const Product = new mongoose.Schema(product, { _id: false });

Product.path('id').required(true);
Product.path('quantity').required(true);
Product.path('price').required(true);


const BigcommerceEvent = new mongoose.Schema({
  ...StreamEvent.schema.obj,
  accountId: { type: mongoose.SchemaTypes.ObjectId, required: true },
  store: { type: String, required: true },
  orderId: { type: Number, required: true },
  domain: String,
  company: { type: String },
  total: { type: Number },
  currency: { type: String },
  products: { type: [Product], required: true },
}, {
  collection: 'bigcommerceEvents',
  timestamps: true,
});

BigcommerceEvent.index({ accountId: 1, date: -1 });
BigcommerceEvent.index({ accountId: 1, orderId: 1 });
BigcommerceEvent.index({ accountId: 1, store: 1, orderId: 1 });
BigcommerceEvent.methods = Object.assign({}, StreamEvent.schema.methods);

BigcommerceEvent.statics.getHint = function getHint() {
  return { accountId: 1, date: -1 };
};

module.exports = mongoose.model('BigcommerceEvent', BigcommerceEvent);
