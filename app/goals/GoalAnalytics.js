const mongoose = require('mongoose');
const dateUtils = require('../../lib/utils/dateUtils');

const GoalAnalytics = new mongoose.Schema({
  accountId: { type: mongoose.SchemaTypes.ObjectId, required: true },
  goalId: { type: mongoose.SchemaTypes.ObjectId, required: true },
  date: { type: Date, required: true },
  conversions: { type: Number, default: 0 },
  clickConversions: { type: Number, default: 0 },
  hoverConversions: { type: Number, default: 0 },
  engagementConversions: { type: Number, default: 0 },
  views: { type: Number, default: 0 },
  hovers: { type: Number, default: 0 },
  clicks: { type: Number, default: 0 },
  notifications: [new mongoose.Schema({
    // _id is notificationId
    // _id: { type: mongoose.SchemaTypes.ObjectId, required: true, ref: 'Notification' },
    conversions: { type: Number, default: 0 }, // conversions the notification "participated in" even a view
    engagementConversions: { type: Number, default: 0 },
    views: { type: Number, default: 0 },
    hovers: { type: Number, default: 0 },
    clicks: { type: Number, default: 0 },
  })],

}, { collection: 'goalAnalytics' });

GoalAnalytics.index({ accountId: 1, goalId: 1, date: -1 });

GoalAnalytics.statics.increment = async function (accountId, goalId, events) {
  const date = dateUtils.todayNormalized12am();
  let goalAnalytics = await this.findOne({ accountId, goalId, date });

  if(!goalAnalytics) {
    goalAnalytics = new this({ accountId, goalId, date });
  }
  goalAnalytics.conversions += 1;

  let hadClick = false;
  let hadHover = false;
  const length = events && events.length;
  for(let i = 0; i < length; i += 1) {
    const event = events[i];
    if(!event.notificationId) {
      continue;
    }
    const nId = event.notificationId;
    let notification = goalAnalytics.notifications.id(nId);
    if(!notification) {
      notification = goalAnalytics.notifications.create({ _id: nId });
      goalAnalytics.notifications.push(notification);
    }
    // notification was at least shown on the way to the conversion (i.e. notification "participated in")
    notification.conversions += 1;
    if(event.view) {
      notification.views += 1;
      goalAnalytics.views += 1;
    }
    if(event.hover) {
      hadHover = true;
      notification.hovers += 1;
      goalAnalytics.hovers += 1;
    }
    if(event.click) {
      hadClick = true;
      notification.clicks += 1;
      goalAnalytics.clicks += 1;
    }
    if(hadHover || hadClick) {
      notification.engagementConversions += 1;
    }
  }
  if(hadClick) {
    goalAnalytics.clickConversions += 1;
  }
  if(hadHover) {
    goalAnalytics.hoverConversions += 1;
  }
  if(hadHover || hadClick) {
    goalAnalytics.engagementConversions += 1;
  }

  return goalAnalytics.save();
};

/** @class GoalAnalytics */
module.exports = mongoose.model('GoalAnalytics', GoalAnalytics);
