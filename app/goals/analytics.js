const authTypes = require('../../middleware/authTypes');
const config = require('../../config');
const Account = require('../account/models/Account');
const GoalAnalytics = require('./GoalAnalytics');
const dateUtils = require('../../lib/utils/dateUtils');
const slack = require('../../lib/apis/slackNotifier');
const logger = require('../../lib/logger/LoggerFactory')('/goals/analytics');
const cookieUtils = require('../../lib/utils/cookieUtils');
const segment = require('../../lib/apis/segment');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.api,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {
      url: { type: 'string' },
      id: { type: 'string', minLength: 1 },
      segmentUserId: { type: ['string', 'number'] },
      events: {
        type: 'array',
        items: {
          type: 'object',
          additionalProperties: false,
          properties: {
            notificationId: { type: 'string', required: true },
            view: { type: 'boolean' },
            hover: { type: 'boolean' },
            click: { type: 'boolean' },
          },
        },
      },
    },
  },
  async handle(req, res, next) {
    const { url, id, segmentUserId } = req.body;
    const { accountId } = req.jwtData;
    const account = await Account.findOne({ _id: accountId }, 'goals integrations');
    const goals = account.goals || [];

    const incrementPromises = [];
    const segmentWriteKey = account.integrations.segment.writeKey;
    const segmentPromises = [];

    for(let i = 0; i < goals.length; i += 1) {
      const goal = goals[i];
      let shouldTrack = false;

      if(goal.codeTrack) {
        if(id && goal.id === id) {
          shouldTrack = true;
        }
      } else if(url && url.includes(goal.url)) {
        shouldTrack = true;
      }

      const cookieKey = `psgoal${goal.id.toString()}`;
      const cookieValue = cookieUtils.getCookie(req, cookieKey);
      if(shouldTrack && cookieValue !== 'true') {
        const cookieOpts = Object.assign({
          maxAge: dateUtils.MILLISECONDS_IN_DAY * 7,
        }, config.cookies.generic);

        cookieUtils.setCookie(req, res, cookieUtils.makeCookie(cookieKey, true, cookieOpts));

        incrementPromises.push(
          GoalAnalytics.increment(accountId, goal.id, req.body.events),
        );
        if(segmentUserId && segmentWriteKey) {
          const data = {
            userId: segmentUserId,
            event: 'Goal Completed',
            properties: {
              goalId: goal.id,
              goalName: goal.name,
              events: req.body.events,
            },
            context: config.segment.context,
          };
          segmentPromises.push(segment.track(segmentWriteKey, data));
        }
      }
    }

    Promise.all(segmentPromises).catch((err) => {
      logger.error({ err }, 'failed to send goals to segment');
    });

    await Promise.all(incrementPromises).catch((err) => {
      slack.notifyError(err, 'failed to increment analytics', { data: { accountId } });
      logger.error({ err }, 'failed to increment analytics');
    });

    next();
  },
};
