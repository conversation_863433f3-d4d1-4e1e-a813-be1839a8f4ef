/* eslint-disable no-prototype-builtins,no-underscore-dangle */
const _ = require('lodash');
const authTypes = require('../../middleware/authTypes');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const Account = require('../account/models/Account');
const Notification = require('../notifications/models/Notification');
const GoalAnalytics = require('../goals/GoalAnalytics');
const AnalyticsEvent = require('../notifications/models/AnalyticsEvent');
const stringUtils = require('../../lib/utils/stringUtils');
const dateUtils = require('../../lib/utils/dateUtils');
const analyticsService = require('../account/analytics.service');

module.exports = {
  config: {
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    authType: authTypes.console,
  },
  schema: {
    POST: {
      type: 'object',
      additionalProperties: false,
      properties: {
        name: { type: 'string', required: true },
        value: { type: 'number', default: 1 },
        url: { type: ' string', required: true },
      },
    },

    GET: {
      type: 'object',
      additionalProperties: false,
      properties: {
        startDate: { type: 'string', pattern: /^\d{4}-\d{1,2}-\d{1,2}$/ },
        endDate: { type: 'string', pattern: /^\d{4}-\d{1,2}-\d{1,2}$/ },
        notificationId: { type: 'string', pattern: stringUtils.regex.objectId },
        goalId: { type: 'string', pattern: stringUtils.regex.objectId },
      },
    },
  },
  async handlePOST(req, res, next) {
    try {
      const { accountId } = req.locals;
      const account = await Account.findOne({ _id: accountId });
      if(!account) return next(ErrorFactory.AccountNotFound(accountId));

      const { name, url, value } = req.body;
      const goalExists = !!account.goals.find(i => i.name === name);
      if(goalExists) {
        return next(ErrorFactory('a goal with this name exists, please choose another name'));
      }

      _.set(account, 'stats.goals.created', _.get(account, 'stats.goals.created', 0) + 1);
      _.set(account, 'stats.goals.lastCreated', Date.now());

      account.goals.push({
        name,
        url,
        value: value < 1 ? 1 : value,
      });
      await account.save();

      res.body = { message: 'success' };
      res.status(201);
      next();
    } catch(err) {
      next(err);
    }
  },

  async handlePUT(req, res, next) {
    try {
      const { accountId } = req.locals;
      const account = await Account.findOne({ _id: accountId });
      if(!account) return next(ErrorFactory.AccountNotFound(accountId));

      const goalId = req.params.id;
      const goal = account.goals.id(goalId);
      if(!goal) return next(ErrorFactory('goal not found', 404));

      const { name, codeTrack, value } = req.body;
      const goalWithName = account.goals.find(g => (g.name === name && g.id !== goalId));
      if(goalWithName) {
        return next(ErrorFactory('a goal with same name exists, please choose a different name'));
      }

      _.set(account, 'stats.goals.updated', _.get(account, 'stats.goals.updated', 0) + 1);
      _.set(account, 'stats.goals.lastUpdated', Date.now());

      if(req.body.hasOwnProperty('codeTrack')) {
        goal.codeTrack = codeTrack;
      }
      if(req.body.hasOwnProperty('name')) {
        goal.name = name;
      }
      if(req.body.hasOwnProperty('value')) {
        goal.value = value < 1 ? 1 : value;
      }
      await account.save();
      next();
    } catch(err) {
      next(err);
    }
  },

  async handleDELETE(req, res, next) {
    try {
      const { accountId } = req.locals;
      const account = await Account.findOne({ _id: accountId });
      if(!account) return next(ErrorFactory.AccountNotFound(accountId));

      const goalId = req.params.id;
      const goal = account.goals.id(goalId);
      if(!goal) return next(ErrorFactory('goal not found', 404));

      _.set(account, 'stats.goals.deleted', _.get(account, 'stats.goals.deleted', 0) + 1);
      _.set(account, 'stats.goals.lastDeleted', Date.now());

      goal.remove();
      await account.save();
      next();
    } catch(err) {
      next(err);
    }
  },

  async handleGET(req, res, next) {
    try {
      const { accountId } = req.locals;
      const account = await Account.findOne({ _id: accountId }, { goals: 1 });
      if(!account) {
        return next(ErrorFactory.AccountNotFound(accountId));
      }

      let startDate;
      if(req.query.startDate) {
        startDate = new Date(`${req.query.startDate} UTC`);
      } else {
        startDate = dateUtils.todayWithAddedDays(-7).normalizeTo12Am();
      }
      let endDate;
      if(req.query.endDate) {
        endDate = new Date(`${req.query.endDate} UTC`);
      } else {
        endDate = dateUtils.todayNormalized12am();
      }

      let analytics = null;
      let visitors = 0;
      const { notificationId } = req.query;
      // specific notification goal analytics
      if(notificationId) {
        analytics = await analyticsService.getNotificationGoalAnalytics({
          accountId, notificationId, startDate, endDate,
        });
      } else {
        // all goal analytics,
        const notificationIds = await Notification.distinct('_id', { accountId });
        await Promise.all([
          analyticsService.getAnalytics(accountId, { startDate, endDate }).then(ret => ret && ret.visitors),
          analyticsService.getGoalAnalytics({
            accountId, startDate, endDate,
          }),
          analyticsService.getMultiNotificationGoalAnalytics({
            accountId, notificationIds, startDate, endDate,
          }),
        ]).then(([totalVisitors, goalAnalytics, notifAnalytics]) => {
          visitors = totalVisitors;
          analytics = goalAnalytics;
          analytics.forEach((goalRow) => {
            goalRow.notifications = notifAnalytics
              .filter(n => n.goalId.equals(goalRow._id))
              .map(n => _.omit(n, ['goalId', 'notificationId'])) || [];
          });
        });
      }
      const goals = account.goals.toObject();
      for(let i = 0; i < goals.length; i += 1) {
        const goal = goals[i];
        if(!goal.value) {
          goal.value = 1;
        }
        goal.visitors = visitors || 0;
        const theGoalAnalytics = analytics && analytics.find(row => row._id.equals(goal._id));
        Object.assign(goal, theGoalAnalytics || {
          conversions: 0,
          clickConversions: 0,
          hoverConversions: 0,
          engagementConversions: 0,
          views: 0,
          hovers: 0,
          clicks: 0,
        });
      }

      res.body = goals;
      next();
    } catch(err) {
      next(err);
    }
  },
};
