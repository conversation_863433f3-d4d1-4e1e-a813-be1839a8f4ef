const authTypes = require('../../middleware/authTypes');
const config = require('../../config');
const PaypalWebhook = require('./PaypalWebhook');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const notifier = require('../common/notifier.service');
const Account = require('../account/models/Account');

const { webhookId, clientId, secret } = config.paypal;
const paypal = require('../../lib/apis/Paypal').getClient(clientId, secret, config.env !== 'prod');


module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
    // keepRawBody: true,
  },
  schema: {
    type: 'object',
    properties: {},
  },
  async handle(req, res, next) {
    // TODO: implemenet webhook support to update subscriptions?
    const { body, headers } = req;
    const verified = await paypal.verifyWebhook({ headers, body, webhookId }).catch((err) => {
      notifier.notifyError(err, 'failed to verify paypal webhook', { body, headers });
      return false;
    });
    if(!verified) {
      throw ErrorFactory('Paypal webhook could not be verified', 500);
    }
    const { resource_type: resource, event_type: event } = body;
    if(event.resource && event.resource.custom_id) {
      const account = await Account.findOne({ _id: event.resource.custom_id }).catch(() => {});
      if(account) {
        body.accountEmail = account.email;
      }
    }
    notifier.paypalWebhook(body);
    PaypalWebhook.create({
      event, resource, body, headers,
    }).catch(() => {});
    next();
  },
};
