const authTypes = require('../../middleware/authTypes');
const config = require('../../config');

const { stringUtils } = require('../../lib/utils');

const TEST_PLANS = {
  starter: 'P-1Y5064310M608562TMG3DLEY',
  growth: 'P-73G35301N6846273DMG43U3Q',
};

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {
      plan: { type: 'string', required: true },
    },
  },
  handle(req, res, next) {
    const planId = TEST_PLANS[req.query.plan] || TEST_PLANS.starter;
    const plan = stringUtils.capitalizeFirstLetter(req.query.plan);
    return res.render('paypal-buttons', { clientId: config.paypal.clientId, planId, plan });
  },
};
