const _ = require('lodash');
const config = require('../../config');
const Paypal = require('../../lib/apis/Paypal');
const paypalService = require('./paypal.service');
const authTypes = require('../../middleware/authTypes');

const notifierService = require('../common/notifier.service');
const mailerService = require('../common/mailer.service');

const GreenInvoice = require('../../lib/apis/GreenInvoice.v2');
const PaypalIPN = require('./PaypalIPN');
const Invoice = require('../billing/Invoice');
const Account = require('../account/models/Account');

const { ErrorFactory } = require('../../lib/errors');

const { sandbox } = config.paypal;
const purpleGreenInvoice = GreenInvoice.getClient(
  config.greenInvoice.purpleApiKey,
  config.greenInvoice.purpleSecret,
  sandbox,
);
const provesrcGreenInvoice = GreenInvoice.getClient(
  config.greenInvoice.apiKey,
  config.greenInvoice.secret,
  sandbox,
);

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
    keepRawBody: true,
  },
  schema: {
    type: 'object',
    properties: {},
  },
  async handle(req, res, next) {
    const { headers, body, rawBody } = req;
    const { ipn_track_id: ipnId, txn_id: txnId } = body;
    const existingIPN = await PaypalIPN.findOne({ txnId, ipnId });
    PaypalIPN.create({
      type: body.txn_type || 'unknown',
      ipnId,
      txnId,
      data: body,
    }).catch((err) => {
      notifierService.notifyError(err, 'Failed to save paypal IPN', body);
    });
    const verified = Paypal
      .verifyIPN({ rawBody, sandbox })
      .catch((err) => {
        notifierService.notifyError(err, 'Failed to verify paypal IPN');
      });
    if(!verified) {
      throw ErrorFactory('Paypal IPN could not be verified', 500, { body, headers });
    }
    const {
      txn_type: txn,
      transaction_subject: subject,
      receiver_id: merchantId,
      receiver_email: merchantEmail,
      payer_id: payerId,
      payer_email: payerEmail,
      payer_business_name: businessName,
      first_name: firstName,
      last_name: lastName,
      case_type: caseType,
      payment_status: paymentStatus,
      reason_code: reasonCode,
      residence_country: country,
      mc_currency: currency,
      txn_id: transactionId,
      parent_txn_id: prevTransactionId,
      recurring_payment_id: subscriptionId,
      product_name: productName,
      payment_gross: paymentGross,
      mc_gross: mcGross,
      address_street: address,
      address_zip: zip,
      address_city: city,
      memo,
      amount,
    } = body;

    const price = Math.abs(mcGross || paymentGross);
    const name = getName({
      businessName, fullName: `${firstName} ${lastName}`, subject, memo, email: payerEmail,
    });
    const purpleAdsRelated = isForPurpleAds(payerEmail, name, memo);
    if((caseType && caseType.toLowerCase() === 'chargeback')
      || (paymentStatus && paymentStatus.toLowerCase() === 'refunded')
      || (reasonCode && reasonCode.toLowerCase() === 'chargeback')
      || (reasonCode && reasonCode.toLowerCase() === 'refund')) {
      if(!purpleAdsRelated) {
        const invoice = await Invoice.findOne({ chargeId: prevTransactionId });
        if(!invoice) {
          mailerService.sendAdminEmail({
            to: '<EMAIL>',
            subject: 'Failed to refund paypal related invoice',
            html: JSON.stringify(body, null, 2),
          }).catch((err) => {
            notifierService.notifyError(err, 'Failed to send admin paypal refund email', body);
          });
          return next();
        }
        await provesrcGreenInvoice.cancelInvoice({
          id: invoice.docId,
          num: invoice.number,
          clientId: invoice.clientId || _.get(invoice, 'client.id'),
          clientName: _.get(invoice, 'client.name'),
          description: `${invoice.plan || invoice.description}`,
          vat: invoice.vat,
          currency: 'USD',
          price,
          paypal: true,
          chargeId: prevTransactionId,
        });
      }
      return next();
    }

    notifierService.notifyPayPalIPN(body, { txn });
    if(!['recurring_payment'].includes(txn)) {
      mailerService.sendPayPalIPNNotice(body, { txn }).catch(() => {});
    }

    if((merchantId === config.paypal.merchantId || merchantEmail === '<EMAIL>')) {
      await paypalService.checkSubscription(body, existingIPN).catch((err) => {
        notifierService.notifyError(err, 'Failed to update Paypal subscription', body);
      });

      if(price > 0 && paymentStatus && paymentStatus.toLowerCase() === 'completed') {
        if(existingIPN && existingIPN.data.time_created === body.time_created) {
          // invoice already created? avoid creating duplicates
          mailerService.sendAdminEmail({
            to: '<EMAIL>',
            subject: 'Duplicate Paypal IPN? Check invoice created',
            html: JSON.stringify(body, null, 2),
          });
          return next();
        }
        // TODO: this can be replaced by simply forwarding the IPN to the GreenInvoice IPN endpoint
        const client = purpleAdsRelated ? purpleGreenInvoice : provesrcGreenInvoice;
        let account;
        const invoiceClient = { name, country };
        if(!purpleAdsRelated) {
          account = await Account.findOne({ 'subscription.subscriptionId': subscriptionId });
          invoiceClient.name = _.get(account, 'subscription.customInvoiceName', null) || name;
          invoiceClient.address = _.get(account, 'subscription.customAddress', null) || address;
          invoiceClient.city = _.get(account, 'subscription.customCity', null) || city;
          invoiceClient.zip = _.get(account, 'subscription.customZip', null) || zip;
        }
        const invoice = await client.addInvoicePayPal({
          client: invoiceClient,
          vat: country === 'IL',
          currency,
          description: purpleAdsRelated ? 'PurpleAds' : 'ProveSource',
          lineDescription: productName,
          price,
          transactionId,
          payerId,
        }).catch((err) => {
          const msg = 'Failed to generate GreenInvoice from PayPal IPN';
          notifierService.notifyError(err, msg, { data: body, headers });
        });
        if(!purpleAdsRelated) {
          await Invoice.create({
            accountId: account.id,
            clientId: _.get(invoice, 'client.id'),
            chargeId: transactionId,
            docId: invoice.id,
            ...invoice,
            vat: country === 'IL',
            client: {
              id: _.get(invoice, 'client.id'),
              ...client,
            },
            currency: 'USD',
            amount,
            paymentMethod: {
              name: `${firstName} ${lastName}`,
              details: payerEmail,
            },
            plan: productName,
            description: productName,
            docType: invoice.type,
            charge: body,
          }).catch((err) => {
            const msg = 'Failed to create GreenInvoice document from PayPal IPN';
            notifierService.notifyError(err, msg, { data: body, headers });
          });
        }
      }
    }
    return next();
  },
};

function getName({
  businessName, fullName, subject, memo, email,
}) {
  if(subject && subject.toLowerCase().includes('shopify')
    && email && email.toLowerCase() === '<EMAIL>'
  ) {
    return 'Shopify';
  }
  if((memo && memo.toLowerCase().includes('unibots'))
    || (fullName && fullName.toLowerCase() === 'saurabh goel')
  ) {
    return 'SOMO Media Private Limited (Unibots)';
  }
  return businessName || fullName;
}

function isForPurpleAds(email, business, memo) {
  const ids = ['adskeeper', 'propeller', 'mgid', '152 media', '152media', 'purpleads', 'unibots'];
  for(let i = 0; i < ids.length; i += 1) {
    const id = ids[i];
    if(email && email.toLowerCase().includes(id)) {
      return true;
    }
    if(business && business.toLowerCase().includes(id)) {
      return true;
    }
    if(memo && memo.toLowerCase().includes(id)) {
      return true;
    }
  }
  return false;
}
