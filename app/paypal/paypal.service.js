const _ = require('lodash');
const config = require('../../config');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const accountService = require('../account/account.service');
const Account = require('../account/models/Account');
const subscriptionService = require('../billing/subscription.service');
const notifierService = require('../common/notifier.service');
const redis = require('../common/redis.service').getClient();
const { PAYPAL_PLANS, PERIODS } = require('../account/plansEnum');
const { SUBSCRIPTION_SOURCE, TRANSACTION_TYPES } = require('../billing/constants');
const triggers = require('../../lib/triggers');
const profitwellController = require('../billing/profitwell.controller');
const mailer = require('../billing/mailer');
const analyticsService = require('../common/analytics.service');
const billingService = require('../billing/billing.service');
// const trackerService = require('../common/tracker.service');

const { clientId, secret, sandbox } = config.paypal;
const paypal = require('../../lib/apis/Paypal').getClient(clientId, secret, sandbox);

const SUBSCRIPTION_TXN = [
  'recurring_payment',
  'recurring_payment_expired',
  'recurring_payment_failed',
  'recurring_payment_skipped',
  'recurring_payment_suspended',
  'recurring_payment_suspended_due_to_max_failed_payment',
  'recurring_payment_profile_cancel',

  'subscr_signup',
  'subscr_payment',
  'subscr_modify',
  'subscr_failed',
  'subscr_cancel',
  'subscr_eot',
];

module.exports = {
  checkSubscription,
  changeSubscription,
};

async function checkSubscription(ipn, existingIPN) {
  const {
    txn_id: transactionId,
    txn_type: txn,
    // receiver_id: merchantId,
    // receiver_email: merchantEmail,
    payer_id: payerId,
    payer_email: payerEmail,
    payer_business_name: businessName,
    first_name: firstName,
    last_name: lastName,
    // case_type: caseType,
    payment_status: paymentStatus,
    // residence_country: country,
    // mc_currency: currency,
    // mc_gross: price,
    recurring_payment_id: subscriptionId,
  } = ipn;
  const price = parseFloat(ipn.mc_gross || ipn.amount);
  if(!SUBSCRIPTION_TXN.includes(txn)) {
    // Not a subscription IPN/transaction, do nothing
    return;
  }
  const paypalSub = await paypal.getSubscription(subscriptionId);
  if(!paypalSub) {
    throw ErrorFactory('Paypal subscription not found', 500, { payerId, subscriptionId });
  }
  const {
    custom_id: accountId,
    plan_id: planId,
    billing_info: { next_billing_time: nextDate },
  } = paypalSub;
  const { plan, period } = PAYPAL_PLANS[planId] || {};
  if(!plan) {
    throw ErrorFactory('Paypal plan not found', 500, paypalSub);
  }
  const account = await accountService.getAccount(accountId);
  if(txn.includes('recurring_payment')
    && paymentStatus && paymentStatus.toLowerCase() === 'completed'
    && paypalSub.status === 'ACTIVE'
  ) {
    const hadSubscription = account.isSubscriptionActive();
    const recentIPN = hadSubscription
      ? TRANSACTION_TYPES.RECURRING
      : TRANSACTION_TYPES.CHARGE;
    if(!account.subscription) {
      account.subscription = subscriptionService.createSubscription(
        accountId, SUBSCRIPTION_SOURCE.paypal, plan, period, nextDate,
      );
      analyticsService.trackPurchasedEvent(account.id, {
        plan, paymentPlatform: SUBSCRIPTION_SOURCE.paypal,
      });
    } else {
      account.subscription = subscriptionService.updateSubscription(account.subscription, {
        plan, period, untilDate: nextDate, ipn: recentIPN,
      });
    }
    account.subscription.source = SUBSCRIPTION_SOURCE.paypal;
    account.subscription.subscriptionId = subscriptionId;
    account.subscription.lastChargeId = transactionId;
    account.subscription.email = payerEmail;
    account.subscription.invoiceEmail = payerEmail;
    account.subscription.username = payerId;
    account.subscription.sourceId = payerId;
    const name = businessName || `${firstName} ${lastName}`;
    const invoiceName = _.get(account, 'subscription.customInvoiceName', name);

    if(invoiceName) {
      account.subscription.invoiceNames.addToSet(invoiceName);
    }

    let value = subscriptionService.getPlanPrice(plan) * 100;
    if(period === PERIODS.YEARLY) {
      value *= 10;
    }
    // noinspection JSVoidFunctionReturnValueUsed
    await Promise.all([
      redis.delAsync(accountId),
      profitwellController.updateSubscription(accountId, {
        email: account.email,
        plan: account.subscription.contractName,
        value,
        interval: profitwellController.getInterval(period),
        startDate: Date.now(),
      }),
      triggers.subscriptionStateChanged(account, recentIPN),
      // trackerService.purchase({
      //   id: `${accountId}:purchase`, email: account.email, ua: userAgent, ip, value,
      // }),
    ]).catch((err) => {
      notifierService.notifyError(err, 'Paypal service failed to update plan details', { accountId, ipn });
    });

    if(!existingIPN) {
      // TODO: affiliate.mrr is not calculated correctly
      billingService.handleAffiliate({
        affiliateId: account.getReferringAffiliate(),
        transactionType: recentIPN,
        amount: price,
        period,
      }).catch((err) => {
        notifierService.notifyError(err, 'failed updating affiliate commission on paypal', { accountId, ipn });
      });
    }
  } else if(txn.toLowerCase().includes('skip') || (paymentStatus && paymentStatus.toLowerCase() !== 'completed')) {
    account.subscription.recentIPN = TRANSACTION_TYPES.SUBSCRIPTION_CHARGE_FAILURE;
    billingService.sendFailedChargeEmail({
      account,
      daysTillCancelDate: 0,
      paymentMethod: { type: 'paypal' },
      failureReason: 'Paypal payment failed',
      source: SUBSCRIPTION_SOURCE.paypal,
    });
  } else if(txn.toLowerCase().includes('cancel')) {
    account.subscription.recentIPN = TRANSACTION_TYPES.CANCELLATION;
    account.subscription.untilDate = Date.now() - 86400 * 1000;
    triggers.subscriptionStateChanged(account, TRANSACTION_TYPES.CANCELLATION);
    profitwellController.churnSubscription(account.id, new Date());
  }
  await account.save();
}

async function changeSubscription() {
  /** TODO: handle revise subscription
   * https://developer.paypal.com/docs/business/subscriptions/customize/revise-subscriptions/
   * https://developer.paypal.com/docs/api/subscriptions/v1/#subscriptions_revise
   */
}
