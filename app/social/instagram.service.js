const _ = require('lodash');
const config = require('../../config');
const redisService = require('../common/redis.service');
const instagram = require('../../lib/apis/instagram');
const socialNotificationService = require('../notifications/social.notification.service');
const Feed = require('../account/models/Feed');
const OpLog = require('../../cron/models/OpLog');
// const Scrapfly = require('../../lib/apis/Scrapfly');

const KEYS = {
  primary: 'instagram',
  challenge: 'challengeUrl',
  code: 'securityCode',
  timestamp: 'timestamp',
  errors: 'instagram-errors',
  channel: 'instagram-code',
};

module.exports = {
  updateInstagramCount,
};

async function updateInstagramCount(notification) {
  const instagramId = _.get(notification, 'profiles.instagram.id', null);
  if(!instagramId) {
    return null;
  }
  const { id: notificationId, accountId } = notification;
  const oplog = new OpLog({
    name: 'updateInstagram',
    accountId,
    resourceId: notification.id,
    resource: notification,
  });
  try {
    const proxyIP = config.proxies[Math.floor(Math.random() * config.proxies.length)];
    const proxy = proxyIP && `http://${proxyIP}`;
    // const json = Math.random() > 0.5;
    // const scrapfly = new Scrapfly(config.scrapfly.apiKey);
    let followers = await instagram.getFollowers(instagramId, proxy, config.scrape_do.token);
    if(followers) {
      followers = followers.toString().replace(/,/g, '');
    }
    const updateRes = await socialNotificationService
      .updateCounters(notificationId, { instagram: followers });
    oplog.result = { followers, updateRes };
    oplog.safeSave();
    redisService.getClient().delAsync(KEYS.primary);
    Feed.saveFeed(accountId, `Updated instagram social counter (${instagramId})`, {
      notification: notification.name, id: instagramId, followers,
    });
    return followers;
  } catch(err) {
    oplog.error = err;
    oplog.safeSave();
    Feed.saveFeed(accountId, `Failed to fetch instagram social followers count (${instagramId})`, {
      error: err.message,
      notification: notification.name,
      id: instagramId,
    });
    throw err;
  }
}
