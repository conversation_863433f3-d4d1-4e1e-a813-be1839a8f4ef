const _ = require('lodash');
const numeral = require('numeral');
const Feed = require('../account/models/Feed');
const socialNotificationService = require('../notifications/social.notification.service');
const { proxies = [] } = require('../../config');

const facebook = require('../../lib/apis/facebook');
const twitter = require('../../lib/apis/twitter');
const youtube = require('../../lib/apis/youtube');
const instagramService = require('./instagram.service');

module.exports = {
  updateSocialCounters,
};

async function updateSocialCounters(notification) {
  const { id: notificationId, accountId } = notification;
  const fbId = _.get(notification, 'profiles.facebook.id', null);
  let facebookRes;
  if(_.get(notification, 'profiles.facebook.pageToken')) {
    const { id: pageId, pageToken } = notification.profiles.facebook;
    facebookRes = await facebook.getPageLikesGraph(pageId, pageToken).catch(err => err);
  } else if(fbId) {
    // const randomIdx = Math.floor(Math.random() * proxies.length + 1); // +1 for "null" proxy
    // const proxyIp = randomIdx < proxies.length ? proxies[randomIdx] : null;
    // const proxy = proxyIp && `http://${proxyIp}`;
    // facebookRes = await facebook.getPageLikes(fbId, { proxy }).catch(err => err);
  }
  const twitterId = _.get(notification, 'profiles.twitter.id', null);
  const youtubeId = _.get(notification, 'profiles.youtube.id', null);
  const [twitterRes, youtubeRes, instagramRes] = await Promise.all([
    twitterId && twitter.getFollowers(twitterId).catch(err => err),
    youtubeId && youtube.getSubscribers(youtubeId).catch(err => err),
    instagramService.updateInstagramCount(notification).catch(err => err),
  ]);
  const platforms = [{
    id: fbId,
    platform: 'facebook',
    result: facebookRes,
  }, {
    id: twitterId,
    platform: 'twitter',
    result: twitterRes,
  }, {
    id: youtubeId,
    platform: 'youtube',
    result: youtubeRes,
  }];
  const profiles = {};
  for(let i = 0; i < platforms.length; i += 1) {
    const { id, platform, result } = platforms[i];
    if(result) {
      if(result instanceof Error) {
        Feed.saveFeed(accountId, `Failed to fetch ${platform} social counter (${id})`, {
          notification: notification.name, id,
        });
      } else {
        const num = getNumber(result);
        profiles[platform] = num;
        Feed.saveFeed(accountId, `Updated ${platform} social counter (${id})`, {
          notification: notification.name, id, count: num,
        });
      }
    }
  }
  await socialNotificationService.updateCounters(notificationId, profiles);
  return {
    facebook: facebookRes,
    twitter: twitterRes,
    instagram: instagramRes,
    youtube: youtubeRes,
  };
}

function getNumber(numStr) {
  if(!numStr) return null;
  if(_.isFinite(numStr)) return numStr;

  const num = numeral(numStr.toLowerCase());
  const value = num.value();
  return Math.floor(value);
}
