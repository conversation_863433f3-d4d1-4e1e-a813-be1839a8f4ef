module.exports = {
  BACKOFFICE: {
    accountListFields: '_id email createdAt parent kind name subscription.plan subscription.recentIPN',
    excludeFields: '-hashedPassword -password -stats -__v -metrics -emails -transactions',
    roles: {
      admin: 'admin',
      agent: 'agent',
    },
    permissions: {
      admin: 'admin',
      agent: 'admin,agent',
    },
  },
  MESSAGE_TYPES: {
    paypal: 'paypal',
  },
  WIX_CATALOG: {
    stores: '215238eb-22a5-4c36-9e7b-e7c08025e04e',
    bookings: '13d21c63-b5ec-5912-8397-c3a5ddb27a97',
    restaurants: '9a5d83fd-8570-482e-81ab-cfa88942ee60',
    reservations: 'f9c07de2-5341-40c6-b096-8eb39de391fb',
  },
  WIX_EVENT_TYPES: {
    all: 'all',
    storeOrder: 'storeOrder',
    booking: 'booking',
    restaurantOrder: 'restaurantOrder',
    reservation: 'reservation',
    subscription: 'subscription',
    form: 'form',
  },
  SHOPIFY: {
    partnerId: '798711',
    appId: '2477155',
    posAppId: 129785,
    scopes: 'read_products,read_orders,write_script_tags,read_locations',
    negativeStatus: {
      frozen: 'frozen',
      cancelled: 'cancelled',
      // dormant: 'dormant',
      fraudulent: 'fraudulent',
      pause: 'pause',
      paused: 'paused',
    },
  },
  SHOPIFY_EVENT_TYPES: {
    all: 'all',
    pos: 'pos',
    online: 'online',
  },
  THINKIFIC_EVENT_TYPES: {
    all: 'all',
    orders: 'order',
    users: 'user',
    enrollment: 'enrollment',
  },
  TRANSACTION_TYPES: {
    CHARGE: 'CHARGE',
    RECURRING: 'RECURRING',
    CONTRACT_CHANGE: 'CONTRACT_CHANGE',
    CANCELLATION: 'CANCELLATION',
    REFUND: 'REFUND',
    CANCELLATION_REFUND: 'CANCELLATION_REFUND',
    CANCEL_ON_RENEWAL: 'CANCEL_ON_RENEWAL',
    CHARGEBACK: 'CHARGEBACK',
    SUBSCRIPTION_CHARGE_FAILURE: 'SUBSCRIPTION_CHARGE_FAILURE',
    FRAUD_DECLINE: 'FRAUD_DECLINE',
    CC_CHARGE_FAILED: 'CC_CHARGE_FAILED',
  },
};
