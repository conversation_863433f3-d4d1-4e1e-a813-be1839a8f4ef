<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{{title}}</title>
    <style>
        @import url('https://fonts.googleapis.com/css?family=Lato:200,400,700,900');

        html, body {
            font-family: 'Lato', sans-serif;
            background-color: #fff;
        }

        .container {
            margin: 0 auto;
            max-width: 600px;
        }

        .text-center {
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <p class="text-center">Buy ProveSource {{plan}} Subscription</p>
        <div id="paypal-button-container-{{planId}}"></div>
        <script src="https://www.paypal.com/sdk/js?client-id={{clientId}}&vault=true&intent=subscription" data-sdk-integration-source="button-factory"></script>
        <script>
          paypal.Buttons({
              style: {
                  shape: 'rect',
                  color: 'gold',
                  layout: 'vertical',
                  label: 'subscribe'
              },
              createSubscription: function(data, actions) {
                return actions.subscription.create({
                  /* Creates the subscription */
                  plan_id: '{{planId}}',
                  // plan: {
                    // this does not work (i.e. changing the frequency has no effect
                    // billing_cycles: [{
                    //   tenure_type: 'REGULAR',
                    //   sequence: 1,
                    //   pricing_scheme: {
                    //     fixed_price: {
                    //       currency_code: 'USD',
                    //       // this work though and malicious user can change the price
                    //       value: '210',
                    //     },
                    //   },
                    //   frequency: {
                    //     interval_unit: 'YEAR',
                    //     interval_count: 1,
                    //   }
                    // }],
                    // add taxes dynamically
                    // taxes: {
                    //     percentage: "18",
                    //     inclusive: false,
                    // },
                  // },
                  custom_id: 'test-custom-id',
                  custom: 'test-custom',
                });
              },
              onApprove: function(data, actions) {
                alert(data.subscriptionID); // You can add optional success message for the subscriber here
              }
          }).render('#paypal-button-container-{{planId}}'); // Renders the PayPal button
        </script>
    </div>
</body>
</html>
