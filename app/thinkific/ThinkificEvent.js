const mongoose = require('mongoose');
const StreamEvent = require('../events/models/StreamEvent');
const product = require('../events/models/Product');

const Product = new mongoose.Schema(product, { _id: false });

const ThinkificEvent = StreamEvent.schema.clone();
ThinkificEvent.add({
  type: { type: String, enum: ['order', 'user', 'enrollment'], required: true },
  resourceId: { type: String, required: true },
  gid: { type: String, required: true },
  domain: { type: String, required: true },
  products: { type: [Product] },
});

ThinkificEvent.options.timestamps = true;
ThinkificEvent.options.collection = 'thinkificEvents';

ThinkificEvent.index({ accountId: 1, date: -1 });
ThinkificEvent.index({ accountId: 1, domain: 1, date: -1 });
ThinkificEvent.index({
  accountId: 1, gid: 1, resourceId: 1, type: 1,
}, { unique: true });

ThinkificEvent.statics.getHint = function getHint() {
  return { accountId: 1, date: -1 };
};

ThinkificEvent.methods.getFeedEventName = function getName() {
  switch(this.type) {
  default:
    return 'Thinkific Event';
  case 'order':
    return 'Thinkific Order';
  case 'user':
    return 'Thinkific User Signup';
  case 'enrollment':
    return 'Thinkific Enrollment';
  }
};

module.exports = mongoose.model('ThinkificEvent', ThinkificEvent);
