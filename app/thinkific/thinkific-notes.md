Thinkific Notes
================

When using this authorization, course creators must be on a Paid Thinkific plan (Basic, Pro + Growth, Premier or Plus)

What needs to be handled:

- auth
- import
- webhook 
	- order - payment
	- enrollment - free course
	- user - signup (is this required when there's an enrollment? users who signup but don't take a course)
	- uninstall
- notification create (+ auto create on install?) 
- notification get (store/subdomain filter?)
- feed events: install, uninstall, order, webhook
	- dashboard icons
- delete events
- review source? (customer reviews)

## Test
Initiate the install from our API (not clicking install button or similar), go to:
`http://localhost:3000/thinkific/install?subdomain=provesrc`

If using **ngrok**, make so to manually login to the dashboard (e.g. call fetch `POST /login` from the browser console when in the ngrok domain root page.

## Auth

- ? check user is granted for admin access (there are many user roles that can install the app) 
  `GET /api/public/v1/users` If the user is an admin they will receive a successful response

### Account

User must be logged in or signup when connecting thinkific and a re-install can't happen (?), so either connect to logged-in account or redirect to signup.

If signup required - save cookie ps_thinkific=gid and handle the cookie on signup/login/googleLogin (also delete cookie once handled)

if we could get thinkific account email - account connect priority:

1. existing account already connected to thinkific site
2. logged in account
3. create account with email if not exists
4. redirect to signup if email exists

## Webhooks

Identify site by using `x-thinkific-subdomain` header in the webhook request
Compare `x-thinkific-hmac-sha256` header value with `req.body` sha256 digest

To test an order webhook, create a 100% discount coupon and go through checkout with the coupon.

### Edge Cases
Not sure about enrollment and order behavior in regards to `GET /product`, for example for enrollment we need to use `GET /courses`, what's the case with bundles, categories, etc?

## Course Reviews
There's a Course Reviews API, we can add it to Reviews notification...?
Although there's no webhook so we will need to poll for reviews
