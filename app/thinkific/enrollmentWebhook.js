const authTypes = require('../../middleware/authTypes');
const config = require('../../config');
const cryptoUtils = require('../../lib/utils/cryptoUtils');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const notifierService = require('../common/notifier.service');
const thinkificService = require('./thinkific.service');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
    keepRawBody: true,
  },
  schema: {
    type: 'object',
    properties: {},
  },
  async handle(req, res, next) {
    const subdomain = req.headers['x-thinkific-subdomain'];
    const sha = req.headers['x-thinkific-hmac-sha256'];
    const digest = cryptoUtils.hmacSha256(req.rawBody, config.thinkific.secret);
    if(sha !== digest) {
      throw ErrorFactory('webhook could not be verified', 400, {
        body: req.body,
        headers: req.headers,
      });
    }
    await thinkificService.enrollmentWebhook(req.body, subdomain).catch((err) => {
      notifierService.notifyError(err, 'failed to process thinkific enrollment webhook', {
        body: req.body,
        headers: req.headers,
      });
    });
    next();
  },
};
