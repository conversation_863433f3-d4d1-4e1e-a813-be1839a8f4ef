const mongoose = require('mongoose');

const Thinkific = require('../account/models/Thinkific');

const ThinkificSite = Thinkific.clone();
ThinkificSite.add({
  uninstallDetails: {
    reason: { type: String },
    detail: { type: String },
  },
});
ThinkificSite.options.timestamps = true;
ThinkificSite.options.collection = 'thinkificSites';
ThinkificSite.index({ gid: 1 }, { unique: true });

module.exports = mongoose.model('ThinkificSite', ThinkificSite);
