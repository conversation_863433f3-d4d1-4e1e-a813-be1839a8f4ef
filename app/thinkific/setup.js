const authTypes = require('../../middleware/authTypes');
const config = require('../../config');
const ErrorFactory = require('../../lib/errors/ErrorFactory');

const thinkificService = require('./thinkific.service');
const notifierService = require('../common/notifier.service');
const { PLATFORMS } = require('../notifications/constants');
const analyticsService = require('../common/analytics.service');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {
      code: { type: 'string', required: true },
      subdomain: { type: 'string', required: true },
      state: { type: 'string' },
    },
  },
  async handle(req, res, next) {
    const { code, subdomain, state } = req.query;
    if(state !== 'dashboard') {
      throw ErrorFactory('bad setup state', 500, req.query);
    }
    const { accountId } = req.session;
    if(accountId) {
      const { orders, enrollments } = await thinkificService.integrateSite({
        accountId,
        code,
        subdomain,
      }).catch((err) => {
        notifierService.thinkific(`Failed to integrate site ${subdomain}`, { accountId, err });
        throw ErrorFactory('failed to integrate thinkific site', 500, { err });
      });
      const domain = thinkificService.getThinkificDomain(subdomain);
      const ordersCount = (orders && orders.length) || 0;
      const enrollmentsCount = (enrollments && enrollments.length) || 0;
      const totalEvents = ordersCount + enrollmentsCount;
      const params = {
        source: PLATFORMS.thinkific,
        events: totalEvents,
        website: `https://${domain}`,
        domain,
      };
      analyticsService.trackInstalledApp(accountId, params);
      const completeIntegrationUrl = `${config.consoleUrl}/#/integration-complete?${new URLSearchParams(params).toString()}`;
      return res.redirect(completeIntegrationUrl);
    }
    const site = await thinkificService.saveSite({ code, subdomain });
    res.cookie('ps_thinkific', site.gid, config.getCookieConfig());
    const message = encodeURI('login or create an account to connect your Thinkific site');
    return res.redirect(`${config.consoleUrl}/#/signup?message=${message}`);
  },
};
