const _ = require('lodash');
const request = require('superagent');
const config = require('../../config');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const notifierService = require('../common/notifier.service');
const Feed = require('../account/models/Feed');
const Account = require('../account/models/Account');
const accountService = require('../account/account.service');
const notificationService = require('../notifications/notification.service');
const ThinkificSite = require('./ThinkificSite');
const ThinkificEvent = require('./ThinkificEvent');
const ThinkificToken = require('./ThinkificToken');
const WebhookData = require('../webhooks/WebhookData');
const emailService = require('../common/mailer.service');
const redis = require('../common/redis.service').getClient();
const locker = require('../../lib/redisClient/Locker').getLocker(redis);

module.exports = {
  integrateAccount,
  integrateSite,
  refreshAndSaveTokens,
  saveSite,
  orderWebhook,
  userWebhook,
  enrollmentWebhook,
  uninstallWebhook,

  getCourse,
  getProducts,
  getProductImage,
  getWebhooks,
  getThinkificDomain,
};

const API_V1 = 'https://api.thinkific.com/api/public/v1';
const API_V2 = 'https://api.thinkific.com/api/v2';

async function integrateAccount(account, gid = null) {
  const site = await ThinkificSite.findOne({ gid });
  if(!site) {
    throw ErrorFactory('Thinkific site not found', 500, { account: account.email, gid });
  }
  return integrateAccountWithSite(account, site);
}

async function integrateSite({
  accountId, code, subdomain,
}) {
  const account = await Account.findOne({ _id: accountId });
  if(!account) {
    throw ErrorFactory('account not found', 500, { accountId, code, subdomain });
  }
  const site = await saveSite({ code, subdomain });
  const { orders, enrollments } = await integrateAccountWithSite(account, site);
  await account.save();
  return { site, orders, enrollments };
}

async function saveSite({ code, subdomain }) {
  const {
    gid, accessToken, refreshToken, expires,
  } = await getTokensFromCode({ code, subdomain }).catch((err) => {
    throw ErrorFactory('failed to get tokens from Thinkific', 500, { code, subdomain, err });
  });
  await ThinkificToken.updateOne({
    gid, refreshToken,
  }, { accessToken, expireDate: expires }, { upsert: true });
  return ThinkificSite.findOneAndUpdate({ gid }, {
    code, subdomain, accessToken, refreshToken, tokenExpires: expires,
  }, { upsert: true, new: true });
}

async function integrateAccountWithSite(account, site) {
  const accountId = account.id;
  const { subdomain, gid } = site;
  const anotherAccount = await getAccount({ subdomain, gid, excludeAccountId: accountId });
  if(anotherAccount) {
    notifierService.notifyError(new Error('duplicate thinkific account'), 'thinkific site already associated with account', {
      accountId, otherAccount: anotherAccount.id, subdomain, gid,
    });
  }
  account.addThinkific(site);
  const accessToken = await refreshAndSaveTokens({ account, site });
  await Promise.all([
    addScript({ apiKey: account.apiKey, accessToken }).catch((err) => {
      notifierService.notifyError(err, 'failed adding thinkific site script', { accountId, site });
    }),
    addWebhooks(accessToken).catch((err) => {
      notifierService.notifyError(err, 'failed adding thinkific webhooks', { accountId, site });
    }),
    createNotifications(account).catch((err) => {
      notifierService.notifyError(err, 'failed creating thinkific notification', { accountId, site });
    }),
  ]);

  const domain = getThinkificDomain(subdomain);
  const { orders, enrollments } = await importData({
    accountId, subdomain, gid, accessToken,
  }).then((result) => {
    const { totalOrders, totalEnrollments } = result;
    notifierService.thinkific(`${account.email} app installed in ${domain}`, { totalOrders, totalEnrollments });
    return result;
  }).catch((err) => {
    Feed.saveFeed(accountId, 'Thinkific order import failed', { error: err && err.message });
    notifierService.notifyError(err, 'failed to import thinkific data', { subdomain, gid, accountId });
  }).finally(() => {
    Feed.saveFeed(accountId, `Thinkific app installed in ${domain}`);
  });
  return { site, orders, enrollments };
}

async function addScript({ apiKey, accessToken }) {
  const scripts = await request.get(`${API_V1}/site_scripts`)
    .set(getAuthHeader(accessToken))
    .then(res => res.body && res.body.items);
  if(scripts && scripts.length && scripts.find(s => s.src.includes('provesrc'))) {
    return null;
  }
  return request.post(`${API_V1}/site_scripts`).set(getAuthHeader(accessToken)).send({
    src: `${config.shopify.scriptUrl}?apiKey=${apiKey}`,
    name: 'ProveSource Code',
    description: 'This code is responsible for your social proof, tracking events and displaying notifications using the ProveSource app https://console.provesrc.com',
    page_scopes: ['all'],
    location: 'head',
    load_method: 'async',
    category: 'analytics',
  });
}

function addWebhooks(accessToken) {
  return Promise.all([
    addWebhook({
      accessToken, topic: 'order.created', url: `${config.apiUrl}/thinkific/webhook/order`,
    }),
    addWebhook({
      accessToken, topic: 'user.signup', url: `${config.apiUrl}/thinkific/webhook/user`,
    }),
    addWebhook({
      accessToken, topic: 'enrollment.created', url: `${config.apiUrl}/thinkific/webhook/enrollment`,
    }),
    addWebhook({
      accessToken, topic: 'app.uninstalled', url: `${config.apiUrl}/thinkific/webhook/uninstall`,
    }),
  ]);
}

async function importData({
  accountId, gid, accessToken, subdomain,
}) {
  const [ordersRes, enrollmentsRes] = await Promise.all([
    request.get(`${API_V1}/orders`).query({ limit: 30 }).set(getAuthHeader(accessToken)),
    request.get(`${API_V1}/enrollments`).query({ limit: 30 }).set(getAuthHeader(accessToken)),
  ]);
  const orderItems = _.get(ordersRes, 'body.items');
  const enrollmentItems = _.get(enrollmentsRes, 'body.items');
  const totalOrders = _.get(ordersRes, 'body.meta.pagination.total_items');
  const totalEnrollments = _.get(enrollmentsRes, 'body.meta.pagination.total_items');
  const params = {
    accountId, gid, accessToken, subdomain,
  };
  const orders = await Promise.all(orderItems.map(order => saveOrder({ ...params, order })));
  const enrollments = await Promise.all(enrollmentItems.map(enrollment => saveEnrollment({ ...params, enrollment })));
  return {
    orders, enrollments, totalOrders, totalEnrollments,
  };
}

async function orderWebhook(webhookBody, subdomain) {
  const gid = webhookBody.tenant_global_id;
  const account = await getAccount({ subdomain, gid });
  if(!account) {
    throw ErrorFactory('account not found', 500, { webhookBody, subdomain });
  }
  const site = getThinkificSite(account, { subdomain, gid });
  if(site.ignoreEvents) {
    return null;
  }
  WebhookData.saveData({ accountId: account.id, source: 'thinkific', body: webhookBody });
  const { payload } = webhookBody;
  const { id: orderId, product_id: productId, user } = payload;
  const { first_name: firstName, last_name: lastName, email } = user;
  const order = {
    id: orderId,
    user_email: email,
    user_name: [firstName, lastName].join(' '),
    product_id: productId,
  };
  const accessToken = await refreshAndSaveTokens({ account, site, newSubdomain: subdomain });
  return saveOrder({
    accountId: account.id, gid, accessToken, subdomain, order,
  });
}

async function saveOrder({
  accountId, gid, accessToken, subdomain, order,
}) {
  const {
    id: resourceId,
    user_email: email,
    user_name: name,
    product_id: productId,
    created_at: createdDate,
  } = order;
  const dbEvent = await ThinkificEvent.findOne({
    accountId, gid, resourceId, type: 'order',
  }).select('_id');
  if(dbEvent) {
    return null;
  }
  const product = await getCourse({
    accessToken, subdomain, courseId: productId, api: 'products',
  }).catch((err) => {
    // notifierService.notifyError(err, 'failed to get thinkific course details', {
    //   accountId, gid, order,
    // });
  });
  const event = new ThinkificEvent({
    gid,
    type: 'order',
    accountId,
    resourceId,
    date: createdDate || Date.now(),
    email,
    firstName: name,
    domain: getThinkificDomain(subdomain),
    ...(product && { products: [product] }),
  });
  await event.save().catch((err) => {
    // ignore duplicate error, caused by race condition
    if(err.code === 11000) {
      return;
    }
    notifierService.notifyError(err, 'failed to save thinkific webhook event', {
      accountId, gid, order, event,
    });
  });
  Feed.saveFeed(accountId, 'Thinkific Order', event.toObject());
  return event;
}

async function userWebhook(webhookData, subdomain) {
  const gid = webhookData.tenant_global_id;
  const account = await getAccount({ subdomain, gid });
  if(!account) {
    throw ErrorFactory('account not found', 500, { webhookData, subdomain });
  }
  const site = getThinkificSite(account, { subdomain, gid });
  if(site.ignoreEvents) {
    return null;
  }
  WebhookData.saveData({ accountId: account.id, source: 'thinkific', body: webhookData });
  if(site.subdomain !== subdomain) {
    site.subdomain = subdomain;
    await account.save();
  }
  const { payload: user } = webhookData;
  return saveUser({
    accountId: account.id, gid, subdomain, user,
  });
}

async function saveUser({
  accountId, gid, subdomain, user,
}) {
  const type = 'user';
  const {
    id: resourceId,
    email,
    first_name: firstName,
    last_name: lastName,
  } = user;
  const dbEvent = await ThinkificEvent.findOne({
    accountId, gid, resourceId, type,
  }).select('_id');
  if(dbEvent) {
    return null;
  }
  const event = new ThinkificEvent({
    gid,
    type,
    accountId,
    resourceId,
    domain: getThinkificDomain(subdomain),
    date: Date.now(),
    email,
    firstName,
    lastName,
  });
  await event.save().catch((err) => {
    // ignore duplicate error, caused by race condition
    if(err.code === 11000) {
      return;
    }
    notifierService.notifyError(err, 'failed to save thinkific user event', { accountId, gid, user });
  });
  Feed.saveFeed(accountId, 'Thinkific User Signup', event.toObject());
  return event;
}

async function enrollmentWebhook(webhookData, subdomain) {
  const gid = webhookData.tenant_global_id;
  const account = await getAccount({ subdomain, gid });
  if(!account) {
    throw ErrorFactory('account not found', 500, { webhookData, subdomain });
  }
  const site = getThinkificSite(account, { subdomain, gid });
  if(site.ignoreEvents) {
    return null;
  }
  WebhookData.saveData({ accountId: account.id, source: 'thinkific', body: webhookData });
  const { payload } = webhookData;
  const enrollment = {
    id: payload.id,
    course_id: payload.course_id,
    user_email: _.get(payload, 'user.email'),
    user_name: [_.get(payload, 'user.first_name'), _.get(payload, 'user.lastName')].join(' '),
    started_at: payload.started_at,
  };
  const accessToken = await refreshAndSaveTokens({ account, site, newSubdomain: subdomain });
  return saveEnrollment({
    accountId: account.id, gid, subdomain, accessToken, enrollment,
  });
}

async function saveEnrollment({
  accountId, gid, accessToken, subdomain, enrollment,
}) {
  const type = 'enrollment';
  const {
    id: resourceId,
    course_id: courseId,
    user_name: firstName,
    user_email: email,
  } = enrollment;
  const date = enrollment.started_at || enrollment.created_at;
  const dbEvent = await ThinkificEvent.findOne({
    accountId, gid, resourceId, type,
  }).select('_id');
  if(dbEvent) {
    return null;
  }
  const course = await getCourse({
    accessToken, subdomain, courseId, api: 'courses',
  }).catch((err) => {
    // notifierService.notifyError(err, 'failed to get thinkific course details', {
    //   accountId, gid, enrollment,
    // });
  });
  const event = new ThinkificEvent({
    gid,
    type,
    accountId,
    resourceId,
    date: date || Date.now(),
    email,
    firstName,
    domain: getThinkificDomain(subdomain),
    ...(course && { products: [course] }),
  });
  await event.save().then((ev) => {
    Feed.saveFeed(accountId, 'Thinkific Course Enrollment', ev.toObject());
  }).catch((err) => {
    // ignore duplicate error, caused by race condition
    if(err.code === 11000) {
      return;
    }
    notifierService.notifyError(err, 'failed to save thinkific enrollment event', {
      accountId, gid, enrollment, event,
    });
  });
  return event;
}

async function uninstallWebhook(webhookData, subdomain) {
  const { payload } = webhookData;
  const { reason, detail } = payload;
  const gid = (payload.site && payload.site.id) || webhookData.tenant_global_id;
  const account = await getAccount({ subdomain, gid });
  await ThinkificSite.updateOne(gid ? { gid } : { subdomain }, {
    uninstalled: Date.now(),
    'uninstallDetails.reason': reason,
    'uninstallDetails.detail': detail,
  });
  if(account) {
    WebhookData.saveData({ accountId: account.id, source: 'thinkific', body: webhookData });
    Feed.saveFeed(account.id, `Thinkific app removed ${getThinkificDomain(subdomain)}`);
    const site = getThinkificSite(account, { subdomain, gid });
    site.uninstalled = Date.now();
    await account.save();
    emailService.sendUninstall({
      to: account.email, domain: site.subdomain, platform: 'Thinkific site', category: 'thinkific-uninstall',
    });
  }
  notifierService.thinkific(`${(account && account.email) || 'N/A'} app removed from ${getThinkificDomain(subdomain)}`);
}

async function createNotifications(account) {
  const hasNotification = await accountService.hasThinkificNotification(account.id);
  if(!hasNotification) {
    accountService.incrementNotificationCreateStats(account);
    return notificationService.makeThinkificNotification(account.id).save();
  }
  return null;
}

async function refreshAndSaveTokens({
  account, site, newSubdomain = null, force = false,
}) {
  const {
    gid, refreshToken, tokenExpires,
  } = site;
  let { subdomain, accessToken } = site;
  if(newSubdomain && newSubdomain !== subdomain) {
    subdomain = newSubdomain;
  }
  if(force || !tokenExpires || tokenExpires < Date.now()) {
    const lockKey = `thinkific-token:${gid}`;
    const lock = await locker.lock(lockKey, 60).catch(() => false);
    if(!lock) {
      return accessToken;
    }
    const tokens = await getNewTokens({ subdomain, refreshToken });
    // eslint-disable-next-line prefer-destructuring
    accessToken = tokens.accessToken;
    const params = {
      subdomain, accessToken, refreshToken: tokens.refreshToken, tokenExpires: tokens.expires,
    };
    Object.assign(site, params);
    await Promise.all([
      account.save(),
      ThinkificSite.updateOne({ gid }, params),
      ThinkificToken.updateOne({
        gid, refreshToken: tokens.refreshToken,
      }, { accessToken, expireDate: tokens.expires }, { upsert: true }),
      locker.unlock(lockKey),
    ]);
  }
  return accessToken;
}

// region helpers

function getAccount({ subdomain, gid, excludeAccountId = null }) {
  return Account.findOne({
    ...(excludeAccountId && { _id: { $ne: excludeAccountId } }),
    thinkific: {
      $elemMatch: {
        $or: [
          { subdomain, uninstalled: null },
          { gid, uninstalled: null },
        ],
      },
    },
  });
}

function getThinkificSite(account, { subdomain, gid }) {
  return account.thinkific.find(s => s.subdomain === subdomain || s.gid === gid);
}

function getTokensFromCode({ code, subdomain }) {
  const { key, secret } = config.thinkific;
  return request
    .post(`https://${getThinkificDomain(subdomain)}/oauth2/token`)
    .auth(key, secret)
    .send({ grant_type: 'authorization_code', code })
    .then((res) => {
      if(!res.body.access_token || !res.body.refresh_token || res.body.token_type !== 'bearer') {
        throw ErrorFactory('failed to get access token', 500, { body: res.body, subdomain, code });
      }
      return {
        gid: res.body.gid,
        accessToken: res.body.access_token,
        refreshToken: res.body.refresh_token,
        expires: Date.now() + res.body.expires_in * 1000,
      };
    });
}

function getNewTokens({ subdomain, refreshToken }) {
  const { key, secret } = config.thinkific;
  return request
    .post(`https://${getThinkificDomain(subdomain)}/oauth2/token`)
    .auth(key, secret)
    .send({ grant_type: 'refresh_token', refresh_token: refreshToken })
    .then((res) => {
      if(!res.body.access_token || !res.body.refresh_token || res.body.token_type !== 'bearer') {
        throw ErrorFactory('failed to get access token', 500, { body: res.body, subdomain });
      }
      return {
        accessToken: res.body.access_token,
        refreshToken: res.body.refresh_token,
        expires: Date.now() + res.body.expires_in * 1000,
      };
    });
}

async function addWebhook({ accessToken, topic, url }) {
  const webhooks = await getWebhooks(accessToken);
  const hasWebhook = !!webhooks.find(w => w.target_url === url && w.topic === topic);
  if(!hasWebhook) {
    return request
      .post(`${API_V2}/webhooks`)
      .set(getAuthHeader(accessToken))
      .send({ topic, target_url: url });
  }
  return null;
}

function getWebhooks(accessToken) {
  return request.get(`${API_V2}/webhooks`)
    .set(getAuthHeader(accessToken))
    .then(res => res.body && res.body.items);
}

function getAuthHeader(accessToken) {
  return { Authorization: `bearer ${accessToken}` };
}

/**
 *
 * @param accessToken
 * @param subdomain
 * @param courseId
 * @param {('products'|'courses')} api
 * @returns {Promise<{image: (null|*), name: *, link: unknown, id: *}>}
 */
async function getCourse({
  accessToken, subdomain, courseId, api = 'courses',
}) {
  const cacheKey = `${subdomain}:${courseId}`;
  let result = await redis.getAsync(cacheKey).then((res) => {
    try {
      return JSON.parse(res);
    } catch(err) {
      // nothing to do here, we'll just let it fetch data from the API.
    }
    return null;
  });
  if(!result) {
    let product = await request
      .get(`${API_V1}/${api}/${courseId}`)
      .set(getAuthHeader(accessToken))
      .then(res => res.body);
    if(api === 'courses') {
      const image = getProductImage(subdomain, product);
      if(!image && product && product.product_id) {
        const apiProduct = await request
          .get(`${API_V1}/products/${product.product_id}`)
          .set(getAuthHeader(accessToken))
          .then(res => res.body);
        if(apiProduct) {
          product = apiProduct;
        }
      }
    }
    const typePath = (product && product.productable_type === 'Bundle') ? 'bundles' : 'courses';
    result = {
      id: courseId,
      productId: product.product_id || product.id,
      name: product && product.name,
      image: getProductImage(subdomain, product),
      link: product && product.slug && `https://${getThinkificDomain(subdomain)}/${typePath}/${product.slug}`,
    };
    redis.setexAsync(cacheKey, 300, JSON.stringify(result));
  }
  return result;
}

function getProducts(accessToken) {
  return request
    .get(`${API_V1}/products`)
    .set(getAuthHeader(accessToken))
    .then(res => res.body);
}

function getProductImage(subdomain, product) {
  if(!product) {
    return null;
  }
  const imageOptions = [
    product.course_card_image_url,
    product.card_image_url,
    product.image,
    product.banner_image_url,
  ];

  let productImage = null;
  for(let i = 0; i < imageOptions.length; i += 1) {
    const currentImage = imageOptions[i];
    if(!currentImage || (currentImage && currentImage.includes('default-'))) {
      continue;
    }
    if(currentImage.indexOf('http') === -1) {
      if(currentImage.startsWith('/')) {
        productImage = `https://${getThinkificDomain(subdomain)}${currentImage}`;
      } else {
        productImage = `https://${getThinkificDomain(subdomain)}/${currentImage}`;
      }
    } else {
      productImage = currentImage;
    }
  }
  return productImage;
}

function getThinkificDomain(subdomain) {
  return `${subdomain}.thinkific.com`;
}

// endregion
