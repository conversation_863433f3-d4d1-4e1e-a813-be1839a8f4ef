const mongoose = require('mongoose');

const ThinkificToken = new mongoose.Schema({
  gid: { type: String, required: true },
  refreshToken: { type: String, required: true },
  accessToken: { type: String, required: true },
  expireDate: { type: Date },
}, {
  collection: 'thinkificTokens', versionKey: false,
});

ThinkificToken.index({ gid: 1, expireDate: -1 });
ThinkificToken.index({ subdomain: 1, expireDate: -1 });
ThinkificToken.index({ expireDate: -1 }, { expires: '30d' });

module.exports = mongoose.model('ThinkificToken', ThinkificToken);
