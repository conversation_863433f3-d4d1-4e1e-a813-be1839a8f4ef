const querystring = require('querystring');
const authTypes = require('../../middleware/authTypes');
const config = require('../../config');
const thinkificService = require('./thinkific.service');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {
      subdomain: { type: 'string', required: true },
    },
  },
  async handle(req, res, next) {
    const { subdomain } = req.query;
    // const challenge = await thinkificService.generateCodeChallenge(subdomain);
    const params = {
      client_id: config.thinkific.key,
      redirect_uri: `${config.apiUrl}/thinkific/setup`,
      state: 'dashboard',
      response_type: 'code',
      response_mode: 'query', // other values: form_post
      scope: 'write:site_scripts',
      // code_challenge: challenge,
      // code_challenge_method: 'S256',
    };
    const query = querystring.stringify(params);
    return res.redirect(`https://${subdomain}.thinkific.com/oauth2/authorize?${query}`);
  },
};
