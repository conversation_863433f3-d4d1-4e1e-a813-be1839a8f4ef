const path = require('path');
const authTypes = require('../../middleware/authTypes');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {
      apiKey: { type: 'string', required: true },
    },
  },
  handle(req, res, next) {
    const { apiKey } = req.query;
    const file = path.resolve(__dirname, 'template');
    return res.render(file, { apiKey });
  },
};
