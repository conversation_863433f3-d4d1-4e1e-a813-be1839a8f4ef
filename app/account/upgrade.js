const _ = require('lodash');
const authTypes = require('../../middleware/authTypes');
const config = require('../../config');
const bluesnap = require('../../lib/apis/bluesnap').getClient(config.bluesnap.apiKey, config.bluesnap.password, config.env);
const paypal = require('../../lib/apis/Paypal').getClient(config.paypal.clientId, config.paypal.secret, config.paypal.sandbox);

const ErrorFactory = require('../../lib/errors/ErrorFactory');
const {
  PLANS,
  WHITELABEL_CONTRACTS,
  CONTRACT_DETAILS,
  PAYPAL_PLANS,
  STRIPE_PORTAL_FLOWS,
  STRIPE_PLANS,
} = require('./plansEnum');
const { SUBSCRIPTION_SOURCE } = require('../billing/constants');
const Account = require('./models/Account');
const stripeService = require('../billing/stripe/stripe.service');
const notifierService = require('../common/notifier.service');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {
      contractId: { type: 'string', required: 'true' },
    },
  },
  async handle(req, res, next) {
    const { accountId } = req.locals;
    const account = await Account.findOne({ _id: accountId });
    if(!account) {
      return next(ErrorFactory.AccountNotFound(accountId));
    }

    const { contractId } = req.body;
    const { subscriptionId, source } = account.subscription || {};
    if(!subscriptionId) {
      return next(ErrorFactory('no subscription found'));
    }
    try {
      if(source === SUBSCRIPTION_SOURCE.stripe) {
        const { period, plan } = CONTRACT_DETAILS[contractId];
        const priceIds = config.isProduction() ? STRIPE_PLANS.production : STRIPE_PLANS.test;
        const priceId = _.get(priceIds, `${period}.${plan}`);
        if(!priceId) {
          return next(ErrorFactory('plan/period combination not found', 500, { accountId, plan, period }));
        }
        res.body = {
          url: await stripeService.getStripePortal({
            account,
            flow: STRIPE_PORTAL_FLOWS.subscription_update,
            priceId,
          }),
        };
        return next();
      }
      const isPaypal = subscriptionId.startsWith('I-') || source === SUBSCRIPTION_SOURCE.paypal;
      if(isPaypal) {
        const { period } = CONTRACT_DETAILS[contractId];
        let { plan } = CONTRACT_DETAILS[contractId];
        if(WHITELABEL_CONTRACTS.includes(contractId)) {
          plan = PLANS.STARTER_BRANDING;
        }
        let planId = null;
        const paypalPlans = Object.keys(PAYPAL_PLANS);
        for(let i = 0; i < paypalPlans.length; i += 1) {
          const paypalPlanId = paypalPlans[i];
          const { plan: paypalPlan, period: paypalPeriod } = PAYPAL_PLANS[paypalPlanId];
          if(paypalPlan === plan && paypalPeriod === period) {
            planId = paypalPlanId;
            break;
          }
        }
        if(!planId) {
          notifierService.paypal('Failed to upgrade Paypal subscription', { account });
          return next(ErrorFactory('subscription is managed by PayPal, please reach out to support'));
        }
        const upgradeRes = await paypal.updateSubscription(subscriptionId, {
          planId,
          returnUrl: config.consoleUrl,
          cancelUrl: `${config.consoleUrl}/#/billing/upgrade`,
        });
        const approve = upgradeRes.links.find(link => link.rel === 'approve');
        if(!approve) {
          return next(ErrorFactory('upgrade not possible, already on plan', 500, { accountId, ...req.body }));
        }
        res.body = { url: approve.href };
        return next();
      }

      const result = await bluesnap.switchContract(subscriptionId, contractId);
      if(result.status === 204) {
        res.body = { message: 'success' };
        return next();
      }
      return next(ErrorFactory('upgrade failed, please try again later', 500, result));
    } catch(err) {
      const frontErr = new Error(err.message);
      const message = _.get(err, 'data.messages.message.description')
        || _.get(err, 'data.messages.message[0].description');
      if(message) {
        frontErr.message += `: ${message}`;
      }

      let plan = 'N/A';
      if(CONTRACT_DETAILS[contractId] && CONTRACT_DETAILS[contractId].plan) {
        // eslint-disable-next-line prefer-destructuring
        plan = CONTRACT_DETAILS[contractId].plan;
      }
      notifierService.notifyError(err, `failed to upgrade ${account.email}: ${plan}`, req.body);
      return next(frontErr);
    }
  },
};
