const config = require('../../../config');
const authTypes = require('../../../middleware/authTypes');
const logger = require('../../../lib/logger')('create sub account');
const Account = require('../models/Account');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    properties: {
      id: { type: 'string', required: true },
    },
  },
  async handle(req, res, next) {
    const { accountId } = req.locals;
    const { id: subId } = req.query;

    const currentAccount = await Account.findById(accountId);
    const nextAccount = await Account.findById(subId);
    let canSwitch = false;
    if(!currentAccount || !nextAccount) {
      throw ErrorFactory('Account not found', 400, { accountId });
    } else if(currentAccount.parent && nextAccount.parent) {
      canSwitch = currentAccount.parent.id.toString() === nextAccount.parent.id.toString();
    } else if(currentAccount.parent) {
      canSwitch = currentAccount.parent.id.toString() === nextAccount.id;
    } else if(nextAccount.parent) {
      canSwitch = currentAccount.id === nextAccount.parent.id.toString();
    } else if(currentAccount.id === nextAccount.id) {
      canSwitch = true;
    }
    if(!canSwitch) {
      throw ErrorFactory('Account not found', 400, { accountId });
    }


    if(req.locals.ps_admin) {
      res.cookie('ps_admin', `${nextAccount.id}.${config.admin.pass}`, config.getCookieConfig());
    } else {
      req.session.accountId = nextAccount.id;
      req.session.apiKey = nextAccount.apiKey;
      req.session.email = nextAccount.email || nextAccount.parent.email;
      req.session.name = nextAccount.name || null;
    }

    logger.info({ accountId, name: (nextAccount.name || 'main') }, 'switch account');
    res.redirect(302, config.consoleUrl);
  },
};
