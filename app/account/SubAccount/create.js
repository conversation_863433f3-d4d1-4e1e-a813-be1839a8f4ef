const authTypes = require('../../../middleware/authTypes');
const logger = require('../../../lib/logger')('create sub account');
const Account = require('../models/Account');
const SubAccount = require('../models/SubAccount');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');
const strUtils = require('../../../lib/utils/stringUtils');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    properties: {
      name: { type: 'string', required: true },
    },
  },
  async handle(req, res, next) {
    const { name } = req.body;
    const { accountId } = req.locals;

    if(strUtils.isEmptyString(name)) {
      throw ErrorFactory('Invalid name (empty or undefined)', 400, { accountId });
    }

    if(name.length < 3 || name.length > 17) {
      throw ErrorFactory('Invalid name length (3-17 characters)', 400, { accountId, name });
    }

    const currentAccount = await Account.findById(accountId);
    let mainAccount = null;
    if(currentAccount && currentAccount.parent && currentAccount.parent.id) {
      mainAccount = await Account.findById(currentAccount.parent.id);
    } else {
      mainAccount = currentAccount;
    }
    if(!mainAccount) {
      throw ErrorFactory('Account not found', 400, { accountId });
    }

    // find sub accounts
    const subs = await SubAccount.find({ 'parent.id': mainAccount.id }).select('name').lean();
    const maxAccounts = (mainAccount.settings && mainAccount.settings.maxAccounts) || 10;
    if(subs.length >= maxAccounts - 1) {
      const msg = `You have too many accounts (max ${maxAccounts})`;
      throw ErrorFactory(msg, 400, { accountId });
    }

    const sameName = subs.length && subs.find(sub => sub.name === name);
    if(sameName) {
      const msg = 'A sub account with this name exists, please choose another name';
      throw ErrorFactory(msg, 400, { accountId });
    }
    if(name.toLowerCase() === 'main') {
      const msg = 'This name reserved for main account, please choose another name';
      throw ErrorFactory(msg, 400, { accountId });
    }

    // create new sub account
    const subAccount = new SubAccount({
      name,
      parent: { id: mainAccount.id, email: mainAccount.email },
      active: true,
      origin: mainAccount.origin,
      source: mainAccount.source,
      affiliate: {
        id: mainAccount.affiliate.id,
        referrer: mainAccount.affiliate.referrer,
      },
      ip: mainAccount.ip,
      location: mainAccount.location,
      query: mainAccount.query,
      configuration: {
        trackers: {
          googleAnalytics: true,
          mixpanel: true,
          amplitude: true,
        },
      },
    });

    await subAccount.save()
      .catch((err) => {
        const message = 'sub account save failed';
        logger.error({ err }, message);
        throw ErrorFactory(message, 500, err);
      });

    logger.info({ accountId, subAccountName: subAccount.name }, 'sub account created successfully');

    res.body = { message: 'success', name: subAccount.name, id: subAccount.id };
    next();
  },
};
