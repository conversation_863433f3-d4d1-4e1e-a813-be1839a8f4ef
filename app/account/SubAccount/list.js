const authTypes = require('../../../middleware/authTypes');
const Account = require('../models/Account');
const SubAccount = require('../models/SubAccount');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {},
  async handle(req, res, next) {
    const projection = '_id name subscription parent createdAt settings';
    const { accountId } = req.locals;
    const currentAccount = await Account.findOne({ _id: accountId }).select(projection);
    if(!currentAccount) {
      throw ErrorFactory('Account not found', 400);
    }

    let mainAccount;
    if(currentAccount && currentAccount.parent && currentAccount.parent.id) {
      mainAccount = await Account.findOne({ _id: currentAccount.parent.id }).select(projection);
    } else {
      mainAccount = currentAccount;
    }
    mainAccount.subscription = mainAccount.subscriptionInfo();
    const subAccounts = await SubAccount.find({ 'parent.id': mainAccount.id }).select(projection);
    subAccounts.forEach((account) => {
      account.subscription = account.subscriptionInfo();
    });
    res.body = { main: mainAccount, sub: subAccounts };
    next();
  },
};
