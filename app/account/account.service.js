const _ = require('lodash');
const numeral = require('numeral');
const cachegoose = require('cachegoose');
const Account = require('./models/Account');
const Notification = require('../notifications/models/Notification');
const Bill = require('../billing/Bill');
const notifConsts = require('../notifications/constants');
const mailerService = require('../common/mailer.service');
const analyticsService = require('./analytics.service');
const notificationService = require('../notifications/notification.service');
const suggestionsService = require('../common/urlSuggestions/suggestions.service');
const notifierService = require('../common/notifier.service');
const getLimitInfo = require('../visitorCount/getLimitInfo');
const {
  dateUtils, stringUtils, emptyFn, cryptoUtils,
} = require('../../lib/utils');
const maxmind = require('../../lib/maxmind');

const FROM_HELP = {
  email: '<EMAIL>',
  name: 'ProveSource Support',
};

module.exports = {
  makeAccount,
  shouldNotificationBeActive,
  hasWixNotification,
  hasThinkificNotification,
  incrementNotificationCreateStats,
  addGoal,
  getAccount,
  clearCache,
  getAccountByEmail,
  getQuerySummaryEmail,
  sendSummaryEmail,
  sendFacebookReviewsFailedEmail,
  sendPlanLimit,
  send90Limit,
  sendReviewRequest,
};

async function makeAccount({
  email, password, active = false, loginType = 'email', ip, source, cookies = {},
}) {
  const location = await maxmind.geoIP(ip).catch(() => {});
  let query = null;
  if(cookies.ps_query_params) {
    query = stringUtils.getQuery(cookies.ps_query_params);
  }
  let referrer = cookies.ps_aff;
  const origin = cookies.referrer;
  if((origin && origin.includes('google')) || (query && !query.aff)) {
    referrer = null;
  }
  return Account.create({
    email,
    emails: [email],
    hashedPassword: false,
    password: password || cryptoUtils.randomString(14),
    origin,
    source: source || cookies.source,
    loginType,
    active,
    affiliate: {
      id: cryptoUtils.randomString(8),
      referrer,
    },
    ip,
    ips: [ip],
    location,
    query,
  });
}

/**
 * @param accountId
 * @param checkCache
 * @param cacheResult
 * @param cacheExpiry
 * @returns {Promise<{fromCache: boolean, account: Account}>}
 */
async function getAccount(accountId, {
  cache = 0,
} = {}) {
  if(!accountId) {
    return null;
  }
  let account = null;
  const accountPromise = Account.findOne({ _id: accountId });
  if(cache > 0) {
    accountPromise.cache(cache, accountId);
  }
  account = await accountPromise;
  return account;
}

async function clearCache(id) {
  return cachegoose.clearCache(id);
}

function getAccountByEmail(email) {
  if(!email) {
    return null;
  }
  return Account.findOne({ email });
}

async function shouldNotificationBeActive(account) {
  return true;
  // let active = true;
  // const hasActive = !!(await Notification.findOne({ accountId: account.id, active: true }));
  // if(account && !account.isSubscriptionActive() && hasActive) {
  //   active = false;
  // }
  // return active;
}

async function hasWixNotification(accountId) {
  return Notification.findOne({
    accountId, 'settings.platform': notifConsts.PLATFORMS.wix,
  }).select('_id').then(notification => !!notification);
}

async function hasThinkificNotification(accountId) {
  return Notification.findOne({
    accountId, 'settings.platform': notifConsts.PLATFORMS.thinkific,
  }).select('_id').then(notification => !!notification);
}

function incrementNotificationCreateStats(account) {
  _.set(account, 'stats.createdNotifications', _.get(account, 'stats.createdNotifications', 0) + 1);
  _.set(account, 'stats.notifications.created', _.get(account, 'stats.notifications.created', 0) + 1);
  _.set(account, 'stats.notifications.lastCreated', Date.now());
}

function addGoal(account, name, url) {
  const hasGoal = account.goals && account.goals.find(g => g.name === name || g.url === url);
  if(hasGoal) {
    return;
  }
  account.goals.push({ name, url });
  if(!account.stats) {
    account.stats = {};
  }
  if(!account.stats.goals) {
    account.stats.goals = {};
  }
  if(account.stats.goals.created) {
    account.stats.goals.created += 1;
  } else {
    account.stats.goals.created = 1;
  }
  account.stats.goals.lastCreated = Date.now();
}

function getQuerySummaryEmail() {
  return {
    active: true,
    email: { $not: /DELETED/ },
    createdAt: { $lt: Date.now() - dateUtils.MILLISECONDS_IN_DAY * 7 },
    $or: [
      // Monday to Monday
      { 'stats.sentEmails.summary': { $lt: Date.now() - dateUtils.MILLISECONDS_IN_DAY * 6 } },
      { 'stats.sentEmails.summary': null },
    ],
    'configuration.emailOpt.summary': true,
    kind: { $ne: 'SubAccount' },
  };
}

async function sendSummaryEmail(account, { bcc = false, ignoreLastSent = false } = {}) {
  if(account.createdAt.getTime() + dateUtils.MILLISECONDS_IN_DAY * 7 > Date.now()) {
    throw new Error('account was created less than 7 days ago');
  }
  if(!ignoreLastSent) {
    const lastSent = _.get(account, 'stats.sentEmails.summary');
    if(lastSent && lastSent.getTime() + dateUtils.MILLISECONDS_IN_DAY * 8 > Date.now()) {
      throw new Error('last email sent less than 7 days ago');
    }
    if(!_.get(account, 'configuration.emailOpt.summary', false)) {
      throw new Error('opted out from summary email');
    }
  }

  const startDate = dateUtils.normalize(Date.now() - dateUtils.MILLISECONDS_IN_DAY * 7);
  const endDate = new Date();
  const opts = { startDate, endDate };
  const [
    analytics,
    goalAnalytics,
    eventCounts,
    reviewsCounts,
    activeNotifications,
    badNotifications,
    hasFreshLastHit,
    limitInfo,
  ] = await Promise.all([
    analyticsService.getAnalytics(account.id, opts).catch(emptyFn),
    analyticsService.getAccountGoalAnalytics(account.id, opts).catch(emptyFn),
    analyticsService.getEventCount(account.id, opts).catch(emptyFn),
    analyticsService.getReviewsCount(account.id, opts).catch(emptyFn),
    Notification.count({ accountId: account.id }).catch(emptyFn),
    notificationService.getNotShownNotifications(account.id, { projection: 'name' }).catch(emptyFn),
    suggestionsService.getLastHit(account.id, 7).catch(emptyFn),
    getLimitInfo(account.id).catch(emptyFn),
  ]);
  const { visitors = 0, engagedVisitors = 0, clicks = 0 } = analytics || {};
  const engagementRatio = visitors ? engagedVisitors / visitors : 0;
  const {
    clicks: goalClicks = 0, conversions = 0, engagementConversions = 0,
  } = goalAnalytics || {};
  const conversionRatio = conversions ? engagementConversions / conversions : 0;
  const events = [];
  const streamEntries = Object.entries(eventCounts);
  for(let i = 0; i < streamEntries.length; i += 1) {
    const [k, v] = streamEntries[i];
    if(v > 0) {
      events.push({
        name: stringUtils.capitalizeFirstLetter(k),
        value: stringUtils.humanReadableNumber(v),
      });
    }
  }
  const reviews = [];
  const reviewEntries = Object.entries(reviewsCounts);
  for(let i = 0; i < reviewEntries.length; i += 1) {
    const [k, v] = reviewEntries[i];
    if(v > 0) {
      reviews.push({
        name: stringUtils.capitalizeFirstLetter(k),
        value: stringUtils.humanReadableNumber(v),
      });
    }
  }
  const installed = !!(account.installed
    || _.get(account, 'onboarding.installed', null)
    || (account.hosts && account.hosts.length));
  const removedCode = !!(installed && !hasFreshLastHit);

  let limit = null;
  if(limitInfo && limitInfo.visitorCount >= limitInfo.limit) {
    limit = {
      visitors: stringUtils.humanReadableNumber(limitInfo.limit),
      untilDate: limitInfo.expires,
    };
  }

  const sendgridRes = await mailerService.sendTemplateEmail({
    to: account.email,
    templateId: 'd-33be386bb80249c7a7214242ef222414',
    from: FROM_HELP,
    bcc,
    data: {
      startDate: (new Date(startDate)).toISOString(),
      endDate: (new Date(endDate)).toISOString(),
      dateFormat: 'MMM DD',
      email: account.email,
      limit,
      installed,
      removedCode,
      activeNotifications,
      badNotifications: badNotifications.map(n => n.name),
      // used for double separator in the top
      hasProblems: !installed
        || !activeNotifications
        || badNotifications.length
        || removedCode
        || limit,
      visitors: stringUtils.humanReadableNumber(visitors),
      engagedVisitors: stringUtils.humanReadableNumber(engagedVisitors),
      engagementRatio: stringUtils.percentage(engagementRatio),
      clicks: stringUtils.humanReadableNumber(clicks),
      conversions: stringUtils.humanReadableNumber(conversions),
      engagementConversions: stringUtils.humanReadableNumber(engagementConversions),
      conversionRatio: stringUtils.percentage(conversionRatio),
      goalClicks: stringUtils.humanReadableNumber(goalClicks),
      events,
      reviews,
    },
    category: 'summary',
  });

  _.set(account, 'stats.sentEmails.summary', Date.now());
  await account.save();

  return sendgridRes;
}

async function sendFacebookReviewsFailedEmail(accountId, { notification }) {
  let account = null;
  try {
    account = await Account.findOne({ _id: accountId });
    const timeBetweenEmails = _.get(account, 'settings.timeBetweenEmails.facebookReviewErrors', 3);
    const lastSent = _.get(account, 'stats.sentEmails.facebookReviewError');
    if(lastSent
      && lastSent.getTime() + dateUtils.MILLISECONDS_IN_DAY * timeBetweenEmails > Date.now()) {
      return null;
    }
    if(!_.get(account, 'configuration.emailOpt.errors', false)) {
      return null;
    }
    const res = await mailerService.sendTemplateEmail({
      to: account.email,
      templateId: 'd-524366640cd74529953d378ff2dff172',
      from: FROM_HELP,
      data: {
        email: account.email,
        name: account.name,
        notification: notification && notification.name,
        page: notification && notification.pageName,
      },
      category: 'fb-review-error',
    });
    _.set(account, 'stats.sentEmails.facebookReviewError', new Date());
    await account.save();
    return res;
  } catch(err) {
    notifierService.notifyError(err, 'failed to send facebook review failure email', {
      accountId, account,
    });
    return err;
  }
}

async function send90Limit(accountId, { limit, cycleEndDate }) {
  let account = null;
  try {
    account = await Account.findOne({ _id: accountId });
    await mailerService.sendTemplateEmail({
      to: account.getEmails(),
      templateId: 'd-d937050a17da4e69a1ef5b93b71604bb',
      category: 'plan-limit-90',
      from: FROM_HELP,
      data: {
        email: account.email,
        name: account.name,
        limit: stringUtils.humanReadableNumber(limit),
        cycleEndDate,
      },
    });
  } catch(err) {
    notifierService.notifyError(err, 'failed to send 90% limit email', { account, accountId });
  }
}

async function sendPlanLimit(accountId, { limit }) {
  let account = null;
  try {
    const [acc, bill] = await Promise.all([
      Account.findOne({ _id: accountId }),
      Bill.findOne({ accountId }).sort({ _id: -1 }).skip(1).limit(1),
    ]);
    account = acc;
    let missedVisitors = null;
    if(bill) {
      missedVisitors = bill.total - (bill.limit || limit);
      if(!Number.isNaN(missedVisitors) && missedVisitors > 0) {
        missedVisitors = numeral(missedVisitors).format('0,0');
      } else {
        missedVisitors = null;
      }
    }
    return await mailerService.sendTemplateEmail({
      to: account.getEmails(),
      templateId: 'd-2b1d2aac1ff0495cb6c18ca9b7fa2ea9',
      category: 'plan-limit',
      from: FROM_HELP,
      data: {
        email: account.email,
        name: account.name,
        limit: stringUtils.humanReadableNumber(limit),
        untilDate: account.getBillingCycleEndDate(),
        subscription: account.isSubscriptionActive(),
        dateFormat: 'MMM DD',
        ...(missedVisitors && { missedVisitors }),
      },
    });
  } catch(err) {
    notifierService.notifyError(err, 'failed to send plan limit email', { account, accountId, limit });
    return err;
  }
}

async function sendReviewRequest({ account, platform }) {
  let reviewLink = null;
  if(platform === 'wix') {
    reviewLink = 'https://www.wix.com/app-market/add-review/0359ef26-3f2d-4c81-a7cf-6f771c913dce';
  } else if(platform === 'shopify') {
    reviewLink = 'https://apps.shopify.com/provesource#modal-show=WriteReviewModal';
  } else {
    throw new Error(`unsupported review request platform ${platform}`);
  }
  return mailerService.sendReviewRequest({
    to: account.email,
    platform: stringUtils.capitalizeFirstLetter(platform),
    reviewLink,
  }).then(() => Account.updateOne({ _id: account.id }, { 'stats.sentEmails.reviewRequest': Date.now() }));
}
