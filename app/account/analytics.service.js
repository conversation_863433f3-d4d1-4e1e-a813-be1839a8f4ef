const { ObjectId } = require('mongoose').Types;
const AnalyticsEvent = require('../notifications/models/AnalyticsEvent');
const GoalAnalytics = require('../goals/GoalAnalytics');
const { PromiseAllObject, emptyFn } = require('../../lib/utils');

const {
  GoogleReview,
  FacebookReview,
  CapterraReview,
  ReviewsioReview,
  StampedReview,
  TrustpilotReview,
  YotpoReview,
} = require('../reviews/models');

const {
  CleanFormStreamEvent,
  Magento1Event,
  Magento2Event,
  ShopifyEvent,
  WebhookStreamEvent,
  WooEvent,
  WixEvent,
  BigCommerceEvent,
} = require('../events/models');

module.exports = {
  getAnalytics,
  getAccountGoalAnalytics,
  getGoalAnalytics,
  getNotificationGoalAnalytics,
  getMultiNotificationGoalAnalytics,
  getEventCount,
  getReviewsCount,
};

function getAnalytics(accountId, { startDate, endDate }) {
  return AnalyticsEvent.aggregate([
    {
      $match: {
        accountId: ObjectId(accountId),
        date: { $gte: new Date(startDate), $lte: new Date(endDate) },
        notificationId: null,
      },
    },
    {
      $group: {
        _id: null,
        visitors: { $sum: '$total.visitors' },
        engagedVisitors: { $sum: '$total.engagedVisitors' },
        clicks: { $sum: '$total.clicks' },
      },
    },
  ]).exec().then(res => res[0]);
}

function getAccountGoalAnalytics(accountId, { startDate, endDate }) {
  return GoalAnalytics.aggregate([
    { $match: { accountId: ObjectId(accountId), date: { $gte: startDate, $lte: endDate } } },
    {
      $group: {
        _id: null,
        clicks: { $sum: '$clicks' },
        conversions: { $sum: '$conversions' },
        engagementConversions: { $sum: '$engagementConversions' },
      },
    },
  ]).exec().then(res => res[0]);
}

async function getGoalAnalytics({ accountId, startDate, endDate }) {
  return GoalAnalytics.aggregate([{
    $match: {
      accountId: ObjectId(accountId),
      date: { $gte: startDate, $lte: endDate },
    },
  }, {
    $group: {
      _id: '$goalId',
      conversions: { $sum: '$conversions' },
      clickConversions: { $sum: '$clickConversions' },
      hoverConversions: { $sum: '$hoverConversions' },
      views: { $sum: '$views' },
      hovers: { $sum: '$hovers' },
      clicks: { $sum: '$clicks' },
    },
  }, {
    $addFields: {
      engagementConversions: { $add: ['$hoverConversions', '$clickConversions'] },
    },
  }]).exec();
}

async function getNotificationGoalAnalytics({
  accountId, notificationId, startDate, endDate,
}) {
  return GoalAnalytics.aggregate([{
    $match: {
      accountId: ObjectId(accountId),
      date: { $gte: startDate, $lte: endDate },
      'notifications._id': ObjectId(notificationId),
    },
  }, {
    $unwind: '$notifications',
  }, {
    $match: { 'notifications._id': ObjectId(notificationId) },
  }, {
    $group: {
      _id: '$goalId',
      conversions: { $sum: '$notifications.conversions' },
      views: { $sum: '$notifications.views' },
      hovers: { $sum: '$notifications.hovers' },
      clicks: { $sum: '$notifications.clicks' },
    },
  }]).exec();
}

async function getMultiNotificationGoalAnalytics({
  accountId, notificationIds, startDate, endDate,
}) {
  return GoalAnalytics.aggregate([{
    $match: {
      accountId: ObjectId(accountId),
      date: { $gte: startDate, $lte: endDate },
      'notifications._id': {
        $in: notificationIds.map(id => ObjectId(id)),
      },
    },
  }, {
    $unwind: '$notifications',
  }, {
    $lookup: {
      from: 'notifications',
      localField: 'notifications._id',
      foreignField: '_id',
      as: 'notification',
    },
  }, {
    $unwind: '$notification',
  }, {
    $group: {
      _id: {
        goalId: '$goalId',
        notificationId: '$notifications._id',
      },
      conversions: { $sum: '$notifications.conversions' },
      views: { $sum: '$notifications.views' },
      hovers: { $sum: '$notifications.hovers' },
      clicks: { $sum: '$notifications.clicks' },
      engagements: { $sum: { $add: ['$notifications.hovers', '$notifications.clicks'] } },
      type: { $first: '$notification.type' },
      name: { $first: '$notification.name' },
      platform: { $first: '$notification.settings.platform' },
      source: { $first: '$notification.source' },
    },
  }, {
    $project: {
      _id: 0,
      goalId: '$_id.goalId',
      notificationId: '$_id.notificationId',
      conversions: 1,
      views: 1,
      hovers: 1,
      clicks: 1,
      engagements: 1,
      type: 1,
      name: 1,
      platform: 1,
      source: 1,
    },
  }, {
    $sort: {
      goalId: 1, notificationId: 1,
    },
  }]).exec();
}

async function getEventCount(accountId, { startDate, endDate }) {
  const query = { accountId };
  if(startDate) {
    query.date = query.date || {};
    query.date.$gte = startDate;
  }
  if(endDate) {
    query.date = query.date || {};
    query.date.$lte = endDate;
  }
  return PromiseAllObject({
    form: CleanFormStreamEvent.count(query).catch(emptyFn),
    magento1: Magento1Event.count(query).catch(emptyFn),
    magento2: Magento2Event.count(query).catch(emptyFn),
    shopify: ShopifyEvent.count(query).catch(emptyFn),
    webhook: WebhookStreamEvent.count(query).catch(emptyFn),
    woocommerce: WooEvent.count(query).catch(emptyFn),
    wix: WixEvent.count(query).catch(emptyFn),
    bigcommerce: BigCommerceEvent.count(query).catch(emptyFn),
  });
}

async function getReviewsCount(accountId, { startDate, endDate }) {
  const query = { accountId, createdAt: { $lte: endDate, $gte: startDate } };
  return PromiseAllObject({
    google: GoogleReview.count(query).catch(emptyFn),
    facebook: FacebookReview.count(query).catch(emptyFn),
    reviewsio: ReviewsioReview.count(query).catch(emptyFn),
    trustpilot: TrustpilotReview.count(query).catch(emptyFn),
    yotpo: YotpoReview.count(query).catch(emptyFn),
    stamped: StampedReview.count(query).catch(emptyFn),
    capterra: CapterraReview.count(query).catch(emptyFn),
  });
}
