const authTypes = require('../../../middleware/authTypes');
const config = require('../../../config');
const cryptoUtils = require('../../../lib/utils/cryptoUtils');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {},
  handle(req, res, next) {
    const { apiKey } = req.session;
    const { accountId } = req.session;
    const token = cryptoUtils.encrypt(accountId, config.cryptoKeys.mailto);
    const url = `${config.apiUrl}/account/install?apiKey=${apiKey}&token=${token}`;
    res.body = { link: url };
    next();
  },
};
