const _ = require('lodash');
const authTypes = require('../../../middleware/authTypes');

const Account = require('../../account/models/Account');
const Event = require('../../events/models/Event');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');
const logger = require('../../../lib/logger')('isInstalled');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.console,
  },
  schema: {},
  handle(req, res, next) {
    const { accountId } = req.locals;
    Account.findOne({ _id: accountId }).then((account) => {
      if(!account) throw ErrorFactory.AccountNotFound(accountId);

      let installed = false;
      if(account.installed || _.get(account, 'onboarding.installed', null) || account.hosts.length) {
        installed = true;
      }
      res.body = { installed };
      next();
    }).catch(next);
  },
};
