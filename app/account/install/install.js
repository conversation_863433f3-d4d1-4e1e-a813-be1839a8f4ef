const urlModule = require('url');
const jwt = require('jsonwebtoken');
const path = require('path');

const { URLSearchParams } = urlModule;
const cryptoUtils = require('../../../lib/utils/cryptoUtils');
const authTypes = require('../../../middleware/authTypes');
const config = require('../../../config');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.noAuth,
  },
  schema: {},
  handle(req, res, next) {
    const url = urlModule.parse(req.url);
    const params = new URLSearchParams(url.query);
    const apiKey = params.get('apiKey');

    new Promise((resolve, reject) => {
      try {
        const decoded = jwt.verify(apiKey, config.jwt.api);
        if(decoded && decoded.accountId) resolve(decoded.accountId);
        else reject(ErrorFactory.ProcessingError(400, { reason: 'bad token' }));
      } catch(err) {
        reject(ErrorFactory.ProcessingError(400, { reason: 'bad token' }));
      }
    }).then((accountId) => {
      const token = params.get('token');
      const decrypted = cryptoUtils.decrypt(token, config.cryptoKeys.mailto);
      if(decrypted !== accountId) throw ErrorFactory.ProcessingError(400, { reason: 'failed to decrypt token or not same accountId' });
    }).then(() => {
      const file = path.resolve(__dirname, 'install');
      return res.render(file, { apiKey });
    }).catch(next);
  },
};
