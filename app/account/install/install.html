<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>ProveSource Snippet Setup - Developers</title>
	<link rel="icon" type="image/png" href="https://i.imgur.com/JTc1T86.png">

	<style>
		@import url('https://fonts.googleapis.com/css?family=Lato:200,400,700,900');

		html, body {
			font-family: 'Lato', sans-serif;
			background-color: #fff;
		}

		body {
			overflow-y: auto;
			overflow-x: hidden;
			text-align: center;
		}

		pre, code {
			font-family: monospace, monospace;
			text-align: left;
		}

		b {
			font-weight: 700;
		}

		.navbar-brand {
			font-family: 'Lato', sans-serif;
			font-size: 1.3rem;
			margin-top: 15px;
			height: 56px;
			line-height: 43px;
			color: #7a25f3;
			margin-bottom: 0px;
			position: relative;
			display: inline-block;
			text-align: center;
		}

		.navbar-brand img {
			width: 150px;
			position: relative;
		}

		pre:hover {
			opacity: 0.8;
			border: 2px dashed #7826f3;
		}

		pre {
			overflow: auto;
			font-size: 11px;
			margin: 1.5rem 0 0;
			border-radius: .3125rem;
			border: 2px dashed #dfdfdf;
			color: #7a7a7a;
			resize: none;
			background: #fbfbfb;
			outline: 0;
			cursor: pointer;
			position: relative;
			padding: .75rem;
			max-height: 14rem;
			word-wrap: break-word;
			white-space: normal;

		}

		pre > code {
			display: block;
			padding: 1rem;
			word-wrap: normal;
		}

		.ps-container {
			padding: 25px 0px;
		}

		.ps-body-container.sm {
			min-width: 650px !important;
			max-width: 650px !important;
			margin: 0 auto;
		}

		.ps-box .header p {
			margin: 0;
			font-size: 1rem;
			color: rgb(136, 136, 136);
		}

		.ps-box .header h1 {
			font-weight: 800;
			font-size: 1.7em;
			margin: 0 0 5px;
			line-height: 1.1em;
			letter-spacing: -0.02em;
			margin-bottom: 13px;
		}

		.ps-box .header {
			padding: 30px 110px;
			position: relative;
			text-align: center;
		}

		.ps-box .section-collapse {
			color: #575757;
			font-size: .875rem;
		}

		code {
			font-size: 87.5%;
			color: #e83e8c;
			word-break: break-word;
		}

		.ps-box {
			margin: 0 auto 20px;
			background-color: white;
			box-shadow: 0 5px 10px rgba(21, 57, 130, 0.07), 0 0 0 1px rgba(0, 0, 0, 0.05);
			overflow-x: hidden;
			position: relative;
			z-index: 0;
			border-radius: 5px;
			min-width: 650px !important;
			max-width: 650px !important;
		}

		.ps-box .section.is-open {
			background: #fff;
			box-shadow: 0 0px 15px 1px #f4f4f4;
		}

		.ps-box .section.hover:hover {
			background: #ffffff;
		}

		.ps-box .section {
			padding: 30px 35px;
			border-top: 1px solid #EEE;
			position: relative;
			text-align: left;
		}

		.wizard-btn.sm {
			padding: 5px 25px !important;
			width: auto !important;
			font-size: 16px;
		}

		.wizard-btn {
			font-family: 'Lato', sans-serif;
			outline: none !important;
			padding: 15px 30px;
			text-align: center;
			font-size: 20px;
			background-color: #7826f3;
			color: white;
			font-weight: bold;
			border-radius: 5px;
			border: 4px solid #7726ef;
			cursor: pointer;
			margin-top: 15px;
			letter-spacing: .3px;
		}

		.wizard-btn.copied-code {
			background-color: #00c069;
			border-color: #00c069;
		}
	</style>
</head>
<body>

<script>
	function copyToClipboard() {
		var text = document.getElementById("provesrc-code").innerText;
		if (window.clipboardData && window.clipboardData.setData) {
			return clipboardData.setData("Text", text);

		} else if (document.queryCommandSupported && document.queryCommandSupported("copy")) {
			var textarea = document.createElement("textarea");
			textarea.textContent = text;
			textarea.style.position = "fixed";
			document.body.appendChild(textarea);
			textarea.select();
			try {
				document.getElementsByClassName("wizard-btn")[0].classList.add("copied-code");
				document.getElementsByClassName("wizard-btn")[0].innerText = "COPIED!";
				return document.execCommand("copy");
			} catch (ex) {
				console.warn("Copy to clipboard failed.", ex);
				return false;
			} finally {
				document.body.removeChild(textarea);
			}
		}
	}
</script>

<div class="navbar-brand"><img src="https://i.imgur.com/wnvEY3Y.png"></div>

<div class="ps-container ng-scope">
	<div class="ps-body-container sm ">
		<div class="ps-box">
			<div class="header no-text-select">
				<h1>Let's Do This</h1>
				<p>Dear developer, thanks for stopping by.<br>Here is how to get ProveSource on your website in minutes!
				</p>

			</div>
			<div class="section row hover is-open section-collapse">

				<div>
					Copy and paste this JS code snippet in the <code>&lt;/head&gt;</code> of each page that you want to
					track or display
					notifications on, we'll do the rest.
				</div>
				<pre id="provesrc-code" onclick="copyToClipboard()">
				</pre>
				<button class="wizard-btn sm" onclick="copyToClipboard()">COPY CODE</button>
			</div>
		</div>
	</div>
</div>
<script>
(function(){
  var snippet = '\x3C!-- Start of Async ProveSource Code --\x3E\x3Cscript\x3E!function(o,i){window.provesrc\&\&window.console\&\&console.error\&\&console.error(\"ProveSource is included twice in this page.\"),provesrc=window.provesrc={dq:[],display:function(){this.dq.push(arguments)}},o._provesrcAsyncInit=function(){provesrc.init({apiKey:\"{{apiKey}}\",v:\"0.0.4\"})};var r=i.createElement(\"script\");r.type=\"text\x2Fjavascript\",r.async=!0,r[\"ch\"+\"ar\"+\"set\"]=\"UTF-8\",r.src=\"https:\x2F\x2Fcdn.provesrc.com\x2Fprovesrc.js\";var e=i.getElementsByTagName(\"script\")[0];e.parentNode.insertBefore(r,e)}(window,document);\x3C\x2Fscript\x3E\x3C!-- End of Async ProveSource Code --\x3E';
  document.querySelector("#provesrc-code").innerText = snippet;
})()
</script>
</body>
</html>
