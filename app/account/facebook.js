const authTypes = require('../../middleware/authTypes');
const config = require('../../config');
const lib = require('../../lib/apis/facebook');
const logger = require('../../lib/logger/LoggerFactory')('facebook.api');
const slack = require('../../lib/apis/slackNotifier');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const Account = require('./models/Account');

module.exports = {
  config: {
    methods: ['POST'],
    noAuth: false,
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    properties: {
      pageId: { type: 'string', minLength: 1, required: true },
      pageName: { type: 'string', minLength: 1, required: true },
      accessToken: { type: 'string', minLength: 1, required: true },
    },
  },
  async handle(req, res, next) {
    const { accountId } = req.locals;
    try {
      const userToken = await lib.getUserAccessToken({
        grant_type: 'fb_exchange_token',
        client_id: config.facebook.clientId,
        client_secret: config.facebook.clientSecret,
        fb_exchange_token: req.body.accessToken,
      });
      if(!userToken || !userToken.access_token) {
        next(ErrorFactory('Unable to retrieve long lived user token'));
      }

      const pageToken = await lib.getPageAccessToken(req.body.pageId, {
        access_token: userToken.access_token,
        fields: 'access_token',
      });
      if(!pageToken || !pageToken.access_token) {
        next(ErrorFactory('Unable to retrieve long lived page token'));
      }

      const account = await Account.findOne({ _id: accountId });
      if(!account) {
        return next(ErrorFactory.AccountNotFound(accountId));
      }

      const fbConfig = account.facebook.find(i => i.pageId === req.body.pageId);
      if(fbConfig) {
        fbConfig.pageName = req.body.pageName;
        fbConfig.userToken = userToken.access_token;
        fbConfig.pageToken = pageToken.access_token;
      } else {
        account.facebook.push({
          pageId: req.body.pageId,
          pageName: req.body.pageName,
          userToken: userToken.access_token,
          pageToken: pageToken.access_token,
        });
      }
      account.save();
      res.body = { token: userToken.access_token };
      return next();
    } catch(err) {
      const msg = `facebook token error: ${err.message}`;
      logger.error({ err }, msg);
      slack.notifyError(err, msg, { accountId });
      return next(ErrorFactory(msg));
    }
  },
};
