const _ = require('lodash');
const parseDomain = require('parse-domain');
const authTypes = require('../../middleware/authTypes');
const ErrorFactory = require('../../lib/errors/ErrorFactory');

const Account = require('./models/Account');
const NotifConsts = require('../notifications/constants');
const Stream = require('../notifications/models/Stream');
const PageVisits = require('../notifications/models/PageVisits');
const triggers = require('../../lib/triggers');
const webhookGenerate = require('../webhooks/generate');

module.exports = {
  config: {
    methods: ['GET', 'POST', 'PUT'],
    authType: authTypes.console,
  },
  schema: {
    POST: {
      type: 'object',
      properties: {
        website: { type: 'string', minLength: 1, required: true },
        refer: { type: 'string', minLength: 1, required: true },
        message: { type: 'string', minLength: 1, required: true },
        localization: { type: 'string', minLength: 2, maxLength: 2 },
        image: { type: 'string' },
      },
    },
  },
  async handleGET(req, res, next) {
    try {
      const checklist = {};
      const { accountId } = req.locals;
      Account.updateOne({ _id: accountId }, { $addToSet: { ips: req.remoteAddress } });
      const account = await Account.findOne({ _id: accountId }, 'onboarding installed stats deleted');
      if(!account) {
        throw ErrorFactory.AccountNotFound();
      }
      if(account.deleted) {
        req.session = null;
        throw ErrorFactory('Account was deleted', 401);
      }

      const installed = _.get(account, 'onboarding.installed', null);
      const created = _.get(account, 'onboarding.created', null);

      let currentStep = 1;
      if(installed || account.installed) {
        currentStep += 1;
        if(!installed) account.onboarding.installed = Date.now();

        if(created) currentStep += 1;

        if(account.isModified()) {
          account.save();
        }

        checklist.createdGoal = _.get(account, 'stats.goals.created', null);
        checklist.createdNotification = _.get(account, 'stats.createdNotifications', null);
        checklist.visitedHelpCenter = _.get(account, 'stats.visitedHelpCenter', null);
        checklist.visitedWhatsNew = _.get(account, 'stats.visitedWhatsNew', null);
        checklist.visitedAffiliateProgram = _.get(account, 'stats.visitedAffiliateProgram', null);
        checklist.visitedVideoGuides = _.get(account, 'stats.visitedVideoGuides', null);
      }

      res.body = { step: currentStep, checklist };
      return next();
    } catch(err) {
      return next(err);
    }
  },

  async handlePUT(req, res, next) {
    try {
      const { accountId } = req.session;
      const { step } = req.body;
      const account = await Account.findOne({ _id: accountId });
      if(!account) {
        throw ErrorFactory.AccountNotFound(accountId);
      }

      _.set(account, `stats.${step}`, true);

      await account.save();
      next();
    } catch(err) {
      next(err);
    }
  },

  async handlePOST(req, res, next) {
    try {
      const parsed = parseDomain(req.body.website);
      if(!parsed || !parsed.tld || !parsed.domain) throw ErrorFactory('website address is invalid');

      const { accountId } = req.locals;
      const account = await Account.findOne({ _id: accountId });
      if(!account) {
        throw ErrorFactory.AccountNotFound(accountId);
      }

      const { refer } = req.body;
      const { message } = req.body;
      const domain = `${parsed.domain}.${parsed.tld}`;
      const template = {
        urlTypes: {
          track: NotifConsts.URL_TYPES.contains,
          display: NotifConsts.URL_TYPES.all,
          trackAbs: false,
          displayAbs: false,
        },
        localization: req.body.localization || 'en',
        autoTrack: true,
        trackURL: [domain],
        settings,
      };

      if(!account.onboarding.created) {
        const stream = new Stream(Object.assign({}, template, {
          type: NotifConsts.notificationTypes.stream,
          name: 'Recent Conversions (All Website)',
          webhookId: webhookGenerate.generate().webhookId,
          accountId,
          message,
          refer,
        }));

        const pageVisits = new PageVisits(Object.assign({}, template, {
          type: NotifConsts.notificationTypes.pageVisits,
          name: 'Website Page Views (All Website)',
          message: 'visited our website',
          image: req.body.image,
          accountId,
          refer,
        }));

        await Promise.all([
          stream.save(),
          pageVisits.save(),

          (Account.updateOne({ _id: accountId }, {
            $inc: { 'stats.createdNotifications': 2 },
            $set: { 'onboarding.created': Date.now() },
          }).exec()),
        ]);

        triggers.onboardingComplete(account, domain, req.body);
      }

      res.body = { message: 'success' };

      next();
    } catch(err) {
      next(err);
    }
  },
};
