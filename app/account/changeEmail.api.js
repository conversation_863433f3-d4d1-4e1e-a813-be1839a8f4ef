const authTypes = require('../../middleware/authTypes');
const changeEmail = require('./changeEmail');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const logger = require('../../lib/logger/LoggerFactory')('changeEmail.api');
const config = require('../../config');
const slack = require('../../lib/apis/slackNotifier');

module.exports = {
  config: {
    methods: ['PUT', 'GET'],
    authType: {
      PUT: authTypes.console,
      GET: authTypes.noAuth,
    },
  },
  schema: {
    POST: {
      type: 'object',
      properties: {
        email: { type: 'string', required: true },
      },
    },
    GET: {
      type: 'object',
      properties: {
        token: { type: 'string', required: true },
      },
    },
  },
  async handlePUT(req, res, next) {
    const oldEmail = req.session.email;
    if(req.body.email === oldEmail) {
      return next(ErrorFactory('Email must be different from your current account email', 400));
    }
    try {
      await changeEmail.sendConfirmationEmail(oldEmail, req.body.email);
      res.body = { message: 'success' };
      return next();
    } catch(err) {
      const msg = 'failed sending email change confirmation';
      logger.error({ err }, msg);
      slack.notifyError(err, msg, { data: { email: oldEmail } });
      return next(ErrorFactory(`failed sending confirmation: ${err.message}`, 400, { err }));
    }
  },

  async handleGET(req, res, next) {
    const { token } = req.query;
    try {
      const email = await changeEmail.changeEmail(token);

      if(email && req.session) {
        req.session.email = email;
        req.session.save();
      }
      return res.redirect(config.consoleUrl);
    } catch(err) {
      const msg = 'failed changing email';
      logger.error({ err }, msg);
      slack.notifyError(err, msg, { data: { token } });
      return next(ErrorFactory(`failed changing email: ${err.message}`, 400, { err }));
    }
  },
};
