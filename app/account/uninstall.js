const jwt = require('jsonwebtoken');
const authTypes = require('../../middleware/authTypes');
const slack = require('../../lib/apis/slackNotifier');
const config = require('../../config');
const Account = require('./models/Account');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {
      clientId: { type: 'string', required: true },
      host: { type: 'string', required: true },
      message: { type: 'string', required: true },
    },
  },
  async handle(req, res, next) {
    const { clientId, host } = req.body;
    let accountId = clientId;
    try {
      ({ accountId } = jwt.verify(clientId, config.jwt.api).accountId);
    } catch(err) {
      // Do nothing
    }
    const account = await Account.findOne({ _id: accountId }).select('email');
    slack.notify(
      `${(account && account.email) || 'unknown'} uninstalled from ${host}`,
      req.body,
      { webhook: config.slack.uninstall },
    );
    next();
  },
};
