const Account = require('./models/Account');

const updateIntegrations = async function (accountId, data) {
  const account = await Account.findOne({ _id: accountId });
  account.integrations = Object.assign(account.integrations, data);
  await account.save();

  return account.integrations;
};

const getIntegrations = async function (accountId) {
  const account = await Account.findOne({ _id: accountId });
  if(!account) {
    return null;
  }
  const integrations = (account.integrations && account.integrations.toObject()) || {};
  integrations.sites = account.getSiteIntegrations();
  return integrations;
};

module.exports = {
  updateIntegrations,
  getIntegrations,
};
