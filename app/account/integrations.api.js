const authTypes = require('../../middleware/authTypes');
const integrations = require('./integrations');
const slack = require('../../lib/apis/slackNotifier');
const logger = require('../../lib/logger/LoggerFactory')('integrations.api');

module.exports = {
  config: {
    methods: ['PUT', 'GET'],
    authType: authTypes.console,
  },
  schema: {
    PUT: {
      type: 'object',
      properties: {
      },
    },
  },
  async handlePUT(req, res, next) {
    const data = req.body;
    const { accountId } = req.locals;
    try {
      res.body = await integrations.updateIntegrations(accountId, data);
      next();
    } catch(err) {
      slack.notifyError(err, 'failed to update integrations', { data: { accountId, data } });
      logger.error({ err }, 'failed to update integrations');
      next(err);
    }
  },

  async handleGET(req, res, next) {
    const { accountId } = req.locals;
    try {
      res.body = await integrations.getIntegrations(accountId);
      next();
    } catch(err) {
      logger.error({ err }, 'failed to GET integrations');
      slack.notifyError(err, 'failed to GET integrations', { data: { accountId } });
      next(err);
    }
  },
};
