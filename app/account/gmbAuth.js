const OAuth2 = require('google-auth-library').OAuth2Client;
const config = require('../../config');

const { gmbCallback, clientId, secret } = config.googleAuth;
const auth = new OAuth2(clientId, secret, gmbCallback);

module.exports = {
  config: {
    methods: ['GET'],
    noAuth: true,
  },
  schema: {
    type: 'object',
    properties: {
      redirect: { enum: ['true', 'false'] },
    },
  },
  handle(req, res, next) {
    const authUrl = auth.generateAuthUrl({
      access_type: 'offline',
      scope: ['profile', 'email', 'https://www.googleapis.com/auth/plus.business.manage'],
    });
    res.redirect(302, authUrl);
  },
};
