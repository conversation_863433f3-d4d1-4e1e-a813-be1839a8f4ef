const OAuth2 = require('google-auth-library').OAuth2Client;
const authTypes = require('../../middleware/authTypes');
const config = require('../../config');

const { gmbCallback, clientId, clientSecret } = config.googleAuth;
const auth = new OAuth2(clientId, clientSecret, gmbCallback);

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    properties: {},
  },
  async handle(req, res, next) {
    const { code } = req.query;
    const { tokens } = await auth.getToken(code);
    console.log('tokens', tokens);
    const result = await auth.verifyIdToken({ idToken: tokens.id_token });
    const payload = result.getPayload();
    console.log('payload', payload);
    // res.redirect(config.consoleUrl);
    next();
  },
};
