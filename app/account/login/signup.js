const querystring = require('querystring');
const MailChecker = require('mailchecker');
const Account = require('../models/Account');
const logger = require('../../../lib/logger')('signup');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');
const config = require('../../../config');
const triggers = require('../../../lib/triggers');
const mailer = require('../mailer');
const activate = require('./activate');
const slack = require('../../../lib/apis/slackNotifier');
const wixService = require('../../wix/wix.service');
const thinkificService = require('../../thinkific/thinkific.service');
const notifConsts = require('../../notifications/constants');
const accountService = require('../account.service');

module.exports = {
  config: {
    methods: ['POST'],
    noAuth: true,
  },
  schema: {
    type: 'object',
    required: ['email', 'password'],
    properties: {
      email: { type: 'string', minLength: 1 },
      password: { type: 'string', minLength: 1, maxLength: 50 },
    },
  },
  async handle(req, res, next) {
    const { email, password } = req.body;

    logger.info({ email }, 'searching account');

    if(!MailChecker.isValid(email)) {
      return next(ErrorFactory('The provided email address is invalid', 400, { email }));
    }

    try {
      let account = await Account.findOne({ email });
      if(account) {
        return next(ErrorFactory('Email already exists', 400, { email }));
      }

      account = await accountService.makeAccount({
        email, password, ip: req.remoteAddress, cookies: req.cookies,
      });

      if(req.cookies.ps_wix) {
        account.source = notifConsts.PLATFORMS.wix;
        const instanceId = req.cookies.ps_wix;
        await wixService.integrateNewAccount(account, instanceId).finally(() => {
          res.cookie('ps_wix', '', config.getCookieConfig(null, 0));
        });
      }
      if(req.cookies.ps_thinkific) {
        const gid = req.cookies.ps_thinkific;
        await thinkificService.integrateAccount(account, gid).finally(() => {
          res.cookie('ps_thinkific', '', config.getCookieConfig(null, 0));
        });
      }

      logger.info({ accountId: account.id, email: account.email }, 'creating account');
      await account.save();

      triggers.presignup(account);
      await mailer
        .sendActivation(config.sendgrid.apiKey, account.email, activate.getActivationLink(account))
        .catch((err) => {
          slack.notifyError(err, ' failed to send activation', { data: { email: account.email } });
        });

      res.body = { accountId: account.id };
      next();
    } catch(err) {
      slack.notifyError(err, 'failed to signup', { data: { email } });
      next(ErrorFactory(`Failed to signup: ${err.message}`, 500, { err, email }));
    }
  },
};
