const { RateLimiterRedis } = require('rate-limiter-flexible');

const config = require('../../../config');
const authTypes = require('../../../middleware/authTypes');
const mailer = require('../mailer');
const logger = require('../../../lib/logger')('forgot');
const cryptoUtils = require('../../../lib/utils/cryptoUtils');

const Account = require('../models/Account');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');
const redisService = require('../../common/redis.service');

const rateLimiter = new RateLimiterRedis({
  storeClient: redisService.getClient(),
  blockDuration: 60 * 60,
  duration: 60 * 60,
  points: 100,
  keyPrefix: 'forgot',
});

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {
      email: { type: 'string', minLength: 1 },
    },
  },
  async handle(req, res, next) {
    const { email } = req.body;
    const ip = req.remoteAddress;
    await Promise.all([
      rateLimiter.consume(email, 20), // max 5 attempts per email
      rateLimiter.consume(ip, 10), // max 10 attempts per IP
    ]).catch((rateLimitRes) => {
      throw ErrorFactory('Too many attempts', 429, { email, ...rateLimitRes });
    });
    let err = null;
    Account.findOne({ email }).then((account) => {
      if(account) {
        const token = generateToken(account.id);
        if(token) {
          const link = `${config.consoleUrl}/#/new-password?token=${token}`;
          mailer.sendPasswordReset(config.sendgrid.apiKey, email, link);
        } else {
          err = ErrorFactory('failed to generate password reset token', 500, { email });
        }
      } else {
        err = ErrorFactory.AccountNotFound(email);
      }

      next(err);
    });
  },
  testExports: {
    generateToken,
  },
};

function generateToken(accountId, time) {
  const timestamp = time || Date.now();
  const data = JSON.stringify({ timestamp, accountId });
  return cryptoUtils.encrypt(data, config.cryptoKeys.passReset);
}
