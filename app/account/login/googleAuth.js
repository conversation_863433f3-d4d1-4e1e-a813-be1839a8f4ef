const config = require('../../../config');
const logger = require('../../../lib/logger')('google');
const OAuth2 = require('google-auth-library').OAuth2Client;

const auth = new OAuth2(config.googleAuth.clientId, config.googleAuth.clientSecret, config.googleAuth.callback);

module.exports = {
  config: {
    methods: ['GET'],
    noAuth: true,
  },
  schema: {
    type: 'object',
    properties: {
      redirect: { enum: ['true', 'false'] },
    },
  },
  handle(req, res, next) {
    const authUrl = auth.generateAuthUrl({
      access_type: 'online',
      scope: ['profile', 'email'],
    });

    logger.info({ authUrl }, 'generated google auth url');

    if(req.query.redirect === 'true') {
      res.redirect(302, authUrl);
    } else {
      res.body = { authUrl };
      next();
    }
  },
};
