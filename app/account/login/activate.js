const querystring = require('querystring');
const config = require('../../../config');
const cryptoUtils = require('../../../lib/utils/cryptoUtils');
const authTypes = require('../../../middleware/authTypes');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');
const dateUtils = require('../../../lib/utils/dateUtils');
const Account = require('../models/Account');
const triggers = require('../../../lib/triggers');
const trackerService = require('../../common/tracker.service');
const mailer = require('../mailer');
const notifierService = require('../../common/notifier.service');

const key = config.cryptoKeys.activate;

module.exports.getActivationLink = function (account) {
  const token = cryptoUtils.encrypt(generateTokenValue(account._id), key);
  return `${config.apiUrl}/account/activate?token=${token}`;
};

module.exports._getToken = function (account) {
  return cryptoUtils.encrypt(generateTokenValue(account._id), key);
};

module.exports.activateApi = {
  config: {
    methods: ['GET'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {
      token: { type: 'string', required: true },
    },
  },
  async handle(req, res, next) {
    try {
      const tokenValue = cryptoUtils.decrypt(req.query.token, key);
      const values = getTokenValues(tokenValue);
      if(!values) return next(ErrorFactory('this link is invalid or expired', 401));

      const { timestamp, accountId } = values;
      let account = await Account.findById(accountId);
      const diff = Date.now() - parseInt(timestamp);
      if(diff < 1 || diff > dateUtils.MILLISECONDS_IN_DAY) {
        const link = module.exports.getActivationLink(account);
        await mailer.sendActivation(config.sendgrid.apiKey, account.email, link).catch((err) => {
          notifierService.notifyError(err, ' failed to send activation', { accountId });
        });
        return next(ErrorFactory('this link has expired, check your email for a new link', 400));
      }

      account = await Account.findOneAndUpdate({ _id: accountId }, { $set: { active: true } });
      const message = 'Your account has been activated, please login';
      const query = querystring.stringify({ email: account.email, success: message });
      if(!account.active) {
        // already first party (on provesrc domain)
        res.cookie('ps_signup', 'true', config.cookies.ps_signup);
        const ip = req.remoteAddress;
        triggers.signup(account, { ip });
        trackerService.signup({
          id: account.id, email: account.email, ua: req.headers['user-agent'], ip,
        });
      }
      return res.redirect(`${config.consoleUrl}/#/login?${query}`);
    } catch(err) {
      next(ErrorFactory('failed to activate account'));
    }
  },
};

function generateTokenValue(accountId) {
  return `${accountId}|${Date.now()}`;
}

function getTokenValues(tokenValue) {
  if(!tokenValue) return null;

  const comps = tokenValue.split('|');
  if(comps.length < 2 || isNaN(comps[1])) return null;
  return { accountId: comps[0], timestamp: parseInt(comps[1]) };
}
