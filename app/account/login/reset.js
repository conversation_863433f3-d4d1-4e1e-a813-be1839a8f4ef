const authTypes = require('../../../middleware/authTypes');
const config = require('../../../config');
const { cryptoUtils, dateUtils } = require('../../../lib/utils');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');
const Account = require('../models/Account');
const logger = require('../../../lib/logger')('reset');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {
      token: { type: 'string', minLength: 1 },
      password: { type: 'string', minLength: 1 },
    },
  },
  async handle(req, res, next) {
    const { token } = req.body;
    const decrypted = cryptoUtils.decrypt(token, config.cryptoKeys.passReset);
    if(!decrypted) return next(ErrorFactory('bad token'));

    const json = JSON.parse(decrypted);
    const now = Date.now();
    const diff = now - json.timestamp;
    if(diff > dateUtils.MILLISECONDS_IN_HOUR) {
      return next(ErrorFactory('token expired'));
    }

    const { accountId } = json;
    const hashed = await Account.hashPassword(req.body.password);
    Account.updateOne({ _id: accountId }, { $set: { password: hashed, hashedPassword: true } }).then((result) => {
      logger.info({ accountId, result }, 'password reset');
      res.body = { success: true };
      next();
    }).catch(next);
  },
};
