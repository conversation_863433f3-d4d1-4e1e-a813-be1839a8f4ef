/* eslint-disable no-underscore-dangle */
const async = require('async');
const { RateLimiterRedis } = require('rate-limiter-flexible');
const Account = require('../models/Account');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');
const signupSchema = require('./signup').schema;
const logger = require('../../../lib/logger')('login');
const config = require('../../../config');
const wixService = require('../../wix/wix.service');
const thinkificService = require('../../thinkific/thinkific.service');
const { stringUtils } = require('../../../lib/utils');
const redisService = require('../../common/redis.service');
const notifierService = require('../../common/notifier.service');
const analyticsService = require('../../common/analytics.service');

const rateLimiter = new RateLimiterRedis({
  storeClient: redisService.getClient(),
  blockDuration: 60 * 60,
  duration: 60 * 60,
  points: 100,
  keyPrefix: 'login',
});

module.exports = {
  config: {
    methods: ['POST'],
    noAuth: true,
  },
  schema: signupSchema,
  handle(req, res, next) {
    const params = req.body;
    const { email } = params;
    const ip = req.remoteAddress;
    async.waterfall([
      cb => Promise.all([
        rateLimiter.consume(params.email, 10),
        rateLimiter.consume(ip, 5),
      ]).then(() => cb()).catch(() => {
        const err = ErrorFactory('Too many attempts', 429);
        notifierService.notifyError(err, `Account locked ${email} (${ip})`);
        cb(err);
      }),
      cb => Account.findOne({ email }, cb),
      (account, cb) => {
        if(!account) {
          return cb(ErrorFactory('Wrong email or password', 400, { email }));
        }
        return account.comparePassword(params.password, (err, equal) => cb(err, account, equal));
      },
      async (account, equal) => {
        if(!equal) {
          if(account.loginType && account.loginType !== 'email') {
            const platform = stringUtils.capitalizeFirstLetter(account.loginType);
            const message = `Use ${platform} Login`;
            return Promise.reject(ErrorFactory(message, 400, { email: params.email }));
          }
          const info = { email, info: 'bad password' };
          return Promise.reject(ErrorFactory('Wrong email or password', 400, info));
        }

        if(!account.active) {
          const msg = 'Your account has not been activated, please check your email or send us a message';
          return Promise.reject(ErrorFactory(msg, 403));
        }
        if(account.deleted) {
          const msg = 'Your account has been deleted';
          return Promise.reject(ErrorFactory(msg, 400));
        }
        if(!account.lastSeen) {
          // first login
          analyticsService.trackSignupEvent(account.id, { loginType: 'email', $email: email });
        }

        logger.info({ accountId: account._id, email }, 'login account');
        if(req.cookies.ps_wix) {
          const instanceId = req.cookies.ps_wix;
          await wixService.integrateExistingAccount(account, instanceId).finally(() => {
            res.cookie('ps_wix', '', config.getCookieConfig(null, 0));
          });
        }
        if(req.cookies.ps_thinkific) {
          const gid = req.cookies.ps_thinkific;
          await thinkificService.integrateAccount(account, gid).finally(() => {
            res.cookie('ps_thinkific', '', config.getCookieConfig(null, 0));
          });
        }
        await account.save();

        req.session.accountId = account._id;
        req.session.apiKey = account.apiKey;
        req.session.email = email;
        res.body = {
          email,
          apiKey: account.apiKey,
          accountId: account._id,
        };
        return Promise.resolve();
      },
    ], next);
  },
};
