const async = require('async');
const OAuth2 = require('google-auth-library').OAuth2Client;
const config = require('../../../config');
const logger = require('../../../lib/logger')('google');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');
const Account = require('../models/Account');
const wixService = require('../../wix/wix.service');
const thinkificService = require('../../thinkific/thinkific.service');
const accountService = require('../account.service');
const notifierService = require('../../common/notifier.service');
const tracker = require('../../common/tracker.service');
const notifConsts = require('../../notifications/constants');
const triggers = require('../../../lib/triggers');
const analyticsService = require('../../common/analytics.service');
const redis = require('../../common/redis.service').getClient();

const { callback, clientId, clientSecret } = config.googleAuth;
const auth = new OAuth2(clientId, clientSecret, callback);

module.exports = {
  config: {
    methods: ['GET'],
    noAuth: true,
  },
  schema: {},
  // eslint-disable-next-line consistent-return
  async handle(req, res, next) {
    const { code } = req.query;
    if(!code) {
      // const msg = 'missing google login code';
      // notifierService.notifyError(ErrorFactory(msg), msg, { query: req.query, locals: req.locals });
      return res.redirect(config.consoleUrl);
    }

    const codeKey = `google-code:${code}`;
    const codeUsed = await redis.getAsync(codeKey);
    if(codeUsed) {
      const msg = 'google code already used';
      notifierService.notifyError(ErrorFactory(msg), msg, { code, locals: req.locals });
      return res.redirect(config.consoleUrl);
    }
    await redis.setexAsync(codeKey, 10, 1);

    let payload = null;
    async.waterfall([
      (cb) => {
        logger.info({ code }, 'fetching token');
        auth.getToken(code, cb);
      },
      (tokens, response, cb) => {
        logger.info({ tokens }, 'verifying id');
        auth.verifyIdToken({ idToken: tokens.id_token }, cb);
      },
      (result, cb) => {
        payload = result.getPayload();
        const { email } = payload;
        const name = [payload.given_name, payload.family_name].filter(Boolean).join(' ');
        const nextResult = { email, name };

        logger.info({ email }, 'searching account');
        Account.findOne({ email }, (err, doc) => {
          nextResult.account = doc;
          cb(err, nextResult);
        });
      },
      async (result) => {
        let { account } = result;
        const { email } = result;
        const ip = req.remoteAddress;
        const userAgent = req.headers['user-agent'];
        if(!account) {
          account = await accountService.makeAccount({
            email,
            active: false,
            cookies: req.cookies,
            ip,
            loginType: 'google',
          });
        }
        account.google = { name: result.name };
        if(!account.active) {
          account.active = true;
          triggers.signup(account, { platform: 'Google', ip });
          tracker.signup({
            id: account.id, email, ip, ua: userAgent,
          });
          analyticsService.trackSignupEvent(account.id, { loginType: 'google', $email: account.email });
          // already first party (on provesrc domain)
          res.cookie('ps_signup', 'true', config.cookies.ps_signup);
        }

        if(req.cookies.ps_wix) {
          const instanceId = req.cookies.ps_wix;
          let promise;
          if(account.isNew) {
            account.source = notifConsts.PLATFORMS.wix;
            promise = wixService.integrateNewAccount(account, instanceId);
          } else {
            promise = wixService.integrateExistingAccount(account, instanceId);
          }
          await promise.finally(() => {
            res.cookie('ps_wix', '', config.getCookieConfig(null, 0));
          });
        }
        if(req.cookies.ps_thinkific) {
          const gid = req.cookies.ps_thinkific;
          await thinkificService.integrateAccount(account, gid).finally(() => {
            res.cookie('ps_thinkific', '', config.getCookieConfig(null, 0));
          });
        }

        return account.save();
      },
      (account, cb) => {
        const { email } = account;
        logger.info({ accountId: account.id, email }, 'login account');
        req.session.accountId = account.id;
        req.session.apiKey = account.apiKey;
        req.session.email = email;
        cb();
      },
    ],
    (err) => {
      if(err) {
        notifierService.notifyError(err, 'failed google login', { locals: req.locals, payload, code });
        logger.error({ err }, 'failed google login');
      }
      res.redirect(config.consoleUrl);
    });
  },
};
