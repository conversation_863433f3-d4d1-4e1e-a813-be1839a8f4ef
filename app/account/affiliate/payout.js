const _ = require('lodash');
const config = require('../../../config');
const authTypes = require('../../../middleware/authTypes');
const Account = require('../models/Account');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');
const slack = require('../../../lib/apis/slackNotifier');
const emailService = require('../../common/mailer.service');
const dateUtils = require('../../../lib/utils/dateUtils');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {
      email: { type: 'string', required: true },
    },
  },
  async handle(req, res, next) {
    try {
      const account = await Account.findOne({ _id: req.locals.accountId });
      const balance = account.getBalance();
      const paypalEmail = req.body.email;
      if(balance < 25) {
        return next(ErrorFactory('minimum balance for payout is $25'));
      }
      let message = `payout requested by ${account.email} to paypal ${paypalEmail} balance: ${balance}`;
      if(account.location && account.location.countryCode && account.location.countryCode === 'IL') {
        message = `(ISRAEL) ${message}`;
      }
      slack.notify(message, account.affiliate, { webhook: config.slack.affiliates });
      emailService.sendAdminEmail({
        to: '<EMAIL>',
        subject: `Affiliate payout requested by ${account.email} on ${dateUtils.getISODate(new Date())}`,
        html: `${message}<br><br>${JSON.stringify(account.affiliate, null, 2)}`,
      });
      account.affiliate.paid = (account.affiliate.paid || 0) + balance;
      account.affiliate.balance = 0;
      account.affiliate.paypal = paypalEmail;
      account.affiliate.payouts.push({ date: Date.now(), sum: balance, paypal: paypalEmail });
      await account.save();

      res.body = { message: 'payout requested' };
      next();
    } catch(err) {
      next(err);
    }
  },
};
