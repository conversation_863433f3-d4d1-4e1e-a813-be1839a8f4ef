const _ = require('lodash');
const authTypes = require('../../../middleware/authTypes');
const Account = require('../models/Account');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {},
  },
  async handle(req, res, next) {
    try {
      const { accountId } = req.locals;
      const account = await Account.findOne({ _id: accountId });
      if(!account) return next(ErrorFactory.AccountNotFound(accountId));

      let signups = 0;
      let customers = 0;
      const affiliateId = account.getAffiliateId();
      if(affiliateId) {
        [signups, customers] = await Promise.all([
          Account.count({ 'affiliate.referrer': affiliateId }),
          Account.count({
            'affiliate.referrer': affiliateId,
            'subscription.recentIPN': { $in: ['CHARGE', 'RECURRING', 'CONTRACT_CHANGE'] },
          }),
        ]);
      }
      const emptyAffiliate = {
        id: null, referrer: null, paypal: null, balance: 0, mrr: 0, paid: 0, signups, customers,
      };
      const result = Object.assign(emptyAffiliate, account.affiliate.toObject());
      result.affiliateLink = _.get(account, 'configuration.affiliateLink', false);
      res.body = result;
      next();
    } catch(err) {
      next(err);
    }
  },
};
