/* eslint-disable global-require */
module.exports = {
  '/signup': require('./login/signup'),
  '/login': require('./login/login'),
  '/logout': require('./login/logout'),
  '/isInstalled': require('./install/isInstalled'),
  '/configuration': require('./configuration'),
  '/googleCallback': require('./login/googleCallback'),
  '/googleAuth': require('./login/googleAuth'),
  '/gmb-auth': require('./gmbAuth'),
  '/gmb-callback': require('./gmbCallback'),
  '/mailto': require('./install/mailto'),
  '/install': require('./install/install'),
  '/forgot': require('./login/forgot'),
  '/reset': require('./login/reset'),
  '/feed': require('./feed/feed'),
  '/feed/export': require('./feed/feedExport'),
  '/affiliate/payout': require('./affiliate/payout'),
  '/affiliate$': require('./affiliate/info'),
  '/onboarding': require('./onboarding'),
  '/upgrade': require('./upgrade'),
  '/updateCard': require('./updateCard'),
  '/activate': require('./login/activate').activateApi,
  '/change-email': require('./changeEmail.api'),
  '/integrations': require('./integrations.api'),
  '/facebook': require('./facebook'),
  '/uninstall': require('./uninstall'),
  '/cancel-reason': require('./cancel-reason'),
  '/sub/create': require('./SubAccount/create'),
  '/sub/switch': require('./SubAccount/switch'),
  '/sub/list': require('./SubAccount/list'),
};
