const _ = require('lodash');

const PLANS = {
  FREE: 'free',
  BASIC: 'basic',
  OLD_STARTER: 'old-starter',
  STARTER: 'starter',
  STARTER_BRANDING: 'starter-branding', // Shopify only currently
  GROWTH: 'growth',
  MON<PERSON><PERSON>: 'monster',
  GORILL<PERSON>: 'gorilla',
  // DA_BOSS: 'da_boss',
  UNLIMITED: 'unlimited',
  LIFETIME_39: 'lifetime-39',
  LIFETIME_39_ADDON: 'lifetime-39-addon',
  LIFETIME_20K: 'lifetime-20k',
  LIFETIME_40K: 'lifetime-40k',
  LIFETIME_60K: 'lifetime-60k',
  LIFETIME_80K: 'lifetime-80k',
  LIFETIME_100K: 'lifetime-100k',
};
const PLANS_ARRAY = Object.values(PLANS);
const ORDERED_PLANS = [
  PLANS.FREE, PLANS.BASIC,
  PLANS.OLD_STARTER, PLANS.STARTER, PLANS.STARTER_BRANDING,
  PLANS.GROWTH, PLANS.MONSTER, PLANS.GORILLA, PLANS.UNLIMITED];

const WIX_PLANS = {
  BASIC: PLANS.BASIC,
  OLD_STARTER: PLANS.OLD_STARTER,
  STARTER: PLANS.STARTER,
  STARTER_BRANDING: PLANS.STARTER_BRANDING,
  GROWTH: PLANS.GROWTH,
  MONSTER: PLANS.MONSTER,
  GORILLA: PLANS.GORILLA,
  UNLIMITED: PLANS.UNLIMITED,
};

const PAID_PLANS = Object.values(_.omit(PLANS, ['FREE', 'LIFETIME_39', 'LIFETIME_39_ADDON', 'LIFETIME_20K', 'LIFETIME_40K', 'LIFETIME_60K', 'LIFETIME_80K', 'LIFETIME_100K']));

const PRICES = {
  [PLANS.BASIC]: 7,
  [PLANS.OLD_STARTER]: 21,
  [PLANS.STARTER]: 29,
  [PLANS.STARTER_BRANDING]: 29,
  [PLANS.GROWTH]: 54,
  [PLANS.MONSTER]: 109,
  [PLANS.GORILLA]: 219,
  // [PLANS.DA_BOSS]: 299,
  [PLANS.UNLIMITED]: 1095,
};

const PERIODS = {
  MONTHLY: 'monthly',
  YEARLY: 'yearly',
  LIFETIME: 'lifetime',
};

const STRIPE_PERIODS = {
  MONTH: 'month',
  YEAR: 'year',
};

const PERIODS_ARRAY = [PERIODS.MONTHLY, PERIODS.YEARLY, PERIODS.LIFETIME];

const LIMITS = {
  [PLANS.FREE]: 1000,
  [PLANS.BASIC]: 3 * 1000,
  [PLANS.OLD_STARTER]: 10 * 1000,
  [PLANS.STARTER]: 20 * 1000,
  [PLANS.STARTER_BRANDING]: 20 * 1000,
  [PLANS.GROWTH]: 50 * 1000,
  [PLANS.MONSTER]: 200 * 1000,
  [PLANS.GORILLA]: 500 * 1000,
  // [PLANS.DA_BOSS]: 1000 * 1000, //1M
  [PLANS.UNLIMITED]: 100 * 1000 * 1000, // 100M
  [PLANS.LIFETIME_39]: 50 * 1000,
  [PLANS.LIFETIME_39_ADDON]: 100 * 1000,
  [PLANS.LIFETIME_20K]: 20 * 1000,
  [PLANS.LIFETIME_40K]: 40 * 1000,
  [PLANS.LIFETIME_60K]: 60 * 1000,
  [PLANS.LIFETIME_80K]: 80 * 1000,
  [PLANS.LIFETIME_100K]: 100 * 1000,
};

const FREE_CONTRACT = {
  id: '1000000',
  name: 'Free Plan',
  period: 'monthly',
  plan: PLANS.FREE,
};

const CONTRACT_DETAILS = {
  [FREE_CONTRACT.id]: FREE_CONTRACT,

  3948872: { plan: PLANS.BASIC, period: PERIODS.MONTHLY },
  3948874: { plan: PLANS.BASIC, period: PERIODS.MONTHLY, vat: true },
  3948876: { plan: PLANS.BASIC, period: PERIODS.YEARLY },
  3948878: { plan: PLANS.BASIC, period: PERIODS.YEARLY, vat: true },

  4029390: { plan: PLANS.OLD_STARTER, period: PERIODS.MONTHLY },
  4029360: { plan: PLANS.OLD_STARTER, period: PERIODS.YEARLY },

  3473968: { plan: PLANS.STARTER, period: PERIODS.MONTHLY },
  3253899: { plan: PLANS.STARTER, period: PERIODS.MONTHLY, vat: true },
  3434684: { plan: PLANS.STARTER, period: PERIODS.MONTHLY },
  3434688: { plan: PLANS.STARTER, period: PERIODS.MONTHLY, vat: true },
  3246771: { plan: PLANS.STARTER, period: PERIODS.YEARLY },
  3608952: { plan: PLANS.STARTER, period: PERIODS.YEARLY, vat: true },
  3434690: { plan: PLANS.STARTER, period: PERIODS.YEARLY },
  3434692: { plan: PLANS.STARTER, period: PERIODS.YEARLY, vat: true },

  3434676: { plan: PLANS.GROWTH, period: PERIODS.MONTHLY },
  3434678: { plan: PLANS.GROWTH, period: PERIODS.MONTHLY, vat: true },
  3434680: { plan: PLANS.GROWTH, period: PERIODS.YEARLY },
  3434682: { plan: PLANS.GROWTH, period: PERIODS.YEARLY, vat: true },

  3434694: { plan: PLANS.MONSTER, period: PERIODS.MONTHLY },
  3434696: { plan: PLANS.MONSTER, period: PERIODS.MONTHLY, vat: true },
  3434698: { plan: PLANS.MONSTER, period: PERIODS.YEARLY },
  3434702: { plan: PLANS.MONSTER, period: PERIODS.YEARLY, vat: true },

  3477356: { plan: PLANS.GORILLA, period: PERIODS.MONTHLY },
  3478588: { plan: PLANS.GORILLA, period: PERIODS.MONTHLY, vat: true },
  3478582: { plan: PLANS.GORILLA, period: PERIODS.YEARLY },
  3478584: { plan: PLANS.GORILLA, period: PERIODS.YEARLY, vat: true },

  // '3463358': {plan: PLANS.DA_BOSS, period: PERIODS.MONTHLY},
  // '3478590': {plan: PLANS.DA_BOSS, period: PERIODS.MONTHLY}, //vat
  // '3478592': {plan: PLANS.DA_BOSS, period: PERIODS.YEARLY},
  // '3478594': {plan: PLANS.DA_BOSS, period: PERIODS.YEARLY}, //vat

  3489766: { plan: PLANS.UNLIMITED, period: PERIODS.MONTHLY },
  3489768: { plan: PLANS.UNLIMITED, period: PERIODS.MONTHLY, vat: true }, // vat
  3489770: { plan: PLANS.UNLIMITED, period: PERIODS.YEARLY },
  3489772: { plan: PLANS.UNLIMITED, period: PERIODS.YEARLY, vat: true }, // vat

  3452234: { plan: PLANS.LIFETIME_39, period: PERIODS.LIFETIME },
  3452236: { plan: PLANS.LIFETIME_39, period: PERIODS.LIFETIME, vat: true }, // vat
  3480290: { plan: PLANS.LIFETIME_39, period: PERIODS.MONTHLY }, // LTD no branding upgrade
  3452698: { plan: PLANS.LIFETIME_39_ADDON, period: PERIODS.LIFETIME },
  3452702: { plan: PLANS.LIFETIME_39_ADDON, period: PERIODS.LIFETIME, vat: true }, // vat

  3488852: { plan: PLANS.LIFETIME_20K, period: PERIODS.LIFETIME },
  3488854: { plan: PLANS.LIFETIME_40K, period: PERIODS.LIFETIME },
  3488856: { plan: PLANS.LIFETIME_60K, period: PERIODS.LIFETIME },
  3488858: { plan: PLANS.LIFETIME_20K, period: PERIODS.LIFETIME },
  3488860: { plan: PLANS.LIFETIME_40K, period: PERIODS.LIFETIME },
  3488862: { plan: PLANS.LIFETIME_60K, period: PERIODS.LIFETIME },
  3488960: { plan: PLANS.LIFETIME_80K, period: PERIODS.LIFETIME },
  3488962: { plan: PLANS.LIFETIME_100K, period: PERIODS.LIFETIME },
};

const BLUESNAP_CONTRACTS = {
  monthly: {
    basic: '3948872',
    starter: '3434684',
    growth: '3434676',
    monster: '3434694',
    gorilla: '3477356',
    unlimited: '3489766',
    vat: {
      basic: '3948874',
      starter: '3434688',
      growth: '3434678',
      monster: '3434696',
      gorilla: '3478588',
      unlimited: '3489768',
    },
  },
  yearly: {
    basic: '3948876',
    starter: '3434690',
    growth: '3434680',
    monster: '3434698',
    gorilla: '3478582',
    unlimited: '3489770',
    vat: {
      basic: '3948878',
      starter: '3434692',
      growth: '3434682',
      monster: '3434702',
      gorilla: '3478584',
      unlimited: '3489772',
    },
  },
};

const STRIPE_PLANS = {
  production: {
    [PERIODS.MONTHLY]: {
      basic: 'price_1R9PwjBRyHtdkaAHkV8gWHzR',
      starter: 'price_1R9PwpBRyHtdkaAHUoHsqCFX',
      growth: 'price_1R9PwtBRyHtdkaAH3NJZcMgm',
      monster: 'price_1R9PwwBRyHtdkaAHq6zX6j73',
      gorilla: 'price_1R9Px0BRyHtdkaAHO0uLFSHI',
      unlimited: 'price_1R9Px2BRyHtdkaAHlb3xwFVs',
    },
    [PERIODS.YEARLY]: {
      basic: 'price_1R9PwnBRyHtdkaAHQsl4JaS8',
      starter: 'price_1R9PwrBRyHtdkaAHuRq8nPIQ',
      growth: 'price_1R9PwuBRyHtdkaAHInKlEP5c',
      monster: 'price_1R9PwyBRyHtdkaAH7iy17UNP',
      gorilla: 'price_1R9Px1BRyHtdkaAHNXeNqj4X',
      unlimited: 'price_1R9Px4BRyHtdkaAHdbMG4Hl8',
    },
  },
  test: {
    [PERIODS.MONTHLY]: {
      basic: 'price_1R9PkwBUE3MpZsYiLMftQJAq',
      starter: 'price_1R9OwFBUE3MpZsYiMwGwvYkM',
      growth: 'price_1R9OzRBUE3MpZsYi9TBekAMf',
      monster: 'price_1R9P0ABUE3MpZsYiLxnHnfEL',
      gorilla: 'price_1R9PuNBUE3MpZsYiwqLFkfe4',
      unlimited: 'price_1R9PvjBUE3MpZsYiIkt4j3ik',
    },
    [PERIODS.YEARLY]: {
      basic: 'price_1R9PtiBUE3MpZsYiohfFJCI8',
      starter: 'price_1R9OwfBUE3MpZsYiVzczZtD2',
      growth: 'price_1R9OztBUE3MpZsYisfd1gpTX',
      monster: 'price_1R9P0nBUE3MpZsYiuiw5kgSJ',
      gorilla: 'price_1R9PuvBUE3MpZsYi7jxa54RH',
      unlimited: 'price_1R9PvzBUE3MpZsYi7JHPlEG0',
    },
  },
};

const STRIPE_PAYMENT_TYPE = {
  card: 'card',
  ideal: 'ideal',
  bancontact: 'bancontact',
  sofort: 'sofort',
  link: 'link',
  cashapp: 'cashapp',
  amazonPay: 'amazon_pay',
};

const STRIPE_PORTAL_FLOWS = {
  payment_method_update: 'payment_method_update',
  // subscription_cancel: 'subscription_cancel',
  subscription_update: 'subscription_update',
};

const PAYPAL_PLANS = {
  'P-8B943031C0494784BMG44OQI': { plan: PLANS.BASIC, period: PERIODS.MONTHLY },
  'P-06X14881NC349684WMGYJDNI': { plan: PLANS.STARTER, period: PERIODS.MONTHLY },
  'P-0X09504028794752CMG44ZVA': { plan: PLANS.STARTER_BRANDING, period: PERIODS.MONTHLY },
  'P-60383294UL911973WMGYJD2Y': { plan: PLANS.GROWTH, period: PERIODS.MONTHLY },
  'P-0MN48877X8588102WMG43S6A': { plan: PLANS.MONSTER, period: PERIODS.MONTHLY },
  'P-9E250054AE2447406MG44ZNA': { plan: PLANS.GORILLA, period: PERIODS.MONTHLY },

  'P-9DF617277C1248416MG4442I': { plan: PLANS.BASIC, period: PERIODS.YEARLY },
  'P-76R82939PA0909005MG442BQ': { plan: PLANS.STARTER, period: PERIODS.YEARLY },
  'P-4JA083341G875805AMG442SI': { plan: PLANS.STARTER_BRANDING, period: PERIODS.YEARLY },
  'P-97766540AD881172PMG4423Y': { plan: PLANS.GROWTH, period: PERIODS.YEARLY },
  'P-33F13110TX444423LMG443DA': { plan: PLANS.MONSTER, period: PERIODS.YEARLY },
  'P-1W6434296A3752340MG443NI': { plan: PLANS.GORILLA, period: PERIODS.YEARLY },
};

const WHITELABEL_CONTRACTS = ['3473968', '3246771', '3253899', '3608952'];
// lifetime
WHITELABEL_CONTRACTS.push('3488852', '3452702', '3480290', '3452698', '3488858', '3488854', '3488856', '3488860', '3488862', '3488960', '3488962');

const VAT_LTD_CONTRACT = ['3452236'];

const CLOUDFLARE_MAP = {
  starter: PLANS.STARTER,
  growth: PLANS.GROWTH,
  monster: PLANS.GROWTH,
};

module.exports = {
  FREE_CONTRACT,
  PLANS,
  ORDERED_PLANS,
  PLANS_ARRAY,
  WIX_PLANS,
  PAYPAL_PLANS,
  PERIODS,
  PERIODS_ARRAY,
  LIMITS,
  CONTRACT_DETAILS,
  BLUESNAP_CONTRACTS,
  VAT_LTD_CONTRACT,
  CLOUDFLARE_MAP,
  WHITELABEL_CONTRACTS,
  PRICES,
  PAID_PLANS,
  STRIPE_PLANS,
  STRIPE_PORTAL_FLOWS,
  STRIPE_PAYMENT_TYPE,
  STRIPE_PERIODS,
};
