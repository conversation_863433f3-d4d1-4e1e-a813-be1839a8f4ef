const sendgrid = require('../../lib/apis/sendgrid');

const bcc = '<EMAIL>';
const infoEmail = '<EMAIL>';
const from = {
  email: infoEmail,
  name: 'Team ProveSource',
};

module.exports.sendActivation = function (apiKey, email, link) {
  if(!apiKey) throw Error('must include SendGrid API Key');
  if(!email) throw Error('must be a valid email');
  if(!link) throw Error('must contain valid activation link');

  const text = `Hi there,\n\n
Thanks for signing up to ProveSource, please confirm your email ${email} by clicking the following link:\n
${link}\n\n
If you received this email by error, please reply back or contact <NAME_EMAIL>\n\n
Team ProveSource
`;

  const html = `Hi there,<br><br>
Thanks for signing up to ProveSource, please confirm your email <strong>${email}</strong> by clicking the following link:<br>
<a href="${link}">${link}</a><br><br>
If you received this email by error, please reply back or contact us at <a href="mailto:${infoEmail}">${infoEmail}<a>
<br><br>
<strong>Team ProveSource</strong>
`;

  return sendgrid.send(apiKey, {
    to: email,
    from,
    bcc,
    replyTo: from,
    subject: 'Activate your ProveSource account',
    text,
    html,
    category: 'activation',
  });
};

module.exports.sendChangeEmailConfirmation = function (apiKey, email, newEmail, link) {
  if(!apiKey) throw Error('invalid sendgrid API Key');
  if(!email) throw Error('invalid email');
  if(!newEmail) throw Error('invalid new email');
  if(!link) throw Error('invalid token');

  const subject = 'Change your ProveSource account email';
  const html = `Hi there,<br><br>
You've requested to switch your ProveSource account email from ${email} to ${newEmail}.<br>
To confirm this change please click the link below (valid for 30 minutes):<br>
<a href="${link}">${link}</a><br><br>
If you received this email by error, please reply back or contact us at <a href="mailto:${infoEmail}">${infoEmail}<a>
<br><br>
<strong>Team ProveSource</strong>`;

  return sendgrid.send(apiKey, {
    to: email,
    from,
    bcc,
    replyTo: from,
    subject,
    html,
    category: 'email-change',
  });
};

module.exports.sendPasswordReset = function (apiKey, email, link) {
  if(!apiKey) throw Error('invalid api key');
  if(!email) throw Error('invalid email');
  if(!link) throw Error('invalid password reset link');

  const html = `Hi there,<br><br>
You requested to reset the password for your ProveSource account (${email}).<br>
To change your password, click the link below (valid for 1 hour):<br>
<a href="${link}">${link}</a><br><br>
Team ProveSource`;

  return sendgrid.send(apiKey, {
    to: email,
    bcc,
    from,
    replyTo: from,
    subject: 'Reset Your ProveSource Password',
    html,
    category: 'password-reset',
  });
};
