const MailChecker = require('mailchecker');
const mailer = require('./mailer');
const config = require('../../config');
const cryptoUtils = require('../../lib/utils/cryptoUtils');
const dateUtils = require('../../lib/utils/dateUtils');
const Account = require('./models/Account');
const SubAccount = require('./models/SubAccount');
const emailAutomationService = require('../common/email.automation.service');

const ErrorFactory = require('../../lib/errors/ErrorFactory');

module.exports.sendConfirmationEmail = async function (email, newEmail) {
  const exists = await Account.findOne({ email: newEmail }, { _id: 1 });
  if(exists) throw Error('email is already taken');

  if(!MailChecker.isValid(newEmail)) {
    throw new Error('The provided email address is invalid');
  }

  const data = { timestamp: Date.now(), email, newEmail };
  const token = cryptoUtils.encrypt(JSON.stringify(data), config.cryptoKeys.changeEmail);
  const link = `${config.apiUrl}/account/change-email?token=${token}`;
  await mailer.sendChangeEmailConfirmation(config.sendgrid.apiKey, email, newEmail, link);
};

/**
 * changes account email if token is valid (30 minutes timestamp), returns new email if changed
 * @param token
 * @return {Promise<*>}
 */
module.exports.changeEmail = async function (token) {
  const decrypted = cryptoUtils.decrypt(token, config.cryptoKeys.changeEmail);
  if(!decrypted) throw ErrorFactory('invalid token', 400, { token });

  const data = JSON.parse(decrypted);
  if(Date.now() - data.timestamp > dateUtils.MILLISECONDS_IN_MINUTE * 30) {
    throw ErrorFactory('link expired', 400, data);
  }

  const exists = await Account.findOne({ email: data.newEmail }, { _id: 1 });
  if(exists) {
    throw ErrorFactory('email already exists', 400, data);
  }

  const [res, resSubs] = await Promise.all([
    Account.updateOne(
      { email: data.email },
      {
        $set: { email: data.newEmail },
        $addToSet: {
          emails: {
            $each: [data.email, data.newEmail],
          },
        },
      },
    ),
    SubAccount.updateMany(
      { 'parent.email': data.email },
      { $set: { 'parent.email': data.newEmail } },
    ),
  ]);

  emailAutomationService.createSubscription(data.newEmail, { group: config.mailerlite.groups.all });

  if(res && res.nModified && resSubs && resSubs.nModified) {
    return data.newEmail;
  }
};
