const authTypes = require('../../middleware/authTypes');

const Account = require('./models/Account');
const CancelReason = require('./models/CancelReason');

const config = require('../../config');
const slack = require('../../lib/apis/slackNotifier');
const ErrorFactory = require('../../lib/errors/ErrorFactory');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {
      reason: { type: ['string', 'null'] },
      requestedFix: { type: 'boolean' },
      downgradePlan: { type: 'string' },
    },
  },
  async handle(req, res, next) {
    const { accountId } = req.session;
    const account = await Account.findOne({ _id: accountId });
    if(!account) {
      return ErrorFactory.AccountNotFound(accountId);
    }
    const { email } = account;
    const { reason, requestedFix, downgradePlan } = req.body;
    let msg;
    if(downgradePlan && !downgradePlan.toLowerCase().includes('free')) {
      msg = `${email} requested to downgrade to ${downgradePlan} plan`;
    } else if(requestedFix) {
      msg = `${email} requested cancellation, wants to fix, reason: ${reason}`;
    } else {
      msg = `${email} requested cancellation, reason: ${reason}`;
    }
    await CancelReason.create({
      accountId,
      email: account.email,
      reason,
      requestedFix,
    });
    if(msg) {
      slack.notify(msg, null, { webhook: config.slack.cancellations });
    }
    return next();
  },
};
