const _ = require('lodash');
const json2csv = require('json2csv').parse;
const authTypes = require('../../../middleware/authTypes');
const Feed = require('../models/Feed');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');
const feedValidator = require('./feedValidator');
const constants = require('./feedConstants');
const stringUtils = require('../../../lib/utils/stringUtils');
const { dateUtils } = require('../../../lib/utils');
const Account = require('../models/Account');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    properties: {
      search: String,
      startDate: { type: 'string', pattern: /^\d{4}-\d{1,2}-\d{1,2}$/ },
      endDate: { type: 'string', pattern: /^\d{4}-\d{1,2}-\d{1,2}$/ },
    },
  },
  async handle(req, res, next) {
    const { accountId } = req.locals;

    const { startDate, endDate, search = null } = req.query;
    try {
      feedValidator.search(search, startDate, endDate);
    } catch(err) {
      return next(err);
    }

    const account = await Account.findById(accountId).select('settings');
    const hideData = _.get(account, 'settings.hideSensitiveFeedData', false);

    const mongoQuery = Feed.getQuery(accountId, startDate, endDate, search);

    const feed = await Feed.find(mongoQuery)
      .sort({ createdAt: -1 })
      // .limit(constants.exportLimit)
      .maxTime(constants.queryTimeout)
      .catch((err) => {
        throw ErrorFactory('Failed to retrieve events, try making the date range smaller (3 months)', 500, { err, accountId });
      });

    const exportFeed = feed.map(feedItem => ({
      date: feedItem.createdAt.toISOString().split('T')[0],
      message: feedItem.message,
      email: _.get(feedItem, 'data.email', 'N/A'),
      name: feedItem.getName(),
      source: feedItem.getSource(),
      country: _.get(feedItem, 'data.location.country') || _.get(feedItem, 'data.country'),
      city: _.get(feedItem, 'data.location.city') || _.get(feedItem, 'data.city'),
    }));
    if(hideData) {
      const keys = ['email', 'name', 'city'];
      exportFeed.forEach((item) => {
        for(let i = 0; i < keys.length; i += 1) {
          const val = _.get(item, keys[i]);
          if(val) {
            _.set(item, keys[i], stringUtils.getHiddenString(val));
          }
        }
      });
    }

    const fields = ['date', 'message', 'email', 'name', 'source', 'country', 'city'];
    const csv = json2csv(exportFeed, { fields });
    let filename = 'Feed Export';
    if(startDate && endDate) {
      filename += ` (${startDate} - ${endDate})`;
    } else {
      filename += ` (${dateUtils.getISODate(new Date())})`;
    }
    res.setHeader('Content-disposition', `attachment; filename=${filename}.csv`);
    res.set('Content-Type', 'text/csv');
    res.status(200).send(csv);
  },

};
