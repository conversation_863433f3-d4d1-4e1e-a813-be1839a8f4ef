const _ = require('lodash');
const authTypes = require('../../../middleware/authTypes');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');
const constants = require('./feedConstants');
const stringUtils = require('../../../lib/utils/stringUtils');

const Feed = require('../models/Feed');
const Account = require('../models/Account');
const feedValidator = require('./feedValidator');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    properties: {
      search: String,
      page: String,
      startDate: { type: 'string', pattern: /^\d{4}-\d{1,2}-\d{1,2}$/ },
      endDate: { type: 'string', pattern: /^\d{4}-\d{1,2}-\d{1,2}$/ },
    },
  },
  async handle(req, res, next) {
    const { accountId } = req.locals;
    const { startDate, endDate, search = null } = req.query;
    try {
      feedValidator.search(search, startDate, endDate);
    } catch(err) {
      return next(err);
    }

    const account = await Account.findById(accountId).select('settings');
    const hideData = _.get(account, 'settings.hideSensitiveFeedData', false);

    const page = parseInt(req.query.page, 10) || 1;
    const startIndex = (page - 1) * constants.searchLimit;
    const mongoQuery = Feed.getQuery(accountId, startDate, endDate, search);
    const feed = await Feed.find(mongoQuery)
      .sort({ createdAt: -1 })
      .limit(constants.searchLimit + 1)
      .skip(startIndex)
      .maxTime(constants.queryTimeout)
      .catch((err) => { throw ErrorFactory('Failed to retrieve events, try making the date range smaller (3 months)', 500, { err, accountId }); });

    const hasNext = !!feed[constants.searchLimit];
    if(hasNext) feed.pop();

    const flatFeed = feed.map(feedItem => feedItem.responseObject(hideData));
    if(hideData) {
      const keys = [
        'data.email', 'data.ip', 'data.name', 'data.firstName', 'data.lastName',
        'data.city', 'data.location.city',
      ];
      flatFeed.forEach((item) => {
        for(let i = 0; i < keys.length; i += 1) {
          const val = _.get(item, keys[i]);
          if(val) {
            _.set(item, keys[i], stringUtils.getHiddenString(val));
          }
        }
      });
    }
    res.body = {
      feed: flatFeed,
      hasNext,
    };
    next();
  },
};
