const ErrorFactory = require('../../../lib/errors/ErrorFactory');
const constants = require('./feedConstants');

module.exports.search = function search(searchQuery, startDate, endDate) {
  const { min, max } = constants.searchLength;
  if(searchQuery && searchQuery.length < min) {
    throw ErrorFactory(`Search string is to short (${min}-${max} characters)`, 400);
  }

  if(searchQuery && searchQuery.length > max) {
    throw ErrorFactory(`Search string is too long (${min}-${max} characters)`, 400);
  }

  if(startDate && endDate && Date.parse(startDate) > Date.parse(endDate)) {
    throw ErrorFactory('Start date is bigger than end date', 400);
  }
};
