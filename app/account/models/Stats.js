const mongoose = require('mongoose');

module.exports = new mongoose.Schema({
  planLimitReached: Date,
  planLimit90Reached: Date,
  visitedHelpCenter: <PERSON><PERSON>an,
  visitedVideoGuides: <PERSON><PERSON><PERSON>,
  visitedAffiliateProgram: <PERSON><PERSON><PERSON>,
  visitedWhatsNew: <PERSON><PERSON>an,
  createdNotifications: Number,
  notifications: {
    active: Number,
    created: Number,
    lastCreated: Date,
    updated: Number,
    lastUpdated: Date,
    deleted: Number,
    lastDeleted: Date,
    priorityChanged: Number,
    lastImpression: Date,
  },
  goals: {
    created: Number,
    lastCreated: Date,
    updated: Number,
    lastUpdated: Date,
    deleted: Number,
    lastDeleted: Date,
  },
  sentEmails: {
    summary: { type: Date },
    facebookReviewError: { type: Date },
    cancellation: { type: Date },
    shapo: { type: Date },
    invoice: { type: Date },
    reviewRequest: { type: Date },
    shopifyCredit: { type: Date },
  },
  lastClientRequest: { type: Date },
}, { _id: false });
