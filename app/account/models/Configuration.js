const { Schema } = require('mongoose');

module.exports = new Schema({
  blocked: Boolean, // this allows blocking JS from sending data
  blockedDomains: { type: [String] },
  allowOptOut: Boolean,
  branding: {
    active: Boolean,
    color: String,
    link: String,
    text: String,
    forceColor: { type: Boolean, default: true },
  },
  emailOpt: {
    summary: { type: Boolean, default: false },
    errors: { type: Boolean, default: false },
    ipns: { type: Boolean, default: true },
  },
  delayBetweenNotifications: Number,
  displayHold: Number,
  firstShowDelay: Number,
  optOutDays: Number,
  trackers: {
    googleAnalytics: { type: Boolean, default: true },
    gaKey: { type: String },
    mixpanel: { type: Boolean, default: true },
    amplitude: { type: Boolean, default: true },
  },
  whitelabel: Boolean,
  specialWhitelabel: Boolean,
  zindex: {
    mobile: { type: Number, default: 9999 },
    desktop: { type: Number, default: 9999 },
  },
  forceNoProductLinks: Boolean, // make product links not clickable
  affiliateLink: Boolean,
  ignoreShopify: <PERSON>olean,
  randomize: <PERSON>olean,
  loop: <PERSON>olean,
  sendInvoices: Boolean,
  GANonInteraction: Boolean,
  disableShopifyPaywall: Boolean,
  consolePromotion: { type: Boolean, default: true },
  stopFormTracking: { type: Boolean },
  customFormTracking: {
    forms: [],
    submit: [],
    email: [],
    firstName: [],
  },
  customCSS: { type: String },
  blockGoogleFonts: { type: Boolean },
  forwardingWebhook: {
    enabled: { type: Boolean, default: false },
    url: { type: String, maxLength: 2048 },
    errorCount: { type: Number, default: 0 },
    lastError: { type: String },
  },
  customCode: { type: String },
  filters: {
    names: [{ type: String }],
    emails: [{ type: String }],
    phrases: [{ type: String }],
    ips: [{ type: String }],
  },
  streamQuery: {
    cursorLimit: { type: Number },
    maxDateLookBack: { type: Number },
    maxConversionsLimit: { type: Number },
  },
}, {
  _id: false,
});
