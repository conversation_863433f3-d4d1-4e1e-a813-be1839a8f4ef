const _ = require('lodash');
const mongoose = require('mongoose');
const logger = require('../../../lib/logger')('feedModel');
const { dateUtils } = require('../../../lib/utils');

const FeedSchema = new mongoose.Schema({
  accountId: { type: mongoose.SchemaTypes.ObjectId, required: true },
  message: { type: String, required: true },
  data: {},
  searchHash: { type: String },
}, { collection: 'feed2', timestamps: true });

FeedSchema.index({ accountId: 1, createdAt: -1 });
FeedSchema.index({ accountId: 1, searchHash: 1, createdAt: -1 });


FeedSchema.methods.responseObject = function () {
  const result = _.pick(this, ['message', 'data', '_id']);
  result.timestamp = this.createdAt.getTime();
  return result;
};

/**
 * @memberOf Feed
 * @param accountId
 * @param {string} message
 * @param {object} d - data
 * @param {object} searchHelper - add to search hash
 */
FeedSchema.statics.saveFeed = function (accountId, message, d = null, searchHelper) {
  const data = _(d).omitBy(_.isUndefined).omitBy(_.isNull).value();
  let searchHash = message;
  if(data) {
    if(data.email) {
      searchHash += `|${data.email}`;
    }
    if(data.name) {
      searchHash += `|${data.name}`;
    } else if(data.firstName) {
      searchHash += `|${data.firstName}`;
    }
    if(data.author) {
      searchHash += `|${data.author}`;
    }
    if(data.authorEmail) {
      searchHash += `|${data.authorEmail}`;
    }
    const country = _.get(data, 'location.country', data.country);
    if(country) {
      searchHash += `|${country}`;
    }
    const city = _.get(data, 'location.city', data.city);
    if(city) {
      searchHash += `|${city}`;
    }
  }
  if(searchHelper) {
    searchHash += `|${searchHelper}`;
  }
  searchHash = searchHash.toLowerCase();
  return this.create({
    accountId, message, data, searchHash,
  }).then((result) => {
    logger.info({ feed: result }, 'saved feed');
  }).catch((err) => {
    logger.error({ err, message, data: d }, 'failed to save feed');
  });
};

FeedSchema.methods.getName = function () {
  const NA = 'N/A';
  if(!this.data) return NA;

  let { name } = this.data;
  if(!name) {
    if(this.data.firstName) name = this.data.firstName;
    if(this.data.lastName) name += ` ${this.data.lastName}`;
  }
  if(!name) name = this.data.author;
  if(!name) name = NA;
  return name;
};

FeedSchema.methods.getSource = function () {
  const NA = 'N/A';
  if(!this.data) return NA;

  return this.data.url
    || this.data.store
    || this.data.storeName
    || this.data.businessName
    || this.data.source
    || 'N/A';
};

/**
 * @memberOf Feed
 * @param accountId
 * @param {date?} startDate
 * @param {date?} endDate
 * @param {(string|null)?} search
 * @returns {object} query
 */
FeedSchema.statics.getQuery = function (accountId, startDate, endDate, search) {
  const query = { accountId };
  if(startDate) {
    query.createdAt = { $gte: startDate };
  }
  if(endDate) {
    if(!query.createdAt) query.createdAt = {};
    const timestamp = Date.parse(endDate) + dateUtils.MILLISECONDS_IN_DAY;
    query.createdAt.$lte = timestamp;
  }
  if(search) {
    // TODO: remove this regex and use only lowercase search (sometime after Dec 2024)
    query.searchHash = new RegExp(_.escapeRegExp(search), 'gi');
  }
  return query;
};

/** @class Feed */
module.exports = mongoose.model('Feed2', FeedSchema);
