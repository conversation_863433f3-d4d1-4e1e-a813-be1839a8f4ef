const { Schema } = require('mongoose');

module.exports = new Schema({
  id: { type: Number },
  installed: Date,
  uninstalled: { type: Date },
  token: { type: String },
  myshopify_domain: { type: String, index: true },
  plan_name: { type: String },
  plan_display_name: String,
  email: { type: String },
  domain: { type: String },
  name: String,
  country_name: String,
  shop_owner: String,
  phone: String,
  chargeId: Number,
  ps_plan: String,
  price: Number,
  paying: Boolean,
  shopCreated: { type: Date },
  shopUpdated: { type: Date },
}, { _id: false });
