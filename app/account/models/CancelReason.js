const mongoose = require('mongoose');

const CancelReason = new mongoose.Schema({
  accountId: { type: mongoose.SchemaTypes.ObjectId, required: true, index: true },
  email: { type: String, required: true, index: true },
  reason: { type: String },
  requestedFix: { type: Boolean, default: false },
}, { timestamps: true, collection: 'cancelReasons' });

CancelReason.index({ createdAt: -1 });

module.exports = mongoose.model('CancelReason', CancelReason);
