const mongoose = require('mongoose');
const Account = require('./Account');

const subAccount = new mongoose.Schema({
  parent: {
    id: { type: mongoose.SchemaTypes.ObjectId, required: true, ref: 'Account' },
    email: { type: String, required: true },
  },
  name: { type: String, required: true },
  email: {
    type: String,
    get() {
      return this.parent.email;
    },
  },
  password: { type: String },
});

const SubAccount = Account.discriminator('SubAccount', subAccount);
module.exports = SubAccount;
