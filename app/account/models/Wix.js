const { Schema } = require('mongoose');

module.exports = new Schema({
  code: String,
  refreshToken: String,
  accessToken: String,
  instanceId: { type: String, index: true },
  siteId: { type: String },
  configuration: {
    // need to be exact webhook eventType to ignore e.g. wix.pricing_plans.v2.order_created
    ignoreWebhookEvents: { type: [String] },
  },
  isFree: Boolean,
  appVersion: String,
  billing: {},
  siteName: { type: String },
  description: { type: String },
  url: { type: String },
  apps: { type: [String] },
  email: { type: String },
  ownerEmail: { type: String },
  ownerInfo: {
    email: { type: String },
    emailStatus: { type: String },
  },
  businessName: { type: String },
  categories: { type: [String] },
  country: { type: String },
  city: { type: String },
  phone: { type: String },
  installedDate: Date,
  uninstalledDate: {
    type: Date,
    default: null,
  },
  availablePlans: {},
}, { _id: false });
