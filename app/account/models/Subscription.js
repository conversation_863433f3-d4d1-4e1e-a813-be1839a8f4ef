const mongoose = require('mongoose');

const { Schema, SchemaTypes } = mongoose;

const { PLANS_ARRAY, PERIODS_ARRAY } = require('../plansEnum');

const card = new Schema({
  firstName: String,
  lastName: String,
  fourDigits: String,
  expDate: String,
  brand: String,
  details: String,
}, { _id: false });

module.exports = new Schema({
  created: Date,
  source: String,
  recentIPN: String,
  untilDate: Date,
  transactionDate: Date,
  plan: { type: String, enum: PLANS_ARRAY },
  period: { type: String, enum: PERIODS_ARRAY },
  contractId: String,
  contractName: String,
  email: String,
  invoiceEmail: String,
  customInvoiceName: String,
  customTaxId: String,
  customAddress: String,
  customCity: String,
  customZip: String,
  customFirstName: String,
  customLastName: String,
  customCountryCode: String,
  customState: String,
  customCompanyName: String,
  sourceId: String,
  bluesnapId: String,
  subscriptionId: SchemaTypes.Mixed,
  lastChargeId: String,
  username: String,
  invoiceCompanies: [String],
  invoiceNames: [String],
  card: {
    type: card,
    default: {},
  },
}, { _id: false });
