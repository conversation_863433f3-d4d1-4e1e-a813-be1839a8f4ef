const { Schema } = require('mongoose');
const { SUBSCRIPTION_SOURCE } = require('../../billing/constants');

module.exports = new Schema({
  _id: false,
  showBasicPlan: { type: Boolean, default: false },
  maxAccounts: { type: Number, default: 10 },
  blockBranding: { type: Boolean, default: false },
  ignoreShopifyPOS: { type: Boolean, default: false },
  timeBetweenEmails: {
    facebookReviewErrors: { type: Number, default: 3 },
  },
  ignoreWixSiteLimit: { type: Boolean, default: false },
  ignoreWixBilling: { type: Boolean, default: false },
  hideSensitiveFeedData: { type: Boolean, default: false },
  enablePaypal: { type: Boolean, default: false },
  paymentProcessor: {
    type: String,
    enum: Object.values(SUBSCRIPTION_SOURCE),
  },
});
