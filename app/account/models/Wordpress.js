const { Schema } = require('mongoose');

module.exports = new Schema({
  installed: { type: Date },
  uninstalled: { type: Date, default: null },
  reinstalled: { type: Date },
  email: { type: String },
  name: { type: String },
  url: { type: String },
  woocommerce: { type: Boolean, default: false },
  pluginVersion: { type: String },
  wpVersion: { type: String },
  wooVersion: { type: String },
  selectedEvents: { type: [String] },
}, { _id: false });
