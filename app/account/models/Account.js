/* eslint-disable no-underscore-dangle */
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const _ = require('lodash');
const config = require('../../../config');
const dateUtils = require('../../../lib/utils/dateUtils');
const stringUtil = require('../../../lib/utils/stringUtils');
const {
  PLANS, LIMITS, PERIODS, FREE_CONTRACT, WHITELABEL_CONTRACTS,
} = require('../plansEnum');
const { PLATFORMS } = require('../../notifications/constants');
const domainUtils = require('../../../lib/utils/domainUtils');
const Goal = require('../../goals/Goal');
const Configuration = require('./Configuration');
const Settings = require('./Settings');
const Subscription = require('./Subscription');
const Shopify = require('./Shopify');
const { SHOPIFY } = require('../../constants');
const Wordpress = require('./Wordpress');
const Integrations = require('./Integrations');
const Transaction = require('./Transaction');
const Wix = require('./Wix');
const Bigcommerce = require('./Bigcommerce');
const Thinkific = require('./Thinkific');
const Stats = require('./Stats');

const BAD_IPNS = ['REFUND', 'CANCELLATION_REFUND', 'CHARGEBACK', 'DECLINE'];
const GOOD_IPNS = ['CHARGE', 'RECURRING', 'CONTRACT_CHANGE'];

const facebook = new mongoose.Schema({
  pageId: {
    type: String,
    required: true,
  },
  pageName: {
    type: String,
    required: true,
  },
  userToken: {
    type: String,
    required: true,
  },
  pageToken: {
    type: String,
    required: true,
  },
});

const account = new mongoose.Schema({
  email: {
    type: String,
    lowercase: true,
    required: true,
    set(v) {
      return v && v.replace(/\s/g, '');
    },
  },
  emails: [{ type: String, lowercase: true }],
  hashedPassword: { type: Boolean },
  password: {
    type: String,
    required: true,
    set(password) {
      if(this.hashedPassword === false) {
        this.hashedPassword = true;
        return bcrypt.hashSync(password);
      }
      return password;
    },
  },
  loginType: { type: String, default: 'email' },
  active: { type: Boolean, default: false },
  deleted: { type: Boolean },
  query: {},
  gaClientId: { type: String },
  lastSeen: { type: Date },
  settings: { type: Settings, default: {} },
  configuration: { type: Configuration, default: {} },
  integrations: { type: Integrations, default: {} },
  apiKey: {
    type: String,
    default() {
      return jwt.sign({ accountId: this._id }, config.jwt.api);
    },
  },
  affiliate: {
    id: String,
    referrer: { type: String, index: true },
    balance: Number,
    mrr: Number,
    paid: Number,
    paypal: String,
    commissionRate: {
      type: Number, min: 0.1, max: 0.9, default: 0.2,
    },
    payouts: [{
      date: Date,
      sum: Number,
      paypal: String,
    }],
  },
  metrics: {
    cltv: { type: Number, default: 0 },
    mrr: { type: Number, default: 0 },
  },
  transactions: [Transaction],
  google: {
    name: { type: String },
  },
  subscription: {
    type: Subscription,
    default: null,
  },
  onboarding: {
    installed: Date,
    created: Date,
    impression: Date,
  },
  installed: Date,
  remarketing: {
    subscription: Date,
    wizard: Date,
    planLimit: Date,
  },
  hosts: [String],
  origin: String,
  source: String,
  ip: String,
  ips: [String],
  location: {
    city: String,
    country: String,
    countryCode: String,
    state: String,
    stateCode: String,
  },
  ltd: Boolean,
  customLimit: Number,
  wordpress: [Wordpress],
  shopify: [Shopify],
  removedShops: [Shopify],
  stats: { type: Stats },
  coupons: [String],
  additionalLimit: Number,
  goals: [Goal],
  facebook: [facebook],
  wix: [Wix],
  removedWix: [Wix],
  bigcommerce: [Bigcommerce],
  removedBigcommerce: [Bigcommerce],
  thinkific: [Thinkific],
}, { timestamps: true, discriminatorKey: 'kind' });

account.index({ email: 1 }, { unique: true, partialFilterExpression: { email: { $type: 'string' } } });
account.index({ 'subscription.subscriptionId': 1 });
account.index({ 'subscription.email': 1 });
account.index({ updatedAt: -1 });
account.index({ 'shopify.id': 1 });
account.index({ 'wix.instanceId': 1 });

account.pre('save', function (next) {
  if(!this.apiKey) {
    this.apiKey = jwt.sign({ accountId: this._id }, config.jwt.api);
  } else {
    try {
      jwt.verify(this.apiKey, config.jwt.api);
    } catch(err) {
      this.apiKey = jwt.sign({ accountId: this._id }, config.jwt.api);
    }
  }
  next();
});

/** @memberOf Account */
account.methods.comparePassword = function (password, cb) {
  if(!cb) cb = void 0;
  return bcrypt.compare(password, this.password, cb);
};

account.statics.hashPassword = function (password) {
  return bcrypt.hash(password, 10);
};

account.methods.hashPassowrd = function () {
  return bcrypt.hash(this.password, 10);
};

/** @memberOf Account# */
account.methods.addApiKey = function () {
  this.apiKey = jwt.sign({ accountId: this._id }, config.jwt.api);
  return this.apiKey;
};

account.methods.addEmail = function (email) {
  const lower = email && email.toLowerCase && email.toLowerCase();
  if(lower && lower.length && !this.emails.includes(lower)) {
    this.emails.push(lower);
    return true;
  }
  return false;
};

account.methods.getEmails = function () {
  const retval = [this.email];
  if(this.emails && this.emails.length) {
    const lower = this.emails.map(e => e.toLowerCase());
    retval.push(...lower);
  }
  if(this.subscription && this.subscription.invoiceEmail) {
    retval.push(this.subscription.invoiceEmail.toLowerCase());
  }
  return [...new Set(retval.filter(email => !email.includes('DELETED')))];
};

/** @memberOf Account# */
account.methods.subscriptionInfo = function () {
  if(!this.isSubscriptionActive()) {
    const untilDate = this.getBillingCycleEndDate();
    const recentIPN = (this.subscription && this.subscription.recentIPN) || 'N/A';
    return {
      created: this.createdAt,
      untilDate,
      plan: FREE_CONTRACT.plan,
      period: FREE_CONTRACT.period,
      contractName: FREE_CONTRACT.name,
      contractId: FREE_CONTRACT.id,
      recentIPN,
    };
  }

  const keys = [
    'created', 'untilDate', 'transactionDate', 'contractName', 'contractId',
    'plan', 'period', 'card', 'recentIPN', 'source', 'customFirstName', 'customLastName', 'customZip',
    'customCity', 'customAddress', 'customTaxId', 'customState', 'customCountryCode', 'customCompanyName',
  ];

  return _.pick(this.subscription, keys);
};

/** @memberOf Account# */
account.methods.isSubscriptionActive = function (opts = { ignoreShopify: false }) {
  if(!this.subscription || BAD_IPNS.indexOf(this.subscription.recentIPN) > -1) {
    return false;
  }

  if(!opts.ignoreShopify && this.subscription.source === 'shopify') {
    const { lastChargeId, subscriptionId } = this.subscription;
    let shop = this.shopify
      && this.shopify.find(s => s.chargeId === subscriptionId || s.chargeId === parseFloat(lastChargeId));
    if(this.shopify && this.shopify.length && !shop) {
      shop = this.shopify[0];
    }
    if(shop) {
      const planName = shop && shop.plan_name;
      const negativeStatuses = Object.values(SHOPIFY.negativeStatus).join(',');
      if(shop && planName && (negativeStatuses.includes(planName.toLowerCase()) || shop.uninstalled)) {
        return false;
      }
    }
  }

  const { untilDate } = this.subscription;
  const now = Date.now();
  return now < untilDate || Date.daysBetween(now, untilDate) === 0;
};

account.methods.canUseWhitelabel = function canUseWhitelabel() {
  if(this.settings && this.settings.blockBranding) {
    return false;
  }
  if(this.configuration && this.configuration.specialWhitelabel) {
    return true;
  }

  if(!this.isSubscriptionActive()) {
    return false;
  }

  const sub = this.subscriptionInfo();
  return ((sub.plan !== PLANS.OLD_STARTER && sub.plan !== PLANS.BASIC)
    || WHITELABEL_CONTRACTS.includes(sub.contractId)
    || false);
};

account.methods.canUsePremiumFeature = function canUsePremiumFeature() {
  return this.isSubscriptionActive();
};

account.methods.getBalance = function () {
  return _.get(this, 'affiliate.balance', 0);
};

account.methods.getMRR = function () {
  return _.get(this, 'affiliate.mrr', 0);
};

account.methods.getAffiliateId = function () {
  return _.get(this, 'affiliate.id', null);
};

account.methods.getReferringAffiliate = function () {
  return _.get(this, 'affiliate.referrer', null);
};

account.methods.getAffiliateCommissionRate = function () {
  return _.get(this, 'affiliate.commissionRate', 0.2);
};

/**
 * @memberOf Account
 */
account.methods.getPlanLimit = function getPlanLimit() {
  let additional = 0;
  let { plan } = FREE_CONTRACT;
  if(this.isSubscriptionActive()) {
    const subscription = this.subscriptionInfo();
    ({ plan } = subscription);
    additional = this.additionalLimit || 0;
  }
  let limit = LIMITS[plan] || 0;

  if(this.customLimit != null && this.customLimit > limit) {
    limit = this.customLimit;
  }
  return limit + additional;
};

account.methods.getBillingCycleDate = function () {
  if(this.isSubscriptionActive()) {
    return dateUtils.getCycleStartDate(this.subscription.transactionDate);
  }
  return dateUtils.getCycleStartDate(this.createdAt);
};

account.methods.getBillingCycleEndDate = function () {
  if(this.isSubscriptionActive()) {
    return dateUtils.getSubscriptionUntilDate(this.subscription.transactionDate);
  }
  return dateUtils.getSubscriptionUntilDate(this.createdAt);
};

account.methods.hasShopifyShop = function (shopId) {
  if(!this.shopify || !this.shopify.length) return false;
  const shopIdx = this.shopify.findIndex(item => item.id === shopId);
  return shopIdx > -1;
};

account.methods.isShopifyStaff = function () {
  if(!this.shopify || !this.shopify.length) return false;

  const staffShop = this.shopify.find(s => s.plan_name && s.plan_name === 'staff_business');
  return !!staffShop;
};

account.methods.isShopifyStaffRelated = function isShopifyStaffRelated() {
  if((!this.shopify || !this.shopify.length)
    && (!this.removedShops || !this.removedShops.length)) {
    return false;
  }

  const staffRemoved = this.removedShops.find(s => s.plan_name && s.plan_name.includes('staff'));
  const staffShop = this.shopify.find(s => s.plan_name && s.plan_name.includes('staff'));
  return !!(staffShop || staffRemoved);
};

account.methods.isShopify = function () {
  let retval = false;
  if(this.configuration && this.configuration.disableShopifyPaywall) {
    retval = false;
  } else if(this.loginType === 'shopify' || this.subscriptionInfo().source === 'shopify') {
    retval = (this.shopify && this.shopify.length > 0);
  }
  return retval;
};

account.methods.addShopifyShop = function (shop) {
  if(!this.shopify || !this.shopify.length) {
    this.shopify = [];
  }
  const removedShop = this.getRemovedShop(shop.myshopify_domain);
  if(removedShop) {
    this.removedShops.remove(removedShop);
  }
  const shopItem = this.shopify.find(s => s.id === shop.id);
  if(shopItem) {
    Object.assign(shopItem, shop);
    if(!shopItem.installed || shopItem.uninstalled) {
      shopItem.installed = Date.now();
    }
    shopItem.uninstalled = null;
  } else {
    this.shopify.push({ ...shop, installed: Date.now() });
  }
  return this.addHost(shop.domain);
};

// region bigcommerce

account.methods.getBigcommerceStore = function getBigcommerceStore(storeHash, fromRemoved) {
  if(((!this.bigcommerce || !this.bigcommerce.length)
    && (!this.removedBigcommerce || !this.removedBigcommerce.length))
    || stringUtil.isEmptyString(storeHash)) {
    return null;
  }

  const stores = fromRemoved ? this.removedBigcommerce : this.bigcommerce;
  return stores.find(s => s.storeHash === storeHash);
};

account.methods.addBigcommerceStore = function addBigcommerceStore(store) {
  const removed = this.getBigcommerceStore(store.storeHash, true);
  if(removed) {
    removed.uninstalledDate = null;
    removed.installedDate = Date.now();
    this.bigcommerce.push(removed);
    this.removedBigcommerce.remove(removed);
  }

  let bigcommerceStore = this.getBigcommerceStore(store.storeHash, false);
  if(!bigcommerceStore) {
    this.bigcommerce.push({ ...store, installedDate: Date.now() });
    bigcommerceStore = this.getBigcommerceStore(store.storeHash, false);
  }
  bigcommerceStore.accessToken = store.accessToken;

  if(!this.installed) {
    this.installed = Date.now();
  }
  if(!this.onboarding.installed) {
    this.onboarding.installed = Date.now();
  }
  if(!this.onboarding.created) {
    this.onboarding.created = Date.now();
  }

  return bigcommerceStore;
};

account.methods.getActiveBigcommerceStore = function getActiveBigcommerceStore() {
  return this.bigcommerce && this.bigcommerce.find(store => !store.uninstalledDate);
};

// end of bigcommerce region

// region Wordpress

/**
 * Adds wordpress info to `wordpress` array
 * @returns {boolean} true if added, false if already existed
 */
account.methods.addWordpress = function addWordpress({
  email, siteUrl, domain, siteName, description, woocommerce, pluginVersion, wooVersion, wpVersion, selectedEvents,
}) {
  if(!this.wordpress) {
    this.wordpress = [];
  }
  let added = false;
  const foundSite = this.wordpress.find(i => i.url === siteUrl);
  if(foundSite) {
    if(foundSite && foundSite.uninstalledDate) {
      added = true;
    }
    foundSite.woocommerce = woocommerce;
    foundSite.uninstalledDate = null;
    foundSite.reinstalled = Date.now();
    foundSite.pluginVersion = pluginVersion;
    foundSite.wpVersion = wpVersion;
    foundSite.wooVersion = wooVersion;
    if(selectedEvents) {
      foundSite.selectedEvents = selectedEvents;
    }
  } else {
    added = true;
    this.wordpress.push({
      installed: Date.now(),
      email,
      name: siteName,
      url: siteUrl,
      description,
      woocommerce,
      pluginVersion,
      wooVersion,
      wpVersion,
      selectedEvents,
    });
    this.addHost(domain);
  }
  return added;
};

account.methods.removeWordpress = function removeWordpress(siteUrl) {
  const foundSite = this.wordpress && this.wordpress.find(i => i.url === siteUrl);
  if(foundSite) {
    foundSite.uninstalled = Date.now();
  }
  return foundSite;
};

// endregion

account.methods.hasWixSite = function hasWixSite() {
  return !!(this.wix && this.wix.length);
};

account.methods.canInstallWixSite = function canInstallWixSite() {
  if(this.settings && this.settings.ignoreWixSiteLimit) {
    return true;
  }
  // noinspection RedundantIfStatementJS
  if(this.wix && this.wix.length > 0) {
    return false;
  }
  return true;
};

account.methods.getWixSite = function (instanceId, fromRemoved) {
  let site = this.wix && this.wix.find(i => i.instanceId === instanceId);
  if(fromRemoved && !site) {
    site = this.removedWix && this.removedWix.find(i => i.instanceId === instanceId);
  }
  return site;
};

/**
 * @memberOf Account#
 */
account.methods.connectWixSite = function connectWixSite(site) {
  const { instanceId } = site;
  const removed = this.getWixSite(instanceId, true);
  if(removed) {
    removed.uninstalledDate = null;
    removed.installedDate = Date.now();
    this.wix.push(removed);
    this.removedWix.remove(removed);
  }

  const wixSite = this.getWixSite(instanceId, false);
  if(wixSite) {
    Object.assign(wixSite, site, {
      uninstalledDate: null,
    });
  } else {
    this.wix.push(Object.assign({}, site, {
      installedDate: Date.now(),
    }));
  }

  if(!this.installed) this.installed = Date.now();
  if(!this.onboarding.installed) this.onboarding.installed = Date.now();
  if(!this.onboarding.created) this.onboarding.created = Date.now();

  return { reinstall: !!removed };
};

account.methods.addThinkific = function addThinkific({
  gid, accessToken, refreshToken, subdomain, code, tokenExpires = Date.now() + 60 * 60 * 1000,
}) {
  const site = this.thinkific.find(s => s.gid === gid || s.subdomain === subdomain);
  if(site) {
    site.accessToken = accessToken;
    site.refreshToken = refreshToken;
    site.subdomain = subdomain;
    site.code = code;
    site.tokenExpires = tokenExpires;
    site.installed = Date.now();
    site.uninstalled = null;
  } else {
    this.thinkific.push({
      gid, code, refreshToken, accessToken, subdomain, tokenExpires, installed: Date.now(),
    });
  }
};

account.methods.removeThinkific = function removeThinkific({ gid, subdomain }) {
  const site = this.thinkific.find(s => s.gid === gid || s.subdomain === subdomain);
  if(site) {
    site.uninstalled = Date.now();
  }
};

/**
 * Get the shopify shop object from the account
 * @param {string|number} shop the shop's myshopify domain OR domain OR id
 * @return {object} shop object
 */
account.methods.getShop = function (shop) {
  if(!this.shopify || !this.shopify.length) return;

  return this.shopify.find(i => i.myshopify_domain === shop || i.domain === shop || i.id === shop);
};

account.methods.getRemovedShop = function (shop) {
  if(!this.removedShops || !this.removedShops.length) return;

  return this.removedShops.find(i => i.myshopify_domain === shop || i.domain === shop || i.id === shop);
};

account.methods.getActiveShop = function getActiveShop() {
  return this.shopify && this.shopify.find((s) => {
    const planName = s.plan_name;
    const negativeStatuses = Object.values(SHOPIFY.negativeStatus).join(',');
    const badPlan = negativeStatuses.includes(planName) || planName.includes('dormant');
    return !s.uninstalled && !badPlan;
  });
};

account.methods.getSubscriptionShop = function getSubscriptionShop() {
  const { subscriptionId, lastChargeId } = this.subscription || {};
  return this.shopify.find(s => s.chargeId === subscriptionId
    || s.chargeId === parseFloat(lastChargeId)) || null;
};

account.methods.isNewShop = function isNewShop() {
  const shop = this.getActiveShop && this.getActiveShop();
  if(!shop || !shop.shopCreated) {
    return false;
  }
  const creationDate = new Date(shop.shopCreated);
  if(Number.isNaN(creationDate.getTime())) {
    return false;
  }
  const now = new Date();
  const daysSinceCreation = (now - creationDate) / (1000 * 60 * 60 * 24);
  return daysSinceCreation < 30;
};


account.methods.isInstalled = function isInstalled() {
  return this.installed || _.get(this, 'onboarding.installed', null) || this.hosts.length;
};

account.methods.addHost = function (host) {
  if(!this.installed) this.installed = Date.now();
  if(!this.onboarding.installed) this.onboarding.installed = Date.now();
  if(!this.onboarding.created) this.onboarding.created = Date.now();

  if(!host || host === 'null' || host === 'undefined' || host.includes('provesrc.com')) return false;

  // allow cloudflare.works only for cloudflare_preview account
  const cloudFlareAcc = '5ad2f44be5b4d20cacba7df8';
  if(this._id && this._id.toString() !== cloudFlareAcc && host.includes('cloudflare')) return false;

  if(domainUtils.isProxyDomain(host)) return false;
  if(this.hosts.includes(host)) return false;

  this.hosts.push(host);
  return true;
};

account.methods.getName = function () {
  let name = null;
  const shop = this.getActiveShop();
  const bigcommerceStore = this.getActiveBigcommerceStore();
  if(this.google && this.google.name) {
    name = this.google.name;
  } else if(shop && shop.shop_owner) {
    name = shop.shop_owner;
  } else if(this.subscription && this.subscription.card && this.subscription.card.name) {
    name = this.subscription.card.firstName;
  } else if(bigcommerceStore && bigcommerceStore.firstName) {
    name = bigcommerceStore.firstName;
  }
  if(name) return name.split(' ')[0];
  return null;
};

account.methods.getInvoiceNames = function () {
  const retval = [];
  const invoiceCompanies = this.subscription && this.subscription.invoiceCompanies;
  const invoiceNames = this.subscription && this.subscription.invoiceNames;
  if(invoiceCompanies) {
    const trimmedNames = invoiceCompanies.map(name => name.trim());
    retval.push(...trimmedNames);
  }
  if(invoiceNames) {
    const trimmedNames = invoiceNames.map(name => name.trim());
    retval.push(...trimmedNames);
  }
  return [...(new Set(retval))];
};

account.statics.getBillingCycleDate = async function (accountId) {
  const account = await this.findOne({ _id: accountId });
  if(account) {
    return account.getBillingCycleDate();
  }
  return null;
};

account.statics.getGoals = async function (accountId) {
  const account = await this.findOne({ _id: accountId }, { goals: 1 });
  return account && account.goals;
};

account.methods.isGoalUrl = function (url) {
  if(!this.goals || !this.goals.length || !url) return false;

  const decoded = decodeURI(url);
  const goal = this.goals.filter(g => !g.codeTrack)
    .find(g => url.includes(g.url) || decoded.includes(g.url));
  return !!goal;
};

account.methods.getSiteIntegrations = function () {
  return [
    ...(this.wix || []).map(site => ({
      platform: PLATFORMS.wix,
      siteName: site.siteName,
      domain: site.url,
      installed: site.installedDate,
      uninstalled: site.uninstalledDate,
      adminLink: site.siteId ? `https://manage.wix.com/dashboard/${site.siteId}/home` : 'https://manage.wix.com/studio/sites',
      removeLink: site.siteId ? `https://manage.wix.com/dashboard/${site.siteId}/manage-installed-apps` : 'https://manage.wix.com/studio/sites',
    })),
    ...(this.removedWix || []).map(site => ({
      platform: PLATFORMS.wix,
      siteName: site.siteName,
      domain: site.url,
      installed: site.installedDate,
      uninstalled: site.uninstalledDate,
      adminLink: site.siteId ? `https://manage.wix.com/dashboard/${site.siteId}/home` : 'https://manage.wix.com/studio/sites',
      removeLink: site.siteId ? `https://manage.wix.com/dashboard/${site.siteId}/manage-installed-apps` : 'https://manage.wix.com/studio/sites',
    })),
    ...(this.shopify || []).map(shop => ({
      platform: PLATFORMS.shopify,
      siteName: shop.name,
      id: shop.id,
      domain: `https://${shop.domain}` || `https://${shop.myshopify_domain}`,
      installed: shop.installed,
      uninstalled: shop.uninstalled,
      adminLink: `https://${shop.myshopify_domain}/admin`,
      removeLink: `https://${shop.myshopify_domain}/admin/settings/apps?before=&after=&tab=installed`,
    })),
    ...(this.removedShops || []).map(shop => ({
      platform: PLATFORMS.shopify,
      siteName: shop.name,
      id: shop.id,
      domain: shop.domain || shop.myshopify_domain,
      installed: shop.installed,
      uninstalled: shop.uninstalled,
      adminLink: `https://${shop.myshopify_domain}/admin`,
      removeLink: `https://${shop.myshopify_domain}/admin/settings/apps?before=&after=&tab=installed`,
    })),
    ...(this.wordpress || []).map(site => ({
      platform: PLATFORMS.wordpress,
      siteName: site.name,
      domain: site.url,
      installed: site.reinstalled || site.installed,
      uninstalled: site.uninstalled,
      adminLink: `${site.url}/wp-admin`,
      removeLink: `${site.url}/wp-admin/plugins.php`,

    })),
    ...(this.bigcommerce || []).map(store => ({
      platform: PLATFORMS.bigcommerce,
      siteName: store.storeName,
      domain: `https://${store.domain}` || store.bigCommerceDomain,
      installed: store.installedDate,
      uninstalled: store.uninstalledDate,
      adminLink: `${store.bigCommerceDomain}/manage/dashboard`,
      removeLink: `${store.bigCommerceDomain}/manage/marketplace/apps/my-apps`,
    })),
    ...(this.removedBigcommerce || []).map(store => ({
      platform: PLATFORMS.bigcommerce,
      siteName: store.storeName,
      domain: `https://${store.domain}` || store.bigCommerceDomain,
      installed: store.installedDate,
      uninstalled: store.uninstalledDate,
      adminLink: `${store.bigCommerceDomain}/manage/dashboard`,
      removeLink: `${store.bigCommerceDomain}/manage/marketplace/apps/my-apps`,

    })),
    ...(this.thinkific || []).map(site => ({
      platform: PLATFORMS.thinkific,
      siteName: site.subdomain,
      domain: `${site.subdomain}.thinkific.com`,
      installed: site.installed,
      uninstalled: site.uninstalled,
      adminLink: `https://${site.subdomain}.thinkific.com/manage`,
      removeLink: `https://${site.subdomain}.thinkific.com/manage/apps`,
    })),
  ];
};

/** @class Account */
const Account = mongoose.model('Account', account);
module.exports = Account;
