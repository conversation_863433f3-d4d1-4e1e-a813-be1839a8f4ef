const { Schema } = require('mongoose');

const BigCommerce = new Schema({
  storeHash: { type: String, index: true },
  accessToken: { type: String, required: true },
  storeId: { type: String },
  status: { type: String },
  ownerId: { type: String },
  ownerEmail: { type: String },
  domain: { type: String },
  bigCommerceDomain: { type: String },
  secureUrl: { type: String },
  storeName: { type: String },
  adminEmail: { type: String },
  orderEmail: { type: String },
  firstName: { type: String },
  lastName: { type: String },
  country: { type: String },
  countryCode: { type: String },
  address: { type: String },
  phone: { type: String },
  language: { type: String },
  currency: { type: String },
  planName: { type: String },
  planLevel: { type: String },
  isTrial: { type: Boolean },
  industry: { type: String },
  logo: { type: String },
  installedDate: Date,
  uninstalledDate: { type: Date, default: null },
  features: {},
}, { _id: false });

module.exports = BigCommerce;
