const { Schema } = require('mongoose');

module.exports = new Schema({
  gid: { type: String, required: true, index: true },
  subdomain: { type: String, required: true, index: true },
  accessToken: { type: String, required: true },
  refreshToken: { type: String, required: true },
  tokenExpires: { type: Date },
  code: { type: String },
  installed: { type: Date },
  uninstalled: { type: Date },
  ignoreEvents: { type: Boolean },
}, { _id: false, timestamps: true });
