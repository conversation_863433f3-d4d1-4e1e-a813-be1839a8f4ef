const _ = require('lodash');
const parseDomain = require('parse-domain');
const isUrl = require('is-url');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const authTypes = require('../../middleware/authTypes');
const triggers = require('../../lib/triggers');
const redis = require('../common/redis.service').getClient();
const { dateUtils, cookieUtils, urlUtils } = require('../../lib/utils');
const logger = require('../../lib/logger/LoggerFactory')('account/configuration');
const notifierService = require('../common/notifier.service');

const Account = require('./models/Account');
const accountService = require('./account.service');
const Notification = require('../notifications/models/Notification');
const NotifConsts = require('../notifications/constants');
const UrlSuggestion = require('../common/urlSuggestions/models/UrlSuggestion');
const handleCookie = require('../visitorCount/handleCookie');
const cacheService = require('../common/cache.service');

const FIELDS = {
  PICK_POST: [
    'firstShowDelay',
    'displayHold',
    'delayBetweenNotifications',
    'allowOptOut',
    'optOutDays',
    'loop',
    'randomize',
    'affiliateLink',
    'branding',
    'trackers',
    'emailOpt',
    'forwardingWebhook',
    'blockGoogleFonts',
    'filters',
    'zindex',
  ],
  apiGETOmit: [
    'sendInvoices',
    'specialWhitelabel',
    'disableShopifyPaywall',
    'ignoreShopify',
    'emailOpt',
    'blockedDomains',
  ],
};

module.exports = {
  config: {
    methods: ['GET', 'POST'],
    authType: {
      GET: [authTypes.api, authTypes.console],
      POST: authTypes.console,
    },
    checkBot: true,
  },
  schema: {
    type: 'object',
    properties: {
      firstShowDelay: { type: 'number' },
      displayHold: { type: 'number' },
      delayBetweenNotifications: { type: 'number' },
      allowOptOut: { type: 'boolean' },
      optOutDays: { type: 'number' },
      whitelabel: { type: 'boolean' },
      loop: { type: 'boolean' },
      randomize: { type: 'boolean' },
      host: { type: 'string' },
      affiliateLink: { type: 'boolean' },
      forwardingWebhook: {
        type: 'object',
        properties: {
          enabled: { type: 'boolean' },
          url: { type: 'string', maxLength: 2048 },
        },
      },
      blockGoogleFonts: { type: 'boolean' },
      zindex: {
        desktop: { type: 'number' },
        mobile: { type: 'number' },
      },
      // this is sent back due to GET /configuration, added here just to pass validation
      wixSites: {
        type: 'array',
        maxItems: 20,
        items: {
          type: 'object',
          properties: {
            siteName: { type: 'string', maxLength: 100 },
            instanceId: { type: 'string', maxLength: 100 },
          },
          required: ['siteName', 'instanceId'],
        },
      },
      filters: {
        type: 'object',
        additionalProperties: false,
        properties: {
          names: {
            type: 'array',
            maxItems: 100,
            items: {
              type: 'string',
              maxLength: 50,
            },
          },
          emails: {
            type: 'array',
            maxItems: 100,
            items: {
              type: 'string',
              maxLength: 50,
            },
          },
          phrases: {
            type: 'array',
            maxItems: 100,
            items: {
              type: 'string',
              maxLength: 50,
            },
          },
          ips: {
            type: 'array',
            maxItems: 100,
            items: {
              type: 'string',
              maxLength: 50,
            },
          },
        },
      },
      branding: {
        type: 'object',
        additionalProperties: false,
        properties: {
          active: { type: 'boolean', required: true },
          text: { type: 'string', required: true },
          link: { type: 'string' },
          color: { type: 'string', required: true },
          forceColor: { type: 'boolean' },
        },
      },
      trackers: {
        type: 'object',
        additionalProperties: false,
        properties: {
          googleAnalytics: { type: 'boolean' },
          gaKey: { type: 'string' },
          mixpanel: { type: 'boolean' },
          amplitude: { type: 'boolean' },
        },
      },
    },
  },
  handle(req, res, next) {
    if(req.method === 'POST') handlePOST(req, res, next);
    else handleGET(req, res, next);
  },
};

async function handlePOST(req, res, next) {
  try {
    const { accountId, email } = req.locals;
    const { forwardingWebhook } = req.body;
    if(forwardingWebhook && forwardingWebhook.url) {
      if(!isUrl(forwardingWebhook.url)) {
        throw ErrorFactory('Webhook is not a valid URL', 400);
      }
      if(!forwardingWebhook.url.includes('https://')) {
        throw ErrorFactory('Webhook must use HTTPS', 400);
      }
      if(forwardingWebhook.url.includes('provesrc.com')) {
        notifierService.notifyError(null, 'someone added provesrc.com as forwarding webhook', {
          email, accountId, forwardingWebhook,
        });
      }
      // Reset error tracking when webhook is updated
      forwardingWebhook.errorCount = 0;
      forwardingWebhook.lastError = null;
    }
    // const account = await accountDAL.get(accountId);
    const account = await Account.findOne({ _id: accountId });
    if(!account) throw ErrorFactory.AccountNotFound(accountId);
    if(!account.configuration) {
      account.configuration = {};
    }
    const config = account.configuration && account.configuration.toObject();

    Object.assign(config, _.pick(req.body, FIELDS.PICK_POST));
    if(account.canUseWhitelabel() && req.body.hasOwnProperty('whitelabel')) {
      config.whitelabel = req.body.whitelabel;
    }
    account.configuration = config;
    account.markModified('configuration');
    await Promise.all([
      account.save(),
      accountService.clearCache(account.id),
      redis.delAsync(`webhook:${accountId}`),
      cacheService.memDelete(`config:${accountId}`),
    ]);
    res.body = config;
    next();
  } catch(err) {
    next(err);
  }
}

function handleGET(req, res, next) {
  // req.query.host is for legacy reasons (can be removed in the future)
  const isApiCall = !!(req.query
    && (req.query.url || req.query.host)
    && req.jwtData && req.jwtData.accountId);
  if(isApiCall) return apiGET(req, res, next);
  return consoleGET(req, res, next);
}

async function consoleGET(req, res, next) {
  try {
    const { accountId } = req.locals;
    const account = await Account.findOne({ _id: accountId });
    if(!account) {
      throw ErrorFactory.AccountNotFound(accountId);
    }

    const config = (account && account.configuration && account.configuration.toObject()) || {};
    config.affiliateId = account.affiliate.id || null;
    config.affiliateLink = config.affiliateLink || false;
    config.console = true;
    config.kind = account.kind || null;

    if(!account.canUseWhitelabel()) {
      config.whitelabel = (void 0);
    }

    if(account.wix && account.wix.length > 0) {
      config.wixSites = account.wix.map(store => ({
        siteName: store.siteName,
        instanceId: store.instanceId,
      }));
    }
    res.body = config;
    next();
  } catch(err) {
    next(err);
  }
}

async function apiGET(req, res, next) {
  try {
    // We can extract it so the rest of the function doesn't wait for it?
    // It could cause responses to return before we put a cookie on them
    // handleCookie(req, res)
    const { accountId } = req.jwtData;
    const cacheKey = `config:${accountId}`;
    let account = cacheService.memget(cacheKey);
    if(!account) {
      account = await accountService.getAccount(accountId, { cache: 60 });
      cacheService.memset(cacheKey, account, {});
    }
    if(!account || account.deleted) {
      throw ErrorFactory.AccountNotFound();
    }
    if(!account.active) {
      throw ErrorFactory('account not activated', 403, { accountId });
    }

    const [live, shouldGet] = await Promise.all([
      Notification.findOne({
        accountId, type: NotifConsts.notificationTypes.live, active: true,
      }, { _id: 1 }).cache(30).exec(),
      handleCookie(req, res, { timestamp: account.getBillingCycleEndDate().getTime() }),
    ]);

    let host;
    const parsed = parseDomain(req.headers.origin);
    if(parsed) {
      host = `${parsed.domain}.${parsed.tld}`;
      if(parsed.subdomain && parsed.subdomain.length) {
        host = `${parsed.subdomain}.${host}`;
      }
    } else {
      host = req.headers.origin;
    }

    const didInstall = !!account.onboarding.installed;
    if(!req.locals.isBot && account.addHost(host)) {
      triggers.installedScript(account, host);
      if(!didInstall) {
        triggers.onboardingComplete(account, host);
      }
    }

    const url = Buffer.from(req.query.url, 'base64').toString('utf-8');
    const accountConfig = (account.configuration && account.configuration.toObject()) || {};
    const omitFields = [...FIELDS.apiGETOmit];
    const customCode = accountConfig && accountConfig.customCode;
    if(!customCode) {
      omitFields.push('customCode');
    }
    if(!account.canUseWhitelabel()) {
      omitFields.push('whitelabel');
    }
    const retConfig = _.omit(accountConfig, omitFields);
    retConfig.remarketing = null;
    retConfig.affiliateId = account.affiliate.id || null;
    retConfig.affiliateLink = retConfig.affiliateLink || false;
    retConfig.hasLive = !!live;
    retConfig.stop = !shouldGet || (account.hasWixSite() && url.includes('/checkout' && url.includes('checkoutId')));

    const blockedDomains = _.get(account, 'configuration.blockedDomains', null);
    if(blockedDomains && blockedDomains.length && host) {
      const isBlocked = blockedDomains.find(d => host.includes(d));
      if(isBlocked) {
        retConfig.stop = true;
        retConfig.blocked = true;
      }
    }

    retConfig.shouldSendGoals = false;
    if(!req.locals.isBot && req.query.url && req.query.url.length) {
      try {
        if(url.includes(host)) {
          updateUrlSuggestions(accountId, url);
        }
        retConfig.shouldSendGoals = account.isGoalUrl(url);
      } catch(err) {}
    }

    const psLoginCookie = cookieUtils.getCookie(req, 'ps_login');
    // remarketing to the user
    const sameId = (accountId === req.session.accountId || accountId === psLoginCookie);
    if(sameId) {
      retConfig.dashboardDebugMode = (cookieUtils.getCookie(req, 'ps_preview_mode') === 'true');

      const [redisRes, notification] = await Promise.all([
        redis.hmgetAsync(accountId, 'limit', 'visitorCount', 'expires').catch(() => {}),
        Notification.findOne({ accountId }, { _id: 1 }).cache(30).exec(),
      ]);
      retConfig.remarketing = getRemarketing(notification, account, redisRes);
      if(retConfig.remarketing.wizard && !account.remarketing.wizard) {
        account.remarketing.wizard = Date.now();
        triggers.remarketingSent(account.email, triggers.REMARKETING_TYPES.wizard);
      }

      const planLimitRemarketingNeverShown = !account.remarketing.planLimit;
      // eslint-disable-next-line max-len
      const oneDaySinceRemarketing = account.remarketing.planLimit + dateUtils.MILLISECONDS_IN_DAY < Date.now();
      const remarketingPlanLimit = !!retConfig.remarketing.planLimit;
      if(remarketingPlanLimit && (planLimitRemarketingNeverShown || oneDaySinceRemarketing)) {
        account.remarketing.planLimit = Date.now();
        triggers.remarketingSent(account.email, triggers.REMARKETING_TYPES.planLimit);
      }
    }

    const lastRequest = _.get(account, 'stats.lastClientRequest');
    if(!lastRequest || lastRequest < Date.now() - 600 * 1000) {
      if(!account.stats) {
        account.stats = {};
      }
      account.stats.lastClientRequest = Date.now();
    }

    if(account.isModified()) {
      Promise.all([
        account.save(),
        accountService.clearCache(account.id),
      ]).catch(() => {});
    }

    res.body = retConfig;
    next();
  } catch(err) {
    next(err);
  }
}

function getRemarketing(notification, account, redisResults) {
  const retval = {};
  if(!notification) {
    retval.wizard = true;
  }

  if(redisResults) {
    let [limit, visitors, expires] = redisResults;
    if(limit && visitors && expires) {
      limit = parseInt(limit, 10);
      visitors = parseInt(visitors, 10);
      expires = new Date(expires);
      if(visitors > limit) {
        retval.planLimit = {
          limit,
          passed: visitors - limit,
          days: dateUtils.daysBetween(expires, Date.now()),
          shopify: account.isShopify(),
          showCoupon: !account.isSubscriptionActive(),
        };
      }
    }
  }
  return retval;
}

function updateUrlSuggestions(accountId, url) {
  const cleanUrl = urlUtils.transform(url);
  const normalizedUrl = urlUtils.normalize(cleanUrl);

  if(urlUtils.validUrlSuggestion(normalizedUrl)) {
    UrlSuggestion.update(
      { accountId, url: normalizedUrl },
      { $set: { lastHit: Date.now() }, $inc: { hits: 1 } },
      { upsert: true },
    ).catch((err) => {
      const msg = 'failed to update urlSuggestion';
      logger.error({ accountId, normalizedUrl, err }, msg);
    });
  }
}
