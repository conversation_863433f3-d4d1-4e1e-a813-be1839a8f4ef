const _ = require('lodash');
const authTypes = require('../../middleware/authTypes');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const config = require('../../config');
const maxmind = require('../../lib/maxmind');

const Account = require('./models/Account');
const bluesnap = require('../../lib/apis/bluesnap').getClient(config.bluesnap.apiKey, config.bluesnap.password, config.env);
const creditCardUtils = require('../../lib/utils/creditCardUtils');

const slack = require('../../lib/apis/slackNotifier');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {
      firstName: { type: 'string', required: true },
      lastName: { type: 'string', required: true },
      cardNumber: { type: 'string', required: true },
      expiry: { type: 'string', required: true },
      cvv: {
        type: 'string', minLength: 3, maxLength: 4, required: true,
      },
    },
  },
  async handle(req, res, next) {
    const { accountId } = req.locals;
    let account = null;
    let data = null;
    try {
      account = await Account.findOne({ _id: accountId });
      if(!account) throw ErrorFactory.AccountNotFound(accountId);

      const shopperId = _.get(account, 'subscription.bluesnapId', null);
      const subscriptionId = _.get(account, 'subscription.subscriptionId', null);

      if(!account.isSubscriptionActive() || !shopperId || !subscriptionId) {
        throw ErrorFactory('missing subscription details');
      }

      const { expiry } = req.body;
      const [expMonth, expYear] = expiry.split('/').map(i => i.trim());

      const validationErr = getValidationError({ expMonth, expYear });
      if(validationErr) throw validationErr;

      data = _.pick(req.body, ['firstName', 'lastName', 'cardNumber', 'cvv']);

      data.ip = req.remoteAddress;
      const location = await maxmind.geoIP(data.ip);
      data.countryCode = _.get(location, 'countryCode', 'US');
      if(data.countryCode === 'US') {
        data.stateCode = _.get(location, 'stateCode', 'NY');
      } else if(data.countryCode === 'CA') {
        data.stateCode = _.get(location, 'stateCode', 'ON');
      }

      if(expMonth.length === 1) data.expMonth = `0${expMonth}`;
      else data.expMonth = expMonth;
      if(expYear.length === 2) data.expYear = `20${expYear}`;
      else data.expYear = expYear;

      let bsRes = await bluesnap.addCard(shopperId, data);
      if(bsRes.status !== 204) {
        throw ErrorFactory(`failed to add card (status code ${bsRes.status})`, 500);
      }

      const cardType = creditCardUtils.getVendor(data.cardNumber);
      const fourDigits = creditCardUtils.getFourDigits(data.cardNumber);
      bsRes = await bluesnap.changeCard(subscriptionId, fourDigits, cardType);
      if(bsRes.status !== 204) {
        throw ErrorFactory(`failed to change card (status code ${bsRes.status})`, 500);
      }

      account.subscription.card = {
        firstName: data.firstName,
        lastName: data.lastName,
        expDate: `${expMonth}/${expYear}`,
        fourDigits,
        brand: cardType,
      };
      await account.save();

      slack.notify(`updated card ${account.email}`, null, { webhook: config.slack.ipns });

      next();
    } catch(err) {
      const message = _.get(err, 'data.messages.message.description')
        || _.get(err, 'data.messages.message[0].description') || err.message;

      let slackMsg = 'failed to update card';
      if(account) slackMsg += ` ${account.email} (${accountId})`;
      slack.notifyError(err, slackMsg, { data: _.omit(data, ['cardNumber']) });

      let frontMsg = 'failed to update card';
      if(message) frontMsg += ` (${message})`;
      next(ErrorFactory(frontMsg, 500, err));
    }
  },
};

function getValidationError({ expMonth, expYear }) {
  if(expMonth.length !== 2 && expMonth.length !== 1) {
    return ErrorFactory('bad card expiration expMonth format', 400, { month: expMonth });
  }
  const monthNum = parseInt(expMonth);
  if(isNaN(monthNum) || monthNum === 0) {
    return ErrorFactory('bad card expiration expMonth format', 400, { month: expMonth });
  }

  if(expYear.length !== 2 && expYear.length !== 4) {
    return ErrorFactory('bad card expiration expYear format', 400, { year: expYear });
  }
  const yearInt = parseInt(expYear);
  if(isNaN(yearInt) || yearInt === 0) {
    return ErrorFactory('bad card expiration expYear format', 400, { year: expYear });
  }
}
