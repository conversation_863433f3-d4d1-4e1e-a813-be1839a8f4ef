/** Created by natanavra on 26/02/2018 */

const impression = 'impression';
const conversion = 'conversion';

module.exports = {
  eventTypesArray: [impression, conversion],
  eventTypes: { impression, conversion },
  maxUrlLength: 900,
  eventNames: {
    StreamEvent: 'StreamEvent',
    WebhookStreamEvent: 'Webhook Event',
    WooEvent: 'WooCommerce Order',
    WPEvent: 'Wordpress Signup',
    Magento1Event: 'Magento 1 Order',
    Magento2Event: 'Magento 2 Order',
    ShopifyEvent: 'Shopify Order',
    CleanFormStreamEvent: 'Form Submission',
    FormStreamEvent: 'Form Submission',
    WixEvent: 'Wix Order',
    BigcommerceEvent: 'BigCommerce Order',
    ThinkificEvent: 'Thinkific Event',
  },
  MAP_HASH_PASS: '12P8jiB!',
  MAP_BASE_URL: 'https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/maps',
};
