const async = require('async');
const urlModule = require('url');

const config = require('../../config');
const constants = require('./constants');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const logger = require('../../lib/logger')('events-tracker');
const urlUtils = require('../../lib/utils/urlUtils');
const dateUtils = require('../../lib/utils/dateUtils');
const authTypes = require('../../middleware/authTypes');
const maxmind = require('../../lib/maxmind');

const WebsiteEvent = require('./models/WebsiteEvent');
const CleanWebsiteEvent = require('./models/CleanWebsiteEvent');
const WebhookEvent = require('./models/WebhookEvent');
const WebhookStreamEvent = require('./models/WebhookStreamEvent');

function track(accountId, type, normalizedUrl, date, minute) {
  const { host, pathname, query } = urlModule.parse(normalizedUrl);
  const shortUrl = normalizedUrl.substr(0, constants.maxUrlLength);

  const countKey = `counts.${minute}`;
  const eventType = type || constants.eventTypes.impression;

  const find = {
    accountId,
    date,
    shortUrl,
    type: eventType,
  };
  const $setOnInsert = {
    url: normalizedUrl,
    day: date,
    host,
    pathname,
    query,
    firstMinute: minute,
  };
  const update = { $inc: { [countKey]: 1, total: 1 }, $setOnInsert };
  return WebsiteEvent.update(find, update, { upsert: true }).hint(WebsiteEvent.getHint());
}

function trackClean(accountId, cleanUrl, date, minute) {
  const shortUrl = cleanUrl.substr(0, constants.maxUrlLength);
  const countKey = `counts.${minute}`;
  const find = { accountId, date, shortUrl };
  const $setOnInsert = { firstMinute: minute, url: cleanUrl };
  const update = { $inc: { [countKey]: 1, total: 1 }, $setOnInsert };
  return CleanWebsiteEvent.update(find, update, { upsert: true }).hint(CleanWebsiteEvent.getHint());
}

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.api,
  },
  schema: {
    type: 'object',
    properties: {
      // TODO: use a regex?
      url: { type: 'string', minLength: 1, required: true },
      type: { enum: constants.eventTypesArray },
    },
  },
  handle(req, res, next) {
    module.exports.trackUrl(req.body.url, req.body.type, req.jwtData.accountId);
    res.body = { message: 'success' };
    next();
  },

  trackUrl(urlArg, type, accountId) {
    const date = dateUtils.todayNormalized12am();
    const minuteOfDay = dateUtils.minuteOfTheDay();
    const normalized = urlUtils.normalize(urlArg);
    const cleanUrl = urlUtils.clean(urlArg);


    Promise.all([
      track(accountId, type, normalized, date, minuteOfDay),
      trackClean(accountId, cleanUrl, date, minuteOfDay),

    ]).then((results) => {
      logger.info({ url: urlArg, results }, 'trackUrl updated db');
    }).catch((err) => {
      logger.error({ url: urlArg, err, critical: true }, 'trackUrl failed to update db');
    });
  },

  trackWebhook(webhookId, options) {
    const opts = options || {};
    const guid = opts.guid || null;

    const day = dateUtils.todayNormalized12am();
    const date = day;
    const minuteOfDay = dateUtils.minuteOfTheDay();
    const countKey = `counts.${minuteOfDay}`;

    const find = {
      accountId: opts.accountId,
      webhookId,
      guid,
      day,
      type: constants.eventTypes.conversion,
    };
    const update = { $inc: { [countKey]: 1 }, $setOnInsert: { date } };
    WebhookEvent.update(find, update, { upsert: true, w: 0 }, (err, result) => {
      if(err) logger.error({ guid, webhookId, err }, 'trackWebhook failed to update db');
      else logger.info({ guid, webhookId, result }, 'trackWebhook updated db');
    });

    const email = opts.email || null;
    if(email) {
      const {
        firstName,
        lastName,
        accountId,
        ip,
        timestamp,
        location,
      } = opts;
      const now = Date.now();
      const eventTimestamp = Math.min(now, timestamp || now);

      const params = {
        accountId, date: eventTimestamp, email, ip, location, webhookId, guid, firstName, lastName,
      };
      const streamEvent = new WebhookStreamEvent(params);
      const promises = [maxmind.geoIP(ip)];
      if(config.getSocialProfiles) {
        promises.push(streamEvent.addGravatar());
        promises.push(streamEvent.addPicasa());
      }
      Promise.all(promises).then((results) => {
        streamEvent.location = Object.assign(results[0] || {}, location);
        return streamEvent.save();
      }).then((savedEvent) => {
        logger.info({ event: savedEvent }, 'saved stream event');
      }).catch((err) => {
        logger.error({
          email, guid, webhookId, err,
        }, 'failed to save stream event');
      });
    } else {
      logger.info({ webhookId, options: opts }, 'no email for stream event');
    }
  },
};
