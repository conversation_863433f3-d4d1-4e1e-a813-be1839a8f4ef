const mongoose = require('mongoose');

const { Schema } = mongoose;
const CONSTANTS = require('../constants');

const event = new Schema({
  accountId: { type: Schema.Types.ObjectId, required: true },
  date: { type: Date, required: true },
  url: {
    type: String,
    required: true,
    set(value) {
      this.shortUrl = value.substr(0, CONSTANTS.maxUrlLength);
      return value;
    },
  },
  shortUrl: { type: String, maxlength: CONSTANTS.maxUrlLength },
  total: Number,
  firstMinute: Number,
  counts: {},
}, { timestamps: true, collection: 'cleanWebsiteEvents' });

event.index({ date: -1 }, { expires: '120d' });

const selectedIndex = { accountId: 1, date: -1, shortUrl: 1 };
event.index(selectedIndex);
event.statics.getHint = () => (selectedIndex);

/** @memberOf Event */
event.methods.getTotal = function (toMinute) {
  if(!toMinute || toMinute < 1) {
    return { sum: this.total, minMinute: this.firstMinute };
  }

  let sum = 0;
  let minMinute;
  const counts = Object.keys(this.counts);
  for(let i = counts.length - 1; i >= 0; i--) {
    const minute = parseInt(counts[i], 10);
    if(minute < toMinute) break;

    minMinute = minute;
    sum += this.counts[minute];
  }
  return { sum, minMinute };
};

event.methods.getTime = function () {
  return this.date.getTime();
};

/** @class Event */
module.exports = mongoose.model('CleanWebsiteEvent', event);
