const countriesTranslate = require('i18n-iso-countries');
const _ = require('lodash');
const mongoose = require('mongoose');
const gravatar = require('../../../lib/apis/gravatar');
const picasa = require('../../../lib/apis/picasa');
const { stringUtils, cryptoUtils } = require('../../../lib/utils');
const { MAP_HASH_PASS, MAP_BASE_URL } = require('../constants');

const stream = new mongoose.Schema({
  accountId: { type: mongoose.SchemaTypes.ObjectId, required: true },
  date: { type: Date, required: true },
  email: { type: String, required: true },
  ip: { type: String },
  location: {
    city: { type: String },
    country: { type: String },
    countryCode: { type: String },
    state: { type: String },
    stateCode: { type: String },
  },
  firstName: String,
  lastName: String,
  gravatar: {},
  picasa: {},
}, { collection: 'streamEvents' });

stream.index({ accountId: 1, date: -1 });

/** @memberOf StreamEvent */
stream.methods.getName = function (anonymize) {
  const someone = 'Someone';
  if(anonymize) return { name: someone, initials: null, anonymous: true };

  let initials = null;
  let name = [this.firstName, this.lastName].join(' ').trim();

  if(!name) {
    name = _.get(this.gravatar, 'name.formatted',
      _.get(this.gravatar, 'name.givenName',
        _.get(this.gravatar, 'displayName',
          _.get(this.gravatar, 'preferredUsername'))));
  }

  if(name) {
    initials = stringUtils.getInitials(name);
    const comps = name.split(' ');
    const firstPart = comps[0].toLowerCase();
    if(comps.length > 1 && (
      firstPart.startsWith('mr')
      || firstPart.startsWith('ms')
      || firstPart.startsWith('mis')
      || firstPart.startsWith('dr')
    )) {
      name = comps[1];
    } else {
      name = comps[0];
    }
    if(name && name.split) {
      name = name.split('@')[0];
    }
  }

  return { name: name || someone, initials, anonymous: !name };
};

stream.methods.getLocation = function getLocation(locale) {
  let retval = this.location.toObject();
  const countryCode = _.get(retval, 'countryCode');
  if(countryCode && countryCode !== 'US') {
    retval = _.pick(retval, ['city', 'country', 'countryCode']);
  }
  if(retval && retval.country && retval.country.length && retval.country === retval.city) {
    retval = _.omit(retval, ['country']);
  }
  if(retval && retval.country && !retval.countryCode && retval.country.length === 2) {
    retval.countryCode = retval.country;
  }
  if(locale !== 'en') {
    const translatedCountry = countriesTranslate.getName(countryCode, locale || 'en');
    if(retval && retval.country && translatedCountry) {
      retval.country = translatedCountry;
    }
  }
  return retval || {
    country: null,
    countryCode: null,
    state: null,
    stateCode: null,
    city: null,
  };
};

stream.methods.getPhoto = function getPhoto() {
  return null;
  /*
  let retval = null;
  const hash = this.gravatar && this.gravatar.hash;
  if (hash) {
    const defaultImg = 'https://cdn.provesrc.com/placeholder-profile.png';
    retval = `https://gravatar.com/avatar/${hash}?s=72&d=${defaultImg}`;
  }
  return retval;
  */
};

/**
 * @param {object?} filter
 * @param {boolean} filter.include
 * @param {string[]} filter.values
 * @returns {{name, link, image}|null} product
 */
stream.methods.getProduct = function getProduct(filter) {
  const { products } = this;
  if(!products || !products.length) {
    return null;
  }

  let filteredProducts;
  if(filter && filter.values && filter.values.length > 0) {
    filteredProducts = _.filter(products, (n) => {
      for(let i = 0; i < filter.values.length; i += 1) {
        if(n.name.toLowerCase().includes(filter.values[i].toLowerCase())) {
          return filter.include;
        }
      }
      return !filter.include;
    });
  } else {
    filteredProducts = products;
  }

  const maxPriceProd = _.maxBy(filteredProducts, 'price') || filteredProducts[0];
  return maxPriceProd && maxPriceProd.name && _(maxPriceProd).pick(['name', 'link', 'image']).omitBy(_.isNull).value();
};

/**
 * @memberOf StreamEvent
 */
stream.methods.addGravatar = function () {
  if(this.gravatar && this.gravatar.id) return true;

  return gravatar.getProfile(this.email).then((profile) => {
    this.gravatar = profile;
    return !!profile;
  });
};

/** @memberOf StreamEvent */
stream.methods.addPicasa = function () {
  if(this.picasa && this.picasa.author) return true;

  return false;
  /**
   * return picasa.getProfile(this.email).then(profile => {
   *    this.picasa = profile;
   *    return !!profile;
   *   });
   */
};

stream.methods.getMapIcon = function getMapIcon() {
  const location = this.location || {};
  const locComps = [];
  if(location.country) locComps.push(location.country);
  if((location.country === 'United States' || location.countryCode === 'US')
    && location.state) {
    locComps.push(location.state);
  }
  if(location.city) locComps.push(location.city);
  const loc = locComps.join(',').toLowerCase();

  const hash = cryptoUtils.secureMd5(loc, MAP_HASH_PASS);
  return encodeURI(`${MAP_BASE_URL}/${hash}-${loc}.png`);
};

/** @class StreamEvent */
module.exports = mongoose.model('StreamEvent', stream);
module.exports.schema = stream;
