const mongoose = require('mongoose');

const { Schema } = mongoose;
const BaseEvent = require('./Event');
const constants = require('../constants');

const event = new Schema({
  webhookId: { type: String, required: true },
  type: { type: String, default: constants.eventTypes.conversion },
  guid: { type: Schema.Types.Mixed },
});

event.index({ webhookId: 1, date: -1 });

event.statics.getHint = (guid) => {
  if(guid) {
    return { webhookId: 1, guid: 1, day: -1 };
  }
  return { webhookId: 1, day: -1 };
};

event.statics.getDateKey = () => 'day'; // TODO: change this to 'date' after Aug 1st 2019

/** @class WebhookEvent */
module.exports = BaseEvent.discriminator('WebhookEvent', event);
