const mongoose = require('mongoose');

const { Schema } = mongoose;
const constants = require('../constants');

const event = new Schema({
  accountId: { type: Schema.Types.ObjectId, required: true },
  day: { type: Date, required: true },
  date: { type: Date },
  type: { type: String, enum: constants.eventTypesArray },
  total: Number,
  firstMinute: Number,
  counts: {},
}, { timestamps: true });

event.index({ accountId: 1, date: -1 });
event.index({ date: -1 }, { expires: '120d' });

/** @memberOf Event */
event.methods.getTotal = function getTotal(toMinute = -1) {
  if((!toMinute || toMinute < 1) && this.total > 0) {
    return { sum: this.total, minMinute: this.firstMinute };
  }
  let sum = 0;
  let minMinute;
  const counts = Object.keys(this.counts);
  for(let i = counts.length - 1; i >= 0; i--) {
    const minute = parseInt(counts[i], 10);
    if(minute < toMinute) break;

    minMinute = minute;
    sum += this.counts[minute];
  }
  return { sum, minMinute };
};

event.methods.getTime = function () {
  if(this.date) return this.date.getTime();

  return this.day.getTime();
};

event.statics.getDateKey = () => 'date';

/** @class Event */
module.exports = mongoose.model('Event', event);
