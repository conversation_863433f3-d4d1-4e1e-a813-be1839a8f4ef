const _ = require('lodash');
const mongoose = require('mongoose');
const StreamEvent = require('./StreamEvent');

const Product = new mongoose.Schema(require('./Product'), { _id: false });

Product.path('id').required(true);
Product.path('quantity').required(true);
Product.path('price').required(true);

const Event = Object.assign({
  accountId: { type: mongoose.SchemaTypes.ObjectId, required: true },
  host: { type: String, required: true, index: true },
  frontUrl: { type: String, index: true },
  url: { type: String, index: true },
  total: { type: Number },
  currency: { type: String },
  products: { type: [Product] },

}, StreamEvent.schema.obj);

const MagentoEvent = new mongoose.Schema(Event, { collection: 'magento1Events' });
MagentoEvent.index({ accountId: 1, date: -1 });

MagentoEvent.path('products').validate(products => products && products.length > 0, 'products array must contain at least 1 product');

MagentoEvent.methods = Object.assign({}, StreamEvent.schema.methods);

MagentoEvent.statics.getHint = function () {
  return { accountId: 1, date: -1 };
};

module.exports = mongoose.model('Magento1Event', MagentoEvent);
