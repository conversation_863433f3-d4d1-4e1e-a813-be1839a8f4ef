const mongoose = require('mongoose');
const StreamEvent = require('./StreamEvent');

const { Schema } = mongoose;
const CONSTANTS = require('../constants');

const event = new mongoose.Schema({
  accountId: { type: Schema.Types.ObjectId, required: true },
  date: { type: Date, required: true },
  url: {
    type: String,
    required: true,
    set(value) {
      this.shortUrl = value.substr(0, CONSTANTS.maxUrlLength);
      return value;
    },
  },
  shortUrl: { type: String, maxlength: CONSTANTS.maxUrlLength },

  email: { type: String, required: true },
  ip: { type: String },
  location: {
    city: { type: String },
    country: { type: String },
    countryCode: { type: String },
    state: { type: String },
    stateCode: { type: String },
  },
  firstName: String,
  lastName: String,
  gravatar: {},
  picasa: {},

  submissionId: { type: String, index: true },
  user_uid: { type: String },

}, { timestamps: true, collection: 'cleanFormStreamEvents' });

const selectedIndex = { accountId: 1, date: -1, shortUrl: 1 };
event.index(selectedIndex);
event.statics.getHint = () => (selectedIndex);

event.methods = StreamEvent.schema.methods;

/**
 * @class CleanFormStreamEvent
 * @extends StreamEvent
 */
module.exports = mongoose.model('CleanFormStreamEvent', event);
