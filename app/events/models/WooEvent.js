const _ = require('lodash');
const mongoose = require('mongoose');
const StreamEvent = require('./StreamEvent');

const Product = new mongoose.Schema(require('./Product'), { _id: false });

const WooEventObject = Object.assign({
  accountId: { type: mongoose.SchemaTypes.ObjectId, required: true },
  host: { type: String, required: true, index: true },
  total: { type: Number },
  currency: { type: String },
  products: { type: [Product], required: true },
  orderId: { type: String },
  ips: { type: [String] },
  pluginVersion: { type: String },

}, StreamEvent.schema.obj);

const WooEvent = new mongoose.Schema(WooEventObject, { collection: 'wooEvents' });
WooEvent.index({ accountId: 1, date: -1 });
WooEvent.index({ accountId: 1, host: 1, orderId: 1 });
WooEvent.index({ accountId: 1, host: 1, date: -1 });

// WooEvent.path('products').validate(products => products && products.length > 0, 'products array must contain at least 1 product');

WooEvent.methods = Object.assign({}, StreamEvent.schema.methods);

WooEvent.statics.getHint = function () {
  return { accountId: 1, date: -1 };
};

module.exports = {
  model: mongoose.model('WooEvent', WooEvent),
  schemaObj: WooEvent.obj,
  schema: WooEvent,
};
