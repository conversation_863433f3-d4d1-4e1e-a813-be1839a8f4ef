const mongoose = require('mongoose');

const formEventDataSchema = new mongoose.Schema({
  accountId: { type: mongoose.SchemaTypes.ObjectId, required: true, index: true },
  url: { type: String },
  body: Object,
  query: Object,
  headers: Object,
}, {
  timestamps: { createdAt: 'date' },
  collection: 'formEventData',
});

formEventDataSchema.index({ createdAt: -1 }, { expires: '7d' });

module.exports = mongoose.model('FormEventData', formEventDataSchema);
