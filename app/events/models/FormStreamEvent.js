const mongoose = require('mongoose');
const StreamEvent = require('./StreamEvent');
const urlUtils = require('../../../lib/utils/urlUtils');
const CONSTANTS = require('../constants');

const formStream = new mongoose.Schema({
  submissionId: { type: String, index: true },
  user_uid: { type: String },
  url: {
    type: String,
    required: true,
    lowercase: true,
    set(value) {
      const normalized = urlUtils.normalize(value);
      this.shortUrl = normalized.substr(0, CONSTANTS.maxUrlLength);
      return normalized;
    },
  },
  shortUrl: { type: String, maxlength: CONSTANTS.maxUrlLength },
  host: { type: String, lowercase: true, required: true },
  pathname: { type: String, lowercase: true, required: true },
  query: { type: String, lowercase: true },
});

const selectedIndex = { accountId: 1, date: -1, shortUrl: 1 };
formStream.index(selectedIndex);
formStream.statics.getHint = () => (selectedIndex);

/**
 * @class FormStreamEvent
 * @extends StreamEvent
 */
const FormStreamEvent = StreamEvent.discriminator('FormStreamEvent', formStream);
module.exports = FormStreamEvent;
