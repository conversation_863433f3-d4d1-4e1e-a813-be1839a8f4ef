const mongoose = require('mongoose');
const StreamEvent = require('./StreamEvent');

const WPEvent = new mongoose.Schema(
  Object.assign({
    host: String,
    ips: { type: [String] },
    pluginVersion: { type: String },
  }, StreamEvent.schema.obj),
  { collection: 'wpEvents' },
);

WPEvent.index({ accountId: 1, date: -1 });
WPEvent.index({ accountId: 1, host: 1, date: -1 });

WPEvent.methods = Object.assign({}, StreamEvent.schema.methods);

WPEvent.statics.getHint = () => ({ accountId: 1, date: -1 });

module.exports = mongoose.model('WPEvent', WPEvent);
