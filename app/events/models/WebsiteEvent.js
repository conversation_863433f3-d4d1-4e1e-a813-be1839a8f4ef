const mongoose = require('mongoose');

const { Schema } = mongoose;
const BaseEvent = require('./Event');
const urlUtils = require('../../../lib/utils/urlUtils');
const CONSTANTS = require('../constants');

const event = new Schema({
  url: {
    type: String,
    required: true,
    set(value) {
      const normalized = urlUtils.normalize(value);
      this.shortUrl = normalized.substr(0, CONSTANTS.maxUrlLength);
      return normalized;
    },
  },
  shortUrl: { type: String, maxlength: CONSTANTS.maxUrlLength },
  host: { type: String, lowercase: true, required: true },
  pathname: { type: String, lowercase: true, required: true },
  query: { type: String, lowercase: true },
});

const selectedIndex = { accountId: 1, date: -1, shortUrl: 1 };
event.index({ accountId: 1, host: 1, date: -1 });
event.index(selectedIndex);

event.statics.getHint = () => (selectedIndex);

module.exports = BaseEvent.discriminator('WebsiteEvent', event);
