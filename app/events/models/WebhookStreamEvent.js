const _ = require('lodash');
const mongoose = require('mongoose');
const StreamEvent = require('./StreamEvent');

const { Schema } = mongoose;

const Product = new mongoose.Schema(require('../../events/models/Product'), { _id: false });

const webhookStream = new mongoose.Schema({
  webhookId: { type: String, required: true },
  guid: { type: Schema.Types.Mixed },
  source: { type: String },
  total: Number,
  currency: String,
  products: {
    type: [Product],
    minLength: 1,
  },
  payload: { type: Schema.Types.Mixed },
});
const selectedIndex = { webhookId: 1, date: -1 };
webhookStream.index(selectedIndex);
webhookStream.index({ webhookId: 1, guid: 1, date: -1 }, { sparse: true });
webhookStream.statics.getHint = (guid) => {
  if(guid) {
    return { webhookId: 1, guid: 1, date: -1 };
  }
  return selectedIndex;
};

module.exports = StreamEvent.discriminator('WebhookStreamEvent', webhookStream);
