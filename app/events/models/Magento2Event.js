const _ = require('lodash');
const mongoose = require('mongoose');
const StreamEvent = require('./StreamEvent');

const Product = new mongoose.Schema(require('./Product'), { _id: false });

Product.path('id').required(true);
Product.path('quantity').required(true);
Product.path('price').required(true);

const Event = Object.assign({
  accountId: { type: mongoose.SchemaTypes.ObjectId, required: true },
  host: { type: String, required: true, index: true },
  url: { type: String, required: true, index: true },
  total: { type: Number },
  currency: { type: String },
  products: { type: [Product], required: true },
  ips: { type: [String] },
  store: {
    id: { type: String },
    code: { type: String },
    url: { type: String },
    name: { type: String },
    websiteId: { type: String },
  },
  storeId: { type: String },

}, StreamEvent.schema.obj);

const MagentoEvent = new mongoose.Schema(Event, { collection: 'magento2Events' });
MagentoEvent.index({ accountId: 1, date: -1 });

MagentoEvent.path('products').validate(products => products && products.length > 0, 'products array must contain at least 1 product');

MagentoEvent.methods = Object.assign({}, StreamEvent.schema.methods);

MagentoEvent.statics.getHint = function () {
  return { accountId: 1, date: -1 };
};

module.exports = mongoose.model('Magento2Event', MagentoEvent);
