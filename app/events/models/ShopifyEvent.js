const mongoose = require('mongoose');
const { schemaObj, schema } = require('./WooEvent');
const { SHOPIFY_EVENT_TYPES } = require('../../constants');

delete schemaObj.host;
schemaObj.type = { type: String, enum: Object.values(SHOPIFY_EVENT_TYPES), required: true };
schemaObj.shop = { type: String, required: true, index: true };
schemaObj.orderId = { type: Number, required: true, index: true };
schemaObj.domain = { type: String, index: true };

const ShopifyEvent = new mongoose.Schema(schemaObj, { collection: 'shopifyEvents' });
ShopifyEvent.index({ accountId: 1, date: -1 });
ShopifyEvent.index({ accountId: 1, orderId: 1 });
ShopifyEvent.methods = Object.assign({}, schema.methods);

ShopifyEvent.statics.getHint = function () {
  return { accountId: 1, date: -1 };
};

module.exports = mongoose.model('ShopifyEvent', ShopifyEvent);
