const moment = require('moment');
const _ = require('lodash');
const authTypes = require('../../middleware/authTypes');
const Feed = require('../account/models/Feed');

const StreamEvent = require('./models/StreamEvent');
const CleanFormStreamEvent = require('./models/CleanFormStreamEvent');
const WPEvent = require('./models/WPEvent');
const WooEvent = require('./models/WooEvent').model;
const WixEvent = require('../wix/models/WixEvent');
const Magento2Event = require('./models/Magento2Event');
const Magento1Event = require('./models/Magento1Event');
const ShopifyEvent = require('./models/ShopifyEvent');
const BigCommerceEvent = require('../bigcommerce/models/BigcommerceEvent');
const ThinkificEvent = require('../thinkific/ThinkificEvent');

const reviewConstants = require('../reviews/constants');

const ErrorFactory = require('../../lib/errors/ErrorFactory');
const notifierService = require('../common/notifier.service');
const redis = require('../common/redis.service').getClient();


module.exports = {
  config: {
    methods: ['DELETE'],
    authType: authTypes.console,
  },
  schema: {
    DELETE: {
      type: 'object',
      additionalProperties: false,
      properties: {
        email: { type: 'string' },
        reviewId: { type: ['string', 'number'] },
        source: { type: 'string' },
        id: { type: 'string' },
        message: { type: 'string' },
      },
    },
  },
  async handleDELETE(req, res, next) {
    try {
      const { accountId } = req.locals;
      const {
        email, reviewId, source, id, message,
      } = req.body;
      let Model = null;

      const lockKey = `feed-delete:${accountId}:${email || reviewId}`;
      const delInProg = await redis.getAsync(lockKey);
      if(delInProg) {
        throw ErrorFactory(`Deleting events for "${email || reviewId}" is in progress, try again in 60 seconds`, 500, req.body);
      }
      await redis.setexAsync(lockKey, 60, 1);
      if(email) {
        const feed = await Feed.findOneAndRemove({ _id: id });
        const date = feed ? feed.createdAt : new Date();
        // delete events created in the last 3 months to avoid long delete operations on mongo blocking it
        const dateFilter = {
          $gte: moment(date).subtract(3, 'months').toDate(),
          $lte: moment(date).add(1, 'day').toDate(),
        };
        Promise.all([
          Feed.deleteMany({
            accountId,
            searchHash: new RegExp(_.escapeRegExp(email), 'i'),
            createdAt: dateFilter,
          }),
          StreamEvent.deleteMany({ accountId, email, date: dateFilter }),
          CleanFormStreamEvent.deleteMany({ accountId, email, date: dateFilter }),
          WPEvent.deleteMany({ accountId, email, date: dateFilter }),
          WooEvent.deleteMany({ accountId, email, date: dateFilter }),
          Magento1Event.deleteMany({ accountId, email, date: dateFilter }),
          Magento2Event.deleteMany({ accountId, email, date: dateFilter }),
          ShopifyEvent.deleteMany({ accountId, email, date: dateFilter }),
          BigCommerceEvent.deleteMany({ accountId, email, date: dateFilter }),
          ThinkificEvent.deleteMany({ accountId, email, date: dateFilter }),
          WixEvent.deleteMany({ accountId, email, date: dateFilter }),
        ]).then(async () => {
          await redis.delAsync(lockKey);
        }).catch((err) => {
          notifierService.notifyError(err, 'failed to delete stream events', { email, id, message });
        });
        res.body = { message: 'deleting related events up to 3 months in the past, may take a while...' };
      } else {
        Model = reviewConstants.getReviewModel(source);
        if(!Model) {
          throw ErrorFactory(`Failed to delete ${source} review`, 500, { accountId, reviewId });
        }
        await Promise.all([
          Feed.deleteOne({ _id: id }),
          Model.updateOne({ accountId, reviewId }, { active: false }),
        ]);
        await redis.delAsync(lockKey);
        res.body = { message: 'deleted successfully' };
      }
      next();
    } catch(err) {
      next(err);
    }
  },
};
