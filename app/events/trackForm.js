const urlModule = require('url');
const _ = require('lodash');
const request = require('superagent');

const config = require('../../config');
const logger = require('../../lib/logger')('trackForm');
const authTypes = require('../../middleware/authTypes');
const { dateUtils, cookieUtils, urlUtils } = require('../../lib/utils');
const maxmind = require('../../lib/maxmind');
const CONSTANTS = require('./constants');
const slack = require('../../lib/apis/slackNotifier');
const redis = require('../common/redis.service').getClient();
// const upsertEvent = require('../users/upsertEvent');

const FormEvent = require('./models/FormEvent');
const CleanFormEvent = require('./models/CleanFormEvent');
const FormStreamEvent = require('./models/FormStreamEvent');
const CleanFormStreamEvent = require('./models/CleanFormStreamEvent');
// const FormEventData = require('./models/FormEventData');
const Feed = require('../account/models/Feed');
const Account = require('../account/models/Account');
const AnalyticsEvent = require('../notifications/models/AnalyticsEvent');
const FormUrlSuggestion = require('../common/urlSuggestions/models/FormUrlSuggestion');
const cacheService = require('../common/cache.service');


module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.api,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {
      url: { type: 'string', required: true },
      email: { type: 'string', required: true },
      timestamp: { type: 'number' },
      firstName: { type: 'string' },
      lastName: { type: 'string' },
      submissionId: { type: 'string' },
      viewed: { type: 'boolean' },
      hovered: { type: 'boolean' },
      clicked: { type: 'boolean' },
    },
  },
  async handle(req, res, next) {
    res.body = { message: 'success' };
    next();

    const { accountId } = req.jwtData;
    const { submissionId } = req.body;

    const save = await shouldTrack(req).catch((err) => {
      logger.error({ submissionId, accountId, err }, 'failed to check submissionId');
    });

    if(save) {
      trackAnalytics(accountId, req.body);

      const timestamp = acceptableTimestamp(req.body.timestamp || Date.now());
      const date = dateUtils.normalize(timestamp);
      const minuteOfDay = dateUtils.minuteOfTheDay();
      const { url } = req.body;

      trackEvent(accountId, url, date, minuteOfDay).catch((err) => {
        logger.error({ critical: true, url, err }, 'failed to save form count events');
      });

      trackStream(req, url).then((events) => {
        logger.info({ event: events[0], cleanEvent: events[1] }, 'saved stream events');
      }).catch((err) => {
        logger.error({
          critical: true, accountId, email: req.body.email, url, err,
        }, 'failed to save stream events');
      });

      updateFormUrlSuggestions(accountId, url);
      // (new FormEventData({
      //   accountId, url, headers: req.headers, query: req.query, body: req.body,
      // })).save().catch((err) => {
      //   logger.warn({ err, accountId, url }, 'failed to save FormEventData');
      // });
    } else {
      const info = _.pick(req.body, ['url', 'email', 'submissionId']);
      info.accountId = accountId;
      logger.info(info, 'form event already saved');
    }
  },
};

async function trackEvent(accountId, eventUrl, date, minute) {
  const normalizedUrl = urlUtils.normalize(eventUrl);
  const countKey = `counts.${minute}`;
  const options = { upsert: true };
  const $inc = { [countKey]: 1, total: 1 };

  const { host, pathname, query } = urlModule.parse(normalizedUrl);
  const normalizedShort = normalizedUrl.substr(0, CONSTANTS.maxUrlLength);
  const $setOnInsert = {
    host, pathname, query, firstMinute: minute, date, shortUrl: normalizedShort,
  };
  const update = { $inc, $setOnInsert };
  const find = {
    accountId, day: date, url: normalizedUrl, host,
  };

  const cleanUrl = urlUtils.clean(eventUrl);
  const cleanShort = cleanUrl.substr(0, CONSTANTS.maxUrlLength);
  const findClean = { url: cleanUrl, date, accountId };
  const $setOnInsertClean = { firstMinute: minute, shortUrl: cleanShort };
  const updateClean = { $inc, $setOnInsert: $setOnInsertClean };

  return Promise.all([
    FormEvent.update(find, update, options),
    CleanFormEvent.update(findClean, updateClean, options),
  ]);
}

async function trackStream(req, bodyUrl) {
  const { accountId } = req.jwtData;
  const { firstName, lastName, email } = req.body;
  const { host, pathname, query } = urlModule.parse(bodyUrl);
  const ip = req.remoteAddress;
  const timestamp = acceptableTimestamp(req.body.timestamp || Date.now());
  const { submissionId } = req.body;
  const psuid = cookieUtils.getCookie(req, 'psuid');
  const params = {
    submissionId,
    user_uid: psuid,
    date: timestamp,
    accountId,
    email,
    firstName,
    lastName,
    ip,
    url: bodyUrl,
    host,
    pathname,
    query,
  };

  const event = new FormStreamEvent(params);
  event.url = urlUtils.normalize(bodyUrl);

  const cleanEvent = new CleanFormStreamEvent(params);
  cleanEvent.url = urlUtils.clean(bodyUrl);

  const promises = [maxmind.geoIP(ip)];
  if(config.getSocialProfiles) {
    promises.push(event.addGravatar());
    promises.push(event.addPicasa());
  }

  return Promise.all(promises).then((results) => {
    const ipLocation = results[0];
    const feedData = Object.assign({
      timestamp, email, firstName, lastName, ip, url: bodyUrl,
    }, ipLocation);
    const searchHelper = (new URL(bodyUrl).pathname).slice(0, 30);
    Feed.saveFeed(accountId, 'Form Submission', feedData, searchHelper);
    forwardWebhook({ accountId, feedData });
    event.location = ipLocation;
    cleanEvent.location = ipLocation;
    return Promise.all([event.save(), cleanEvent.save()]);
  });
}

async function shouldTrack(req) {
  let shouldSave = true;
  const { accountId } = req.jwtData;
  const { submissionId } = req.body;
  if(submissionId && submissionId.length) {
    const event = await FormStreamEvent.findOne({ accountId, submissionId });
    shouldSave = event == null;
  }
  return shouldSave;
}

function acceptableTimestamp(timestamp) {
  let retval = timestamp;
  const now = Date.now();
  const threshold = now - dateUtils.MILLISECONDS_IN_DAY * 5; // 5 days ago
  if(retval < threshold || retval > now) {
    retval = now;
  }
  return retval;
}

function trackAnalytics(accountId, body) {
  logger.info('saving /trackForm analytics');
  const date = dateUtils.todayNormalized12am();
  const minuteOfDay = dateUtils.minuteOfTheDay();
  const findQ = { accountId, notificationId: null, date };
  const updateQ = { $inc: { 'total.conversions': 1, [`conversions.${minuteOfDay}`]: 1 } };
  if(body.viewed) Object.assign(updateQ.$inc, { 'total.viewConversions': 1, [`viewConversions.${minuteOfDay}`]: 1 });
  if(body.hovered) Object.assign(updateQ.$inc, { 'total.hoverConversions': 1, [`hoverConversions.${minuteOfDay}`]: 1 });
  if(body.clicked) Object.assign(updateQ.$inc, { 'total.clickConversions': 1, [`clickConversions.${minuteOfDay}`]: 1 });

  AnalyticsEvent.update(findQ, updateQ, { upsert: true }).then(() => {
    logger.info('saved /trackForm analytics');
  }).catch((err) => {
    logger.error({ err }, 'failed to update /trackForm analytics');
  });
}

function updateFormUrlSuggestions(accountId, url) {
  const cleanUrl = urlUtils.transform(url);
  const normalizedUrl = urlUtils.normalize(cleanUrl);

  if(urlUtils.validUrlSuggestion(normalizedUrl)) {
    FormUrlSuggestion.update(
      { accountId, url: normalizedUrl },
      { $set: { lastHit: Date.now() }, $inc: { hits: 1 } },
      { upsert: true },
    ).catch((err) => {
      const msg = 'failed to update formUrlSuggestion';
      logger.error({ accountId, normalizedUrl, err }, msg);
    });
  }
}

async function forwardWebhook({ accountId, feedData }) {
  const cacheKey = `account-config:${accountId}`;
  let account = cacheService.memget(cacheKey);
  if(!account) {
    account = await Account.findOne({ _id: accountId }).select('configuration').cache(60);
    cacheService.memset(cacheKey, account, { seconds: 60 });
  }
  const webhookUrl = _.get(account, 'configuration.forwardingWebhook.url', '');
  const webhookEnabled = _.get(account, 'configuration.forwardingWebhook.enabled', false);
  const errorCount = _.get(account, 'configuration.forwardingWebhook.errorCount', 0);

  if(webhookUrl && webhookEnabled) {
    await request.post(webhookUrl).set('User-Agent', 'ProveSource Webhook 1.0').send({
      event: 'form.submission', ...feedData,
    }).then(async () => {
      // Reset error tracking on success
      if(errorCount > 0) {
        await Account.updateOne({ _id: accountId }, {
          $set: {
            'configuration.forwardingWebhook.errorCount': 0,
            'configuration.forwardingWebhook.lastError': null,
          },
        });
        cacheService.memDelete(cacheKey);
      }
    })
      .catch(async (err) => {
        const errorMessage = err.message;
        Feed.saveFeed(accountId, 'Failed to forward form submission webhook', { error: errorMessage, webhook: webhookUrl, ...feedData });
        slack.notifyError(err, `failed to forward form submission to: ${webhookUrl}`, { accountId, data: feedData });

        const newErrorCount = errorCount + 1;
        const update = {
          $set: {
            'configuration.forwardingWebhook.lastError': errorMessage,
            'configuration.forwardingWebhook.errorCount': newErrorCount,
            ...(newErrorCount > 5 && { 'configuration.forwardingWebhook.enabled': false }),
          },
        };

        await Account.updateOne({ _id: accountId }, update);
        cacheService.memDelete(cacheKey);
      });
  }
}
