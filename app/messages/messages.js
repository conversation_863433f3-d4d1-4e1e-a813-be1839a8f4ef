const authTypes = require('../../middleware/authTypes');
const { MESSAGE_TYPES } = require('../constants');
const notifierService = require('../common/notifier.service');
const Account = require('../account/models/Account');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    properties: {},
  },
  async handle(req, res, next) {
    const { type, message, data } = req.body;
    const extraData = { accountId: req.locals.accountId, ...data };
    if(extraData && extraData.accountId) {
      const account = await Account.findOne({ _id: extraData.accountId }).select('email').catch(() => {});
      if(account) {
        extraData.email = account.email;
      }
    }
    switch(type) {
    default:
      await notifierService.serverMessage(`Frontend: ${message}`, extraData);
      break;
    case MESSAGE_TYPES.paypal:
      await notifierService.paypal(message, extraData);
      break;
    }
    next();
  },
};
