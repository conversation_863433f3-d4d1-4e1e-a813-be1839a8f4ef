const TRANSACTION_TYPES = {
  CHARGE: 'CHARGE',
  RECURRING: 'RECURRING',
  CONTRACT_CHANGE: 'CONTRACT_CHANGE',
  CANCELLATION: 'CANCELLATION',
  REFUND: 'REFUND',
  CANC<PERSON>LATION_REFUND: 'CANCELLATION_REFUND',
  CA<PERSON><PERSON>_ON_RENEWAL: 'CA<PERSON><PERSON>_ON_RENEWAL',
  CHARGEBACK: 'CHARGEBACK',
  SUBSCRIPTION_CHARGE_FAILURE: 'SUBSCRIPTION_CHARGE_FAILURE',
  FRAUD_DECLINE: 'FRAUD_DECLINE',
  CC_CHARGE_FAILED: 'CC_CHARGE_FAILED',
};

const BS_IPNS = TRANSACTION_TYPES;

const STRIPE_EVENTS = {
  CHARGE: 'charge.succeeded',
  REFUND: 'charge.refunded',
  INVOICE_PAID: 'invoice.paid',
  CHARGE_DISPUTE: 'charge.dispute.created',
  INVOICE_PAYMENT_FAILED: 'invoice.payment_failed',
  SUBSCRIPTION_CREATED: 'customer.subscription.created',
  SUBSCRIPTION_DELETED: 'customer.subscription.deleted',
};

const SUBSCRIPTION_SOURCE = {
  wix: 'wix',
  shopify: 'shopify',
  bluesnap: 'bluesnap',
  paypal: 'paypal',
  stripe: 'stripe',
};

module.exports = {
  TRANSACTION_TYPES,
  BS_IPNS,
  AFFECTS_BALANCE: [
    TRANSACTION_TYPES.CHARGE,
    TRANSACTION_TYPES.RECURRING,
    TRANSACTION_TYPES.REFUND,
    TRANSACTION_TYPES.CANCELLATION_REFUND,
    TRANSACTION_TYPES.CHARGEBACK,
    STRIPE_EVENTS.CHARGE,
    STRIPE_EVENTS.REFUND,
    STRIPE_EVENTS.CHARGE_DISPUTE,
  ],
  AFFECTS_MRR: [
    BS_IPNS.CHARGE,
    BS_IPNS.CANCELLATION,
    BS_IPNS.CANCELLATION_REFUND,
    BS_IPNS.CANCEL_ON_RENEWAL,
    BS_IPNS.CHARGEBACK,
    STRIPE_EVENTS.SUBSCRIPTION_CREATED,
    STRIPE_EVENTS.SUBSCRIPTION_DELETED,
  ],
  POSITIVE_BALANCE: [
    TRANSACTION_TYPES.CHARGE,
    TRANSACTION_TYPES.RECURRING,
    STRIPE_EVENTS.CHARGE,
  ],
  NEGATIVE_BALANCE: [
    TRANSACTION_TYPES.REFUND,
    TRANSACTION_TYPES.CANCELLATION_REFUND,
    STRIPE_EVENTS.REFUND,
    STRIPE_EVENTS.CHARGE_DISPUTE,
  ],
  NEGATIVE_IPN: [
    BS_IPNS.CANCELLATION,
    BS_IPNS.CANCELLATION_REFUND,
    BS_IPNS.CANCEL_ON_RENEWAL,
    BS_IPNS.CHARGEBACK,
    BS_IPNS.REFUND,
    STRIPE_EVENTS.REFUND,
    STRIPE_EVENTS.CHARGE_DISPUTE,
  ],

  SUBSCRIPTION_SOURCE,
  STRIPE_SUBSCRIPTION_STATUS: {
    incomplete: 'incomplete',
    incomplete_expired: 'incomplete_expired',
    trialing: 'trialing',
    active: 'active',
    past_due: 'past_due',
    canceled: 'canceled',
    unpaid: 'unpaid',
  },
  STRIPE_EVENTS,
};
