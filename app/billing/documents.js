const _ = require('lodash');
const authTypes = require('../../middleware/authTypes');
const config = require('../../config');
const GreenInvoice = require('../../lib/apis/greeninvoice');
const Account = require('../account/models/Account');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const logger = require('../../lib/logger')('billing/documents');
const slack = require('../../lib/apis/slackNotifier');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {
      page: {
        type: 'string',
        pattern: '^[0-9]+$',
      },
    },
  },
  async handle(req, res, next) {
    const { accountId } = req.locals;
    const page = parseInt(req.query.page, 10) || 1;
    try {
      const account = await Account.findOne({ _id: accountId });
      if(!account) {
        return next(ErrorFactory.AccountNotFound(accountId));
      }

      res.body = [];
      const invoiceNames = account.getInvoiceNames();
      if(!invoiceNames || !invoiceNames.length) {
        return next();
      }

      const { apiKey, secret } = config.greenInvoice;
      const client = GreenInvoice.getClient(apiKey, secret, !config.isProduction());
      const documents = await client.getDocumentsByNames(invoiceNames, page);
      if(documents && documents.length) {
        res.body = _.map(documents, doc => ({
          date: doc.documentDate,
          number: doc.number,
          url: doc.url && (doc.url.en || doc.url.origin),
          clientName: doc.client && doc.client.name,
          amount: doc.amount,
          currency: doc.currency,
          paymentMethod: GreenInvoice.getPaymentMethod(doc.payment),
          documentType: GreenInvoice.getDocumentType(doc.type),
        }));
      }
      return next();
    } catch(err) {
      const msg = 'failed to fetch invoices';
      logger.error({ err }, msg);
      slack.notifyError(err, msg, { data: { accountId } });
      return next(ErrorFactory(msg, 500));
    }
  },
};
