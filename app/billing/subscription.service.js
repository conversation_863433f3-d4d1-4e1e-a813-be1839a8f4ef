const { stringUtils, dateUtils } = require('../../lib/utils');
const { TRANSACTION_TYPES, SUBSCRIPTION_SOURCE } = require('./constants');
const { PLANS, PERIODS, PRICES } = require('../account/plansEnum');

module.exports = {
  createSubscription,
  updateSubscription,
  cancelSubscription,
  getPlanPrice,
};

function createSubscription(accountId, source, plan, period, untilDate) {
  return {
    created: Date.now(),
    source,
    recentIPN: TRANSACTION_TYPES.CHARGE,
    untilDate: untilDate || new Date('2038-01-01'),
    transactionDate: Date.now(),
    plan,
    period: getPeriod(period),
    contractName: getFormattedPlanName(plan, period, source),
    subscriptionId: `${accountId}_${source}`,
  };
}

function updateSubscription(subscription, {
  plan, period, untilDate = null, ipn = null,
} = {}) {
  subscription.recentIPN = ipn || TRANSACTION_TYPES.CONTRACT_CHANGE;
  subscription.untilDate = untilDate || new Date('2038-01-01');
  subscription.transactionDate = Date.now();
  subscription.plan = plan;
  subscription.period = getPeriod(period);
  subscription.contractName = getFormattedPlanName(plan, period);
  return subscription;
}

function cancelSubscription(subscription) {
  subscription.recentIPN = TRANSACTION_TYPES.CANCELLATION;
  if(subscription.untilDate > (new Date('2037-01-01'))) {
    subscription.untilDate = dateUtils.normalize(Date.now() - 86400 * 1000); // yesterday
  }
  return subscription;
}

/**
 * @param {string} plan plansEnum.PLANS
 * @param {string} period plansEnum.PERIODS
 * @param {string} platform
*/
function getFormattedPlanName(plan, period, platform = null) {
  const planValues = Object.values(PLANS);
  let retval = '';
  retval += stringUtils.capitalizeFirstLetter(
    planValues.find(p => p === plan.toLowerCase()) || PLANS.STARTER,
  );
  retval += ` ${stringUtils.capitalizeFirstLetter(getPeriod(period))}`;
  retval += ' Plan';
  if(platform && platform !== SUBSCRIPTION_SOURCE.stripe) {
    retval += ` (${stringUtils.capitalizeFirstLetter(platform)})`;
  }
  return retval;
}

function getPeriod(period) {
  return PERIODS.YEARLY.includes(period.toLowerCase()) ? PERIODS.YEARLY : PERIODS.MONTHLY;
}

function getPlanPrice(plan) {
  return PRICES[plan] || 0;
}
