const authTypes = require('../../../middleware/authTypes');
const config = require('../../../config');
const notifier = require('../../common/notifier.service');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {},
  async handle(req, res, next) {
    const { email } = req.session;
    notifier.stripeChannel(`${email} closed stripe checkout`);
    return res.redirect(`${config.consoleUrl}/#/billing`);
  },
};
