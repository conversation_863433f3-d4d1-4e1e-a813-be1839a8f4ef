const authTypes = require('../../../middleware/authTypes');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');
const accountService = require('../../account/account.service');
const stripeService = require('./stripe.service');
const { STRIPE_PORTAL_FLOWS } = require('../../account/plansEnum');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {},
  async handle(req, res, next) {
    const { accountId } = req.session;
    const { flow } = req.query;
    if(!Object.keys(STRIPE_PORTAL_FLOWS).includes(flow)) {
      throw ErrorFactory('invalid stripe flow', 500, { accountId, flow });
    }
    const account = await accountService.getAccount(accountId);
    if(!account) {
      throw ErrorFactory('account not found', 500, { accountId, flow });
    }
    const portalUrl = await stripeService.getStripePortal({ account, flow });
    res.body = { checkoutUrl: portalUrl };
    next();
  },
};
