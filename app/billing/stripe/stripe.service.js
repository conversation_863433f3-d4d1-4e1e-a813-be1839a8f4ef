const _ = require('lodash');
const config = require('../../../config');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');
const GreenInvoice = require('../../../lib/apis/GreenInvoice.v2');
const emailAutomationService = require('../../common/email.automation.service');
const notifierService = require('../../common/notifier.service');
const mailerService = require('../../common/mailer.service');
const triggers = require('../../../lib/triggers');
const Invoice = require('../Invoice');
const StripeEvent = require('./StripeEvent');
const StripeCustomer = require('./StripeCustomer');
const Account = require('../../account/models/Account');
const {
  STRIPE_PAYMENT_TYPE,
  STRIPE_PLANS,
  STRIPE_PORTAL_FLOWS,
  STRIPE_PERIODS,
  PERIODS,
} = require('../../account/plansEnum');
const { SUBSCRIPTION_SOURCE, TRANSACTION_TYPES } = require('../constants');
const billingService = require('../billing.service');
const trackerService = require('../../common/tracker.service');
const analyticsService = require('../../common/analytics.service');
const redis = require('../../common/redis.service').getClient();

const { greenInvoice: giConf } = config;
const greenInvoice = new GreenInvoice(giConf.apiKey, giConf.secret, giConf.testMode);
// eslint-disable-next-line import/order
const stripe = require('stripe')(config.stripe.secretKey);

const PERIOD_MAP = {
  [STRIPE_PERIODS.MONTH]: PERIODS.MONTHLY,
  [STRIPE_PERIODS.YEAR]: PERIODS.YEARLY,
};

module.exports = {
  getStripeCheckoutSession,
  handleWebhook,
  getStripePortal,
  cancelStripeSubscription,
  reactivateStripeSubscription,
  getStripePaymentMethod,
};

async function getStripeCheckoutSession({
  account, email, countryCode, plan, period,
}) {
// avoid creating multiple customers on stripe
  const accountId = account.id;
  let stripeCustomer = await StripeCustomer.findOne({ accountId });
  if(!stripeCustomer) {
    stripeCustomer = await stripe.customers.create({
      email,
      // tax_id is added manually by us on the checkout session
      // tax_exempt: countryCode === 'IL' ? 'none' : 'exempt',
      // tax: { ip_address: ip },
      metadata: {
        accountId,
        countryCode,
        vat: countryCode === 'IL',
      },
    }).then(cust => StripeCustomer.create({
      accountId,
      customerId: cust.id,
      countryCode,
    }));
  }

  // close old non-expired sessions
  const sessions = await stripe.checkout.sessions.list({
    customer: stripeCustomer.customerId,
    status: 'open',
  });
  if(sessions && sessions.data && sessions.data.length > 0) {
    // eslint-disable-next-line max-len
    await Promise.all(sessions.data.map(async session => stripe.checkout.sessions.expire(session.id)));
  }
  // create a checkout session
  const vat = [_.get(account, 'location.countryCode'), countryCode, stripeCustomer.countryCode].includes('IL');
  const priceIds = config.isProduction() ? STRIPE_PLANS.production : STRIPE_PLANS.test;
  const priceId = _.get(priceIds, `${period}.${plan}`);
  if(!priceId) {
    throw ErrorFactory('plan/period combination not found', 500, {
      accountId, plan, period,
    });
  }
  return stripe.checkout.sessions.create({
    mode: 'subscription',
    customer: stripeCustomer.customerId,
    client_reference_id: accountId,
    metadata: { accountId, email, countryCode },
    // customer_email: email, // already added on stripe.customers.create
    success_url: `${config.apiUrl}/stripe/success?sessionid={CHECKOUT_SESSION_ID}`,
    cancel_url: `${config.apiUrl}/stripe/cancel`,
    // not required if adding tax_rates to line_items
    // automatic_tax: {
    //   enabled: true,
    // },
    // default_tax_rates: ['txr_1NaeTYEZ9n9TGZAUfd9gvxlY'],
    billing_address_collection: 'auto', // can be required
    // this is shit, not always showing, users to update their details using the portal
    tax_id_collection: { enabled: true },
    customer_update: {
      name: 'auto',
      address: 'auto',
    },
    subscription_data: {
      // currently not implemented
      // billing_cycle_anchor: billingAnchor,
      // proration_behavior: 'create_prorations',
      metadata: {
        accountId, email, countryCode,
      },
    },
    line_items: [{
      price: priceId,
      quantity: 1,
      adjustable_quantity: { enabled: false },
      ...(vat && { tax_rates: [config.stripe.taxId] }),
    }],
    allow_promotion_codes: true,
  });
}

async function handleWebhook(signature, rawBody) {
  // eslint-disable-next-line max-len
  const event = await constructStripeEvent(rawBody, signature).catch((err) => {
    throw ErrorFactory('Failed to verify stripe webhook', 400, err);
  });
  const { id: eventId, type, data: { object: eventData } } = event;
  const exists = await StripeEvent.findOne({ eventId }, 'eventId').then(dbEvent => !!dbEvent);
  if(exists || type === 'checkout.session.expired') {
    return null;
  }
  const { metadata, customer: customerId } = eventData;
  let accountId = metadata && metadata.accountId;
  if(!accountId) {
    const stripeCustomer = customerId && await StripeCustomer.findOne({ customerId });
    accountId = stripeCustomer && stripeCustomer.accountId;
  }
  const account = await Account.findById(accountId);
  notifierService.stripeEvent({ event, account });

  switch(type) {
    case 'customer.subscription.created': // not required and actually filtered in handleSubscription
    case 'customer.subscription.updated':
    case 'customer.subscription.deleted': {
      await handleSubscription({ account, eventData, type }).catch((err) => {
        throw ErrorFactory('failed to handle subscription', 500, { err, eventData });
      });
      break;
    }
    case 'invoice.payment_succeeded': {
      await createInvoice({ account, eventData }).catch((err) => {
        throw ErrorFactory(`failed to create invoice: ${err.message}`, 500, { err, eventData });
      });
      break;
    }
    case 'charge.dispute.created': {
      await handleDispute({ accountId }).catch((err) => {
        throw ErrorFactory('failed to handle dispute event', 500, { err, eventData });
      });
      break;
    }
    case 'charge.refunded': // 'credit_note.updated' ?
      await refundInvoice({ eventData }).catch((err) => {
        throw ErrorFactory('failed to refund invoice', 500, { err, eventData });
      });
      break;
    case 'invoice.payment_failed': {
      const periodEndDate = new Date(eventData.period_end * 1000);
      const daysTillCancelDate = Math.ceil((periodEndDate - new Date()) / (1000 * 60 * 60 * 24));
      // TODO: what if it's the first payment?
      await billingService.sendFailedChargeEmail({
        account,
        source: SUBSCRIPTION_SOURCE.stripe,
        paymentMethod: getPaymentMethod(eventData.payment_method_details),
        failureReason: eventData.failure_message,
        daysTillCancelDate: daysTillCancelDate > 0 ? daysTillCancelDate : 0,
      });
      break;
    }

    // case 'charge.failed':
    // case 'invoice.payment_succeeded':
    // case 'invoice.paid': // doesn't include payment method
    // case 'invoice.payment_action_required':
    // case 'invoice.payment_failed':
    // case 'radar.early_fraud_warning.created' / 'radar.early_fraud_warning.updated':
    default:
      // if(type.includes('failed')) {
      //   // send slack to specific channel?
      // } else if(type.includes('dispute')) {
      //   // send slack to specific channel?
      // } else {
      //   // logger.info(event, 'stripe webhook unhandled');
      // }
      break;
  }

  let amount = eventData.amount || _.get(eventData, 'plan.amount');
  if(eventData.amount_refunded) {
    amount = -eventData.amount_refunded;
  }
  amount /= 100;
  if(_.get(eventData, 'billing_details.address.country') === 'IL') {
    amount /= 1.18;
  }
  await billingService.handleAffiliate({
    affiliateId: account.getReferringAffiliate(),
    transactionType: type,
    source: SUBSCRIPTION_SOURCE.stripe,
    amount,
    period: PERIOD_MAP[_.get(eventData, 'plan.interval')],
  });
  return StripeEvent.create({
    accountId, eventId, type, event,
  }).catch((err) => {
    notifierService.notifyError(err, 'failed to save stripe event', event);
  });
}

async function handleSubscription({ account, eventData, type }) {
  const { id: accountId } = account;
  const { id: subscriptionId } = eventData;
  if(['incomplete', 'incomplete_expired'].includes(eventData.status)) {
    return null;
  }
  const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
    expand: ['plan.product', 'default_payment_method', 'customer', 'latest_invoice.payment_intent.payment_method'],
  });
  if(!subscription) {
    throw ErrorFactory('Stripe subscription not found', 500, { accountId, event: eventData });
  }
  const email = _.get(subscription, 'customer.email', account.email);
  const prevSub = account.subscription || null;
  const planName = _.get(subscription, 'plan.nickname') || _.get(subscription, 'plan.product.name');
  const plan = _.get(subscription, 'plan.product.metadata.plan') || planName.split(' ')[0].toLowerCase();
  const stripePaymentMethod = _.get(subscription, 'default_payment_method')
    || _.get(subscription, 'latest_invoice.payment_intent.payment_method');
  const paymentMethod = getPaymentMethod(stripePaymentMethod);

  if(!account.subscription) {
    account.subscription = { created: Date.now() };
    const cents = _.get(eventData, 'amount_paid')
      || _.get(eventData, 'amount_captured')
      || _.get(eventData, 'plan.amount');
    const amount = cents / 100;
    trackerService.purchase({ id: `${account.id}:purchase`, email: account.email, value: amount });
    analyticsService.trackPurchasedEvent(account.id, {
      plan: account.subscription.plan, paymentPlatform: SUBSCRIPTION_SOURCE.stripe,
    });
  }
  account.subscription.source = SUBSCRIPTION_SOURCE.stripe;
  account.subscription.plan = plan;
  account.subscription.period = PERIOD_MAP[_.get(subscription, 'plan.interval')];
  account.subscription.contractName = planName;
  account.subscription.contractId = _.get(subscription, 'plan.id');
  account.subscription.subscriptionId = subscriptionId;
  account.subscription.sourceId = _.get(subscription, 'customer.id');
  account.subscription.email = email;
  account.subscription.lastChargeId = subscription.latest_invoice.id;
  if(!account.subscription.card) {
    account.subscription.card = {};
  }
  account.subscription.card.firstName = paymentMethod.name;
  account.subscription.card.fourDigits = paymentMethod.fourDigits;
  account.subscription.card.brand = paymentMethod.brandName;
  account.subscription.card.expDate = paymentMethod.expYear
    ? `${paymentMethod.expMonth}/${paymentMethod.expYear}`
    : 'N/A';
  account.subscription.card.details = paymentMethod.details;

  if(subscription.status === 'active') {
    account.subscription.transactionDate = subscription.current_period_start
      ? subscription.current_period_start * 1000
      : Date.now();
    account.subscription.recentIPN = account.isSubscriptionActive()
      ? TRANSACTION_TYPES.RECURRING
      : TRANSACTION_TYPES.CHARGE;
    if(subscription.cancel_at) {
      account.subscription.untilDate = new Date(subscription.cancel_at * 1000);
      account.subscription.recentIPN = TRANSACTION_TYPES.CANCELLATION;
    } else {
      account.subscription.untilDate = new Date(subscription.current_period_end * 1000);
    }
  } else if(subscription.status === 'canceled') {
    account.subscription.recentIPN = TRANSACTION_TYPES.CANCELLATION;
    account.subscription.untilDate = Date.now() - 86400 * 1000; // yesterday
  } else if(subscription.status === 'past_due') {
    account.subscription.recentIPN = TRANSACTION_TYPES.SUBSCRIPTION_CHARGE_FAILURE;
    // mailer.sendSubscriptionChargeFailed(
    //   config.sendgrid.apiKey,
    //   account.getEmails(),
    //   'Stripe',
    //   'Stripe payment failed',
    //   0,
    //   account.name || null,
    // );
  }
  triggers.subscriptionStateChanged(account, account.subscription.recentIPN, prevSub, eventData);
  return account.save().then((acc) => {
    redis.delAsync(account.id);
    return acc;
  });
}

async function createInvoice({ account, eventData: invoiceData }) {
  const { id: accountId, email } = account;
  const invoice = await stripe.invoices.retrieve(invoiceData.id, {
    expand: ['customer', 'payment_intent.payment_method'],
  });
  const dbInvoice = await Invoice.findOne({ accountId, chargeId: invoice.id });
  if(dbInvoice) {
    throw new ErrorFactory('invoice already exists', 500, dbInvoice);
  }
  // balance carryover invoice
  if(invoice.amount_paid === 0) {
    return null;
  }
  const { customer, currency, payment_intent: paymentIntent } = invoice;
  const { payment_method: paymentMethod } = paymentIntent;
  const amount = (invoice.amount_paid / 100).toFixed(2);
  const pmDetails = getPaymentMethod(paymentMethod);
  const country = _.get(invoice, 'customer_address.country')
    || _.get(paymentMethod, 'billing_details.address.country')
    || _.get(invoice, 'customer.metadata.countryCode');
  const city = _.get(invoice, 'customer_address.city') || _.get(paymentMethod, 'billing_details.address.city');
  const street = _.get(invoice, 'customer_address.line1') || _.get(paymentMethod, 'billing_details.address.line1');
  const zip = _.get(invoice, 'customer_address.postal_code') || _.get(paymentMethod, 'billing_details.address.postal_code');
  const invoiceName = _.get(account, 'subscription.customInvoiceName', null)
    || invoice.customer_name
    || customer.name
    || invoice.customer_email
    || customer.email
    || account.email;
  const invoiceTaxId = _.get(account, 'subscription.customTaxId', null) || _.get(customer, 'tax_ids.data[0].value');
  const vat = country === 'IL';

  // Get line item details
  const lineItem = _.get(invoice, 'lines.data', []).find(line => line.amount > 0);
  const plan = _.get(lineItem, 'price.nickname') || _.get(lineItem, 'plan.nickname');

  emailAutomationService.updateSubscription(email, { name: invoiceName }).catch(() => {});
  const client = {
    name: invoiceName,
    country,
    city: _.get(account, 'subscription.customCity') || city,
    address: _.get(account, 'subscription.customAddress') || street,
    zip: _.get(account, 'subscription.customZip') || zip,
    ...(invoiceTaxId && { taxId: invoiceTaxId }),
  };
  // const coupon = _.get(stripeInvoice, 'discount.coupon');

  const giData = {
    client,
    vat,
    currency,
    description: 'ProveSource',
    lineDescription: plan,
    price: amount,
    cardType: GreenInvoice.cardTypes[pmDetails.brandName] || GreenInvoice.cardTypes.unknown,
    cardNum: paymentMethod.fourDigits || '0000',
    remarks: `stripe charge ${invoice.charge}, invoice ${invoice.id}`,
  };
  const giInvoice = await greenInvoice.addInvoiceCreditCard(giData);
  if(!_.get(giInvoice, 'client.id')) {
    const msg = 'GreenInvoice missing clientId';
    notifierService.notifyError(new Error(msg), msg, { accountId, data: giData });
  }
  return Promise.all([
    Account.updateOne({ _id: accountId }, {
      $addToSet: { 'subscription.invoiceNames': invoiceName },
    }),
    Invoice.create({
      accountId,
      clientId: _.get(giInvoice, 'client.id'),
      invoiceId: invoice.id,
      chargeId: invoice.charge,
      docId: giInvoice.id,
      ...giInvoice,
      vat,
      // giInvoice has client
      // client: {
      //   id: _.get(giInvoice, 'client.id'),
      //   ...client,
      // },
      currency,
      amount,
      paymentMethod,
      plan,
      description: plan,
      docType: giInvoice.type,
      charge: invoice,
    }),
  ]);
}

async function refundInvoice({ eventData }) {
  const { id: chargeId, currency, amount_refunded: centAmount } = eventData;
  const invoice = await Invoice.findOne({ chargeId });
  if(!invoice) {
    mailerService.sendAdminEmail({
      to: '<EMAIL>',
      subject: 'Failed to refund stripe related invoice',
      html: JSON.stringify(eventData, null, 2),
    }).catch(() => {});
    throw ErrorFactory('Refund failed, invoice not found', 500, eventData);
  }
  const refundedAmount = centAmount / 100;
  const remainingAmount = refundedAmount - (invoice.refundedAmount || 0);
  if(!remainingAmount) {
    mailerService.sendAdminEmail({
      to: '<EMAIL>',
      subject: 'stripe invoice already refunded?',
      html: JSON.stringify(eventData, null, 2),
    }).catch(() => {});
    return null;
  }
  // eslint-disable-next-line max-len
  const data = {
    id: invoice.docId,
    num: invoice.number,
    clientId: invoice.clientId || _.get(invoice, 'client.id'),
    clientName: _.get(invoice, 'client.name'),
    description: `${invoice.plan || invoice.description}`,
    vat: invoice.vat,
    currency,
    price: remainingAmount,
    cardType: GreenInvoice.cardTypes[_.get(invoice, 'card.brand')] || GreenInvoice.cardTypes.mastercard,
    cardNum: _.get(invoice, 'card.fourDigits'),
    chargeId,
  };
  const { invoice: refInvoice, receipt: refundReceipt } = await greenInvoice.cancelInvoice(data);
  return Invoice.updateOne({ _id: invoice.id }, {
    refunded: true,
    refundedAmount,
    refundInvoice: refInvoice,
    refundReceipt,
  });
}

function getPaymentMethod(stripePM) {
  const type = _.get(stripePM, 'type');
  const id = _.get(stripePM, 'id');
  const paymentMethod = { type };
  if(id) {
    paymentMethod.id = id;
  }
  if(type === STRIPE_PAYMENT_TYPE.card) {
    const fourDigits = _.get(stripePM, 'card.last4');
    const expMonth = _.get(stripePM, 'card.exp_month');
    const expYear = _.get(stripePM, 'card.exp_year');
    paymentMethod.name = _.get(stripePM, 'billing_details.name');
    paymentMethod.brandName = _.get(stripePM, 'card.brand');
    paymentMethod.fourDigits = fourDigits;
    paymentMethod.expMonth = expMonth;
    paymentMethod.expYear = expYear;
    paymentMethod.details = `${fourDigits} (${expMonth < 10 ? `0${expMonth}` : expMonth}/${expYear})`;
    paymentMethod.wallet = _.get(stripePM, 'card.wallet.type', null);
  } else {
    const typeData = _.get(stripePM, type);
    paymentMethod.brandName = type;
    switch(type) {
      case STRIPE_PAYMENT_TYPE.bancontact:
        paymentMethod.details = 'Bancontact';
        break;
      case STRIPE_PAYMENT_TYPE.ideal:
        paymentMethod.details = typeData && `${typeData.bank} (${typeData.bic})`;
        break;
      case STRIPE_PAYMENT_TYPE.sofort:
        paymentMethod.details = typeData && typeData.country;
        break;
      case STRIPE_PAYMENT_TYPE.link:
        paymentMethod.details = typeData && typeData.email;
        break;
      case STRIPE_PAYMENT_TYPE.cashapp:
        paymentMethod.details = typeData && typeData.buyer_id;
        break;
      case STRIPE_PAYMENT_TYPE.amazonPay:
        paymentMethod.details = 'Amazon Pay';
        break;
      default:
        break;
    }
  }
  return paymentMethod;
}

async function constructStripeEvent(rawBody, signature) {
  return stripe.webhooks.constructEvent(rawBody, signature, config.stripe.webhookSecret);
}

async function getStripePortal({ account, flow, priceId }) {
  const { id: accountId } = account;
  const customer = await StripeCustomer.findOne({ accountId });
  if(!customer) {
    throw ErrorFactory('Stripe customer not found', 500, null, { accountId });
  }
  if(!account.isSubscriptionActive()) {
    throw new ErrorFactory('Subscription not found', 500, null, { accountId });
  }
  const { subscriptionId } = account.subscription;
  const flowData = flow && { type: flow };
  if(flow === STRIPE_PORTAL_FLOWS.subscription_update) {
    flowData.subscription_update = { subscription: subscriptionId };
    flowData.after_completion = {
      type: 'redirect',
      redirect: { return_url: `${config.consoleUrl}/#/billing` },
    };
  }
  const portalSession = await stripe.billingPortal.sessions.create({
    customer: customer.customerId,
    return_url: `${config.consoleUrl}/#/billing/upgrade`,
    ...(flow && { flow_data: flowData }),
  });
  // handle upgrade flow
  let portalUrl = portalSession.url;
  if(flow === STRIPE_PORTAL_FLOWS.subscription_update && priceId) {
    portalUrl = portalUrl.replace('/flow', '');
    portalUrl = `${portalUrl}/subscriptions/${subscriptionId}/preview/${priceId}?in_flow=true&quantity=1`;
  }
  return portalUrl;
}

async function cancelStripeSubscription({ account }) {
  const { id: accountId } = account;
  const { subscriptionId } = account.subscription;
  const stripeSub = await stripe.subscriptions.retrieve(subscriptionId);
  if(stripeSub.cancel_at_period_end === true) {
    throw ErrorFactory('Subscription is already cancelled', 500, null, { accountId });
  }
  await stripe.subscriptions.update(subscriptionId, { cancel_at_period_end: true });
  account.subscription.recentIPN = TRANSACTION_TYPES.CANCELLATION;
  return account.save().then((acc) => {
    redis.delAsync(account.id);
    return acc;
  });
}

async function reactivateStripeSubscription({ account }) {
  const { subscriptionId, source } = account.subscription || {};
  if(!subscriptionId || source !== SUBSCRIPTION_SOURCE.stripe) {
    throw ErrorFactory('Subscription not found', 500, null, { account });
  }
  const stripeSub = await stripe.subscriptions.retrieve(subscriptionId);
  if(!stripeSub.cancel_at_period_end) {
    throw ErrorFactory('Subscription is already active', 500, null, { account });
  }
  await stripe.subscriptions.update(subscriptionId, { cancel_at_period_end: false });
  account.subscription.recentIPN = TRANSACTION_TYPES.RECURRING;
  return account.save().then((acc) => {
    redis.delAsync(account.id);
    return acc;
  });
}

async function getStripePaymentMethod({ subscription }) {
  const stripeSub = await stripe.subscriptions.retrieve(subscription.subscriptionId, {
    expand: [
      'latest_invoice',
      'default_payment_method',
      'latest_invoice.payment_intent.payment_method',
      'latest_invoice.payment_intent',
    ],
  });
  const stripePaymentMethod = _.get(stripeSub, 'default_payment_method')
    || _.get(stripeSub, 'latest_invoice.payment_intent.payment_method');
  return getPaymentMethod(stripePaymentMethod);
}

async function handleDispute({ accountId }) {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return Account.updateOne({ _id: accountId }, {
    'subscription.recentIPN': TRANSACTION_TYPES.CHARGEBACK,
    untilDate: yesterday,
  });
}
