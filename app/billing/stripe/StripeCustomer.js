const mongoose = require('mongoose');

const StripeCustomer = new mongoose.Schema({
  accountId: {
    type: mongoose.SchemaTypes.ObjectId, required: true, index: true, ref: 'Account',
  },
  customerId: { type: String, required: true },
  countryCode: { type: String },
}, { timestamps: true, versionKey: false, collection: 'stripeCustomers' });

module.exports = mongoose.model('StripeCustomer', StripeCustomer);
