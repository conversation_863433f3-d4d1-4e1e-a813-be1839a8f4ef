const config = require('../../../config');
const authTypes = require('../../../middleware/authTypes');
const notifier = require('../../common/notifier.service');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {},
  async handle(req, res, next) {
    // const { sessionid } = req.query;
    const { email } = req.session;
    notifier.stripeChannel(`${email} completed stripe checkout`);
    return res.redirect(config.stripe.thankYou);
  },
};
