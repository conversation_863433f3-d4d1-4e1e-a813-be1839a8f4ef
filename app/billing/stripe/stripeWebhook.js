const authTypes = require('../../../middleware/authTypes');
const stripeService = require('./stripe.service');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
    keepRawBody: true,
  },
  schema: {},
  async handle(req, res, next) {
    const signature = req.headers['stripe-signature'];
    const stripeEvent = await stripeService.handleWebhook(signature, req.rawBody);
    if(!stripeEvent) {
      res.body = { message: 'already processed' };
    }
    next();
  },
};
