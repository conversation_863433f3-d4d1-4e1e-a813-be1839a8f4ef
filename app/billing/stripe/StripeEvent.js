const mongoose = require('mongoose');

const StripeEvent = new mongoose.Schema({
  accountId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'Account',
    index: true,
  },
  eventId: { type: String, required: true, unique: true },
  type: { type: String, required: true },
  event: {},
}, { timestamps: true, versionKey: false, collection: 'stripeEvents' });

module.exports = mongoose.model('StripeEvent', StripeEvent);
