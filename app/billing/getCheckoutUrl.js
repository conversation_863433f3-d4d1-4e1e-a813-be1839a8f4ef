const _ = require('lodash');
const authTypes = require('../../middleware/authTypes');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const accountService = require('../account/account.service');
const maxmind = require('../../lib/maxmind');
const {
  BLUESNAP_CONTRACTS, STRIPE_PORTAL_FLOWS,
} = require('../account/plansEnum');
const { SUBSCRIPTION_SOURCE } = require('./constants');
const notifierService = require('../common/notifier.service');
const stripeService = require('./stripe/stripe.service');
const appConfigService = require('../common/appConfig.service');
const bluesnapService = require('./bluesnap/bluesnap.service');

// TODO: change all relevant error codes to 400 (currently 500 so we get slack notifications)

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {},
  async handle(req, res, next) {
    const { accountId, email } = req.session;
    const account = await accountService.getAccount(accountId);
    const ip = req.remoteAddress;
    const location = await maxmind.getLocationFromIP(ip).catch(() => {});
    const { countryCode = 'N/A' } = location || {};
    const {
      plan, period, flow = STRIPE_PORTAL_FLOWS.payment_method_update,
      // coupon // unused currently
    } = req.query;

    if(account.isSubscriptionActive()) {
      if(account.subscription.source === SUBSCRIPTION_SOURCE.stripe) {
        res.body = { url: await stripeService.getStripePortal({ account, flow }) };
        return next();
      }
      throw ErrorFactory('account already has a subscription', 500, { accountId, query: req.query });
    }

    // check which payment processor to use
    const stripe = await appConfigService.getValue({ key: 'stripe', defaultValue: false });
    let paymentProcessor = _.get(account, 'settings.paymentProcessor', SUBSCRIPTION_SOURCE.stripe);
    if(!stripe) {
      paymentProcessor = SUBSCRIPTION_SOURCE.bluesnap;
    }
    if(paymentProcessor === SUBSCRIPTION_SOURCE.stripe) {
      notifierService.stripeChannel(`${email} opened stripe checkout ${accountId}`);
      const session = await stripeService.getStripeCheckoutSession({
        account, email, countryCode, plan, period,
      });
      res.body = { url: session.url };
    } else if(paymentProcessor === SUBSCRIPTION_SOURCE.bluesnap) {
      res.body = {
        url: bluesnapService.getBluesnapCheckoutUrl({
          accountId, email, countryCode, plan, period,
        }),
      };
    } else {
      throw ErrorFactory('payment source not found', 500, { accountId, query: req.query });
    }
    return next();
  },
};
