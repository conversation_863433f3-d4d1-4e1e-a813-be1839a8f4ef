const authTypes = require('../../middleware/authTypes');
const stripeService = require('./stripe/stripe.service');
const accountService = require('../account/account.service');
const { SUBSCRIPTION_SOURCE } = require('./constants');
const bluesnapService = require('./bluesnap/bluesnap.service');
const ErrorFactory = require('../../lib/errors/ErrorFactory');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.console,
    keepRawBody: true,
  },
  schema: {},
  async handle(req, res, next) {
    const { accountId } = req.session;
    const account = await accountService.getAccount(accountId);
    const { subscription } = account;
    if(!account.isSubscriptionActive()) {
      throw ErrorFactory('Account does not have an active subscription');
    } else if(subscription.source === SUBSCRIPTION_SOURCE.stripe) {
      await stripeService.reactivateStripeSubscription({ account });
    } else if(subscription.source === SUBSCRIPTION_SOURCE.bluesnap) {
      await bluesnapService.reactivateBluesnapSubscription({ account });
    } else {
      throw ErrorFactory('Subscription cannot be cancelled from dashboard', 500, { accountId, query: req.query });
    }
    return next();
  },
};
