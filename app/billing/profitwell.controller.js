const config = require('../../config');
const { STATUS, INTERVALS, CHURN_TYPES } = require('../../lib/apis/profitwell');
const profitwell = require('../../lib/apis/profitwell').getClient(config.profitWell.apiKey);
const slack = require('../../lib/apis/slackNotifier');
const logger = require('../../lib/logger')('profitwell.controller');
const { dateUtils } = require('../../lib/utils');

module.exports = {
  updateSubscription,
  churnSubscription,
  INTERVALS,
  STATUS,
  CHURN_TYPES,
  getInterval,
};

async function updateSubscription(userId, {
  email, plan, value: centValue, interval, startDate,
}) {
  const logContext = { email, plan };
  const planValue = (plan && plan.includes('VAT')) ? centValue / 1.18 : centValue;
  try {
    let shouldCreateSub = false;
    logger.info(logContext, 'checking subscription');
    const subHistory = await profitwell.getSubscriptionsForUser(userId).catch((err) => {
      if(err.status !== 404) {
        throw err;
      } else {
        shouldCreateSub = true;
      }
    });

    if(subHistory && subHistory.length) {
      const lastUpdate = subHistory[subHistory.length - 1];
      const diff = Date.now() - (lastUpdate.effective_date * 1000);
      if(lastUpdate.status === STATUS.active) {
        logger.info(logContext, 'updating subscription');
        await profitwell.updateSubscription(lastUpdate.subscription_id, {
          planName: plan,
          interval,
          value: planValue,
          startDate,
        });
      } else if(diff < dateUtils.MILLISECONDS_IN_DAY * 30) {
        logger.info(logContext, 'churned recently, re-activating');
        await profitwell.unchurn(lastUpdate.subscription_id);
        await profitwell.updateSubscription(lastUpdate.subscription_id, {
          planName: plan,
          interval,
          value: planValue,
          startDate,
        });
      } else {
        logger.info(logContext, 'subscription churned too long ago');
        shouldCreateSub = true;
      }
    }
    if(shouldCreateSub) {
      logger.info(logContext, 'creating subscription');
      await profitwell.createSubscription({
        accountId: userId,
        email,
        planName: plan,
        value: planValue,
        interval,
        startDate,
      });
    }
  } catch(err) {
    logger.error({ ...logContext, err }, 'failed to update subscription');
    const data = {
      userId, email, plan, value: centValue, planValue,
    };
    slack.notifyError(err, 'failed to update profitwell', data);
  }
}

async function churnSubscription(userId, untilDate, churnType) {
  try {
    logger.info(userId, 'checking subscription');
    const subHistory = await profitwell.getSubscriptionsForUser(userId);

    if(subHistory && subHistory.length) {
      const lastUpdate = subHistory[subHistory.length - 1];

      if(lastUpdate.status === STATUS.active) {
        logger.info(userId, 'churning subscription');
        await profitwell.churnSubscription(lastUpdate.subscription_id, untilDate, churnType);
      }
    } else {
      throw new Error('subscription not found');
    }
  } catch(err) {
    logger.error({ userId, err }, 'failed to churn subscription');
    slack.notifyError(err, 'failed to churn in profitwell', { userId });
  }
}

function getInterval(period) {
  let retval = INTERVALS.month;
  if(period && period.toLowerCase && period.toLowerCase().includes(INTERVALS.year)) {
    retval = INTERVALS.year;
  }
  return retval;
}
