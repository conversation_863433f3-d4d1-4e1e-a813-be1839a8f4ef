const mongoose = require('mongoose');

const { Schema } = mongoose;
const dateUtils = require('../../lib/utils/dateUtils');

const billSchema = new Schema({
  accountId: { type: Schema.Types.ObjectId, required: true },
  date: {
    type: Date,
    required: true,
    set(value) {
      return dateUtils.normalizeTo12Am(value);
    },
  },
  limit: Number,
  total: { type: Number },
  days: {
    type: Schema.Types.Mixed,
  },
}, { timestamps: true });

billSchema.index({ accountId: 1, date: -1 });

/** @memberOf Bill
 *  @param {string} accountId
 *  @param {date?} date
 */
billSchema.statics.getVisitorCountThisMonth = async function (accountId, date) {
  let bill;
  if(date) {
    bill = await Bill.findOne({ accountId, date });
  } else {
    bill = await Bill.findOne({ accountId }, null, { sort: { date: -1 } });

    if(bill) {
      const date = dateUtils.normalizeTo12Am(dateUtils.dateByAddingMonths(new Date(), -1));
      if(date > bill.date) bill = null;
    }
  }

  if(!bill || !bill.days) return 0;
  return bill.total;
};

/** @memberOf Bill */
billSchema.statics.incrementTotal = function (accountId, limit, date) {
  const dayOfMonth = dateUtils.dayOfMonth();
  const update = { $inc: { [`days.${dayOfMonth}`]: 1, total: 1 }, $setOnInsert: { limit } };
  return Bill.update({ accountId, date }, update, { upsert: true }).exec();
};


/** @class Bill */
const Bill = mongoose.model('bill', billSchema);
module.exports = Bill;
