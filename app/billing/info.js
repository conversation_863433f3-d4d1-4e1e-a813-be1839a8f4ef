const _ = require('lodash');
const maxmind = require('../../lib/maxmind');
const authTypes = require('../../middleware/authTypes');
const Account = require('../account/models/Account');
const Bill = require('./Bill');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const { VAT_LTD_CONTRACT } = require('../account/plansEnum');
const { SUBSCRIPTION_SOURCE } = require('../billing/constants');
const stripeService = require('./stripe/stripe.service');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {},
  },
  async handle(req, res, next) {
    const { accountId } = req.locals;

    try {
      const account = await Account.findOne({ _id: accountId });
      if(!account) {
        throw ErrorFactory.AccountNotFound(accountId);
      }

      const visitorsMonth = await Bill.getVisitorCountThisMonth(
        accountId,
        account.getBillingCycleDate(),
      );
      const subscription = account.subscriptionInfo();
      // To show PayPal account email
      if(account.subscription) {
        if(account.subscription.invoiceEmail) {
          subscription.email = account.subscription.invoiceEmail;
        } else if(account.subscription.email) {
          subscription.email = account.subscription.email;
        }
      }
      const ip = req.remoteAddress;
      const location = await maxmind.getLocationFromIP(ip).catch(() => {});
      const { countryCode = 'N/A' } = location || {};
      const vat = VAT_LTD_CONTRACT.indexOf(subscription.contractId) !== -1;
      const visitorsLimit = account.getPlanLimit();
      const shopifyStaff = account.isShopifyStaff();
      const canUseWhitelabel = account.canUseWhitelabel();
      const activeShopifyShop = account.getActiveShop();
      let shopify = false;
      let wix = false;
      if(account.isSubscriptionActive()) {
        if(subscription.source === SUBSCRIPTION_SOURCE.shopify || shopifyStaff) {
          shopify = true;
        } else if(subscription.source === SUBSCRIPTION_SOURCE.wix) {
          wix = true;
        } else {
          wix = false;
          shopify = false;
          subscription.paymentMethod = await getPaymentMethod({ account });
        }
      } else {
        if(!account.configuration.disableShopifyPaywall && activeShopifyShop) {
          shopify = true;
        }
        if((!_.get(account, 'settings.ignoreWixBilling')
          // && account.loginType === 'wix'
          && account.hasWixSite())
          || account.email.includes('wix.com')
        ) {
          wix = true;
        }
        if(shopify && wix) {
          wix = false;
          shopify = false;
        }
      }
      const enablePaypal = _.get(account, 'settings.enablePaypal');
      res.body = {
        showBasicPlan: _.get(account, 'settings.showBasicPlan', false),
        countryCode,
        subscription,
        vat,
        visitorsLimit,
        visitorsMonth,
        createdAt: account.createdAt.getTime(),
        shopify,
        shopifyStaff,
        wix,
        bigcommerce: account.loginType === 'bigcommerce',
        ltd: account.ltd === true,
        canUseWhitelabel,
        cycleEndDate: account.getBillingCycleEndDate(),
        ...(enablePaypal && { enablePaypal }),
      };
      next();
    } catch(err) {
      next(err);
    }
  },
};


async function getPaymentMethod({ account }) {
  const { subscription } = account;
  let paymentMethod = { };
  if(subscription.source === SUBSCRIPTION_SOURCE.bluesnap) {
    const fourDigits = _.get(subscription, 'card.fourDigits');
    const expDate = _.get(subscription, 'card.expDate');
    const firstName = _.get(subscription, 'card.firstName');
    const lastName = _.get(subscription, 'card.lastName');
    paymentMethod.brandName = _.get(subscription, 'card.brand');
    paymentMethod.details = `${fourDigits} (${expDate})`;
    paymentMethod.name = `${firstName} ${lastName}`;
  }
  if(subscription.source === SUBSCRIPTION_SOURCE.stripe) {
    paymentMethod = await stripeService.getStripePaymentMethod({ subscription });
  }
  return paymentMethod;
}
