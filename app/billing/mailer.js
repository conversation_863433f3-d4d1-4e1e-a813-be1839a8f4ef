const moment = require('moment');
const sendgrid = require('../../lib/apis/sendgrid');

const FROM_HELP = {
  email: '<EMAIL>',
  name: 'ProveSource Support',
};
const BCC = '<EMAIL>';

module.exports.sendSubscriptionChargeFailed = function (apiKey, toEmail, paymentMethod, reason, daysLeft, accountName = null, updateLink) {
  if(!apiKey) throw Error('invalid email client API key');

  let subject = '🔴 [URGENT] We had a problem charging your card';
  if(daysLeft > 0) {
    subject += ` - ${daysLeft} days left`;
  }

  let additionalText = '';
  if(paymentMethod.type === 'card' && paymentMethod.brand && paymentMethod.fourDigits) {
    additionalText += ` your payment method (<strong>${paymentMethod.brand}-${paymentMethod.fourDigits}</strong>) failed`;
  }
  if(reason) {
    additionalText += ` due to the following reason:<br><strong>${reason}</strong>`;
  }

  const html = `Hi there,<br><br>
We failed to renew your ProveSource Social Proof subscription${accountName ? ` (sub account "${accountName}")` : ''}, ${additionalText}<br>
Please update your payment details as soon as possible in the following link:<br>
<a href="${updateLink}">update payment details</a>
<br><br>
If you're not logged in, after you login:
<ol>
<li>Click on your email in the top right corner</li>
<li>Click "Plan & Billing"</li>
<li>Click "Update Card"</li>
</ol>
<br>
<strong>Is there a reason not to keep your ProveSource subscription? Please let us know.<strong><br>`;

  return sendgrid.send(apiKey, {
    to: toEmail,
    from: FROM_HELP,
    bcc: BCC,
    replyTo: FROM_HELP,
    subject,
    html,
    category: 'charge-failed',
  });
};

module.exports.sendCancellation = function (apiKey, toEmail, untilDate, accountName = null) {
  if(!apiKey) throw Error('invalid email client API key');

  let dateText = '.';
  if(untilDate) {
    const mUntilDate = moment(untilDate);
    if(mUntilDate > Date.now()) {
      dateText = ` and it will be downgraded to the free plan on ${mUntilDate.format('MMMM Do YYYY')}.`;
    } else {
      dateText = ' and it was downgraded to the free plan today.';
    }
  }
  const subject = 'Your subscription has been cancelled - thanks for being with us!';
  const html = `Hi there,<br><br>
Your ProveSource subscription${accountName ? ` (sub account "${accountName}")` : ''} has been cancelled${dateText}<br>
If there's anything we can improve in the future - please let us know.<br><br>
You can keep using the free plan forever or reactivate your subscription here:<br>
<a href="https://console.provesrc.com/#/billing/upgrade">https://console.provesrc.com/#/billing/upgrade</a><br><br>
Thanks,<br>
Team ProveSource`;

  return sendgrid.send(apiKey, {
    to: toEmail,
    from: FROM_HELP,
    bcc: BCC,
    replyTo: FROM_HELP,
    subject,
    html,
    category: 'cancellation',
  });
};
