const mongoose = require('mongoose');
const { SUBSCRIPTION_SOURCE } = require('./constants');

const Invoice = new mongoose.Schema({
  accountId: { type: mongoose.SchemaTypes.ObjectId, required: true },
  chargeId: { type: String, required: true },
  invoiceId: { type: String },
  platform: { type: String, enum: [SUBSCRIPTION_SOURCE.stripe, SUBSCRIPTION_SOURCE.bluesnap] },
  clientId: { type: String },
  docId: { type: String, required: true, unique: true },
  number: { type: String, required: true, unique: true },
  client: {
    id: { type: String },
    name: { type: String },
    country: { type: String },
    taxId: { type: String },
  },
  vat: { type: Boolean },
  paymentMethod: {
    name: { type: String },
    details: { type: String },
  },
  plan: { type: String },
  currency: { type: String },
  amount: { type: Number },
  originalAmount: { type: Number },
  originalCurrency: { type: String },
  discount: {
    name: { type: String },
    code: { type: String },
    amount: { type: Number },
  },
  description: { type: String },
  docType: { type: Number },
  lang: { type: String },
  url: {
    origin: { type: String },
    he: { type: String },
    en: { type: String },
  },
  refunded: { type: Boolean },
  refundedAmount: { type: Number },
  refundInvoice: {},
  refundReceipt: {},
  charge: {},
}, { timestamps: { createdAt: 'created' }, versionKey: false });

Invoice.index({ accountId: 1, created: -1 });
Invoice.index({ accountId: 1, invoiceId: 1, created: -1 });
Invoice.index({ accountId: 1, chargeId: 1, created: -1 });
Invoice.index({ created: -1 });

module.exports = mongoose.model('Invoice', Invoice);
