const { ObjectId } = require('mongoose').Types;
const authTypes = require('../../middleware/authTypes');
const Account = require('../account/models/Account');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const { SUBSCRIPTION_SOURCE } = require('../billing/constants');
const config = require('../../config');
const notifier = require('../common/notifier.service');
const bluesnap = require('../../lib/apis/bluesnap').getClient(config.bluesnap.apiKey, config.bluesnap.password, config.env);

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
  },
  // eslint-disable-next-line consistent-return
  async handle(req, res, next) {
    const { accountId } = req.locals;
    const { invoiceData } = req.body;
    if(!ObjectId.isValid(accountId)) {
      return next(ErrorFactory('invalid account id'));
    }
    const account = await Account.findOne({ _id: accountId });
    if(!account) {
      return next(ErrorFactory.AccountNotFound(accountId));
    }
    if(!account.subscription) {
      return next(ErrorFactory('Account does not have a subscription'));
    }
    const { bluesnapId, source } = account.subscription;
    if(source !== SUBSCRIPTION_SOURCE.bluesnap && source !== SUBSCRIPTION_SOURCE.stripe) {
      return next(ErrorFactory('Cannot update invoice for non Bluesnap or Stripe account'));
    }
    const { ip } = account;
    if(source === SUBSCRIPTION_SOURCE.bluesnap) {
      await bluesnap.updateInvoice(bluesnapId, { ...invoiceData, email: account.email }, ip).catch((err) => {
        notifier.notifyError(err, 'failed to update invoice in bluesnap', { accountId, invoiceData });
      });
    }
    const {
      firstName, lastName, vatId, companyName, address, city, zipCode, state, countryCode,
    } = invoiceData;
    const update = {
      ...(firstName && { 'subscription.customFirstName': firstName }),
      ...(lastName && { 'subscription.customLastName': lastName }),
      ...(vatId && { 'subscription.customTaxId': vatId }),
      ...(address && { 'subscription.customAddress': address }),
      ...(city && { 'subscription.customCity': city }),
      ...(zipCode && { 'subscription.customZip': zipCode }),
      ...(state && { 'subscription.customState': state }),
      ...(countryCode && { 'subscription.customCountryCode': countryCode }),
      ...(companyName && { 'subscription.customCompanyName': companyName }),
    };
    if(companyName) {
      update['subscription.customInvoiceName'] = companyName;
    } else if(firstName && lastName) {
      update['subscription.customInvoiceName'] = `${firstName} ${lastName}`;
    }
    if(Object.keys(update).length > 0) {
      await Account.updateOne({ _id: accountId }, update);
    }
    next();
  },
};
