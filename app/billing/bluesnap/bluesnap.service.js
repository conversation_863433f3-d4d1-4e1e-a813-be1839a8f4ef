const uuid = require('uuid').v4;
const _ = require('lodash');
const { URLSearchParams } = require('url');

const config = require('../../../config');
const { stringUtils, dateUtils } = require('../../../lib/utils');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');
const IPN = require('./IPNModel');
const Invoice = require('../Invoice');
const ChargesDaily = require('./ChargesDaily');
const ChargesMonthly = require('./ChargesMonthly');
const GreenInvoice = require('../../../lib/apis/GreenInvoice.v2');
const mailerService = require('../../common/mailer.service');
const notifierService = require('../../common/notifier.service');
const redis = require('../../common/redis.service').getClient();
const {
  AFFECTS_BALANCE,
  POSITIVE_BALANCE,
  NEGATIVE_BALANCE,
  TRANSACTION_TYPES,
} = require('../constants');
const { BLUESNAP_CONTRACTS } = require('../../account/plansEnum');

const { greenInvoice: giConf } = config;
const greenInvoice = new GreenInvoice(giConf.apiKey, giConf.secret, giConf.testMode);
const bluesnap = require('../../../lib/apis/bluesnap').getClient(config.bluesnap.apiKey, config.bluesnap.password, config.env);

module.exports = {
  getBluesnapCheckoutUrl,
  saveIPN,
  aggregateRevenueStats,
  createInvoice,
  refundInvoice,
  cancelBluesnapSubscription,
  reactivateBluesnapSubscription,
};

function getBluesnapCheckoutUrl({
  accountId, email, coupon, countryCode, plan, period,
}) {
  const vat = countryCode === 'IL';
  const sku = _.get(BLUESNAP_CONTRACTS, `${period}${vat ? '.vat' : ''}.${plan}`);
  if(!sku) {
    throw ErrorFactory('plan/period combination not found', 500, { accountId, plan, period });
  }
  const url = 'https://checkout.bluesnap.com/buynow/checkout';
  const query = new URLSearchParams({
    storeid: '549684',
    skinid: '31483',
    [`sku${sku}`]: 1,
    email,
    merchanttransactionid: accountId,
    currency: 'USD',
    paymentpaypal: 'N',
    paymentecp: 'N',
    ...(coupon && { coupon }),
  });
  return `${url}?${query.toString()}`;
}

async function saveIPN(data, account, reqId) {
  let accountId = (account && account.id) || data.merchantTransactionId || null;
  if(!accountId || !stringUtils.regex.objectId.test(accountId)) {
    accountId = null;
  }
  const aff = account && account.affiliate && account.affiliate.referrer;
  const {
    email,
    transactionType,
    invoiceAmount,
    referenceNumber: transactionId,
  } = data;
  const hash = getIPNHash(accountId, data);
  const ipn = await IPN.findOne({ hash });
  if(ipn) {
    return ipn;
  }

  const amount = stringUtils.getNumber(invoiceAmount);
  return (new IPN({
    accountId,
    hash,
    email,
    type: transactionType,
    transactionType,
    transactionId,
    amount,
    aff,
    data,
    reqId,
  })).save();
}

async function aggregateRevenueStats(data, accId, contract) {
  const { plan } = contract || {};
  let accountId = accId || data.merchantTransactionId || null;
  if(!accountId || !stringUtils.regex.objectId.test(accountId) || !plan) {
    accountId = null;
  }
  const {
    transactionType,
    invoiceAmount,
  } = data;
  if(!AFFECTS_BALANCE.includes(transactionType)) {
    return Promise.resolve();
  }

  const amount = stringUtils.getNumber(invoiceAmount);
  const update = {
    $inc: {
      [`transactions.${transactionType}`]: 1,
      [`transactionsValue.${transactionType}`]: amount,
      [`planTransactions.${plan}.${transactionType}`]: 1,
    },
  };

  if(POSITIVE_BALANCE.includes(transactionType)) {
    Object.assign(update.$inc, {
      [`planRevenue.${plan}.revenue`]: amount,
      [`planRevenue.${plan}.total`]: amount,
      revenue: amount,
      total: amount,
    });
  } else if(NEGATIVE_BALANCE.includes(transactionType)) {
    Object.assign(update.$inc, {
      [`planRevenue.${plan}.refunds`]: amount,
      [`planRevenue.${plan}.total`]: -amount,
      refunds: amount,
      total: -amount,
    });
  }

  const date = dateUtils.todayNormalized12am();
  const firstDayOfMonth = dateUtils.firstDayOfMonth();
  return Promise.all([
    ChargesDaily.updateOne({ date }, update, { upsert: true }),
    ChargesMonthly.updateOne({ date: firstDayOfMonth }, {
      ...update,
      $setOnInsert: {
        year: firstDayOfMonth.getFullYear(),
        month: firstDayOfMonth.getUTCMonth() + 1,
      },
    }, { upsert: true }),
  ]);
}

function getIPNHash(accountId, {
  email, bluesnapId, referenceNumber: transactionId, transactionType, cbStatus,
}) {
  if(!email && !accountId && !transactionId) {
    return `no-id-${uuid()}`;
  }
  const hashComps = [email, accountId, bluesnapId, transactionId, transactionType];
  if(cbStatus) {
    hashComps.push(cbStatus);
  }
  return hashComps.join('-');
}

async function createInvoice({ account, ipn }) {
  const {
    country,
    cardIssuingCountry,
    invoiceAmountUSD: invoiceAmountUSDStr,
    currency,
    invoiceChargeAmount: originalAmountStr,
    city,
    address1,
    zipCode,
    contractName,
    referenceNumber,
    creditCardType: cardBrand,
    creditCardLastFourDigits: fourDigits,
    creditCardExpDate: expDate,
    vatId: taxId,
    couponCode,
    coupon,
    couponValue: couponValueStr,
    contractPrice: contractPriceStr,
    contractId,
  } = ipn;

  const vat = country === 'IL';
  const invoiceAmountUSD = stringUtils.getNumber(invoiceAmountUSDStr);
  const contractPrice = stringUtils.getNumber(contractPriceStr);
  const couponValue = stringUtils.getNumber(couponValueStr);
  const originalAmount = stringUtils.getNumber(originalAmountStr);
  const invoiceName = _.get(account, 'subscription.customInvoiceName', null) || getNameForInvoice(ipn);
  const invoiceTaxId = _.get(account, 'subscription.customTaxId', null) || taxId;

  const client = {
    name: invoiceName,
    city: _.get(account, 'subscription.customCity', null) || city,
    address: _.get(account, 'subscription.customAddress', null) || address1,
    zip: _.get(account, 'subscription.customZip', null) || zipCode,
    country: country || (cardIssuingCountry && cardIssuingCountry.toUpperCase()),
    ...(invoiceTaxId && { taxId: invoiceTaxId }),
  };

  const plan = contractName;
  const description = `ProveSource ${plan || 'subscription'}`;
  // eslint-disable-next-line max-len
  const cardType = (cardBrand && GreenInvoice.cardTypes[cardBrand.toLowerCase()]) || GreenInvoice.cardTypes.mastercard;
  let remarks = `Reference number: ${referenceNumber}`;
  if(currency !== 'USD') {
    remarks += `\nCredit card was charged: ${originalAmount} ${currency}`;
  }
  const data = {
    client,
    vat,
    currency: 'USD',
    price: invoiceAmountUSD,
    fullPrice: contractPrice,
    description: 'ProveSource',
    lineDescription: description,
    cardType,
    cardNum: fourDigits,
    remarks,
    contractId,
    ...(couponValue && {
      discount: {
        name: coupon,
        code: couponCode,
        amount: couponValue,
      },
    }),
  };
  const invoice = await greenInvoice.addInvoiceCreditCard(data).catch((err) => {
    notifierService.notifyError(err, 'failed to create invoice', data);
    throw err;
  });
  const clientId = _.get(invoice, 'client.id');
  if(!clientId) {
    const err = new Error('GreenInvoice missing clientId');
    notifierService.notifyError(err, 'GreenInvoice did not create a client for invoice', {
      accountId: account.id, data,
    });
  }
  return Invoice.create({
    accountId: account.id,
    clientId,
    chargeId: referenceNumber,
    docId: invoice.id,
    ...invoice,
    vat,
    client: {
      id: _.get(invoice, 'client.id'),
      ...client,
    },
    currency: 'USD',
    amount: stringUtils.getNumber(invoiceAmountUSD),
    ...(currency !== 'USD' && { originalAmount, originalCurrency: currency }),
    paymentMethod: {
      name: cardBrand,
      details: `${fourDigits} (${expDate})`,
    },
    ...(couponCode && couponValue && {
      discount: {
        name: coupon,
        code: couponCode,
        amount: couponValue,
      },
    }),
    plan,
    description,
    docType: invoice.type,
    charge: ipn,
  });
}

async function refundInvoice({ chargeRefundEvent }) {
  const {
    referenceNumber,
    creditCardType: cardBrand,
    creditCardLastFourDigits: fourDigits,
  } = chargeRefundEvent;
  const invoice = await Invoice.findOne({ chargeId: referenceNumber });
  if(!invoice) {
    return mailerService.sendAdminEmail({
      to: '<EMAIL>',
      subject: 'Failed to refund bluesnap related invoice',
      html: JSON.stringify(chargeRefundEvent, null, 2),
    }).catch((err) => {
      notifierService.notifyError(err, 'Failed to send admin refund email', chargeRefundEvent);
    });
  }
  const refundAmount = Math.abs(chargeRefundEvent.invoiceAmountUSD);
  // eslint-disable-next-line max-len
  const cardType = (cardBrand && GreenInvoice.cardTypes[cardBrand.toLowerCase()]) || GreenInvoice.cardTypes.mastercard;
  const data = {
    id: invoice.docId,
    num: invoice.number,
    clientId: invoice.clientId || _.get(invoice, 'client.id'),
    clientName: _.get(invoice, 'client.name'),
    description: `${invoice.plan || invoice.description}`,
    vat: invoice.vat,
    currency: 'USD',
    price: refundAmount,
    cardType,
    cardNum: fourDigits,
    chargeId: referenceNumber,
  };
  const {
    invoice: refInvoice, receipt: refundReceipt,
  } = await greenInvoice.cancelInvoice(data).catch((err) => {
    notifierService.notifyError(err, 'failed to refund invoice', data);
    throw err;
  });
  return Invoice.updateOne({ _id: invoice.id }, {
    refunded: true,
    refundedAmount: refundAmount,
    refundInvoice: refInvoice,
    refundReceipt,
  });
}

function getNameForInvoice(bluesnapData) {
  const {
    company,
    invoiceCompany,
    invoiceFirstName,
    invoiceLastName,
    email,
    invoiceEmail,
  } = bluesnapData;

  if(company) {
    return company;
  }
  if(invoiceCompany) {
    return invoiceCompany;
  }
  if(invoiceFirstName && invoiceLastName) {
    return `${invoiceFirstName} ${invoiceLastName}`;
  }
  if(invoiceEmail) {
    return invoiceEmail;
  }
  return email;
}

async function cancelBluesnapSubscription({ account }) {
  const { subscriptionId } = account.subscription;
  const bluesnapSub = await bluesnap.getSubscription(subscriptionId);
  if(!bluesnapSub) {
    throw ErrorFactory('Subscription not found', 500, { account });
  }
  if(bluesnapSub.subscription && bluesnapSub.subscription.status !== 'A') {
    throw ErrorFactory('Subscription is not active', 500, { account });
  }
  await bluesnap.cancelSubscription(subscriptionId).catch((err) => {
    throw ErrorFactory('Failed to cancel subscription', 500, { err, account });
  });
  account.subscription.recentIPN = TRANSACTION_TYPES.CANCELLATION;
  return account.save().then((acc) => {
    redis.delAsync(account.id);
    return acc;
  });
}

async function reactivateBluesnapSubscription({ account }) {
  const { subscriptionId } = account.subscription;
  const bluesnapSub = await bluesnap.getSubscription(subscriptionId);
  if(!bluesnapSub) {
    throw ErrorFactory('Subscription not found', 500, { account });
  }
  if(bluesnapSub.subscription && bluesnapSub.subscription.status !== 'C') {
    throw ErrorFactory('Subscription is already active', 500, { account });
  }
  await bluesnap.reactivateSubscription(subscriptionId).catch((err) => {
    throw ErrorFactory('Failed to reactivate subscription', 500, { err, account });
  });
  account.subscription.recentIPN = TRANSACTION_TYPES.RECURRING;
  return account.save().then((acc) => {
    redis.delAsync(account.id);
    return acc;
  });
}
