const mongoose = require('mongoose');

/** @class ChargesDaily */
const ChargesDaily = new mongoose.Schema({
  date: { type: Date, required: true, index: true },
  total: { type: Number },
  revenue: { type: Number },
  refunds: { type: Number },
  transactions: {}, // transactionType counter
  transactionsValue: {},
  planRevenue: {},
  planTransactions: {},
}, { timestamps: true, collection: 'chargesDaily' });

/** @class ChargesDaily */
const chargesDailyModel = mongoose.model('ChargesDaily', ChargesDaily);
module.exports = chargesDailyModel;
