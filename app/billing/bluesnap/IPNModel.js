const mongoose = require('mongoose');

const { Schema } = mongoose;

const IPN = new Schema({
  accountId: { type: mongoose.SchemaTypes.ObjectId, index: true },
  hash: { type: String, index: true },
  email: { type: String, index: true },
  type: String,
  uid: { type: String },
  transactionType: String, // same as "type" above
  transactionId: String,
  amount: Number,
  aff: { type: String },
  data: {},
  reqId: String,
}, {
  timestamps: true,
  // strict: false //to save Mixed content in root
});

module.exports = mongoose.model('IPN', IPN);
