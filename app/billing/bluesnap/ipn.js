/* eslint-disable no-param-reassign,max-len */
const _ = require('lodash');

const authTypes = require('../../../middleware/authTypes');
const logger = require('../../../lib/logger')('ipn');
const {
  CONTRACT_DETAILS, PERIODS, PLANS, PRICES,
} = require('../../account/plansEnum');
const {
  AFFECTS_BALANCE,
  BS_IPNS,
  SUBSCRIPTION_SOURCE,
  POSITIVE_BALANCE,
  NEGATIVE_IPN,
} = require('../constants');

const redis = require('../../common/redis.service').getClient();
const Account = require('../../account/models/Account');
const IPN = require('./IPNModel');
const Invoice = require('../Invoice');
const triggers = require('../../../lib/triggers');
const notifierService = require('../../common/notifier.service');
const bluesnapService = require('./bluesnap.service');
const trackerService = require('../../common/tracker.service');
const profitwellController = require('../profitwell.controller');
const dateUtils = require('../../../lib/utils/dateUtils');
const stringUtils = require('../../../lib/utils/stringUtils');
const mailerService = require('../../common/mailer.service');
const analyticsService = require('../../common/analytics.service');
const billingService = require('../billing.service');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {},
  async handle(req, res, next) {
    const data = req.body;

    const {
      email, merchantTransactionId, subscriptionId, referenceNumber,
    } = data;
    let { transactionType } = data;
    if(!transactionType && data.fraudDeclineDate) {
      transactionType = BS_IPNS.FRAUD_DECLINE;
    }

    let accountId = merchantTransactionId;
    logger.info({ transactionType, accountId }, 'received IPN');

    try {
      const contract = CONTRACT_DETAILS[data.contractId] || {};

      let account = null;
      if(merchantTransactionId) {
        account = await Account.findOne({ _id: merchantTransactionId });
      }
      if(!account && subscriptionId) {
        account = await Account.findOne({ 'subscription.subscriptionId': subscriptionId });
      }
      if(!account && email) {
        account = await Account.findOne({ 'subscription.email': email });
      }
      if(!account && email) {
        account = await Account.findOne({ email });
      }
      accountId = account && account.id;

      const dbIPN = await IPN.findOne({ 'data.ipnId': data.ipnId, transactionId: referenceNumber });
      bluesnapService.saveIPN(data, account, req.id).catch((err) => {
        logger.error({ err }, 'ipn save failed');
        notifierService.notifyError(err, `failed to save ipn ${email} (${accountId})`);
      });
      if(!dbIPN) {
        bluesnapService.aggregateRevenueStats(data, accountId, contract).catch((err) => {
          logger.error({ err }, 'ipn aggregate stats failed');
          notifierService.notifyError(err, `aggregate revenue failed ${email} (${accountId})`, { data });
        });
      }

      if(transactionType === BS_IPNS.CC_CHARGE_FAILED && account && account.isSubscriptionActive()) {
        transactionType = BS_IPNS.SUBSCRIPTION_CHARGE_FAILURE;
        data.transactionType = BS_IPNS.SUBSCRIPTION_CHARGE_FAILURE;
        data.originalTransactionType = BS_IPNS.CC_CHARGE_FAILED;
      }

      if([BS_IPNS.SUBSCRIPTION_CHARGE_FAILURE, BS_IPNS.CC_CHARGE_FAILED].includes(transactionType)) {
        notifierService.chargeFailed({ account, ipn: data, source: SUBSCRIPTION_SOURCE.bluesnap });
      }

      // in case merchant transaction id is not sent
      if(!accountId || !account) {
        notifierService.ipnChannel(`no accountId: ${transactionType} (${email})`, data);
        return res.end();
      }
      const cbStatusUpdate = data.cbStatus && data.cbStatus !== 'NEW';
      if(cbStatusUpdate) {
        notifierService.ipnChannel(`chargeback status update ${email}: ${data.cbStatus} (ignoring)`, data);
        return res.end();
      }

      // update account
      const amount = getAmountValue(data);
      account.addEmail(data.email);
      account.addEmail(data.invoiceEmail);
      const prevSubscription = account.subscription;
      account.subscription = handleSubscription({
        subscription: prevSubscription,
        data,
        contract,
        customInvoiceName: _.get(account, 'subscription.customInvoiceName'),
      });
      const { cltv, mrr, transaction } = getMetrics({ data, contract });
      account.metrics.cltv += cltv;
      account.metrics.mrr = mrr;
      const existing = account.transactions.id(referenceNumber);
      if(!existing || existing.ipn !== transactionType) {
        account.transactions.push(transaction);
      }
      account.save().catch((err) => {
        notifierService.notifyError(err, 'ipn failed to update account', { accountId, email: account.email, data });
      }).then(() => {
        redis.delAsync(accountId);
      });

      billingService.handleAffiliate({
        affiliateId: account.getReferringAffiliate(),
        transactionType,
        period: contract && contract.period,
        amount,
      }).catch((err) => {
        logger.error({ err, critical: true }, 'failed to handle affiliate');
        notifierService.notifyError(err, `failed to handle affiliate: ${account.email}`);
      });

      if(transactionType === BS_IPNS.SUBSCRIPTION_CHARGE_FAILURE) {
        billingService.sendFailedChargeEmail({
          account,
          paymentMethod: {
            type: 'card',
            fourDigits: data.creditCardLastFourDigits,
            brand: data.creditCardType,
          },
          daysTillCancelDate: data.daysTillCancelDate,
          failureReason: data.failureReason,
          source: SUBSCRIPTION_SOURCE.bluesnap,
        }).catch((err) => {
          notifierService.notifyError(err, `failed sending ipn email: ${account.email}`);
        });
      }

      updateProfitWell({
        accountId, email: account.email, ipn: data, contract,
      }).catch((err) => {
        notifierService.notifyError(err, 'failed to handle profitwell', { email: account.email });
      });

      const dbInvoice = await Invoice.findOne({ accountId, chargeId: referenceNumber });
      if(dbInvoice && dbIPN && dbIPN.data.transactionDate === data.transactionDate) {
        // invoice already created? avoid creating duplicates
        notifierService.ipnChannel(`invoice already created, ignoring (${email}, ${data.invoiceAmountUSD} USD)`, data);
        mailerService.sendAdminEmail({
          to: '<EMAIL>',
          subject: 'Duplicate Bluesnap IPN? Check invoice created',
          html: JSON.stringify(data, null, 2),
        });
        return res.end();
      }

      if([BS_IPNS.CHARGE, BS_IPNS.RECURRING].includes(transactionType) && amount > 0) {
        await bluesnapService.createInvoice({ account, ipn: data });
      }
      if(!cbStatusUpdate && [BS_IPNS.REFUND, BS_IPNS.CANCELLATION_REFUND, BS_IPNS.CHARGEBACK].includes(transactionType)) {
        await bluesnapService.refundInvoice({ chargeRefundEvent: data });
      }
      triggers.subscriptionStateChanged(account, transactionType, prevSubscription, data);
      if(transactionType === BS_IPNS.CHARGE) {
        trackerService.purchase({ id: `${account.id}:purchase`, email: account.email, value: amount });
        analyticsService.trackPurchasedEvent(account.id, {
          plan: account.subscription.plan, paymentPlatform: SUBSCRIPTION_SOURCE.bluesnap,
        });
      }

      return res.end();
    } catch(err) {
      notifierService.notifyError(err, `ipn failed ${email} (${accountId})`);
      return next(err);
    }
  },
};

function getMetrics({ data, contract }) {
  const retval = { cltv: 0, mrr: 0, transaction: null };
  const { transactionType } = data;
  const amount = getAmountValue(data);

  if(AFFECTS_BALANCE.indexOf(transactionType) > -1) {
    retval.cltv += amount;
    // There is lifetime
    if(contract) {
      if(contract.period === PERIODS.MONTHLY) {
        retval.mrr = amount;
      } else if(contract.period === PERIODS.YEARLY) {
        retval.mrr = amount / 12;
      }
    }
  } else if(NEGATIVE_IPN.indexOf(transactionType) > -1) {
    retval.mrr = 0;
  }
  retval.transaction = {
    _id: data.referenceNumber,
    ipn: transactionType,
    date: data.transactionDate,
    untilDate: data.untilDate,
    currency: data.currency,
    amount: stringUtils.getNumber(data.invoiceAmount),
    amountUSD: stringUtils.getNumber(data.invoiceAmountUSD),
    plan: data.contractName,
    cardType: data.creditCardType,
    cardDigits: data.creditCardLastFourDigits,
  };
  return retval;
}

function handleSubscription({
  subscription, data, contract, customInvoiceName,
}) {
  const ipnType = data.transactionType;
  subscription = subscription || {
    created: new Date(), card: {}, invoiceCompanies: [], invoiceNames: [],
  };
  subscription.source = SUBSCRIPTION_SOURCE.bluesnap;
  subscription.recentIPN = ipnType;
  if(data.accountId) {
    subscription.bluesnapId = data.accountId;
    subscription.sourceId = data.accountId;
  }
  if(data.username) {
    subscription.username = data.username;
  }

  if(!subscription.invoiceCompanies) {
    subscription.invoiceCompanies = [];
  }
  if(data.company && !subscription.invoiceCompanies.includes(data.company)) {
    subscription.invoiceCompanies.push(data.company);
  }
  if(data.invoiceCompany && !subscription.invoiceCompanies.includes(data.invoiceCompany)) {
    subscription.invoiceCompanies.push(data.invoiceCompany);
  }

  const name = `${data.invoiceFirstName} ${data.invoiceLastName}`;
  const invoiceName = customInvoiceName || name;
  if(!subscription.invoiceNames) {
    subscription.invoiceNames = [];
  }
  if(invoiceName.length && !subscription.invoiceNames.includes(invoiceName)) {
    subscription.invoiceNames.push(invoiceName);
  }

  if(data.untilDate) {
    subscription.untilDate = new Date(data.untilDate);
    if(data.transactionType === BS_IPNS.SUBSCRIPTION_CHARGE_FAILURE) {
      subscription.untilDate = subscription.untilDate.getTime() + (15 * dateUtils.MILLISECONDS_IN_DAY);
    } else if(data.transactionType.includes(BS_IPNS.REFUND)
      || data.transactionType.includes(BS_IPNS.CHARGEBACK)) {
      subscription.untilDate = new Date();
    }
  }

  if(POSITIVE_BALANCE.includes(ipnType) && data.transactionDate) {
    subscription.transactionDate = data.transactionDate;
  }

  if(ipnType !== BS_IPNS.CONTRACT_CHANGE) {
    if(data.contractId) {
      subscription.contractId = data.contractId;
    }
    if(data.contractName) {
      subscription.contractName = data.contractName;
    }

    if(contract) {
      const { plan, period } = contract;
      if(plan) {
        subscription.plan = plan;
      }
      if(period) {
        subscription.period = period;
      }
      if(period === PERIODS.LIFETIME) {
        subscription.untilDate = '2038-01-01';
      }
    }
  }

  if(data.subscriptionId) {
    subscription.subscriptionId = data.subscriptionId;
  }
  if(!subscription.card) {
    subscription.card = {};
  }
  if(!stringUtils.isEmptyString(data.creditCardExpDate)) {
    subscription.card.expDate = data.creditCardExpDate;
  }
  if(!stringUtils.isEmptyString(data.creditCardLastFourDigits)) {
    subscription.card.fourDigits = data.creditCardLastFourDigits;
  }
  if(!stringUtils.isEmptyString(data.creditCardType)) {
    subscription.card.brand = data.creditCardType;
  }
  if(data.firstName) {
    subscription.card.firstName = data.firstName;
  }
  if(data.lastName) {
    subscription.card.lastName = data.lastName;
  }
  if(data.email) {
    subscription.email = data.email;
  }
  if(data.invoiceEmail) {
    subscription.invoiceEmail = data.invoiceEmail;
  }
  return subscription;
}

async function updateProfitWell({
  accountId, email, ipn, contract,
}) {
  const transaction = ipn.transactionType;
  // VAT is handled in profitwell... (which is wrong)
  let value = stringUtils.getNumber(ipn.invoiceAmountUSD) * 100;
  if(!value) {
    // ignore 100% discount charge
    if(transaction === BS_IPNS.CHARGE) {
      return null;
    }
    if(contract && contract.plan) {
      value = PRICES[contract.plan];
    } else {
      value = PRICES[PLANS.STARTER];
    }
    value *= 100;
  }
  let interval = profitwellController.INTERVALS.month;
  if(contract && contract.period === PERIODS.YEARLY) {
    interval = profitwellController.INTERVALS.year;
  }
  if(POSITIVE_BALANCE.includes(transaction)) {
    return profitwellController.updateSubscription(accountId, {
      email, plan: ipn.contractName, value, interval,
    });
  }
  if(NEGATIVE_IPN.includes(transaction)) {
    let churnDate = Date.now();
    let reason = null;

    if(new Date(ipn.untilDate) < Date.now()) {
      churnDate = new Date(ipn.untilDate).getTime();
    }
    const cancelReason = ipn && ipn.cancelReason;
    if(!cancelReason || cancelReason === 'CANCELLED_DUE_TO_UNPAID') {
      reason = profitwellController.CHURN_TYPES.delinquent;
    }
    return profitwellController.churnSubscription(accountId, churnDate, reason);
  }
  return null;
}

function getAmountValue(data) {
  let amount = stringUtils.getNumber(data.invoiceAmountUSD);
  if(data.contractName && data.contractName.toLowerCase().includes('vat')) {
    amount /= 1.18;
  }
  return amount;
}
