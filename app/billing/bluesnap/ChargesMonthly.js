const mongoose = require('mongoose');

/** @class ChargesMonthly */
const ChargesMonthly = new mongoose.Schema({
  date: { type: Date, required: true, index: true },
  year: { type: Number, required: true },
  month: { type: Number, required: true },
  total: { type: Number },
  revenue: { type: Number },
  refunds: { type: Number },
  transactions: {}, // transactionType counter
  transactionsValue: {},
  planRevenue: {},
  planTransactions: {},
}, { timestamps: true, collection: 'chargesMonthly' });

ChargesMonthly.index({ year: -1, month: -1 });

/** @class ChargesMonthly */
const chargesMonthlyModel = mongoose.model('ChargesMonthly', ChargesMonthly);
module.exports = chargesMonthlyModel;
