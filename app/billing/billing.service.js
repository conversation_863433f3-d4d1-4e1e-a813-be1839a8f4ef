const _ = require('lodash');
const config = require('../../config');
const {
  AFFECTS_BALANCE,
  AFFECTS_MRR,
  NEGATIVE_IPN,
} = require('./constants');
const mailer = require('./mailer');
const {
  PERIODS,
} = require('../account/plansEnum');
const Account = require('../account/models/Account');
const ErrorFactory = require('../../lib/errors/ErrorFactory');

module.exports = {
  sendFailedChargeEmail,
  handleAffiliate,
};

async function sendFailedChargeEmail({
  account, daysTillCancelDate, paymentMethod, failureReason,
}) {
  if(!_.get(account, 'configuration.emailOpt.ipns', true)) {
    return null;
  }
  const { apiKey } = config.sendgrid;
  const toEmail = account.getEmails();
  const accountName = account.name || null;
  const updateLink = `${config.apiUrl}/billing/update-payment-method-redirect`;
  const daysUntilCancel = (daysTillCancelDate && parseInt(daysTillCancelDate, 10)) || 0;
  if(daysUntilCancel !== 0) {
    return mailer.sendSubscriptionChargeFailed(
      apiKey,
      toEmail,
      paymentMethod,
      failureReason,
      daysUntilCancel,
      accountName,
      updateLink,
    );
  }
  return null;
}

async function handleAffiliate({
  affiliateId, transactionType, period, amount,
}) {
  if(!affiliateId || !transactionType) {
    return null;
  }
  const subPeriod = period || PERIODS.MONTHLY;
  const referrer = await Account.findOne({ 'affiliate.id': affiliateId, kind: { $ne: 'SubAccount' } }).catch((err) => {
    throw ErrorFactory('no referrer affiliate', 500, { err });
  });
  if(!referrer) {
    return null;
  }
  const commission = amount * referrer.getAffiliateCommissionRate();
  const $inc = {};
  if(AFFECTS_MRR.indexOf(transactionType) > -1) {
    let mrrIncrease = commission;
    if(subPeriod === PERIODS.YEARLY) {
      mrrIncrease /= 12;
    }
    if(NEGATIVE_IPN.includes(transactionType)) {
      mrrIncrease = -mrrIncrease;
    }
    $inc['affiliate.mrr'] = mrrIncrease;
  }
  if(AFFECTS_BALANCE.indexOf(transactionType) > -1) {
    $inc['affiliate.balance'] = commission;
  }
  if(Object.keys($inc).length) {
    return Account.updateOne({ _id: referrer.id }, { $inc }).catch((err) => {
      throw ErrorFactory('failed to save referrer affiliate', 500, { err });
    });
  }
  return null;
}
