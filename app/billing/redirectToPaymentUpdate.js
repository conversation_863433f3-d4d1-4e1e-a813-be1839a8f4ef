const _ = require('lodash');
const config = require('../../config');
const authTypes = require('../../middleware/authTypes');
const accountService = require('../account/account.service');
const stripeService = require('./stripe/stripe.service');
const { STRIPE_PORTAL_FLOWS } = require('../account/plansEnum');
const { SUBSCRIPTION_SOURCE } = require('../billing/constants');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
    keepRawBody: true,
  },
  schema: {},
  async handle(req, res, next) {
    const { accountId } = req.session;
    const account = await accountService.getAccount(accountId);
    const source = _.get(account, 'subscription.source', '');
    let redirectUrl = `${config.consoleUrl}/#/billing`;
    if(source === SUBSCRIPTION_SOURCE.stripe) {
      redirectUrl = await stripeService.getStripePortal({
        account, flow: STRIPE_PORTAL_FLOWS.payment_method_update,
      });
    } else if(source === SUBSCRIPTION_SOURCE.bluesnap) {
      redirectUrl = `${config.consoleUrl}/#/billing?updateCardDetails=true`;
    }
    return res.redirect(redirectUrl);
  },
};
