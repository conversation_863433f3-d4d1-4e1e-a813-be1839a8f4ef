const mongoose = require('mongoose');
const Notification = require('./Notification');
const constants = require('../constants');

const LiveVisitors = new mongoose.Schema({
  type: {
    type: String,
    default: constants.notificationTypes.live,
    set() {
      return constants.notificationTypes.live;
    },
  },
  displayURLs: {
    type: [String],
    default: () => void 0,
  },
});

module.exports = Notification.discriminator('LiveVisitors', LiveVisitors);
