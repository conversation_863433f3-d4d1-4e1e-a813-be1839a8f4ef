const mongoose = require('mongoose');
const BaseModel = require('./Notification');
const constants = require('../constants');

const Review = new mongoose.Schema({
  type: {
    type: String,
    default: constants.notificationTypes.review,
  },
  placeId: {
    type: String,
    required: true,
  },
  pageName: {
    type: String,
  },
  pageToken: {
    type: String,
  },
  source: {
    type: String,
    enum: constants.reviewSourcesArray,
    required: true,
  },
  stopAutoSync: {
    type: Boolean,
    default: false,
  },
  stats: {
    lastAttempt: { type: Date },
    lastFetch: { type: Date },
    lastError: { type: Date },
    consecutiveFailures: { type: Number },
    errors: {},
  },
});

Review.path('settings.shuffle', { type: Boolean });
Review.path('settings.hideEmpty', { type: Boolean });
Review.path('settings.firstNameOnly', { type: Boolean });
Review.path('settings.maxReviewsFetch', { type: Number });

/** @class ReviewSchema */
module.exports = BaseModel.discriminator('Review', Review);
