const _ = require('lodash');
const mongoose = require('mongoose');
const constants = require('../constants');

const {
  EXCLUDE_MATCHERS, COMBO_PERIODS, visitorTypes, MOBILE_DESIGN,
} = constants;


/**
 * @class Notification
 */
const notification = new mongoose.Schema({
  accountId: {
    type: mongoose.Schema.Types.ObjectId, required: true, ref: 'Account', index: true,
  },
  type: {
    type: String, enum: constants.notificationTypesArray, required: true, index: true,
  },
  name: { type: String, required: true },
  active: { type: Boolean, default: true },
  refer: {
    type: String,
    default: 'visitors',
    set(value) {
      if(!value || !value.length) {
        return 'visitors';
      }
      return value;
    },
  },
  message: { type: String },
  image: { type: String },
  priority: { type: Number, default: 0 },
  localization: { type: String, default: 'en' },
  trackURL: {
    type: [String],
  },
  displayURLs: {
    type: [String],
    default: () => void 0,
  },
  urlTypes: {
    track: { type: String, enum: constants.URL_TYPES_ARRAY, default: 'simple' },
    display: {
      type: String, enum: constants.URL_TYPES_ARRAY, default: 'simple', index: true,
    },
    trackAbs: { type: Boolean, default: true },
    displayAbs: { type: Boolean, default: true, index: true },
  },
  displayExclude: [{
    _id: false,
    matcher: {
      type: String,
      enum: Object.values(EXCLUDE_MATCHERS),
      default: EXCLUDE_MATCHERS.notContains,
    },
    value: String,
  }],
  manuallyShowNotification: { type: Boolean, default: false },
  settings: {
    default: 0,
    showMap: Boolean,
    position: { type: String, default: constants.positionTypes[0] },
    mobileDesign: {
      type: String, enum: Object.values(MOBILE_DESIGN), default: MOBILE_DESIGN.normal,
    },
    mobileTop: { type: Boolean, default: false },
    allowClose: { type: Boolean },
    // TODO: delete, deprecated in favor of mobileDesign field
    hideOnMobile: { type: Boolean },
    hideLocation: Boolean,
    hideExactTime: { type: Boolean },
    hideExactTimeStream: {
      active: Boolean,
      unit: { type: String, enum: constants.timeUnitsArray },
      value: Number,
    },
    minimumToDisplay: { type: Number },
    maxConversions: { type: Number },
    allTimeEvents: { type: Boolean },
    rtl: { type: Boolean },
    displayHold: { type: Number },
    filterAnonymous: { type: Boolean },
    anonymize: { type: Boolean },
    platform: { type: String },
    sessionShowOnce: { type: Boolean },
    link: {
      active: Boolean,
      value: String,
      newTab: Boolean,
      addUTMParams: Boolean,
      cta: {
        active: Boolean,
        text: String,
        color: String,
      },
    },
    hideExactNumber: {
      active: Boolean,
      max: Number,
    },
    showTitle: { type: Boolean, default: true },
    showImage: { type: Boolean, default: true },
    showDate: { type: Boolean, default: true },
    theme: {
      messageColor: { type: String, default: '#000000' },
      titleColor: { type: String, default: '#7825F3' },
      backgroundColor: { type: String, default: '#FFFFFF' },
      dateColor: { type: String, default: '#888888' },
      glassEffect: { type: Boolean, default: false },
      starColor: { type: String, default: '#FFC102' },
      useGradientBackground: { type: Boolean, default: false },
      fontFamily: { type: String, default: 'Lato' },
      animation: { type: String, default: 'slide-vertical' },
      rounded: { type: Number, default: 7 }, // steps of 3.5 e.g 3.5,7,10.5,14 etc.. (max 35)
    },
    timeLimit: {
      active: { type: Boolean, default: false },
      unit: { type: String, enum: constants.timeUnitsArray },
      value: { type: Number },
    },
    combo: {
      type: { type: String, enum: constants.COMBO_TYPES_ARR },
      period: { type: String, enum: Object.values(COMBO_PERIODS) },
    },
    visitor: {
      type: { type: String, default: visitorTypes.all, enum: Object.values(visitorTypes) },
      daysAsReturning: { type: Number, default: 1000 },
    },
    ignoreEventLimit: { type: Boolean },
  },
  lastView: { type: Date },
}, { timestamps: true });

/** @memberOf Notification# */
notification.methods.isTimeLimitActive = function isTimeLimitActive() {
  return !!(this.settings && this.settings.timeLimit && this.settings.timeLimit.active);
};

/** @memberOf Notification# */
notification.methods.getTimeLimitUnit = function getTimeLimitUnit() {
  return this.settings && this.settings.timeLimit && this.settings.timeLimit.unit;
};

/** @memberOf Notification# */
notification.methods.getTimeLimitValue = function getTimeLimitValue() {
  return this.settings && this.settings.timeLimit && this.settings.timeLimit.value;
};

/** @memberOf Notification#
 * @return {number} minimum events to display or 1 if not valid or exists
 */
notification.methods.getMinimumToDisplay = function getMinimumToDisplay() {
  const retval = this.settings && this.settings.minimumToDisplay;
  if(retval < 1 || isNaN(retval)) return 1;
  return retval;
};

/** @memberOf Notification */
notification.statics.omitKeys = ['_id', '__t', '__v', 'updatedAt', 'createdAt', 'active', 'priority'];

/**
 * @memberOf Notification
 * @param {string|object} accountId
 * @param {string} url
 * @param {string} cleanUrl
 * @param {object} [options] the options
 * @param {boolean} [options.debug]
 * @return {object} notification find query
 */
notification.statics.findWithUrlQuery = (accountId, url, cleanUrl, options) => {
  const opts = options || {};
  const $or = [
    { displayURLs: url },
    { 'urlTypes.displayAbs': false, displayURLs: cleanUrl },
    { 'urlTypes.display': { $ne: constants.URL_TYPES.simple } },
  ];
  const query = {
    accountId, $or, manuallyShowNotification: { $ne: true },
  };
  if(!opts.debug) query.active = true;
  return query;
};

notification.methods.isTrackAbsolute = function isTrackAbsolute() {
  return _.get(this, 'urlTypes.trackAbs', true);
};

/** @memberOf Notification# */
notification.methods.isAllTime = function isAllTime() {
  const comboAll = this.settings.combo && this.settings.combo.period === COMBO_PERIODS.all;
  return this.settings.allTimeEvents || comboAll;
};

/** @class Notification */
const Notification = mongoose.model('Notification', notification);
module.exports = Notification;
