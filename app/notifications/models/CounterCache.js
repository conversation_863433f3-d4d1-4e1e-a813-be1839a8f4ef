const mongoose = require('mongoose');

const CounterCache = new mongoose.Schema({
  notificationId: { type: mongoose.SchemaTypes.ObjectId, required: true },
  inProgress: { type: Boolean, default: false },
  result: {},
}, { timestamps: true, collection: 'counterCache' });

// cron runs every 60 seconds, to avoid bottle-necks on requests, expiry is set to 100 seconds
CounterCache.index({ createdAt: -1 }, { expires: 100 });

module.exports = mongoose.model('CounterCache', CounterCache);
