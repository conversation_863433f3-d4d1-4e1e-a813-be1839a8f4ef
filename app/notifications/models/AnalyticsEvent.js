const mongoose = require('mongoose');
const constants = require('../constants');
const dateUtils = require('../../../lib/utils/dateUtils');

const AnalyticsEvent = new mongoose.Schema({
  accountId: { type: mongoose.SchemaTypes.ObjectId, required: true, ref: 'Account' },
  notificationId: { type: mongoose.SchemaTypes.ObjectId, ref: 'Notification' },
  date: { type: Date, required: true },
  total: {
    visitors: { type: Number, default: 0 },
    engagedVisitors: { type: Number, default: 0 },
    views: { type: Number, default: 0 },
    hovers: { type: Number, default: 0 },
    clicks: { type: Number, default: 0 },
    conversions: { type: Number, default: 0 },
    viewConversions: { type: Number, default: 0 },
    hoverConversions: { type: Number, default: 0 },
    clickConversions: { type: Number, default: 0 },
    webhooks: { type: Number, default: 0 },
    woocommerceOrders: { type: Number, default: 0 },
  },

}, { collection: 'analyticsEvents' });

/** @memberOf AnalyticsEvent */
AnalyticsEvent.statics.incQuery = function (event) {
  let key;
  switch(event) {
  default:
  case constants.ANALYTICS_EVENTS.view:
    key = 'views';
    break;
  case constants.ANALYTICS_EVENTS.hover:
    key = 'hovers';
    break;
  case constants.ANALYTICS_EVENTS.click:
    key = 'clicks';
    break;
  }
  return { [`total.${key}`]: 1 };
};

/**
 * @memberOf AnalyticsEvent
 * @param accountId
 */
AnalyticsEvent.statics.incVisitors = function (accountId) {
  const date = dateUtils.todayNormalized12am();
  return this.update(
    { accountId, notificationId: null, date },
    { $inc: { 'total.visitors': 1 } },
    { upsert: true },
  ).exec();
};

AnalyticsEvent.index({ accountId: 1, notificationId: 1, date: -1 });

/** @class AnalyticsEvent */
module.exports = mongoose.model('AnalyticsEvent', AnalyticsEvent);
