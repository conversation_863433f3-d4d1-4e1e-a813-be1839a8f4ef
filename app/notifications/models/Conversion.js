const mongoose = require('mongoose');
const BaseModel = require('./Notification');
const urlUtils = require('../../../lib/utils/urlUtils');
const constants = require('../constants');

const Conversion = new mongoose.Schema({
  type: { type: String, default: constants.notificationTypes.conversion },
  message: { type: String, required: true },
  webhookId: { type: String },
  autoTrack: { type: Boolean, required: true },
  trackURL: {
    type: [String],
    default: () => void 0,
  },
  displayURLs: {
    type: [String],
    default: () => void 0,
  },
});

/** @class ConversionSchema */
module.exports = BaseModel.discriminator('Conversion', Conversion);
module.exports.schema = Conversion;
