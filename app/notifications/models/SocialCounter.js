const mongoose = require('mongoose');
const BaseModel = require('./Notification');
const constants = require('../constants');

const { socialPlatforms } = constants;

const Profile = new mongoose.Schema({
  _id: false,
  id: String,
  value: Number,
  lastUpdate: { type: Date, default: new Date() },
});

const SocialCounter = new mongoose.Schema({
}, { timestamps: true });

SocialCounter.add({ [`profiles.${socialPlatforms.instagram}`]: Profile });
SocialCounter.add({ [`profiles.${socialPlatforms.youtube}`]: Profile });
SocialCounter.add({ [`profiles.${socialPlatforms.twitter}`]: Profile });

const fbProfile = Profile.clone();
fbProfile.add({ pageName: { type: String }, pageToken: { type: String } });
SocialCounter.add({ [`profiles.${socialPlatforms.facebook}`]: fbProfile });

SocialCounter.pre('save', function () {
  this.type = constants.notificationTypes.social;
});

/** @class ReviewSchema */
module.exports = BaseModel.discriminator('SocialCounter', SocialCounter);
