const mongoose = require('mongoose');
const _ = require('lodash');
const BaseModel = require('./Notification');
const constants = require('../constants');
const urlUtils = require('../../../lib/utils/urlUtils');

const combo = new mongoose.Schema({
  type: { type: String, default: constants.notificationTypes.combo },
  webhookId: { type: String },
  message: {
    type: String,
    required() {
      return _.get(this, 'settings.combo.type', null) === constants.COMBO_TYPES.conversions;
    },
  },
  autoTrack: {
    type: <PERSON><PERSON><PERSON>,
    required() {
      const type = this.settings && this.settings.combo && this.settings.combo.type;
      return type === constants.COMBO_TYPES.conversions;
    },
  },
  trackURL: {
    type: [String],
    default: () => void 0,
  },
  displayURLs: {
    type: [String],
    default: () => void 0,
  },
});

/** @class Combo */
module.exports = BaseModel.discriminator('Combo', combo);
