const mongoose = require('mongoose');

const VisitorPing = new mongoose.Schema({
  accountId: { type: mongoose.SchemaTypes.ObjectId, required: true },
  host: { type: String, required: true },
  uuid: { type: String, required: true, index: true },

}, { timestamps: true });

VisitorPing.path('updatedAt').index({ expires: 600 });
VisitorPing.index({ uuid: 1, host: 1 });
VisitorPing.index({ accountId: 1, host: 1 });

module.exports = mongoose.model('VisitorPing', VisitorPing);
