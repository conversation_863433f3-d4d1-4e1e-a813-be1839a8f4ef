/** Created by natanavra on 25/02/2018 */

const mongoose = require('mongoose');

const { Schema } = mongoose;
const BaseModel = require('./Notification');
const constants = require('../constants');

const notification = new Schema({
  type: { type: String, default: constants.notificationTypes.pageVisits },
  message: { type: String, required: true },
  trackURL: {
    type: [String],
    minLength: 1,
    required: true,
  },
  displayURLs: {
    type: [String],
    default: () => void 0,
  },
  autoTrack: { type: Boolean, default: true },
});

module.exports = BaseModel.discriminator('PageVisits', notification);
