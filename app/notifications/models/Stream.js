const mongoose = require('mongoose');
const Notification = require('./Notification');
const Conversion = require('./Conversion');
const constants = require('../constants');

const stream = new mongoose.Schema(Conversion.schema.obj);
stream.add({ someoneAlternatives: [String] });
stream.add({ 'settings.hideOwnConversions': Boolean });
stream.add({ 'settings.hideProduct': Boolean });
stream.add({ 'settings.disableProductLink': Boolean });
stream.add({ 'settings.sortByLocation': Boolean });
stream.add({ 'settings.showLocationFlag': Boolean });
stream.add({
  'settings.filterProducts': {
    include: Boolean,
    values: [String],
  },
});
stream.add({
  'settings.filters': new mongoose.Schema({
    countries: {
      include: Boolean,
      values: [String],
    },
    status: { type: String },
  }),
});
stream.add({
  'settings.eventType': { type: String, default: 'all' },
});

stream.remove('settings.theme.title.backgroundColor');

stream.pre('save', function () {
  this.type = constants.notificationTypes.stream;
});

stream.methods.canShowProducts = function shouldFilterProducts() {
  const { platform } = this.settings;
  const platformHasProducts = platform !== constants.PLATFORMS.wordpress;
  return platformHasProducts && !this.autoTrack;
};

module.exports = Notification.discriminator('Stream', stream);
