const authTypes = require('../../middleware/authTypes');

const ErrorFactory = require('../../lib/errors/ErrorFactory');
const constants = require('./constants');
const eventConsts = require('../events/constants');
const reviewConsts = require('../reviews/constants');

const Notification = require('./models/Notification');

const streamFind = require('./get/streamEventFind');
const reviewFind = require('./get/reviewFind');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {},
  },
  async handle(req, res, next) {
    try {
      const { id } = req.params;
      const { accountId } = req.locals;

      const notification = await Notification.findOne({ _id: id, accountId });
      if(!notification) {
        throw ErrorFactory('no such notification');
      }

      let events = [];
      switch(notification.type) {
        default:
          break;
        case constants.notificationTypes.stream: {
          const { query, eventModel } = streamFind(notification);
          events = await eventModel
            .find(query, null, { sort: { date: -1 } })
            .limit(5)
            .hint(eventModel.getHint());
          break;
        }
        case constants.notificationTypes.review: {
          const { query, model } = reviewFind(notification);
          events = await model.find(query).sort({ time: -1 }).limit(5);
          break;
        }
      }

      const feeds = [];
      for(let i = 0; i < events.length; i += 1) {
        const event = events[i];
        let message = event.getFeedEventName ? event.getFeedEventName() : event.message;
        if(!message) {
          message = eventConsts.eventNames[event.constructor.modelName]
            || reviewConsts.eventNames[event.constructor.modelName]
            || 'Conversion Event';
        }
        if(event.authorName) {
          message += ` (${event.authorName})`;
        } else if(event.email) {
          message += ` (${event.email})`;
        }
        feeds.push({ message, timestamp: event.date || event.time });
      }
      res.body = { events: feeds };
      next();
    } catch(err) {
      next(err);
    }
  },
};
