const _ = require('lodash');
const config = require('../../config');
const Notification = require('./models/Notification');
const Account = require('../account/models/Account');
const authTypes = require('../../middleware/authTypes');
const emailAutomationService = require('../common/email.automation.service');
const constants = require('./constants');
const socialNotificationService = require('./social.notification.service');
const { cookieUtils } = require('../../lib/utils');
const AnalyticsEvent = require('./models/AnalyticsEvent');
const { ErrorFactory } = require('../../lib/errors');
const { ObjectId } = require('mongoose').Types.ObjectId;

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    required: [],
    additionalProperties: false,
    properties: {
      startDate: {
        type: 'string',
        pattern: /^\d{4}-\d{1,2}-\d{1,2}$/,
      },
      endDate: {
        type: 'string',
        pattern: /^\d{4}-\d{1,2}-\d{1,2}$/,
      },
      search: {
        type: 'string',
        maxLength: 30,
      },
      active: {
        type: 'string',
        enum: ['true', 'false'],
      },
    },
  },
  async handle(req, res, next) {
    try {
      const { accountId } = req.locals;
      res.body = {};

      // Build notification query
      const query = { accountId };
      if(req.query.search) {
        query.name = { $regex: _.escapeRegExp(req.query.search), $options: 'i' };
      }
      if(['true', 'false'].includes(req.query.active)) {
        query.active = req.query.active === 'true';
      }
      let notifications = (await Notification
        .find(query)
        .sort({ priority: 1, _id: -1 })).map(n => n.toObject());
      // Only fetch analytics if date range is provided
      if(req.query.startDate && req.query.endDate) {
        const startDate = new Date(req.query.startDate);
        const endDate = new Date(req.query.endDate);
        if(startDate > endDate) {
          throw ErrorFactory('notification analytics start date must be before end date', 500);
        }

        const defaultAnalytics = {
          visitors: 0, views: 0, hovers: 0, clicks: 0,
        };

        // Single aggregation query for both notification and account analytics
        const analyticsData = await AnalyticsEvent.aggregate([{
          $match: {
            accountId: ObjectId(accountId),
            date: { $gte: startDate, $lte: endDate },
            notificationId: { $in: notifications.map(n => n._id).concat(null) },
          },
        }, {
          $group: {
            _id: '$notificationId',
            visitors: { $sum: '$total.visitors' },
            engagedVisitors: { $sum: '$total.engagedVisitors' },
            views: { $sum: '$total.views' },
            hovers: { $sum: '$total.hovers' },
            clicks: { $sum: '$total.clicks' },
          },
        }]);

        // Add account level/root analytics (where _id is null)
        const { _id, ...accountAnalytics } = _.find(analyticsData, data => data._id === null)
          || { _id: null, ...defaultAnalytics };
        // engaged visitors sometimes bigger than visitors due to tracking bug
        if(accountAnalytics.engagedVisitors > accountAnalytics.visitors) {
          accountAnalytics.engagedVisitors = accountAnalytics.visitors;
        }
        res.body.analytics = accountAnalytics;

        // Attach analytics to notifications
        notifications = notifications.map((notification) => {
          const notificationAnalytics = _.find(analyticsData, data => data._id && data._id.equals(notification._id));
          return {
            ...notification,
            analytics: notificationAnalytics
              ? _.pick(notificationAnalytics, Object.keys(defaultAnalytics))
              : defaultAnalytics,
          };
        });
      }

      if(!req.locals.ps_admin) {
        Account.update({ _id: accountId }, { $set: { lastSeen: Date.now() } }).exec();
        emailAutomationService.updateSubscription(req.session.email, { logged_in: new Date() });
      }
      notifications.forEach((notification) => {
        if(notification.type === constants.notificationTypes.social) {
          const profiles = Object.keys(notification.profiles);
          for(let i = 0; i < profiles.length; i += 1) {
            const profile = profiles[i];
            const { value } = notification.profiles[profile] || {};
            if(value) {
              notification.profiles[profile].value = socialNotificationService.formatSocialCount(value);
            }
          }
        }
      });

      res.body.notifications = notifications;
      cookieUtils.setCookie(req, res, cookieUtils.makeCookie('ps_login', accountId, config.cookies.ps_login));
      next();
    } catch(err) {
      next(err);
    }
  },
};
