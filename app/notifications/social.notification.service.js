const moment = require('moment');
const Notification = require('./models/Notification');
const SocialNotification = require('./models/SocialCounter');
const constants = require('./constants');
const { stringUtils } = require('../../lib/utils');

module.exports = {
  getNotifications,
  updateCounters,
  formatSocialCount,
};

function getNotifications(notificationId) {
  const query = {
    type: constants.notificationTypes.social,
    active: true,
    $or: [{
      // never seen but created recently
      lastView: null, createdAt: { $gt: moment().subtract(90, 'days').toDate() },
    }, {
      // seen in the last 180 days
      lastView: { $gt: moment().subtract(180, 'days').toDate() },
    }],
  };
  if(notificationId) {
    // eslint-disable-next-line no-underscore-dangle
    query._id = notificationId;
  }
  return Notification.find(query, 'profiles accountId name');
}

async function updateCounters(id, {
  facebook, twitter, youtube, instagram,
}) {
  const update = {};

  if(facebook) {
    update['profiles.facebook.value'] = facebook;
    update['profiles.facebook.lastUpdate'] = Date.now();
  }
  if(twitter) {
    update['profiles.twitter.value'] = twitter;
    update['profiles.twitter.lastUpdate'] = Date.now();
  }
  if(youtube) {
    update['profiles.youtube.value'] = youtube;
    update['profiles.youtube.lastUpdate'] = Date.now();
  }
  if(instagram) {
    update['profiles.instagram.value'] = instagram;
    update['profiles.instagram.lastUpdate'] = Date.now();
  }

  const res = await SocialNotification.updateOne({ _id: id }, update);
  if(!res || res.nModified === 0) {
    throw new Error('failed to update social notification, none modified');
  }
  return res;
}

function formatSocialCount(value) {
  return stringUtils.humanReadableNumber(value);
}
