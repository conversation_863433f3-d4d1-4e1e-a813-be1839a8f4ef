const eventFind = require('./get/eventFindQuery');
const streamFind = require('./get/streamEventFind');
const reviewFind = require('./get/reviewFind');
const constants = require('./constants');

module.exports = async (notification) => {
  try {
    let event = true;
    switch(notification.type) {
    default: {
      const { query, sort, model } = eventFind(notification);

      event = await model
        .findOne(query, { _id: 1 })
        .sort(sort)
        .maxTime(100)
        .hint(model.getHint())
        .cache(10);

      break;
    }

    case constants.notificationTypes.stream: {
      const { query, eventModel } = streamFind(notification);
      event = await eventModel
        .findOne(query, { _id: 1, date: 1 }, { sort: { date: -1 } })
        .hint(eventModel.getHint());
      break;
    }

    case constants.notificationTypes.review: {
      const { query, model } = reviewFind(notification);
      event = await model.findOne(query, { _id: 1 });
      break;
    }
    }
    return !!event;
  } catch(err) {
    return true;
  }
};
