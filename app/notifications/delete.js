const _ = require('lodash');
const Notification = require('./models/Notification');
const logger = require('../../lib/logger')('notification');
const authTypes = require('../../middleware/authTypes');
const notifier = require('../../lib/apis/slackNotifier');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const config = require('../../config');
const slack = require('../../lib/apis/slackNotifier');
const Account = require('../account/models/Account');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    properties: {
      _id: { type: 'string', minLength: 1, required: true },
    },
  },
  async handle(req, res, next) {
    try {
      const { accountId } = req.locals;
      const [notification, numNotifications] = await Promise.all([
        Notification.findOne({ _id: req.body._id, accountId }),
        Notification.count({ accountId }),
      ]);
      if(!notification) return next(ErrorFactory('notification not found', { id: req.body._id }));

      await Notification.populate(notification, { path: 'accountId', select: 'email subscription.plan' });
      const res = await Notification.remove({ _id: notification._id });
      logger.info({ notification }, 'notification removed');
      const options = { webhook: config.slack.notifications };
      notifier.notify(`Notification Deleted by ${notification.accountId.email}`, notification, options);

      if(numNotifications === 1) {
        const email = (notification.accountId && notification.accountId.email) || 'N/A';
        const plan = _.get(notification, 'accountId.subscription.plan', 'free');
        slack.notify(`0 notifications (deleted all): ${email} (${plan}, ${accountId})`, null, { webhook: config.slack.warnings });
      }

      Account.update({ _id: accountId }, {
        $inc: { 'stats.notifications.deleted': 1 },
        'stats.notifications.lastDeleted': Date.now(),
      }).catch(() => {});

      res.body = { message: 'success', _id: notification._id };
      next();
    } catch(err) {
      next(err);
    }
  },
};
