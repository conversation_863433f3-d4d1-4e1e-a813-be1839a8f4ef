const url = require('url');
const parseDomain = require('parse-domain');
const authTypes = require('../../middleware/authTypes');
const logger = require('../../lib/logger')('ping');
const ErrorFactory = require('../../lib/errors/ErrorFactory');

const VisitorPing = require('./models/VisitorPing');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.api,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {},
  },
  async handle(req, res, next) {
    const { accountId } = req.jwtData;
    const psuid = req.signedCookies.psuid || req.headers['x-ps-uid'];
    if(!psuid) {
      return next(ErrorFactory('no user id provided'));
    }

    let host;
    const parsed = parseDomain(req.headers.origin || req.headers.referer);
    if(parsed && parsed.domain && parsed.tld) host = `${parsed.domain}.${parsed.tld}`;
    else host = 'localhost';

    await VisitorPing.update({ uuid: psuid, host }, {
      $set: {}, $setOnInsert: { accountId },
    }, { upsert: true }).exec().catch((err) => {
      logger.error({
        err, accountId, psuid, host,
      }, 'failed to store ping');
    });

    next();
  },
};
