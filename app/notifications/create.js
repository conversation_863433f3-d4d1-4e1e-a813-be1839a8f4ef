/* eslint-disable no-underscore-dangle,no-use-before-define */
const _ = require('lodash');
const parsedDomain = require('parse-domain');
const request = require('superagent');
const sanitizeHtml = require('sanitize-html');
const htmlValidator = require('html-validator');
const isUrl = require('is-url');

const logger = require('../../lib/logger')('notification');
const constants = require('./constants');
const {
  SHOPIFY_EVENT_TYPES, WIX_EVENT_TYPES, THINKIFIC_EVENT_TYPES,
} = require('../constants');
const authTypes = require('../../middleware/authTypes');
const objectUtils = require('../../lib/utils/objectUtils');
const urlUtils = require('../../lib/utils/urlUtils');
const stringUtils = require('../../lib/utils/stringUtils');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const triggers = require('../../lib/triggers');
const socialService = require('../social/social.service');
const config = require('../../config');
const notifierService = require('../common/notifier.service');
const redis = require('../common/redis.service').getClient();
const reviewsController = require('../../cron/jobs/reviews.controller');
const { isValidCountry } = require('../../lib/geo/countryCodes');


const Account = require('../account/models/Account');
const Notification = require('./models/Notification');
const PageVisitsNotification = require('./models/PageVisits');
const ConversionNotification = require('./models/Conversion');
const Combo = require('./models/Combo');
const Stream = require('./models/Stream');
const LiveVisitors = require('./models/LiveVisitors');
const Info = require('./models/Info');
const Review = require('./models/Review');
const SocialCounter = require('./models/SocialCounter');
const CounterCache = require('./models/CounterCache');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    properties: {
      _id: { type: 'string', minLength: 1 },
      step: { type: 'string' },
      name: { type: 'string', minLength: 1, required: true },
      type: { enum: constants.notificationTypesArray, required: true },
      source: { enum: constants.reviewSourcesArray },
      message: { type: 'string' },
      refer: { type: 'string' },
      trackURL: { type: 'array', minLength: 1 },
      displayURLs: { type: 'array' },
      displayExclude: { type: 'array' },
      webhookId: { type: 'string' },
      autoTrack: { type: 'boolean' },
      manuallyShowNotification: { type: 'boolean' },
      image: { type: 'string' },
      localization: { type: 'string', minLength: 2, maxLength: 5 }, // e.g. zh_cn
      design: { type: 'object' },
      someoneAlternatives: { type: 'array', items: { type: 'string' } },
      settings: {
        type: 'object',
        properties: {
          position: { type: constants.positionTypes },
          mobileTop: { type: 'boolean' },
          allowClose: { type: 'boolean' },
          hideOnMobile: { type: 'boolean' },
          hideExactTime: { type: 'boolean' },
          hideExactTimeStream: {
            type: 'object',
            additionalProperties: false,
            properties: {
              active: { type: 'boolean' },
              unit: { type: constants.timeUnitsArray },
              value: { type: 'integer' },
            },
          },
          hideProduct: { type: 'boolean' },
          disableProductLink: { type: 'boolean' },
          sortByLocation: { type: 'boolean' },
          hideOwnConversions: { type: 'boolean' },
          minimumToDisplay: { type: 'integer' },
          maxConversions: { type: 'integer' },
          allTimeEvents: { type: 'boolean' },
          rtl: { type: 'boolean' },
          displayHold: { type: 'integer' },
          filterAnonymous: { type: 'boolean' },
          anonymize: { type: 'boolean' },
          platform: { type: 'string' },
          sessionShowOnce: { type: 'boolean' },
          showTitle: { type: 'boolean' },
          showImage: { type: 'boolean' },
          showDate: { type: 'boolean' },
          filterProducts: {
            include: { type: 'boolean' },
            values: { type: 'array', items: { type: 'string' } },
          },
          filters: {
            countries: {
              include: { type: 'boolean' },
              values: { type: 'array', items: { type: 'string' } },
            },
          },
          combo: {
            type: 'object',
            properties: {
              type: { enum: constants.COMBO_TYPES_ARR, required: true },
              period: { enum: Object.values(constants.COMBO_PERIODS), required: true },
            },
          },
          link: {
            type: 'object',
            properties: {
              active: { type: 'boolean' },
              addUTMParams: { type: 'boolean' },
              value: { type: 'string' },
              newTab: { type: 'boolean' },
              cta: { type: 'object' },
            },
          },
          hideExactNumber: {
            type: 'object',
            properties: {
              active: { type: 'boolean' },
              max: { type: 'integer' },
            },
          },
          timeLimit: {
            type: 'object',
            additionalProperties: false,
            properties: {
              active: { type: 'boolean' },
              unit: { type: constants.timeUnitsArray },
              value: { type: 'integer' },
            },
          },
          theme: {
            type: 'object',
            properties: {
              backgroundColor: { type: 'string' },
              titleColor: { type: 'string' },
              messageColor: { type: 'string' },
              dateColor: { type: 'string' },
              glassEffect: { type: 'boolean' },
              starColor: { type: 'string' },
              useGradientBackground: { type: 'boolean' },
              fontFamily: { type: 'string' },
              animation: { type: 'string' },
              rounded: { type: 'float' },
              title: {
                type: 'object',
                properties: {
                  color: { type: 'string' },
                  backgroundColor: { type: 'string' },
                },
              },
            },
          },
        },
      },
    },
  },
  async shouldBeActive(account) {
    const activeNotification = await Notification.findOne({
      accountId: account.id, active: true,
    });

    let active = true;
    if(account && !account.isSubscriptionActive() && activeNotification) {
      active = false;
    }
    return active;
  },
  async handle(req, res, next) {
    req.body.accountId = req.locals.accountId;
    const { accountId } = req.body;
    const isUpdate = !!req.body._id;
    try {
      const notificationId = req.body._id;
      const [account, dbNotification, sameNameNotification] = await Promise.all([
        Account.findOne({ _id: accountId }),
        req.body._id && Notification.findOne({ accountId, _id: req.body._id }),
        Notification.findOne({
          ...(notificationId && { _id: { $ne: notificationId } }),
          accountId,
          name: req.body.name,
        }).select('name'),
      ]);

      if(notificationId && !dbNotification) {
        throw ErrorFactory('notification does not exist, nothing to update', 500, { accountId, notificationId });
      }
      if(sameNameNotification && notificationId !== sameNameNotification.id) {
        throw ErrorFactory('notification with this name exists, please input a different name');
      }

      const data = await normalizeData(req.body);
      const validityErr = await validateParams(data, accountId);
      if(validityErr) {
        throw validityErr;
      }

      const Model = modelFromType(data.type);
      if(!Model) {
        throw ErrorFactory('notification could not be created', data);
      }

      let notification = null;
      if(isUpdate) {
        const {
          updateValues, fetchReviews, fetchSocial,
        } = notificationUpdateValues(Model, dbNotification, data);
        notification = await Notification
          .findOneAndUpdate({ _id: dbNotification.id }, updateValues, { strict: false, new: true });

        clearCache(notification.id).catch(() => {});
        Account.updateOne({ _id: accountId }, {
          $inc: { 'stats.notifications.updated': 1 },
          'stats.notifications.lastUpdated': Date.now(),
        }).catch(() => {});

        triggers.notificationUpdated(account.email);

        if(fetchReviews) {
          queueReviewsTask(notification);
        }
        if(fetchSocial) {
          socialService.updateSocialCounters(notification);
        }

        logger.info({ notification }, 'notification updated');
      } else {
        data.active = await module.exports.shouldBeActive(account);

        const priorityNotif = await Notification.findOne({ accountId }).sort('-priority');
        data.priority = (priorityNotif && priorityNotif.priority + 1) || 0;

        notification = await new Model(data).save();
        if(notification.type === constants.notificationTypes.review) {
          queueReviewsTask(notification);
        }

        if(notification.type === constants.notificationTypes.social) {
          socialService.updateSocialCounters(notification);
        }

        logger.info({ notification }, 'notification created');

        try {
          const dbAccount = await Account.findOneAndUpdate(
            { _id: accountId },
            {
              $inc: { 'stats.createdNotifications': 1, 'stats.notifications.created': 1 },
              'stats.notifications.lastCreated': Date.now(),
            },
          );
          if(!dbAccount.stats.createdNotifications) dbAccount.stats.createdNotifications = 1;
          else dbAccount.stats.createdNotifications += 1;
          triggers.notificationCreated(notification, dbAccount);
        } catch(err) {}
      }

      res.body = { success: true, _id: notification._id };
      next();
    } catch(err) {
      next(err);
    }
  },

  validateParams,
  normalizeData,
};

function queueReviewsTask(notification) {
  if(config.env === 'dev') {
    return reviewsController.fetchReviews(notification);
  }
  return request.post(`${config.cron.baseUrl()}/fetch-reviews`)
    .send({ notificationId: notification.id })
    .catch((err) => {
      notifierService.notifyError(err, 'failed to queue reviews task', { notification });
      logger.error({ err, notification }, 'failed to get reviews');
    });
}

async function validateParams(params, accountId) {
  const trackType = _.get(params, 'urlTypes.track', constants.URL_TYPES.simple);
  const displayType = _.get(params, 'urlTypes.display', constants.URL_TYPES.simple);

  if(displayType !== constants.URL_TYPES.all) {
    if(!params.manuallyShowNotification
      && (!params.displayURLs || params.displayURLs.length === 0)) {
      return ErrorFactory('It seems you did not specify where to display, go back to the "Display" step');
    }
  }

  if(params.type === constants.notificationTypes.pageVisits || params.autoTrack == null) {
    params.autoTrack = true;
  }

  const platform = _.get(params, 'settings.platform', constants.PLATFORMS.custom);
  const isCustomPlatform = platform === constants.PLATFORMS.custom;
  const isLive = params.type === constants.notificationTypes.live;
  const isInfo = params.type === constants.notificationTypes.info;
  const isReview = params.type === constants.notificationTypes.review;
  const isSocial = params.type === constants.notificationTypes.social;
  const isStream = params.type === constants.notificationTypes.stream;

  if(params.autoTrack
  && isCustomPlatform
  && !isLive
  && !isInfo
  && !isReview
  && !isSocial
  && trackType !== constants.URL_TYPES.all) {
    const url = _.get(params, 'trackURL[0]');
    if(!url || !url.length) {
      return ErrorFactory('You did not specify which URLs to track, go back to the "Track" step');
    } if(url.length > 900) {
      return ErrorFactory('Display URL is too long');
    }

    if(isRegexType(trackType) && !objectUtils.isValidRegex(url)) {
      return ErrorFactory('Please specify a valid Track expression in the "Track" step');
    } if(url.length > 900) {
      return ErrorFactory('Track URL is too long');
    }
  }

  if(!params.manuallyShowNotification && isRegexType(displayType)) {
    const url = _.get(params, 'displayURLs[0]');
    if(!url || !url.length || !objectUtils.isValidRegex(url)) {
      return ErrorFactory('Please specify a valid Display expression in the "Display" step');
    }
  }

  if(_.get(params, 'settings.anonymize') === true && _.get(params, 'settings.filterAnonymous') === true) {
    return ErrorFactory("Both 'Hide anonymous conversions' and 'Anonymize' are toggled ON");
  }

  if(isReview) {
    if(params.source === constants.reviewSources.google) {
      if(!params.placeId || !params.placeId.length) {
        return ErrorFactory('Please specify a place ID in the "Track" step');
      }
    } else if(params.source === constants.reviewSources.trustpilot) {
      const parsed = parsedDomain(params.placeId);
      if(!parsed || !parsed.tld || !parsed.domain) {
        return ErrorFactory('Please specify a valid domain in the "Track" step (e.g mywebsite.com)');
      }
    } else if(params.source === constants.reviewSources.yotpo) {
      if(!params.placeId || !params.placeId.length) {
        return ErrorFactory('Please specify a valid App Key in the "Track" step');
      }
    } else if(params.source === constants.reviewSources.reviewsio) {
      if(!params.placeId || !params.placeId.length) {
        return ErrorFactory('Please specify a valid company/store name in the "Track" step');
      }
    } else if(params.source === constants.reviewSources.facebook) {
      if(stringUtils.isEmptyString(params.pageName)) {
        return ErrorFactory('Please select a page in the "Track" step');
      }
      if(stringUtils.isEmptyString(params.pageToken)) {
        return ErrorFactory('Please select a pageToken in the "Track" step');
      }
      if(stringUtils.isEmptyString(params.placeId)) {
        return ErrorFactory('Please select a pageId in the "Track" step');
      }
    } else if(params.source === constants.reviewSources.capterra) {
      const parsed = parsedDomain(params.placeId);
      const isNotId = _.isNaN(Number(params.placeId));

      if(stringUtils.isEmptyString(params.placeId) || (!parsed && isNotId)) {
        return ErrorFactory('Please specify your Capterra page ID in the \'Track\' step');
      }
    } else if(params.source === constants.reviewSources.judgeme) {
      const parsed = parsedDomain(params.placeId);
      if(!parsed || !parsed.tld || !parsed.domain) {
        return ErrorFactory('Please specify a valid domain in the "Track" step (e.g yourstore.myshopify.com)');
      }

      if(stringUtils.isEmptyString(params.pageToken)) {
        return ErrorFactory('Please specify your Judge.me private token in the "Track" step');
      }
    } else if(params.source === constants.reviewSources.feefo) {
      if(stringUtils.isEmptyString(params.placeId)) {
        return ErrorFactory('Please specify your Feefo Merchant Identifier in the "Track" step');
      }
    } else if(params.source === constants.reviewSources.shopperapproved) {
      if(stringUtils.isEmptyString(params.placeId)) {
        return ErrorFactory('Please specify your Shopper Approved Site ID in the "Track" step');
      }

      if(stringUtils.isEmptyString(params.pageToken)) {
        return ErrorFactory('Please specify your Shopper Approved API Token in the "Track" step');
      }
    } else if(params.source === constants.reviewSources.shapo) {
      if(stringUtils.isEmptyString(params.placeId)) {
        return ErrorFactory('Please specify your Shapo API Key in the "Track" step');
      }
    }
  }
  if(isSocial && isEmptyProfiles(params.profiles)) {
    return ErrorFactory('Please add at least one Social Profile in the Track step');
  }
  if(!isSocial && params.message && params.message.length < 2) {
    return ErrorFactory('Please add a message text in the Message step');
  }

  if(params.image) {
    const isImageUrl = isUrl(params.image);
    const doubleBrackets = params.image.match(/{{([^{}]+)}}(?![{}])/g);
    if(!isImageUrl && !doubleBrackets) {
      return ErrorFactory('Invalid Image URL');
    }
    await validateTemplate(params.image).catch((err) => {
      notifierService.notifyError(err, 'invalid notification image', { accountId, params });
      throw ErrorFactory(`Invalid image: ${err.message}`);
    });
    return null;
  }

  if(params.message) {
    await validateTemplate(params.message).catch((err) => {
      notifierService.notifyError(err, 'invalid notification message template', { accountId, params });
      throw ErrorFactory(`Invalid message template: ${err.message}`);
    });

    const mdHTML = stringUtils.snarkdown(params.message);
    const result = await htmlValidator({ data: mdHTML, isFragment: true }).catch((err) => {
      notifierService.notifyError(err, 'notification message html validation failed', { accountId, params });
      return ErrorFactory('Message HTML is invalid');
    });
    const errorMessages = result && result.messages && result.messages.filter((message) => {
      const msg = message.message.toLowerCase();
      if(msg.includes('body')) {
        return false;
      }
      return msg.includes('unclosed element') || msg.includes('stray end tag');
    }).map(errMsg => errMsg.message);
    if(errorMessages.length > 0) {
      const errorDetails = errorMessages.join(', ');
      const err = ErrorFactory(`Invalid markdown/HTML "Message", unclosed characters, check for symbols [,{,(,*,_: ${errorDetails}`);
      notifierService.notifyError(err, 'invalid notification markdown', { accountId, params });
      return err;
    }
  }

  const filterCountries = _.get(params, 'settings.filters.countries.values', []);
  for(let i = 0; i < filterCountries.length; i += 1) {
    const country = filterCountries[i];
    if(!isValidCountry(country)) {
      const err = ErrorFactory(`The country '${country}' is not valid or is not supported.`);
      notifierService.notifyError(err, 'invalid notification country', { accountId, params });
      return err;
    }
  }

  if(isStream) {
    const eventTypesMap = {
      [constants.PLATFORMS.shopify]: SHOPIFY_EVENT_TYPES,
      [constants.PLATFORMS.thinkific]: THINKIFIC_EVENT_TYPES,
      [constants.PLATFORMS.wix]: WIX_EVENT_TYPES,
    };
    const platformEventTypes = eventTypesMap[platform];
    const { eventType } = params.settings;
    if(platformEventTypes && eventType && !Object.values(platformEventTypes).includes(eventType)) {
      const err = ErrorFactory('Invalid track event type');
      notifierService.notifyError(err, 'invalid notification event type', { accountId, params });
      return err;
    }
  }
  return null;
}

function isRegexType(type) {
  return type === constants.URL_TYPES.contains || type === constants.URL_TYPES.regex;
}

function normalizeData(data) {
  if(_.get(data, 'settings.hideExactNumber.active') && !_.get(data, 'settings.hideExactNumber.max')) {
    data.settings.hideExactNumber.active = false;
    data.settings.hideExactNumber.max = 0;
  }

  const displayType = _.get(data, 'urlTypes.display', constants.URL_TYPES.simple);
  const displayAbsolute = _.get(data, 'urlTypes.displayAbs', true);
  normalizeUrls(data.displayURLs, displayType, displayAbsolute);

  _.remove(data.displayExclude, n => n.value === '');
  normalizeExcludedURLs(data.displayExclude, displayAbsolute);

  const platform = _.get(data, 'settings.platform');
  if(platform && ![
    constants.PLATFORMS.custom,
    constants.PLATFORMS.woocommerce,
    constants.PLATFORMS.wordpress,
  ].includes(platform)) {
    _.set(data, 'urlTypes.track', constants.URL_TYPES.contains);
    _.set(data, 'urlTypes.trackAbs', false);
    const domain = urlUtils.getDomain(_.get(data, 'trackURL[0]'));
    if(domain && !stringUtils.isEmptyString(domain)) {
      data.trackURL[0] = domain;
    }
  } else {
    const trackType = _.get(data, 'urlTypes.track', constants.URL_TYPES.simple);
    const trackAbsolute = _.get(data, 'urlTypes.trackAbs', true);
    normalizeUrls(data.trackURL, trackType, trackAbsolute);
  }

  const placeId = _.get(data, 'placeId');
  if(placeId) {
    const parsed = parsedDomain(placeId);
    if(parsed && data.source === constants.reviewSources.trustpilot) {
      data.placeId = `${parsed.domain}.${parsed.tld}`;
      if(parsed.subdomain) {
        data.placeId = `${parsed.subdomain}.${data.placeId}`;
      }
    }
  }

  if(data.type === constants.notificationTypes.social) {
    _.forEach(data.profiles, (value, key) => {
      if(!value || !value.id) {
        data.profiles[key] = null;
      } else {
        data.profiles[key] = { ...value, value: undefined, lastUpdate: undefined };
      }
    });
  }
  if(data.message) {
    data.message = sanitizeHtml(data.message, {
      allowedAttributes: {
        '*': ['style'],
      },
    });
  }
  return data;
}

function normalizeUrls(urls, type, abs) {
  if(!urls || !urls.length) return;

  for(let i = 0; i < urls.length; i += 1) {
    const url = urls[i];
    let transformed = url;
    switch(type) {
      default:
        break;
      case constants.URL_TYPES.simple:
        if(abs) transformed = urlUtils.normalize(url);
        else transformed = urlUtils.clean(url);
        break;

      case constants.URL_TYPES.contains:
        transformed = urlUtils.encode(removeTrailingSlash(url)).toLowerCase();
        if(!abs) transformed = removeProtocolAndWww(transformed);
        break;

      case constants.URL_TYPES.regex:
        transformed = removeTrailingSlash(url).toLowerCase();
        if(!abs) transformed = removeProtocolAndWww(transformed);
        break;
    }
    urls[i] = transformed;
  }
}

function normalizeExcludedURLs(urls, absolute) {
  if(!urls || !urls.length || !urls[0].value) return;

  for(let i = 0; i < urls.length; i += 1) {
    const url = urls[i].value;
    if(absolute) urls[i].value = urlUtils.normalize(url);
    else urls[i].value = urlUtils.clean(url);
  }
}

function removeTrailingSlash(str) {
  if(str && str[str.length - 1] === '/') return str.slice(0, -1);
  return str;
}

function removeProtocolAndWww(str) {
  let retval = str;
  if(str && str.indexOf('www.') > -1) retval = retval.replace(/www\./, '');
  if(str && str.indexOf('http') > -1) retval = retval.replace(/https?:\/\//, '');
  return retval;
}

function modelFromType(type) {
  const {
    conversion, pageVisits, combo, stream, live, info, review, social,
  } = constants.notificationTypes;
  switch(type) {
    default:
      return null;
    case conversion:
      return ConversionNotification;

    case pageVisits:
      return PageVisitsNotification;

    case combo:
      return Combo;

    case stream:
      return Stream;

    case live:
      return LiveVisitors;

    case info:
      return Info;

    case review:
      return Review;

    case social:
      return SocialCounter;
  }
}

function isEmptyProfiles(profiles) {
  if(!profiles) {
    return true;
  }
  return (!profiles.facebook || stringUtils.isEmptyString(profiles.facebook.id))
    && (!profiles.twitter || stringUtils.isEmptyString(profiles.twitter.id))
    && (!profiles.youtube || stringUtils.isEmptyString(profiles.youtube.id))
    && (!profiles.instagram || stringUtils.isEmptyString(profiles.instagram.id));
}

function notificationUpdateValues(Model, dbNotification, data) {
  let fetchReviews = false;
  let fetchSocial = false;
  const updateValues = _.omit(data, [...Notification.omitKeys, 'profiles', 'stopAutoSync']);
  updateValues.__t = Model.modelName;
  updateValues.type = data.type;
  const socialPlatforms = Object.keys(constants.socialPlatforms);
  for(let i = 0; i < socialPlatforms.length; i += 1) {
    const platform = socialPlatforms[i];
    const profileKey = `profiles.${platform}`;
    const idKey = `profiles.${platform}.id`;
    if(_.get(dbNotification, idKey) !== _.get(data, idKey)) {
      fetchSocial = true;
      updateValues[profileKey] = _.get(data, profileKey);
    }
  }
  if(dbNotification.placeId !== data.placeId
    || dbNotification.source !== data.source
    || dbNotification.pageToken !== data.pageToken) {
    fetchReviews = true;
  }
  return { updateValues, fetchReviews, fetchSocial };
}

function clearCache(notificationId) {
  return Promise.all([
    CounterCache.remove({ notificationId }),
    redis.del(`counter:${notificationId}`),
  ]);
}


async function validateTemplate(template) {
  if(!template) {
    return null;
  }
  const tripleBrackets = template.match(/{{{([^{}]+)}}}(?![{}])/g);
  if(tripleBrackets) {
    throw ErrorFactory('template variables should be added between double brackets {{var}}');
  }
  if(template.includes('#with') || template.includes('/with')) {
    throw ErrorFactory('template #with operator is not allowed');
  }
  const doubleBrackets = template.match(/{{([^{}]+)}}(?![{}])/g);
  if(doubleBrackets && doubleBrackets.length > 0) {
    const hasInvalid = doubleBrackets.some(variable => variable.includes(' '));
    if(hasInvalid) {
      throw ErrorFactory('template variables should not contain spaces');
    }
  }
  return null;
}
