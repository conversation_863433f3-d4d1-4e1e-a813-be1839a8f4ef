const moment = require('moment');
const authTypes = require('../../middleware/authTypes');

const config = require('../../config');
const logger = require('../../lib/logger')('analytics');
const dateUtils = require('../../lib/utils/dateUtils');
const constants = require('./constants');

const { ANALYTICS_EVENTS } = constants;
const ErrorFactory = require('../../lib/errors/ErrorFactory');

const Account = require('../account/models/Account');
const AnalyticsEvent = require('./models/AnalyticsEvent');
const Notification = require('./models/Notification');
const segment = require('../../lib/apis/segment');

module.exports = {
  config: {
    methods: ['POST', 'GET'],
    authType: {
      POST: authTypes.api,
      GET: authTypes.console,
    },
  },
  schema: {
    POST: {
      type: 'object',
      additionalProperties: false,
      properties: {
        id: { type: 'string', pattern: /^[a-fA-F0-9]{24}$/ },
        event: { type: 'string', enum: Object.values(constants.ANALYTICS_EVENTS), required: true },
        visitor: { type: 'boolean' },
        segmentUserId: { type: 'string' },
      },
    },
    GET: {
      type: 'object',
      additionalProperties: false,
      properties: {
        id: { type: 'string', pattern: /^[a-fA-F0-9]{24}$/ },
        startDate: { type: 'string', pattern: /^\d{4}-\d{1,2}-\d{1,2}$/ },
        endDate: { type: 'string', pattern: /^\d{4}-\d{1,2}-\d{1,2}$/ },
      },
    },
  },

  async handleGET(req, res, next) {
    const { accountId } = req.locals;
    const notificationId = req.query.id || null;
    let startDate;
    if(req.query.startDate) {
      startDate = new Date(`${req.query.startDate}UTC`);
    } else {
      startDate = dateUtils.todayNormalized12am() - 7 * dateUtils.MILLISECONDS_IN_DAY;
    }

    let endDate;
    if(req.query.endDate) {
      endDate = new Date(`${req.query.endDate}UTC`);
    } else {
      endDate = Date.now();
    }

    if(startDate && endDate && endDate < startDate) {
      const temp = startDate;
      startDate = endDate;
      endDate = temp;
    }

    const query = { accountId, notificationId, date: { $gte: startDate, $lte: endDate } };
    const projection = { views: 0, clicks: 0, hovers: 0 };
    const events = await AnalyticsEvent.find(query, projection).sort({ date: -1 }).lean();

    const result = {
      visitors: 0, engagedVisitors: 0, views: 0, hovers: 0, clicks: 0,
    };
    Object.assign(result, {
      conversions: 0, viewConversions: 0, hoverConversions: 0, clickConversions: 0,
    });
    Object.assign(result, { webhooks: 0, woocommerceOrders: 0 });
    Object.assign(result, { dates: {} });
    for(let i = 0; i < events.length; i++) {
      const event = events[i];
      result.visitors += event.total.visitors || 0;
      const engagedVisitors = event.total.engagedVisitors > event.total.visitors
        ? event.total.visitors
        : event.total.engagedVisitors;
      result.engagedVisitors += engagedVisitors || 0;
      result.views += event.total.views || 0;
      result.hovers += event.total.hovers || 0;
      result.clicks += event.total.clicks || 0;
      result.conversions += event.total.conversions || 0;
      result.viewConversions += event.total.viewConversions || 0;
      result.hoverConversions += event.total.hoverConversions || 0;
      result.clickConversions += event.total.clickConversions || 0;
      result.webhooks += event.total.webhooks || 0;
      result.woocommerceOrders += event.total.woocommerceOrders || 0;
      if(!result.dates[event.date]
          || (result.dates[event.date]
              && result.dates[event.date].visitors < event.total.visitors)
      ) {
        result.dates[event.date] = {
          date: event.date,
          visitors: event.total.visitors || 0,
          engagedVisitors: engagedVisitors || 0,
          views: event.total.views || 0,
          hovers: event.total.hovers || 0,
          clicks: event.total.clicks || 0,
          conversions: event.total.conversions || 0,
          viewConversions: event.total.viewConversions || 0,
          hoverConversions: event.total.hoverConversions || 0,
          clickConversions: event.total.clickConversions || 0,
          webhooks: event.total.webhooks || 0,
          woocommerceOrders: event.total.woocommerceOrders || 0,
        };
      }
    }
    result.dates = Object.values(result.dates).sort((a, b) => b.date - a.date);
    res.body = result;

    next();
  },

  handlePOST(req, res, next) {
    return processNotificationEvent(req, res, next);
  },
};

async function processNotificationEvent(req, res, next) {
  const { accountId } = req.jwtData;
  const notificationId = req.body.id;

  if(!notificationId) {
    logger.warn({ accountId, notificationId }, 'no notification id');
    return next(ErrorFactory('notification id is required'));
  }

  const [notification, account] = await Promise.all([
    Notification.findOne({ _id: notificationId }).select('_id name'),
    Account.findOneAndUpdate({ _id: accountId }, {
      'stats.notifications.lastImpression': Date.now(),
    }).select('integrations onboarding'),
    Notification.updateOne({ _id: notificationId }, { lastView: Date.now() }),
  ]);

  if(account && account.onboarding && !account.onboarding.impression) {
    let impressionDate = Date.now();
    if(account.onboarding.installed < new Date('2021-10-01')) {
      impressionDate = account.onboarding.installed.getTime() + 86400 * 1000;
    }
    Account.updateOne({ _id: accountId }, { 'onboarding.impression': impressionDate }).exec().catch(() => {});
  }

  if(!notification) {
    logger.warn({ accountId, notificationId }, 'notification not found');
    return next();
  }

  const date = dateUtils.todayNormalized12am();
  const { event } = req.body;

  logger.info({ notificationId, event }, 'updating analytics');

  const accountQuery = { accountId, notificationId: null, date };
  const notificationQuery = { accountId, notificationId, date };

  const notificationUpdate = { $inc: AnalyticsEvent.incQuery(event) };
  const accountUpdate = Object.assign({}, notificationUpdate);
  if(req.body.visitor) accountUpdate.$inc['total.engagedVisitors'] = 1;

  const opts = { upsert: true };
  const promises = [
    AnalyticsEvent.update(accountQuery, accountUpdate, opts),
    AnalyticsEvent.update(notificationQuery, notificationUpdate, opts),
  ];

  if(req.body.segmentUserId && account.integrations.segment.writeKey) {
    promises.push(segment.track(account.integrations.segment.writeKey, {
      userId: req.body.segmentUserId,
      event: getSegmentEventName(event),
      properties: {
        notificationId,
        notificationName: notification.name,
      },
      context: config.segment.context,
    }).catch((err) => {
      logger.error({ err, notificationId, event }, 'failed to send notification analytics to segment');
    }));
  }

  Promise.all(promises).catch((err) => {
    logger.error({
      err, critical: true, notificationId, event,
    }, 'failed updating analytics');
  });

  next();
}

function getSegmentEventName(event) {
  switch(event) {
  default:
  case ANALYTICS_EVENTS.view:
    return 'Notification Viewed';
  case ANALYTICS_EVENTS.hover:
    return 'Notification Hovered';
  case ANALYTICS_EVENTS.click:
    return 'Notification Clicked';
  }
}
