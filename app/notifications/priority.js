const Notification = require('./models/Notification');

module.exports.setPriority = async function (accountId, orderedIds) {
  if(!accountId) return;
  if(!orderedIds || !orderedIds.length) return;

  const ops = [];
  for(let i = 0; i < orderedIds.length; i++) {
    const _id = orderedIds[i];
    ops.push({
      updateOne: {
        filter: { _id, accountId },
        update: { $set: { priority: i } },
      },
    });
  }
  await Notification.bulkWrite(ops);
};
