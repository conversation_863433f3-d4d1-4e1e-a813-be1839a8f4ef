const Notification = require('./models/Notification');
const Stream = require('./models/Stream');
const PageVisits = require('./models/PageVisits');
const Info = require('./models/Info');
const notifConsts = require('../notifications/constants');
const { dateUtils, cryptoUtils } = require('../../lib/utils');
const config = require('../../config');

module.exports = {
  getNotification,
  createWooNotification,
  makePageVisits,
  makeThinkificNotification,
  getNotShownNotifications,
  getWebhookStream,
  getInformational,
};

function getNotification({ id }) {
  return Notification.findOne({ _id: id });
}

function createWooNotification({ accountId, domain: fullDomain, active }) {
  let domain = fullDomain;
  if(fullDomain && fullDomain.includes('www.')) {
    domain = fullDomain.replace('www.', '');
  }
  return (new Stream({
    accountId,
    active,
    name: `Recent Purchases - ${domain || fullDomain}`,
    message: 'purchased',
    someoneAlternatives: ['Someone Great', 'A Happy Customer'],
    autoTrack: false,
    urlTypes: {
      track: notifConsts.URL_TYPES.contains,
      display: (domain && notifConsts.URL_TYPES.contains) || notifConsts.URL_TYPES.all,
    },
    trackURL: (domain && [domain]) || [],
    displayURLs: (domain && [domain]) || [],
    settings: {
      platform: notifConsts.PLATFORMS.woocommerce,
      displayHold: 8,
      position: notifConsts.positionTypes[0],
      hideExactTimeStream: {
        active: true,
        unit: notifConsts.timeUnits.Hours,
        value: 48,
      },
    },
  })).save();
}

function getNotShownNotifications(accountId, { projection }) {
  const dayAgo = Date.now() - 24 * dateUtils.MILLISECONDS_IN_HOUR;
  return Notification.find({
    accountId,
    active: true,
    $or: [
      { lastView: { $lt: dayAgo } },
      { lastView: { $exists: false } },
    ],
  }, projection);
}

function makePageVisits(accountId, domain) {
  return new PageVisits({
    accountId,
    active: true,
    urlTypes: {
      track: notifConsts.URL_TYPES.contains,
      display: notifConsts.URL_TYPES.all,
      trackAbs: false,
      displayAbs: false,
    },
    trackURL: [domain],
    localization: 'en',
    autoTrack: true,
    type: notifConsts.notificationTypes.pageVisits,
    name: 'Website Page Views (All Website)',
    message: 'have visited our store',
    refer: 'buyers',
  });
}

function makeThinkificNotification(accountId) {
  return new Stream({
    accountId,
    active: true,
    name: 'Recent Thinkific Events',
    message: 'purchased the course',
    someoneAlternatives: ['Someone Great', 'A Happy Customer'],
    autoTrack: false,
    type: notifConsts.notificationTypes.stream,
    urlTypes: {
      track: notifConsts.URL_TYPES.contains,
      display: notifConsts.URL_TYPES.all,
      trackAbs: false,
      displayAbs: false,
    },
    settings: {
      platform: notifConsts.PLATFORMS.thinkific,
      displayHold: 8,
      position: notifConsts.positionTypes[0],
      theme: { title: { color: '#000000' } },
      hideExactTimeStream: {
        active: true,
        unit: notifConsts.timeUnits.Hours,
        value: 48,
      },
    },
  });
}

function getWebhookStream({ accountId, name }) {
  return new Stream({
    accountId,
    name: name || 'Signup',
    message: 'Got early access',
    trackURL: [],
    webhookId: cryptoUtils.encrypt(cryptoUtils.randomString(8), config.cryptoKeys.webhook),
    autoTrack: false,
    displayExclude: [],
    displayURLs: [],
    urlTypes: {
      displayAbs: false,
      trackAbs: false,
      track: 'contains',
      display: 'all',
    },
    settings: {
      allowClose: false,
      hideExactNumber: {
        active: false,
        max: 0,
      },
      filterProducts: {
        include: true,
        values: [],
      },

      timeLimit: {
        active: false,
        unit: 'Minutes',
      },
      visitor: {
        type: 'all',
      },
      position: 'Bottom Left',
      platform: 'custom',
    },
    active: true,
  });
}

function getInformational({ accountId, name, message }) {
  return new Info({
    accountId,
    name: name || 'Informational',
    message: message || 'Thanks for signing up!',
    type: notifConsts.notificationTypes.info,
    autoTrack: false,
    urlTypes: {
      displayAbs: false,
      trackAbs: false,
      track: 'simple',
      display: 'all',
    },
    active: true,
    settings: {
      theme: {
        title: {
          color: '#2d2d2d',
          backgroundColor: 'transparent',
        },
      },
    },
    trackURL: [],
    image: 'https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/assets/shield.png',
    displayExclude: [],
    displayURLs: [],
  });
}
