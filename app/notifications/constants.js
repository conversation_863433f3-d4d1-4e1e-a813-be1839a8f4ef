const notificationTypes = {
  conversion: 'conversion',
  pageVisits: 'pageVisits',
  combo: 'combo',
  stream: 'stream',
  live: 'live',
  info: 'info',
  review: 'review',
  social: 'social',
};
const notificationTypesArray = Object.values(notificationTypes);

const Hours = 'Hours';
const Minutes = 'Minutes';

const URL_TYPES = {
  simple: 'simple',
  contains: 'contains',
  regex: 'regex',
  all: 'all',
};
const URL_TYPES_ARRAY = Object.values(URL_TYPES);


const COMBO_TYPES = {
  conversions: 'conversions',
  visits: 'visits',
};
const COMBO_TYPES_ARR = Object.values(COMBO_TYPES);

const COMBO_PERIODS = {
  day: 'day',
  week: 'week',
  month: 'month',
  all: 'all',
};

const COMBO_PERIOD_DAYS = {
  [COMBO_PERIODS.day]: 1,
  [COMBO_PERIODS.week]: 7,
  [COMBO_PERIODS.month]: 30,
  [COMBO_PERIODS.all]: Infinity,
};

const PLATFORMS = {
  custom: 'custom',
  woocommerce: 'woocommerce',
  shopify: 'shopify',
  magento: 'magento',
  magento1: 'magento1',
  wordpress: 'wordpress',
  wix: 'wix',
  bigcommerce: 'bigcommerce',
  thinkific: 'thinkific',
};

const reviewSources = {
  shapo: 'shapo',
  google: 'google',
  trustpilot: 'trustpilot',
  reviewsio: 'reviewsio',
  facebook: 'facebook',
  yotpo: 'yotpo',
  stamped: 'stamped',
  capterra: 'capterra',
  judgeme: 'judgeme',
  feefo: 'feefo',
  shopperapproved: 'shopperapproved',
  wix: 'wix',
  other: 'other',
};

const socialPlatforms = {
  facebook: 'facebook',
  instagram: 'instagram',
  twitter: 'twitter',
  youtube: 'youtube',
};

const reviewSourcesArray = Object.values(reviewSources);

const EXCLUDE_MATCHERS = {
  notEqual: 'not-equals',
  notContains: 'not-contains',
};

const MOBILE_DESIGN = {
  hide: 'hide',
  small: 'small',
  normal: 'normal',
};

module.exports = {
  URL_TYPES,
  URL_TYPES_ARRAY,
  COMBO_TYPES,
  COMBO_TYPES_ARR,
  COMBO_PERIODS,
  COMBO_PERIOD_DAYS,
  notificationTypesArray,
  notificationTypes,
  PLATFORMS,
  reviewSources,
  reviewSourcesArray,
  socialPlatforms,
  EXCLUDE_MATCHERS,
  MOBILE_DESIGN,
  ANALYTICS_EVENTS: {
    view: 'view',
    hover: 'hover',
    click: 'click',
  },
  positionTypes: ['Bottom Left', 'Bottom Right', 'Bottom Center', 'Top', 'Top Left', 'Top Right'],
  timeUnitsArray: [Hours, Minutes],
  timeUnits: { Hours, Minutes },
  getPickFields: ['_id', 'type', 'name', 'refer', 'title', 'message', 'image', 'settings', 'localization', 'priority'],
  getOmitField: [
    'settings.filters',
    'settings.filterProducts',
    // 'settings.sortByLocation', // used in getWithUrl/Name sort by location
    'settings.shuffle',
    'settings.hideEmpty',
    'settings.timeLimit',
    'settings.ignoreEventLimit',
    'settings.firstNameOnly',
    // 'priority',
  ],
  maxQueryMS: 30000,
  maxEvents: 1000,
  defaultReviewImage: 'https://proofsource-sdks.s3.amazonaws.com/avatar.svg',
  googleDefaultReview: 'photo.jpg', // includes photo.jpg in the end
  defaultImage: 'https://cdn.provesrc.com/icon.gif',
  visitorTypes: {
    all: 'all',
    new: 'new',
    returning: 'returning',
  },
};
