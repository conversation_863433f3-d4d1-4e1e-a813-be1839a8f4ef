const _ = require('lodash');
const parseDomain = require('parse-domain');
const constants = require('../constants');
const redis = require('../../common/redis.service').getClient();
const VisitorPing = require('../models/VisitorPing');

/**
 *
 * @param {object} notification the live visitor count notification
 * @param {string} url the /notification/get url to parse the visitors for the page
 * @param {function} callback
 * @return {Promise<void>}
 */
module.exports = async function (notification, url, includeSubdomain = false, callback) {
  const { accountId } = notification;

  try {
    let host;
    const parsed = parseDomain(url);
    if(parsed && parsed.tld && parsed.domain) {
      host = `${parsed.domain}.${parsed.tld}`;
      if(includeSubdomain && parsed.subdomain) {
        host = `${parsed.subdomain}.${host}`;
      }
    } else {
      host = 'localhost';
    }

    const date = Date.now() - 600 * 1000;
    const count = await VisitorPing.count({ accountId, host, updatedAt: { $gte: date } }) || 0;
    const minimum = notification.getMinimumToDisplay();

    if(count < minimum) {
		  return callback();
    }
    return callback(null, formatResult(notification, count));
  } catch(err) {
    callback(err);
  }
};

function formatResult(notification, count) {
  return Object.assign(_.pick(notification, constants.getPickFields), { count });
}
