const _ = require('lodash');
const constants = require('../constants');
const { urlUtils, dateUtils } = require('../../../lib/utils');
const utils = require('./utils');

const WebhookEvent = require('../../events/models/WebhookEvent');
const WebsiteEvent = require('../../events/models/WebsiteEvent');
const CleanWebsiteEvent = require('../../events/models/CleanWebsiteEvent');

const FormEvent = require('../../events/models/FormEvent');
const CleanFormEvent = require('../../events/models/CleanFormEvent');

const getEventUrlQuery = require('./getEventUrlQuery');

module.exports = function eventFindQuery(notification, guid = null) {
  const { accountId } = notification;

  let model = null;
  const query = {};

  switch(notification.type) {
  case constants.notificationTypes.conversion:
    if(notification.autoTrack) {
      assignAccountId(query, notification.accountId);
      assignUrl(query, notification);
      model = FormEvent;
    } else {
      assignWebhook(query, notification.webhookId, guid);
      model = WebhookEvent;
    }
    break;

  case constants.notificationTypes.combo:
    const comboType = notification.settings.combo.type;
    if(notification.autoTrack || comboType === constants.COMBO_TYPES.visits) {
      assignAccountId(query, accountId);
      assignUrl(query, notification);

      if(comboType === constants.COMBO_TYPES.visits) model = WebsiteEvent;
      else if(comboType === constants.COMBO_TYPES.conversions) model = FormEvent;
    } else {
      assignWebhook(query, notification.webhookId, guid);
      model = WebhookEvent;
    }
    break;

  case constants.notificationTypes.pageVisits:
    assignAccountId(query, accountId);
    assignUrl(query, notification, guid);
    model = WebsiteEvent;
    break;
  }

  let minimumDay = null;
  let countToMinute = 0;
  if(!notification.isTrackAbsolute()) {
    if(model === WebsiteEvent) model = CleanWebsiteEvent;
    if(model === FormEvent) model = CleanFormEvent;
  }

  const dateKey = (model && model.getDateKey && model.getDateKey()) || 'date';
  if(!notification.isAllTime()) {
    ({ minimumDay, countToMinute } = utils.notificationTimeLimit(notification));
    if(minimumDay) {
      query[dateKey] = { $lte: Date.now(), $gte: minimumDay };
    } else {
      // Temp fix for huge collection scans -> take only 14 days back
      const sevenBack = dateUtils.normalize(Date.now() - 14 * dateUtils.MILLISECONDS_IN_DAY);
      query[dateKey] = { $gte: sevenBack };
    }
  }

  const sort = { [dateKey]: -1 };

  return {
    query, sort, model, minimumDay, countToMinute,
  };
};

function assignAccountId(query, accountId) {
  Object.assign(query, { accountId });
}

function assignUrl(query, notification, page) {
  let shortUrl;
  if(page) {
    if(notification.isTrackAbsolute()) {
      shortUrl = urlUtils.normalize(page);
    } else {
      shortUrl = urlUtils.clean(page);
    }
  } else {
    shortUrl = getEventUrlQuery(notification);
  }
  if(shortUrl) {
    Object.assign(query, { shortUrl });
  }
}

function assignWebhook(query, webhookId, guid) {
  if(guid) {
    Object.assign(query, { webhookId, guid });
  } else {
    Object.assign(query, { webhookId });
  }
}
