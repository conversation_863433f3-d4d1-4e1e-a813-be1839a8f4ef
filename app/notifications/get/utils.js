const constants = require('../constants');
const dateUtils = require('../../../lib/utils/dateUtils');

module.exports = {
  shouldShowToVisitorType(visitorType, daysAsReturning, firstTimestamp) {
    if(visitorType !== constants.visitorTypes.all) {
      const now = Date.now();
      const timeAsReturning = daysAsReturning * dateUtils.MILLISECONDS_IN_DAY;
      // if time passed is less than returning threshold
      const isReturningVisitor = firstTimestamp && now - firstTimestamp < timeAsReturning;
      // don't show if visitor is returning and visitorType = new
      // or if visitor is not returning and visitorType = returning
      if((!isReturningVisitor && visitorType === constants.visitorTypes.returning)
        || (isReturningVisitor && visitorType === constants.visitorTypes.new)) {
        return false;
      }
    }
    return true;
  },

  /**
   * Helper method for finding query day range and lowest minute to search
   * @param {Object} notification
   * @returns {{exactMinimum: Date, minimumDay: Date, countToMinute: Number, daysDiff: Number}}
   */
  notificationTimeLimit(notification) {
    const minuteOfDay = dateUtils.minuteOfTheDay();
    const now = Date.now();
    let exactMinimum;
    let countToMinute = 0;

    if(notification.type === constants.notificationTypes.combo) {
      countToMinute = minuteOfDay;
      switch(notification.settings.combo.period) {
      default:
      case constants.COMBO_PERIODS.day:
        exactMinimum = now - Date.MILLISECONDS_IN_DAY;
        break;
      case constants.COMBO_PERIODS.week:
        exactMinimum = now - Date.MILLISECONDS_IN_DAY * 7;
        break;
      case constants.COMBO_PERIODS.month:
        exactMinimum = now - Date.MILLISECONDS_IN_DAY * 30;
        break;
      }
    } else if(notification.isTimeLimitActive()) {
      let minutesDiff;
      const value = notification.getTimeLimitValue();
      switch(notification.getTimeLimitUnit()) {
      default:
        break;
      case constants.timeUnits.Hours:
        exactMinimum = now - value * Date.MILLISECONDS_IN_HOUR;
        minutesDiff = minuteOfDay - value * Date.MINUTES_IN_HOUR;
        break;
      case constants.timeUnits.Minutes:
        exactMinimum = now - value * Date.MILLISECONDS_IN_MINUTE;
        minutesDiff = minuteOfDay - value;
        break;
      }
      // Find the lowest "counts" key we can traverse in the last date
      const modulo = Math.abs(minutesDiff % Date.MINUTES_IN_DAY);
      if(modulo === minutesDiff) countToMinute = minutesDiff;
      else countToMinute = Date.MINUTES_IN_DAY - modulo;
    }
    const minimumDay = dateUtils.normalize(exactMinimum);
    return {
      exactMinimum, minimumDay, countToMinute, daysDiff: Date.daysBetween(now, minimumDay),
    };
  },
};

module.exports.compareCountries = function (a, b, countryCode) {
  if((a === countryCode && b === countryCode)
    || (a !== countryCode && b !== countryCode)) {
    return 0;
  }
  if(a === countryCode) {
    return -1;
  }
  return 1;
};
