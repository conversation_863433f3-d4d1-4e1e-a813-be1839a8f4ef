const _ = require('lodash');

const config = require('../../../config');
const logger = require('../../../lib/logger')('processReview');

const { endpoint, bucket } = config.digitalOcean.spaces;
const NOTIFICATION_LOG_PARAMS = ['_id', 'accountId', 'name', '__t'];
const constants = require('../constants');
const reviewFind = require('./reviewFind');
const filterService = require('../../common/filter.service');

module.exports = async function processReview(notification, { guid, account }, callback) {
  const notificationInfo = _.pick(notification, NOTIFICATION_LOG_PARAMS);
  const { query, model } = reviewFind(notification, { guid });
  const { maxConversions = 5, shuffle = false, hideEmpty = false } = notification.settings;
  const maxConversionsLimit = _.get(account, 'configuration.streamQuery.maxConversionsLimit', 50);
  let limit = maxConversions;
  if(limit < 1) {
    limit = 1;
  } else if(limit > maxConversionsLimit) {
    limit = maxConversionsLimit;
  }

  const reviews = await model.find(query, null, { sort: { time: -1 } }).limit(100).cache(60).exec();
  let results = reviews
    .map(review => getFormattedReview(notification, review))
    .filter(review => !filterService.shouldIgnoreEvent({
      configuration: account.configuration,
      name: review.data.authorName,
      text: review.data.text,
    }));
  if(hideEmpty) {
    results = results.filter(res => res.data.text && res.data.text.trim().length);
  }
  if(shuffle) {
    results = _.shuffle(results);
  }
  results = results.slice(0, limit);

  if(!results.length) {
    logger.warn({ notification: notificationInfo, query }, 'no review events for notification');
    return callback();
  }
  logger.info({ notification: notificationInfo, results }, 'processed review events for notification');
  return callback(null, { notifications: results });
};

function getFormattedReview(notification, review) {
  const result = _(notification.toObject())
    .pick(constants.getPickFields)
    .omit(constants.getOmitField)
    .value();
  result.data = (review.getData && review.getData(endpoint, bucket)) || {};
  const hasReviewImage = result.data.profilePhotoUrl
    && result.data.profilePhotoUrl !== constants.defaultReviewImage;
  if(notification.image && notification.image !== constants.defaultImage && !hasReviewImage) {
    result.data.profilePhotoUrl = notification.image;
  }
  if(notification.settings && notification.settings.anonymize) {
    result.data.authorName = 'Someone';
  }
  if(_.get(notification, 'settings.firstNameOnly')) {
    result.data.authorName = result.data.authorName.split(' ')[0];
  }
  if(!result.data.profilePhotoUrl) {
    result.data.profilePhotoUrl = constants.defaultReviewImage;
  }
  return result;
}
