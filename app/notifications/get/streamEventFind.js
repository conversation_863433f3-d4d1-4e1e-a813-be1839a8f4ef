const _ = require('lodash');
const constants = require('../constants');
const utils = require('./utils');
const getEventUrlQuery = require('./getEventUrlQuery');

const FormStreamEvent = require('../../events/models/FormStreamEvent');
const CleanFormStreamEvent = require('../../events/models/CleanFormStreamEvent');
const WebhookStreamEvent = require('../../events/models/WebhookStreamEvent');
const WooEvent = require('../../events/models/WooEvent').model;
const ShopifyEvent = require('../../events/models/ShopifyEvent');
const Magento2Event = require('../../events/models/Magento2Event');
const Magento1Event = require('../../events/models/Magento1Event');
const WPEvent = require('../../events/models/WPEvent');
const WixEvent = require('../../wix/models/WixEvent');
const BigcommerceEvent = require('../../bigcommerce/models/BigcommerceEvent');
const ThinkificEvent = require('../../thinkific/ThinkificEvent');

const { PLATFORMS } = constants;

module.exports = function streamEventFind(notification, options = {}) {
  const query = { accountId: notification.accountId };
  const platform = _.get(notification, 'settings.platform', null);
  let eventModel;

  if(platform === PLATFORMS.woocommerce) {
    eventModel = WooEvent;
  } else if(platform === PLATFORMS.shopify) {
    eventModel = ShopifyEvent;
  } else if(platform === PLATFORMS.magento) {
    eventModel = Magento2Event;
  } else if(platform === PLATFORMS.magento1) {
    eventModel = Magento1Event;
  } else if(platform === PLATFORMS.wordpress) {
    eventModel = WPEvent;
  } else if(platform === PLATFORMS.wix) {
    eventModel = WixEvent;
  } else if(platform === PLATFORMS.bigcommerce) {
    eventModel = BigcommerceEvent;
  } else if(platform === PLATFORMS.thinkific) {
    eventModel = ThinkificEvent;
  } else if(notification.autoTrack) {
    const urlQuery = getEventUrlQuery(notification);
    if(urlQuery) {
      query.shortUrl = urlQuery;
    }
    if(_.get(notification, 'urlTypes.trackAbs')) {
      eventModel = FormStreamEvent;
    } else {
      eventModel = CleanFormStreamEvent;
    }
  } else {
    query.webhookId = notification.webhookId;
    eventModel = WebhookStreamEvent;
  }

  Object.assign(
    query,
    getGuidQueryPart(notification, options.guid),
    getTrackHostQueryPart(notification),
    notification.canShowProducts() && getProductPart(notification.settings.filterProducts),
    getFilters(notification),
  );

  const { exactMinimum } = utils.notificationTimeLimit(notification);
  if(exactMinimum && exactMinimum > 0) {
    query.date = { $gte: exactMinimum };
  }

  return { eventModel, query };
};

function getFilters(notification) {
  const platform = _.get(notification, 'settings.platform', null);
  const eventType = _.get(notification, 'settings.eventType', '');
  const status = _.get(notification, 'settings.filters.status');
  let filters = null;
  if(eventType && eventType !== 'all' && [PLATFORMS.thinkific, PLATFORMS.wix, PLATFORMS.shopify].includes(platform)) {
    filters = { type: eventType };
  }
  // currently, only on wix? options are: DRAFT, ACTIVE, PENDING
  if(status) {
    if(!filters) {
      filters = {};
    }
    filters.status = status;
  }
  return filters;
}

function getTrackHostQueryPart(notification) {
  if(notification.trackURL && notification.trackURL[0]) {
    const eventQuery = getEventUrlQuery(notification);
    const platform = _.get(notification, 'settings.platform', null);
    switch(platform) {
      case PLATFORMS.woocommerce:
      case PLATFORMS.magento:
      case PLATFORMS.wordpress:
        return { host: eventQuery };

      case PLATFORMS.magento1:
        return {
          $or: [
            { host: eventQuery },
            { frontUrl: eventQuery },
          ],
        };

      case PLATFORMS.bigcommerce:
      case PLATFORMS.shopify: {
        return {
          $or: [
            { domain: eventQuery },
            { shop: eventQuery },
            { store: eventQuery },
          ],
        };
      }

      case PLATFORMS.thinkific:
        return { domain: getEventUrlQuery(notification) };
    }
  }
  return null;
}

function getGuidQueryPart(notification, guid) {
  if(!guid) {
    return null;
  }
  const platform = _.get(notification, 'settings.platform', null);
  switch(platform) {
  // TODO platforms don't have a guid, these should be with shopify (also default:)?
    case PLATFORMS.woocommerce:
    case PLATFORMS.magento:
    case PLATFORMS.magento1:
    case PLATFORMS.wordpress:
    case PLATFORMS.bigcommerce:
    case PLATFORMS.custom:
      return { guid };

    case PLATFORMS.thinkific:
    case PLATFORMS.shopify:
      return { 'products.id': guid };
  }
  return null;
}

function getProductPart(filter) {
  let retval = null;
  if(filter && filter.values && filter.values.length > 0) {
    const valuesJoined = filter.values.map(_.escapeRegExp).join('|');
    if(filter.include) {
      retval = { 'products.name': new RegExp(`(${valuesJoined})`, 'i') };
    } else {
      retval = { 'products.name': new RegExp(`(?!.*(${valuesJoined}))`, 'i') };
    }
  }
  return retval;
}
