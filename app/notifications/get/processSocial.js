const _ = require('lodash');
const numeral = require('numeral');
const constants = require('../constants');
const { isEmptyString } = require('../../../lib/utils/stringUtils');
const socialNotificationService = require('../social.notification.service');

const twitter = require('../../../lib/apis/twitter');
const youtube = require('../../../lib/apis/youtube');
const instagram = require('../../../lib/apis/instagram');

module.exports = function (notification) {
  const { profiles } = notification;
  const socialNetworks = { profiles: {} };

  if(profiles && profiles.facebook) {
    socialNetworks.profiles.facebook = getProfile(profiles.facebook.id, profiles.facebook.value);
  }
  if(profiles && profiles.twitter) {
    socialNetworks.profiles.twitter = getProfile(
      twitter.getHandle(profiles.twitter.id), profiles.twitter.value,
    );
  }
  if(profiles && profiles.youtube) {
    socialNetworks.profiles.youtube = getProfile(
      youtube.getId(profiles.youtube.id), profiles.youtube.value,
    );
  }
  if(profiles && profiles.instagram) {
    socialNetworks.profiles.instagram = getProfile(
      instagram.getProfileId(profiles.instagram.id), profiles.instagram.value,
    );
  }

  if(emptyValidator(socialNetworks.profiles)) return null;

  return Object.assign(_.pick(notification, constants.getPickFields), socialNetworks);
};

function getProfile(id, value) {
  if(isEmptyString(id) || !value) {
    return undefined;
  }
  return { id, value: socialNotificationService.formatSocialCount(value) };
}

function emptyValidator(profiles) {
  return _.isEmpty(profiles.facebook)
    && _.isEmpty(profiles.twitter)
    && _.isEmpty(profiles.youtube)
    && _.isEmpty(profiles.instagram);
}
