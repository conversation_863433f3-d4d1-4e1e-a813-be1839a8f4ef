const _ = require('lodash');
const async = require('async');

const redis = require('../../common/redis.service').getClient();
const constants = require('../constants');
const dateUtils = require('../../../lib/utils/dateUtils');
const logger = require('../../../lib/logger')('processNotification');
const CounterCache = require('../models/CounterCache');
const eventFindQuery = require('./eventFindQuery');
const sleep = require('../../../lib/utils/sleep');

const NOTIFICATION_LOG_PARAMS = ['_id', 'accountId', 'name', '__t'];
const { MILLISECONDS_IN_MINUTE } = dateUtils;

const CACHE_TIME = 20;

/**
 * Finds and processes a notification's event and adds it to an accumulator array
 * @param {Object} notification The notification
 * @param {Object} options The function params
 * @param {string?} options.guid The event guid
 * @param {function} callback invoked with error or null.
 */
module.exports = async function processNotification(notification, options, callback) {
  let cacheKey = `counter:${notification.id}`;
  if(options.guid) cacheKey += `:${options.guid}`;

  const redisRes = await getRedisCached(cacheKey, {
    retries: 10, delay: 3000, // query timeout 30s
  });
  if(redisRes) {
    if(redisRes.valid) {
      return callback(null, redisRes);
    }
    // if redisRedis.inProgress=true we return null
    return callback();
  }
  if(!options.guid) {
    const cachedResult = await getCached(notification.id, { retries: 5, delay: 2000 });
    if(cachedResult && cachedResult.inProgress) {
      return callback();
    }
    if(cachedResult && cachedResult.result && cachedResult.result.valid) {
      redis.setexAsync(cacheKey, CACHE_TIME, JSON.stringify(cachedResult.result));
      return callback(null, cachedResult.result);
    }
  }

  // add locker so multiple requests do not trigger the same query over and over again
  const querySecs = constants.maxQueryMS / 1000;
  await redis.setexAsync(cacheKey, querySecs, JSON.stringify({ inProgress: true }));

  const {
    query, sort, model, minimumDay, countToMinute,
  } = eventFindQuery(notification, options.guid);
  const notificationInfo = _.pick(notification, NOTIFICATION_LOG_PARAMS);
  if(!model) {
    logger.error({ notification: notificationInfo }, 'no event model for notification');
    return callback();
  }

  const events = await model
    .find(query, null)
    .sort(sort)
    .maxTime(constants.maxQueryMS)
    .hint(model.getHint())
    .limit(_.get(notification, 'settings.ignoreEventLimit', false) ? 10 * 1000 : constants.maxEvents)
    .cache(CACHE_TIME)
    .catch(err => null);
  if(!events || !events.length) {
    return callback();
  }
  const result = processEventsForNotification(notification, events, {
    countToMinute, minimumDay,
  });
  // this overwrites the inProgress=true statement
  redis.setexAsync(cacheKey, CACHE_TIME, JSON.stringify(result));
  if(result.valid) {
    return callback(null, result);
  }
  return callback();
};

module.exports.processEventsForNotification = processEventsForNotification;

async function getCached(notificationId, { retries = 0, delay = 1000 } = {}) {
  let attempt = 0;
  let cachedResult = null;
  do {
    cachedResult = await CounterCache.findOne({ notificationId });
    if(!cachedResult) {
      return null;
    }
    if(cachedResult.result || !cachedResult.inProgress) {
      return cachedResult;
    }
    attempt += 1;
    if(delay && attempt <= retries) {
      await sleep(delay);
    }
  } while(attempt <= retries);
  return cachedResult;
}

// will try to get result if it already inProgress=true (if not exists it just returns null)
async function getRedisCached(cacheKey, { retries = 0, delay = 1000 } = {}) {
  let attempt = 0;
  let cachedResult = null;
  do {
    cachedResult = await redis.getAsync(cacheKey);
    if(!cachedResult) {
      return null;
    }
    const parsedResult = JSON.parse(cachedResult);
    if(!parsedResult.inProgress) {
      return parsedResult;
    }
    attempt += 1;
    if(delay && attempt <= retries) {
      await sleep(delay);
    }
  } while(attempt <= retries);
  return cachedResult;
}

/**
 * @param {Object} notification
 * @param {Array<Event>} events
 * @param {object} params additional parameters
 * @param {number} params.countToMinute the lowest countKey (minute) to traverse (0-1439)
 * @param {number} params.minimumDay The last event day
 */
function processEventsForNotification(notification, events, params) {
  let finalResult;
  const minimumToDisplay = notification.getMinimumToDisplay();

  if(notification.type === constants.notificationTypes.combo) {
    const { period } = notification.settings.combo;
    finalResult = aggregateCombo(events, period);
  } else if(notification.settings.allTimeEvents) {
    finalResult = aggregateAll(events);
  } else {
    finalResult = { count: 0, minutes: 0, valid: false };

    let lastTimestamp = null;
    let oldest;
    for(let i = 0; i < events.length; i++) {
      const event = events[i];
      const timestamp = event.getTime();

      const sameDayAsPreviousEvent = dateUtils.isSameDay(lastTimestamp, timestamp);
      const reachedMinimum = finalResult.count >= minimumToDisplay;
      if(!reachedMinimum || sameDayAsPreviousEvent) {
        const isMinimumDay = dateUtils.isSameDay(params.minimumDay, event.getTime());
        const countToMinute = isMinimumDay ? params.countToMinute : 0;
        const { sum, minMinute } = event.getTotal(countToMinute);

        finalResult.count += sum;
        const adjTimestamp = event.getTime() + minMinute * MILLISECONDS_IN_MINUTE;
        if(!oldest || oldest > adjTimestamp) oldest = adjTimestamp;

        lastTimestamp = timestamp;
      } else {
        break;
      }
    }
    finalResult.minutes = Math.floor((Date.now() - oldest) / MILLISECONDS_IN_MINUTE);
    // finalResult.timestamp = lastTimestamp;
  }

  finalResult.valid = finalResult.count >= minimumToDisplay;

  return Object.assign(_.pick(notification, constants.getPickFields), finalResult);
}

function aggregateCombo(events, period) {
  if(period === constants.COMBO_PERIODS.all) return aggregateAll(events);

  const minuteOfDay = dateUtils.minuteOfTheDay();
  const days = constants.COMBO_PERIOD_DAYS[period];

  const now = dateUtils.normalize(Date.now());
  const minTimestamp = now - (days * Date.MILLISECONDS_IN_DAY);
  let count = 0;
  for(let i = 0; i < events.length; i++) {
    const event = events[i];
    if(dateUtils.isSameDay(event.getTime(), minTimestamp)) {
      count += event.getTotal(minuteOfDay).sum;
    } else {
      count += event.getTotal().sum;
    }
  }

  const minutes = days * Date.MINUTES_IN_DAY;
  return { count, minutes };
}

function aggregateAll(events) {
  let count = 0;
  let oldest;
  for(let i = 0; i < events.length; i++) {
    const event = events[i];
    const { sum, minMinute } = event.getTotal();
    count += sum;
    const timestamp = event.getTime() + minMinute * MILLISECONDS_IN_MINUTE;
    if(!oldest || oldest > timestamp) oldest = timestamp;
  }

  const minutes = Math.floor((Date.now() - oldest) / MILLISECONDS_IN_MINUTE);
  return { count, minutes };
}
