const _ = require('lodash');
const ErrorFactory = require('../../../lib/errors/ErrorFactory');
const maxmind = require('../../../lib/maxmind');

const Notification = require('../models/Notification');
const constants = require('../constants');
const utils = require('./utils');

const processNotification = require('./processNotification');
const processStream = require('./processStream');
const processReview = require('./processReview');
const processInfo = require('./processInfo');
const processSocial = require('./processSocial');
const processLive = require('./processLive');

module.exports = async function getWithName(req, res, next) {
  const { accountId } = req.jwtData;
  const { name, first: firstTimestamp } = req.body;
  const { account } = req.locals;
  const escapedName = `^${_.escapeRegExp(name)}$`;
  const query = { accountId, name: new RegExp(escapedName, 'i') };
  const { guid, dynamicParams } = req.body;
  let url = guid;
  if(!url) {
    const urlHeader = req.headers['x-ps-url'];
    if(urlHeader) {
      url = Buffer.from(urlHeader, 'base64').toString('utf8');
    }
  }
  Notification.findOne(query).then((notification) => {
    if(!notification) {
      return next(ErrorFactory('notification not found', query));
    } if(!notification.active) {
      return next(ErrorFactory('notification is not active', query));
    }

    const visitorType = _.get(notification, 'settings.visitor.type', constants.visitorTypes.all);
    if(!utils.shouldShowToVisitorType(
      visitorType, notification.settings.visitor.daysAsReturning, firstTimestamp,
    )) {
      return next();
    }

    switch(notification.type) {
    default:
      return processNotification(notification, { guid }, (err, notiResults) => {
        res.body = notiResults ? [notiResults] : [];
        next(err);
      });

    case constants.notificationTypes.review:
      return processReview(notification, { guid, account }, (err, results) => {
        res.body = results && results.notifications;
        next(err);
      });

    case constants.notificationTypes.stream:
      return processStream(notification, {
        guid, account, ...dynamicParams,
      }, async (err, results) => {
        const events = results && results.notifications;

        if(events && notification.settings.sortByLocation) {
          const location = await maxmind.geoIP(req.remoteAddress).catch(() => {});
          const countryCode = location && location.countryCode;
          if(countryCode) {
            events.sort((a, b) => {
              const codeA = a.data.location.countryCode;
              const codeB = b.data.location.countryCode;
              return utils.compareCountries(codeA, codeB, countryCode);
            });
          }
        }

        res.body = events;
        next(err);
      });

    case constants.notificationTypes.info:
      res.body = [processInfo(notification)];
      return next();

    case constants.notificationTypes.social: {
      const socialNotif = processSocial(notification);
      res.body = socialNotif ? [socialNotif] : [];
      return next();
    }

    case constants.notificationTypes.live: {
      return processLive(notification, url, true, (err, result) => {
        res.body = (result && [result]) || [];
        next(err);
      });
    }
    }
  });
};
