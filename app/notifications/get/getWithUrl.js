const _ = require('lodash');
const async = require('async');
const constants = require('../constants');
const { urlUtils, cookieUtils, dateUtils } = require('../../../lib/utils');
const logger = require('../../../lib/logger')('getWithUrl');

const { EXCLUDE_MATCHERS } = constants;

const processNotification = require('./processNotification');
const processStream = require('./processStream');
const processReview = require('./processReview');
const processLive = require('./processLive');
const processInfo = require('./processInfo');
const processSocial = require('./processSocial');
const maxmind = require('../../../lib/maxmind');
const utils = require('./utils');

const Notification = require('../models/Notification');

module.exports = async function getWithUrl(req, res, next) {
  const { accountId } = req.jwtData;
  const psuid = cookieUtils.getCookie(req, 'psuid');
  const { account } = req.locals;
  const { first: firstTimestamp } = req.body;
  const opts = {
    debug: req.locals.debug, psuid, ip: req.remoteAddress, firstTimestamp,
  };
  aggregateEventsForNotificationsWithUrl(accountId, req.body.url, opts, account, (err, result) => {
    if(result) res.body = result;
    next(err);
  });
};

function aggregateEventsForNotificationsWithUrl(accountId, url, options, account, topCb) {
  const opts = options || {};
  const { firstTimestamp = null } = opts;
  const normalizedUrl = urlUtils.normalize(url);
  const cleanUrl = urlUtils.clean(url);
  const query = Notification.findWithUrlQuery(
    accountId, normalizedUrl, cleanUrl, { debug: opts.debug },
  );
  return async.waterfall([
    async () => {
      const notifications = await Notification.find(query).cache(10).exec();
      return notifications;
    },
    (notifications, cb) => {
      if(!notifications.length) {
        logger.warn({ query }, 'no notifications found');
        return cb(null, []);
      }
      logger.info({ notifications: notifications.length }, 'found notifications');
      const filtered = filterNotifications(notifications, normalizedUrl, cleanUrl, {
        firstTimestamp,
      });
      return cb(null, filtered);
    },
    (notifs, cb) => {
      if(!notifs.length) {
        logger.warn({ query }, 'all notifications filtered');
        return cb(null, []);
      }
      logger.info({ notifications: _.map(notifs, '_id') }, 'processing notifications');

      const accumulator = [];
      const filterEvents = [];
      const filterEmails = [];
      let sortByLocation = false;
      async.each(notifs, (notification, icb) => {
        switch(notification.type) {
        default:
          return processNotification(notification, {}, (err, result) => {
            if(result) accumulator.push(result);
            // icb(err); TODO: handle this otherwise
            icb();
          });

        case constants.notificationTypes.stream: {
          const streamOpts = {
            filterEvents, filterEmails, psuid: opts.psuid, account,
          };
          if(_.get(notification, 'settings.sortByLocation') === true) {
            sortByLocation = true;
          }
          return processStream(notification, streamOpts, (err, results) => {
            if(results) {
              const { notifications, eventIds, emails } = results;
              accumulator.push(...notifications);
              filterEvents.push(...eventIds);
              filterEmails.push(...emails);
            }
            icb(err);
          });
        }

        case constants.notificationTypes.review:
          return processReview(notification, { account }, (err, results) => {
            if(results) {
              const { notifications } = results;
              accumulator.push(...notifications);
            }
            icb(err);
          });

        case constants.notificationTypes.live:
          return processLive(notification, url, false, (err, result) => {
            if(result) accumulator.push(result);
            icb(err);
          });

        case constants.notificationTypes.info:
          accumulator.push(processInfo(notification));
          return icb();

        case constants.notificationTypes.social: {
          const socialNotif = processSocial(notification);
          if(socialNotif) {
            accumulator.push(socialNotif);
          }
          return icb();
        }
        }
      }, async (err) => {
        await sort(accumulator, sortByLocation, opts.ip);
        return cb(err, accumulator);
      });
    },
  ], topCb);
}

function filterNotifications(notifications, normalizedUrl, cleanUrl, {
  firstTimestamp = null,
} = {}) {
  return notifications.filter((notification) => {
    const urlType = _.get(notification, 'urlTypes.display', constants.URL_TYPES.simple);
    const displayAbs = _.get(notification, 'urlTypes.displayAbs', true);
    const url = displayAbs ? normalizedUrl : cleanUrl;
    let retval = false;
    switch(urlType) {
    default:
      break;
    case constants.URL_TYPES.all:
    case constants.URL_TYPES.simple:
      retval = true;
      break;

    case constants.URL_TYPES.contains: {
      let displayUrl = notification.displayURLs[0];
      if(displayAbs && displayUrl && displayUrl.startsWith('www.')) {
        displayUrl = displayUrl.replace('www.', '');
      }
      retval = url.indexOf(displayUrl) > -1;
      break;
    }

    case constants.URL_TYPES.regex: {
      const regex = new RegExp(notification.displayURLs[0], 'i');
      retval = regex.test(url);
      break;
    }
    }
    const exclude = notification.displayExclude.some((e) => {
      switch(e.matcher) {
      case EXCLUDE_MATCHERS.notContains:
        return normalizedUrl.includes(e.value);
      case EXCLUDE_MATCHERS.notEqual:
        return url === e.value;
      default:
        return false;
      }
    });
    if(exclude) retval = false;

    const visitorType = _.get(notification, 'settings.visitor.type', constants.visitorTypes.all);
    if(!utils.shouldShowToVisitorType(
      visitorType, notification.settings.visitor.daysAsReturning, firstTimestamp,
    )) {
      retval = false;
    }

    if(!retval) {
      logger.info({ notification: notification.id, urlType }, 'filtered notification');
    }
    return retval;
  });
}

async function sort(accumulator, sortByLocation, ip) {
  let countryCode = null;
  if(sortByLocation && ip) {
    const location = await maxmind.geoIP(ip).catch(() => {}) || null;
    countryCode = location && location.countryCode;
  }
  return accumulator.sort((a, b) => {
    if(a.priority > b.priority) {
      return 1;
    }
    if(a.priority < b.priority) {
      return -1;
    }
    if(countryCode && a.settings.sortByLocation && b.settings.sortByLocation) {
      const codeA = a.data.location.countryCode;
      const codeB = b.data.location.countryCode;
      return utils.compareCountries(codeA, codeB, countryCode);
    }
    return 0;
  });
}
