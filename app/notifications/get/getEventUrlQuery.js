const _ = require('lodash');
const constants = require('../constants');

const { URL_TYPES } = constants;

module.exports = getEventUrlQuery;

function getEventUrlQuery(notification) {
  const urlType = _.get(notification, 'urlTypes.track', URL_TYPES.simple);
  const displayAbs = _.get(notification, 'urlTypes.displayAbs', true);
  switch(urlType) {
  default:
  case URL_TYPES.simple:
    return { $in: notification.trackURL };

  case URL_TYPES.contains: {
    let trackUrl = notification.trackURL[0];
    if(!displayAbs && trackUrl && trackUrl.startsWith('www.')) {
      trackUrl = trackUrl.replace('www.', '');
    }
    return { $regex: `.*${_.escapeRegExp(trackUrl)}.*` };
  }

  case URL_TYPES.regex:
    return { $regex: new RegExp(notification.trackURL[0]) };

  case URL_TYPES.all:
    return null;
  }
}
