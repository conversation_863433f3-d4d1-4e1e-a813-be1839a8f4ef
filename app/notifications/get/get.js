const ipMatching = require('ip-matching');
const _ = require('lodash');

const authTypes = require('../../../middleware/authTypes');
const logger = require('../../../lib/logger')('getNotifications');

const cacheService = require('../../common/cache.service');
const trackEvent = require('../../events/track').trackUrl;
const handleCookie = require('../../visitorCount/handleCookie');
const getWithName = require('./getWithName');
const getWithUrl = require('./getWithUrl');
const Account = require('../../account/models/Account');

/** Rubi System Nipel The Kings Blat */

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.api,
    checkBot: true,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {
      url: { type: 'string', minLength: 1 },
      unique: { type: 'boolean' },
      guid: { type: ['string', 'integer'] },
      name: { type: 'string', minLength: 1 },
      first: { type: 'timestamp' },
      dynamicParams: { type: ['object', 'null'], additionalProperties: true },
    },
    oneOf: [
      {
        // Allow empty which returns mobile notifications
        not: {
          anyOf: [
            { required: ['guid'] },
            { required: ['name'] },
            { required: ['url'] },
          ],
        },
      },
      {
        required: ['url'],
        not: {
          anyOf: [
            { required: ['guid'] },
            { required: ['name'] },
          ],
        },
      },
      {
        required: ['name'],
        not: { required: ['url'] },
      },
    ],
  },
  async handle(req, res, next) {
    const { accountId } = req.jwtData;
    const ip = req.remoteAddress;
    const cacheKey = `get:${accountId}`;
    let account = cacheService.memget(cacheKey);
    if(!account) {
      account = await Account.findOne({ _id: accountId }).select('configuration').cache(60);
      cacheService.memset(cacheKey, account, {});
    }
    req.locals.account = account;
    const blacklist = _.get(account, 'configuration.filters.ips', []);
    if(blacklist.length > 0) {
      for(let i = 0; i < blacklist.length; i += 1) {
        const ipRange = blacklist[i];
        try {
          const isBlackListed = ipMatching.getMatch(ipRange).matches(ip);
          if(isBlackListed) {
            res.body = [];
            return next();
          }
        } catch(e) {
          // do nothing if ip matching fails... e.g. bad IP range
        }
      }
    }
    if(req.body.url && req.body.unique === true) {
      req.log.info('request is unique, track event');
      trackEvent(req.body.url, 'impression', accountId);
    }

    handleCookie(req, res).then((cookieValue) => {
      if(!cookieValue) {
        logger.warn({ accountId }, 'visitor is beyond account visitor limits');
        res.body = [];
        return next();
      }
      if(req.body.name) {
        logger.info({ req }, 'getWithName');
        getWithName(req, res, next);
      } else if(req.body.url) {
        logger.info({ req }, 'getWithUrl');
        getWithUrl(req, res, next);
      }
    });
  },
};
