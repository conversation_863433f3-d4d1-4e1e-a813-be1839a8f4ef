/* eslint-disable no-underscore-dangle,max-len */
const _ = require('lodash');
const moment = require('moment');
const Handlebars = require('handlebars');
const logger = require('../../../lib/logger')('processStream');
const constants = require('../constants');
const streamEventFind = require('./streamEventFind');
const ThinkificEvent = require('../../thinkific/ThinkificEvent');
const filterService = require('../../common/filter.service');
const notifierService = require('../../common/notifier.service');

const NOTIFICATION_LOG_PARAMS = ['_id', 'accountId', 'name', '__t'];

/**
 * find and process all stream notification events
 * @param {object} notification the notification
 * @param {object} options
 * @param {object?} options.account the user account for filter configuration
 * @param {array?} options.filterEvents ids of events to ignore
 * @param {array?} options.filterEmails emails to ignore
 * @param {string?} options.psuid the uid of the client making the request
 * @param {string|integer?} options.guid the guid of the events to return
 * @param callback
 * @return {Promise<*>}
 */
module.exports = async function processStream(notification, options, callback) {
  const { query, eventModel } = streamEventFind(notification, options);
  const notificationInfo = _.pick(notification, NOTIFICATION_LOG_PARAMS);

  if(!eventModel) {
    logger.error({ notification: notificationInfo }, 'no event model for notification');
    return callback();
  }

  const { account = {} } = options;
  const filterEvents = options.filterEvents || [];
  const filterEmails = options.filterEmails || [];
  const { filterAnonymous } = notification.settings;
  const { anonymize } = notification.settings;
  const { message } = notification;
  const eventIds = [];
  const emails = [];
  const results = [];

  let limit = 10;
  const maxConversions = _.get(notification, 'settings.maxConversions', null);
  const maxConversionsLimit = _.get(account, 'configuration.streamQuery.maxConversionsLimit', 50);
  if(maxConversions) {
    limit = maxConversions;
  }
  if(limit < 1) {
    limit = 1;
  } else if(limit > maxConversionsLimit) {
    limit = maxConversionsLimit;
  }

  let cursorLimit = 2000;
  if(eventModel === ThinkificEvent) {
    cursorLimit = 5000;
  }
  const configCursorLimit = _.get(account, 'configuration.streamQuery.cursorLimit');
  if(configCursorLimit) {
    cursorLimit = configCursorLimit;
  }
  const maxDateLookBack = _.get(account, 'configuration.streamQuery.maxDateLookBack');
  if(maxDateLookBack) {
    query.date = { $gte: Date.now() - 86400 * 1000 * maxDateLookBack };
  }
  const cursor = eventModel
    .find(query, null, { sort: { date: -1 } })
    .hint(eventModel.getHint())
    .limit(cursorLimit)
    .cursor();

  try {
    let event = await cursor.next();
    const compiledMessage = Handlebars.compile(message);
    while(event != null) {
      if(results.length >= limit) {
        cursor.close();
        break;
      }

      // skip irrelevant events
      const idString = event._id.toString();
      const { email, payload = {} } = event;
      const sameUserUid = event.user_uid && options.psuid && event.user_uid === options.psuid;
      let shouldAddEvent = false;
      if((notification.settings.hideOwnConversions && sameUserUid)
        || filterEvents.indexOf(idString) > 0
        || filterEmails.indexOf(email) > -1
        || emails.indexOf(email) > -1) {
        logger.info({ event: idString }, 'filtered event');
      } else {
        eventIds.push(idString);
        const result = _(notification.toObject())
          .pick(constants.getPickFields)
          .omit(constants.getOmitField)
          .omit('refer', 'combo')
          .value();

        const localization = options.lang || options.localization || notification.localization || 'en';
        result.localization = localization;
        const momentLocalized = moment(event.date);
        momentLocalized.locale(localization);
        const timeText = momentLocalized.fromNow();

        // eslint-disable-next-line prefer-const
        let { name, initials, anonymous } = event.getName(anonymize);

        if(!filterAnonymous || !anonymous) {
          const { someoneAlternatives, image } = notification;
          if(anonymous && someoneAlternatives && someoneAlternatives.length) {
            name = someoneAlternatives[Math.floor(Math.random() * someoneAlternatives.length)];
          }
          const location = event.getLocation(localization);
          result.data = {
            id: idString, name, initials, photo: event.getPhoto(), location, timeText,
          };
          result.timestamp = event.date.getTime();
          if(notification.settings.showMap) {
            result.data.map = event.getMapIcon();
          }
          let product;
          const numProducts = _.get(event, 'products.length', null);
          if(notification.canShowProducts()) {
            const { filterProducts, hideProduct } = notification.settings;
            const hasFilter = _.get(filterProducts, 'values.length') > 0;
            product = event.getProduct && event.getProduct(filterProducts);
            const shouldShow = !hasFilter // no filter
              || (hasFilter && product) // filter (include/exclude) and has product
              || (hasFilter && !filterProducts.include && !numProducts); // exclude filter, no products
            if(shouldShow) {
              shouldAddEvent = true;
            }
            if(product && !hideProduct) {
              result.data.product = product;
            }
          } else {
            shouldAddEvent = true;
          }
          try {
            result.message = compiledMessage({
              ...(!payload.product && product && {
                product,
                productName: product.name,
                productLink: product.link,
              }),
              ...(!payload.location && location && { location }),
              ...(!payload.name && name && { name }),
              ...(numProducts && { numProducts }),
              ...payload,
            });
          } catch(err) {
            result.message = notification.message;
            notifierService.notifyError(err, 'failed to compile message template', {
              notification: notificationInfo, event: idString,
            });
          }

          if(image && image.includes('{{')) {
            result.image = Handlebars.compile(image)({
              ...(!payload.productImage && product && product.image && { productImage: product.image }),
              ...payload,
            });
          }

          const filterCountries = _.get(notification, 'settings.filters.countries.values', []);
          if(filterCountries.length > 0) {
            const includeCountries = _.get(notification, 'settings.filters.countries.include', true);
            const countryCode = event.location.countryCode && event.location.countryCode.toUpperCase();
            if(countryCode) {
              const shouldExclude = includeCountries
                ? !filterCountries.includes(countryCode)
                : filterCountries.includes(countryCode);
              if(shouldExclude) {
                shouldAddEvent = false;
              }
            }
          }
          if(shouldAddEvent && filterService.shouldIgnoreEvent({
            configuration: account.configuration, name, email,
          })) {
            shouldAddEvent = false;
          }
        }
        if(shouldAddEvent) {
          results.push(result);
          emails.push(email);
        }
      }
      // eslint-disable-next-line no-await-in-loop
      event = await cursor.next();
    }

    const cbResult = { notifications: results, eventIds, emails };
    if(!eventIds.length) {
      logger.warn({ notification: notificationInfo, query }, 'no stream events for notification');
      return callback();
    }
    logger.info({ notification: notificationInfo, events: eventIds }, 'processed stream events for notification');
    return callback(null, cbResult);
  } catch(err) {
    return callback(err);
  }
};
