const constants = require('../constants');
const {
  GoogleReview,
  FacebookReview,
  ReviewsioReview,
  TrustpilotReview,
  YotpoReview,
  StampedReview,
  CapterraReview,
  JudgemeReview,
  FeefoReview,
  ShopperApprovedReview,
  ShapoReview,
  WixReview,
  CustomReview,
} = require('../../reviews/models');
const capterra = require('../../../lib/apis/capterra');

module.exports = (notification, { guid = null } = {}) => {
  const { placeId, accountId } = notification;
  const query = { accountId, active: { $ne: false } }; // to support old docs without `active` field
  let model = null;

  switch(notification.source) {
  default:
  case constants.reviewSources.other:
    query.placeId = placeId;
    if(guid) {
      query.guid = guid;
    }
    model = CustomReview;
    break;

  case constants.reviewSources.google:
    query.placeId = placeId;
    model = GoogleReview;
    break;

  case constants.reviewSources.trustpilot:
    query.domain = placeId;
    model = TrustpilotReview;
    break;

  case constants.reviewSources.reviewsio:
    query.storeId = placeId;
    model = ReviewsioReview;
    break;

  case constants.reviewSources.facebook:
    query.pageId = placeId;
    model = FacebookReview;
    break;
  case constants.reviewSources.yotpo:
    query.yotpoAppId = placeId;
    model = YotpoReview;
    break;
  case constants.reviewSources.stamped:
    query.storeUrl = placeId;
    if(guid) {
      query.$or = [{ reviewId: guid }, { productId: guid }];
    }
    model = StampedReview;
    break;
  case constants.reviewSources.capterra:
    query.productId = { $in: [placeId, capterra.getProductId(placeId)] };
    model = CapterraReview;
    break;
  case constants.reviewSources.judgeme:
    query.placeId = placeId;
    if(guid) {
      query.productId = guid;
    }
    model = JudgemeReview;
    break;
  case constants.reviewSources.feefo:
    query.placeId = placeId;
    model = FeefoReview;
    break;
  case constants.reviewSources.shopperapproved:
    query.placeId = placeId;
    model = ShopperApprovedReview;
    break;
  case constants.reviewSources.shapo: {
    const { pageToken: queryStr } = notification;
    query.placeId = [placeId, queryStr].join('');
    model = ShapoReview;
    break;
  }
  case constants.reviewSources.wix:
    query.placeId = placeId;
    model = WixReview;
    break;
  }
  return { query, model };
};
