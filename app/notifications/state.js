const _ = require('lodash');
const authTypes = require('../../middleware/authTypes');
const Notification = require('./models/Notification');
const Account = require('../account/models/Account');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const stringUtils = require('../../lib/utils/stringUtils');
const notifier = require('../../lib/apis/slackNotifier');
const triggers = require('../../lib/triggers');
const slack = require('../../lib/apis/slackNotifier');
const config = require('../../config');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {
      id: { type: 'string', required: true, pattern: stringUtils.regex.objectId },
      active: { type: 'boolean', required: true },
    },
  },
  async handle(req, res, next) {
    try {
      const { accountId } = req.locals;
      const [account, notification, activeNotifications] = await Promise.all([
        Account.findOne({ _id: accountId }),
        Notification.findOne({ _id: req.body.id }),
        Notification.find({ accountId, active: true }, '_id').sort({ priority: 1 }),
      ]);

      if(!notification) {
        return next(ErrorFactory('notification not found', { id: req.body.id, accountId }));
      }

      const email = (account && account.email) || 'N/A';

      let numActive = activeNotifications.length;
      const state = req.body.active;
      if(state && numActive > 0 && !account.isSubscriptionActive()) {
        slack.notify(`${email} tried to activate more than 1 notification`, null, { webhook: config.slack.warnings });
        if(numActive > 1) {
          const ids = activeNotifications.slice(1);
          await Notification.updateMany({ _id: { $in: ids } }, { active: false });
        }
        return next(ErrorFactory('you can only have one active notification on the free plan'));
      }

      numActive += state ? 1 : -1;
      notification.active = state;
      await Promise.all([
        notification.save(),
        Account.updateOne({ _id: accountId }, { 'stats.notifications.active': numActive }),
      ]);

      const plan = _.get(account, 'subscription.plan', 'FREE');
      if(numActive <= 1) {
        slack.notify(`${numActive} active notifications: ${email} (${plan}, ${accountId})`, null, { webhook: config.slack.warnings });
      }

      res.body = { success: true };
      return next();
    } catch(err) {
      return next(err);
    }
  },
};
