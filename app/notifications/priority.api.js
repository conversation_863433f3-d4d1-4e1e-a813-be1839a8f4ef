const authTypes = require('../../middleware/authTypes');
const logger = require('../../lib/logger/LoggerFactory')('priority.api');
const priority = require('./priority');
const accountStats = require('../account/account.stats');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.console,
  },
  schema: {
    type: 'array',
    required: true,
    minItems: 1,
    items: {
      type: 'string',
      pattern: /^[a-fA-F0-9]{24}$/,
    },
  },

  async handle(req, res, next) {
    try {
      const { accountId } = req.locals;
      const orderedIds = req.body;
      await priority.setPriority(accountId, orderedIds);

      accountStats.changedPriority(accountId).catch(() => {});

      return next();
    } catch(err) {
      logger.error({ err }, 'failed to set priority');
      next(err);
    }
  },
};
