const authTypes = require('../../middleware/authTypes');
const config = require('../../config');
const Account = require('../account/models/Account');
const slack = require('../../lib/apis/slackNotifier');
const emailAutomationService = require('../common/email.automation.service');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {
      country: { type: 'string' },
      plan: { type: 'string' },
    },
  },
  async handle(req, res, next) {
    const { accountId } = req.locals;
    try {
      const account = await Account.findOne({ _id: accountId });
      const { email } = account;
      const plan = (account && account.subscription && account.subscription.plan) || 'free';
      if(req.query.country) {
        slack.notify(
          `country selected by ${email}: ${req.query.country} (${plan})`,
          null,
          { webhook: config.slack.leads },
        );
      } else if(req.query.plan) {
        let message = `plan selected by ${email}: ${req.query.plan} (${plan})`;
        if(account.getActiveShop()) {
          message += ` (Shopify ${account.getActiveShop().myshopify_domain})`;
        }
        slack.notify(message, null, { webhook: config.slack.leads });
        emailAutomationService.createSubscription(email, { group: config.mailerlite.groups.plan_selected });
      }

      next();
    } catch(err) {
      next(err);
    }
  },
};
