const Airtable = require('airtable');
const authTypes = require('../../middleware/authTypes');
const slack = require('../../lib/apis/slackNotifier');
const config = require('../../config');

const base = new Airtable({ apiKey: 'keyeh9oy1XXCTzsbZ' }).base('appdhSH1K8mIJ2idb');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {
      email: { type: 'string', minLength: 1 },
      type: { type: 'string', minLength: 1 },
    },
  },
  async handle(req, res, next) {
    const { accountId } = req.locals;
    base('Ads Signups').create({
      email: req.body.email,
      accountId: accountId || 'N/A',
      type: req.body.type,
    }, (err, record) => {
      next();
    });
  },
};
