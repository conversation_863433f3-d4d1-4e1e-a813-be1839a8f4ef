const authTypes = require('../../middleware/authTypes');
const slack = require('../../lib/apis/slackNotifier');
const config = require('../../config');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {
      url: { type: 'string', minLength: 1 },
    },
  },
  handle(req, res, next) {
    slack.notify(`Preview URL: ${req.query.url}`, null, { webhook: config.slack.preview });
    next();
  },
};
