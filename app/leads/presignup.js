const authTypes = require('../../middleware/authTypes');
const triggers = require('../../lib/triggers');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {
      email: { type: 'string', minLength: 1 },
    },
  },
  handle(req, res, next) {
    triggers.presignup(req.body.email);
    next();
  },
};
