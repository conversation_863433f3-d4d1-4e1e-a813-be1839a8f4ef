const authTypes = require('../../middleware/authTypes');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const Account = require('../account/models/Account');
const SubAccount = require('../account/models/SubAccount');
const emailAutomationService = require('../common/email.automation.service');
const { BACKOFFICE } = require('../constants');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.backoffice,
    permissions: BACKOFFICE.permissions.admin,
  },
  schema: {},
  async handle(req, res, next) {
    const { accountId } = req.body;
    const account = await Account.findOne({ _id: accountId });
    if(!account) {
      throw ErrorFactory('Account not found', 400);
    }
    const { email } = account;
    if(account.deleted || email.startsWith('deleted_')) {
      throw ErrorFactory('Account already deleted', 400);
    }
    account.email = `DELETED_${email}`;
    account.emails = account.emails.map(e => (e.startsWith('deleted_') ? e : `deleted_${e}`));
    account.active = false;
    account.deleted = true;
    if(account.kind === 'SubAccount' || account.parent) {
      account.parent.email = null;
      account.parent.id = null;
    }
    const [, unsubscribed] = await Promise.all([
      account.save(),
      emailAutomationService.unsubscribe(email).catch((err) => {
        throw ErrorFactory('Failed to unsubscribe from mailerlite', 500, { err });
      }),
    ]);
    res.body = {
      accountId: account.id,
      unsubscribed: (unsubscribed && unsubscribed.statusCode === 200) || false,
    };
    return next();
  },
};
