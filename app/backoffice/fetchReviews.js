const _ = require('lodash');
const request = require('superagent');
const config = require('../../config');
const authTypes = require('../../middleware/authTypes');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const Account = require('../account/models/Account');
const Review = require('../notifications/models/Review');
const { BACKOFFICE } = require('../constants');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.backoffice,
    permissions: BACKOFFICE.permissions.agent,
  },
  schema: {},
  async handle(req, res, next) {
    const { accountId } = req.body;
    const account = await Account.findOne({ _id: accountId }, { _id: 1 });
    if(!account) {
      throw ErrorFactory('Account not found', 400);
    }
    const notifications = await Review.find({ accountId, active: true });
    if(!notifications.length) {
      throw ErrorFactory('No review notifications found', 400);
    }
    // eslint-disable-next-line arrow-body-style
    res.body = await Promise.all(notifications.map((notification) => {
      return request.post(`${config.cron.baseUrl()}/fetch-reviews`)
        .send({ notificationId: notification.id })
        .then(result => ({
          notificationId: notification.id, res: _.pick(result, ['body', 'statusCode']),
        }))
        .catch(err => ({ notificationId: notification.id, err }));
    }));
    return next();
  },
};
