const bcryptjs = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { RateLimiterRedis } = require('rate-limiter-flexible');
const config = require('../../config');
const authTypes = require('../../middleware/authTypes');
const redisService = require('../common/redis.service');
const BackofficeUser = require('./BackofficeUser');
const ErrorFactory = require('../../lib/errors/ErrorFactory');

const rateLimiter = new RateLimiterRedis({
  storeClient: redisService.getClient(),
  blockDuration: 60 * 60,
  duration: 60 * 60,
  points: 100,
  keyPrefix: 'backoffice-login',
});

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {
      username: { type: 'string', required: true },
      password: { type: 'string', required: true },
    },
  },
  async handle(req, res, next) {
    const { username, password } = req.body;
    const ip = req.remoteAddress;
    await Promise.all([
      rateLimiter.consume(username, 20), // max 5 attempts per username
      rateLimiter.consume(ip, 10), // max 10 attempts per IP
    ]).catch((rateLimitRes) => {
      throw ErrorFactory('Too many attempts', 429, { username, ...rateLimitRes });
    });
    const user = await BackofficeUser.findOne({ username });
    const match = await bcryptjs.compare(password, (user && user.password) || '');
    if(!user || !match) {
      throw ErrorFactory('Backoffice user not found', 500, { username });
    }
    const data = { id: user.id, role: user.role };
    res.cookie('ps_backoffice',
      jwt.sign(data, config.admin.secret),
      config.getCookieConfig(null, 30 * 86400 * 1000));
    res.body = data;
    next();
  },
};
