const authTypes = require('../../middleware/authTypes');
const Account = require('../account/models/Account');
const { BACKOFFICE } = require('../constants');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.backoffice,
    permissions: BACKOFFICE.permissions.agent,
  },
  schema: {
    type: 'object',
  },
  async handle(req, res, next) {
    const { accountId } = req.body;
    const result = await Account.updateOne({ _id: accountId }, { active: true });
    res.body = { result };
    next();
  },
};
