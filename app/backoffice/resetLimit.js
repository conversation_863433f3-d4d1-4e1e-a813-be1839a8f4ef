const { ObjectId } = require('mongoose').Types;
const authTypes = require('../../middleware/authTypes');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const redis = require('../common/redis.service').getClient();
const { BACKOFFICE } = require('../constants');

const Bill = require('../billing/Bill');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.backoffice,
    permissions: BACKOFFICE.permissions.agent,
  },
  schema: {},
  async handle(req, res, next) {
    const { accountId } = req.body;
    if(!ObjectId.isValid(accountId)) {
      return next(ErrorFactory('invalid account id'));
    }
    res.body = await Promise.all([
      Bill.updateOne({ accountId }, { total: 0, days: {} }, { sort: { date: -1 } }),
      redis.hincrbyAsync(accountId, 'cycleDate', 1),
      redis.hmsetAsync(accountId, 'visitorCount', 0),
    ]);
    return next();
  },
};
