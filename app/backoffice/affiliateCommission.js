const _ = require('lodash');
const moment = require('moment');
const { ObjectId } = require('mongoose').Types;
const authTypes = require('../../middleware/authTypes');
const { BACKOFFICE } = require('../constants');
const Account = require('../account/models/Account');
const IPN = require('../../app/billing/bluesnap/IPNModel');
const PaypalIPN = require('../../app/paypal/PaypalIPN');
const { stringUtils, dateUtils } = require('../../lib/utils');

const AFFECTS_BALANCE = ['CHARGE', 'RECURRING', 'REFUND', 'CANCELLATION_REFUND', 'CHARGEBACK'];

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.backoffice,
    permissions: BACKOFFICE.permissions.admin,
  },
  schema: {
    type: 'object',
  },
  async handle(req, res, next) {
    const { q } = req.body;
    const $or = [
      { email: q },
      { 'affiliate.paypal': q },
    ];
    let id = q;
    if(id.includes('ObjectId("')) {
      id = q.split('"')[1];
    }
    if(ObjectId.isValid(id)) {
      $or.push({ _id: id });
      $or.push({ 'parent.id': ObjectId(id) });
    }
    const account = await Account.findOne({ $or });
    if(!account) {
      throw new Error('affiliate not found');
    }
    if(account.affiliate.balance < 0) {
      throw new Error('balance < 0, could be a refund after payout request');
    }
    const lines = [];
    lines.push(`email: ${account.email}`);
    lines.push(`created: ${moment(account.createdAt).format('YYYY-MM-DD')}`);
    lines.push(`affiliate ID: ${account.affiliate.id}`);
    lines.push(`balance: ${_.get(account, 'affiliate.balance', 0).toFixed(2)}`);
    lines.push(`paid: ${_.get(account, 'affiliate.paid', 0).toFixed(2)}`);
    lines.push(`payouts: ${_.get(account, 'affiliate.payouts', [])}`);
    const { free, paying, commissionLines } = await calculate(account);
    lines.push(`free: ${free ? free.length : 0}`);
    lines.push(`paying: ${paying ? paying.length : 0}`);
    lines.push(...commissionLines);
    res.body = lines;
    next();
  },
};

async function calculate(account) {
  if(!account.affiliate.payouts || !account.affiliate.payouts.length) {
    return { commissionLines: ['no payouts'] };
  }
  const currentPayout = account.affiliate.payouts && account.affiliate.payouts.pop();
  const previousPayout = account.affiliate.payouts && account.affiliate.payouts.pop();
  const startDate = dateUtils.normalizeTo12Am((previousPayout && previousPayout.date) || new Date('2018-01-01'));
  const endDate = (currentPayout && currentPayout.date) || new Date();
  if(currentPayout.date.getTime() - account.createdAt.getTime() < 86400 * 10 * 1000) {
    throw new Error('account asked for payout right after it was created');
  }
  const [free, paying] = await Promise.all([
    Account.find({ 'affiliate.referrer': account.affiliate.id, subscription: null }),
    Account.find({ 'affiliate.referrer': account.affiliate.id, subscription: { $ne: null } }),
  ]);
  const commissionLines = await getCommissionLines(account, paying, startDate, endDate);
  return { free, paying, commissionLines };
}

async function getCommissionLines(affAccount, payingAccounts, startDate, endDate) {
  if(!payingAccounts || !payingAccounts.length) {
    throw Error('account not found');
  }
  const lines = [];
  let ltv = 0;
  let total = 0;
  const transactions = [];
  for(let i = 0; i < payingAccounts.length; i += 1) {
    const account = payingAccounts[i];
    const subId = account.subscription.subscriptionId;
    const isShopify = account.shopify && account.shopify.length;
    const queryString = JSON.stringify(account.query);
    lines.push(`scanning ${account.email} ${isShopify ? '(SHOPIFY)' : ''} (${account.id})`);
    lines.push(`|- created: ${account.createdAt}`);
    lines.push(`|- until date: ${moment(account.subscription.untilDate).format('YYYY-MM-DD')}`);
    lines.push(`|- source: ${account.source}, origin: ${account.origin}, query: ${queryString}`);
    lines.push(`|- hosts: ${account.hosts}`);
    // eslint-disable-next-line no-await-in-loop
    const [bsIpns, paypalIpns] = await Promise.all([
      IPN.find({
        accountId: account.id,
        $or: [
          { type: { $in: AFFECTS_BALANCE } },
          { type: { $exists: false } },
        ],
        createdAt: { $gte: startDate, $lte: endDate },
      }),
      PaypalIPN.find({
        type: 'recurring_payment',
        createdAt: { $gte: startDate, $lte: endDate },
        'data.recurring_payment_id': subId,
      }),
    ]);
    const ipns = [].concat(bsIpns, paypalIpns);
    if(!ipns.length) {
      continue;
    }
    const commissionRate = affAccount.affiliate.commissionRate || 0.2;
    for(let j = 0; j < ipns.length; j += 1) {
      const ipnData = getIpnData(ipns[j]);
      const {
        type, reference, name, date, taxlessAmount, contractName, source,
      } = ipnData;
      const added = transactions.find(t => t.reference === reference && t.type === type);
      if(added) {
        lines.push(`|- skipping ${reference}`);
      } else {
        transactions.push(ipnData);
        let commission = 0;
        if(!Number.isNaN(taxlessAmount)) {
          commission = taxlessAmount * commissionRate;
          total += commission;
          ltv += taxlessAmount;
        }
        lines.push(`|- name: ${name}`);
        lines.push(`|- transaction ${type} (#${reference}, ${source})`);
        lines.push(`\t|- date: ${date.toISOString().split('T')[0]}`);
        lines.push(`\t|- contract: ${contractName}`);
        // console.log(`\t|- invoice amount: ${invoiceAmount}`);
        lines.push(`\t|- amount: ${taxlessAmount} (commission: ${commission.toFixed(2)}, rate: ${commissionRate}, total: ${total.toFixed(2)})`);
      }
    }
    lines.push('*********************************\n');
  }
  lines.push(`total ltv: ${ltv.toFixed(2)}`);
  lines.push(`total: ${total.toFixed(2)}`);
  lines.push(`Paypal: ${affAccount.affiliate.paypal}`);
  lines.push(`ProveSource Affiliate Commission ${affAccount.id}`);
  return lines;
}

function getIpnData(ipn) {
  let type;
  let reference;
  let name;
  let date;
  let taxlessAmount;
  let contractName;
  let invoiceAmount;
  let source;
  if(ipn.data.referenceNumber) {
    ({ transactionType: type, invoiceAmount, contractName } = ipn.data);
    source = 'bluesnap';
    reference = ipn.data.referenceNumber;
    date = new Date(ipn.data.transactionDate);
    taxlessAmount = parseFloat(stringUtils.getNumber(invoiceAmount));
    if(contractName && contractName.toLowerCase().includes('vat')) {
      taxlessAmount /= 1.18;
    }
    const { firstName, lastName } = ipn.data;
    name = `${firstName} ${lastName}`;
  } else if(ipn.data.txn_id) {
    ({ type } = ipn);
    source = 'paypal';
    date = ipn.createdAt;
    reference = ipn.data.txn_id;
    contractName = ipn.data.transaction_subject;
    invoiceAmount = parseFloat(ipn.data.mc_gross);
    taxlessAmount = invoiceAmount;
    const { first_name: firstName, last_name: lastName } = ipn.data;
    name = `${firstName} ${lastName}`;
  } else {
    return null;
  }
  return {
    type, reference, name, date, invoiceAmount, taxlessAmount, contractName, source,
  };
}
