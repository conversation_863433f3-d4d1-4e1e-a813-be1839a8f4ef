const _ = require('lodash');
const authTypes = require('../../middleware/authTypes');
const Account = require('../account/models/Account');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const { BACKOFFICE } = require('../constants');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.backoffice,
    permissions: BACKOFFICE.permissions.agent,
  },
  schema: {
    type: 'object',
    properties: {},
  },
  async handle(req, res, next) {
    const {
      accountId,
      sendInvoices,
      showBasicPlan,
      noProductLinks,
      ignoreWixBilling,
      untilDate,
      enablePaypal,
      additionalLimit,
      customCSS,
      customCode,
    } = req.body;
    if(!accountId) {
      throw ErrorFactory('Invalid accountId', 400, req.body);
    }
    const account = await Account.findOne({ _id: accountId });
    if(!account) {
      throw ErrorFactory.AccountNotFound(accountId);
    }
    const update = {};
    if(_.isBoolean(sendInvoices)) {
      update['configuration.sendInvoices'] = sendInvoices;
    }
    if(_.isBoolean(noProductLinks)) {
      update['configuration.forceNoProductLinks'] = noProductLinks;
    }
    if(_.isBoolean(showBasicPlan)) {
      update['settings.showBasicPlan'] = showBasicPlan;
    }
    if(_.isBoolean(ignoreWixBilling)) {
      update['settings.ignoreWixBilling'] = ignoreWixBilling;
    }
    if(_.isString(customCSS) && req.locals.backoffice.role === BACKOFFICE.roles.admin) {
      update['configuration.customCSS'] = customCSS;
    }
    if(_.isString(customCode) && req.locals.backoffice.role === BACKOFFICE.roles.admin) {
      update['configuration.customCode'] = customCode;
    }
    if(_.isString(untilDate) && account.isSubscriptionActive()) {
      if(req.locals.backoffice.role !== BACKOFFICE.roles.admin) {
        throw ErrorFactory('You don\'t have permissions to change until date', 500, req.locals.backoffice);
      }
      const subUntilDate = new Date(untilDate);
      if(subUntilDate.isValid()) {
        update['subscription.untilDate'] = subUntilDate;
      }
    }
    if(_.isBoolean(enablePaypal)) {
      update['settings.enablePaypal'] = enablePaypal;
    }
    if(_.isInteger(additionalLimit)) {
      update.additionalLimit = additionalLimit;
    }
    await Account.findOneAndUpdate({ _id: accountId }, update);
    res.body = { success: true };
    next();
  },
};
