const authTypes = require('../../middleware/authTypes');
const Account = require('../account/models/Account');
const { BACKOFFICE } = require('../constants');
const dateUtils = require('../../lib/utils/dateUtils');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.backoffice,
    permissions: BACKOFFICE.permissions.admin,
  },
  schema: {
    type: 'object',
    properties: {
    },
  },
  async handle(req, res, next) {
    const { page = 1 } = req.body;
    const skip = (page - 1) * 50 || 0;
    const date = dateUtils.normalize(dateUtils.dateByAddingDays(Date.now(), -30));
    const query = {
      'stats.planLimitReached': { $gte: date },
      'stats.notifications.lastImpression': { $gte: Date.now() - 86400 * 1000 * 3 },
      kind: { $ne: 'SubAccount' },
    };
    const [accounts, count] = await Promise.all([
      Account.find(query)
        .select(BACKOFFICE.accountListFields)
        .skip(skip)
        .sort({ createdAt: -1 })
        .limit(50)
        .lean(),
      Account.count(query),
    ]);
    res.body = { accounts, count, pages: Math.ceil(count / 50) };
    next();
  },
};
