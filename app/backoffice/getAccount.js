const { ObjectId } = require('mongoose').Types;

const authTypes = require('../../middleware/authTypes');
const ErrorFactory = require('../../lib/errors/ErrorFactory');

const Account = require('../account/models/Account');
const { BACKOFFICE } = require('../constants');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.backoffice,
    permissions: BACKOFFICE.permissions.agent,
  },
  schema: {
    type: 'object',
    properties: {
      accountId: { type: 'string', required: true },
    },
  },
  async handle(req, res, next) {
    const { accountId } = req.body;
    if(!ObjectId.isValid(accountId)) {
      throw ErrorFactory('invalid account ID');
    }
    res.body = await Account.findOne({ _id: accountId }).select(BACKOFFICE.excludeFields);
    next();
  },
};
