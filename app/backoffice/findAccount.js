const { ObjectId } = require('mongoose').Types;
const authTypes = require('../../middleware/authTypes');

const Account = require('../account/models/Account');
// required for 'parent.id' lookup
const SubAccount = require('../account/models/SubAccount');
const { BACKOFFICE } = require('../constants');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.backoffice,
    permissions: BACKOFFICE.permissions.agent,
  },
  schema: {
    type: 'object',
  },
  async handle(req, res, next) {
    const { q } = req.body;
    const regex = new RegExp(q, 'i');
    const queries = [
      { email: regex },
      { 'parent.email': regex },
      { 'subscription.email': regex },
      { 'affiliate.id': q },
      { hosts: regex },
      { 'subscription.invoiceEmail': regex },
      { 'subscription.invoiceCompanies': regex },
      { 'subscription.invoiceNames': regex },
      { 'wix.instanceId': regex },
      { 'bigcommerce.storeHash': regex },
      { 'wordpress.name': regex },
      { 'shopify.name': regex },
    ];
    let id = q;
    if(id.includes('ObjectId("')) {
      id = q.split('"')[1];
    }
    if(ObjectId.isValid(id)) {
      queries.push({ _id: id });
      queries.push({ 'parent.id': ObjectId(id) });
    }
    let limit = 20;
    if(req.locals.backoffice.role === BACKOFFICE.roles.agent) {
      limit = 3;
    }
    const accounts = await Account
      .find({ $or: queries })
      .select(BACKOFFICE.accountListFields)
      .limit(limit);
    res.body = { accounts };
    next();
  },
};
