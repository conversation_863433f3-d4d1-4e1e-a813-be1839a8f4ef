const authTypes = require('../../middleware/authTypes');

const Account = require('../account/models/Account');
const IPN = require('../billing/bluesnap/IPNModel');
const { BACKOFFICE } = require('../constants');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.backoffice,
    permissions: BACKOFFICE.permissions.admin,
  },
  schema: {
    type: 'object',
    properties: {
      page: { type: ['string', 'number'] },
    },
  },
  async handle(req, res, next) {
    const { page = 1 } = req.body;
    const accountIds = (await IPN.distinct('data.merchantTransactionId', {
      'data.transactionType': 'CANCELLATION',
      'data.cancelReason': { $in: ['', 'CANCELLED_DUE_TO_UNPAID'] },
    })).filter(id => id && id.length);
    const skip = (page > 0 && (page - 1) * 50) || 0;
    const query = {
      _id: { $in: accountIds },
      kind: { $ne: 'SubAccount' },
      'subscription.recentIPN': { $nin: ['CHARGE', 'RECURRING', 'CONTRACT_CHANGE'] },
    };
    const [accounts, count] = await Promise.all([
      Account.find(query)
        .select(BACKOFFICE.accountListFields)
        .skip(skip)
        .limit(50)
        .sort({ 'subscription.untilDate': -1 }),
      Account.count(query),
    ]);
    res.body = { accounts, count, pages: Math.ceil(count / 50) };
    next();
  },
};
