const authTypes = require('../../middleware/authTypes');
const Account = require('../account/models/Account');
const { BACKOFFICE } = require('../constants');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.backoffice,
    permissions: BACKOFFICE.permissions.admin,
  },
  schema: {
    type: 'object',
    properties: {
      page: { type: ['string', 'number'] },
    },
  },
  async handle(req, res, next) {
    const { page = 1 } = req.body;
    const twoMonthsAgo = Date.now() - 30 * 2 * 86400 * 1000;
    const skip = (page > 0 && (page - 1) * 50) || 0;
    const query = {
      kind: { $ne: 'SubAccount' },
      'subscription.created': { $lte: twoMonthsAgo },
      'subscription.recentIPN': { $in: ['RECURRING', 'CHARGE'] },
      'subscription.plan': { $in: ['monster', 'gorilla', 'unlimited'] },
      'subscription.period': { $in: ['monthly', 'yearly'] },
    };
    const [accounts, count] = await Promise.all([
      Account.find(query)
        .skip(skip)
        .select(BACKOFFICE.accountListFields)
        .limit(50)
        .sort({ 'subscription.created': -1 }),
      Account.count(query),
    ]);
    res.body = { accounts, count, pages: Math.ceil(count / 50) };
    next();
  },
};
