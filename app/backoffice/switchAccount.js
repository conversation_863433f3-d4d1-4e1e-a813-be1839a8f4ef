const { ObjectId } = require('mongoose').Types;
const config = require('../../config');
const authTypes = require('../../middleware/authTypes');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const { BACKOFFICE } = require('../constants');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.backoffice,
    permissions: BACKOFFICE.permissions.agent,
  },
  schema: {
    type: 'object',
  },
  handle(req, res, next) {
    const { accountId } = req.body;
    if(!ObjectId.isValid(accountId)) {
      throw ErrorFactory('accountId must be an ObjectID', 400);
    }
    res.cookie('ps_admin', `${accountId}.${config.admin.pass}`, config.getCookieConfig());
    res.body = { redirect: config.consoleUrl };
    next();
  },
};
