const _ = require('lodash');
const authTypes = require('../../middleware/authTypes');
const Review = require('../notifications/models/Review');
const CustomReview = require('../reviews/models/CustomReview');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const Feed = require('../account/models/Feed');
const WebhookData = require('../webhooks/WebhookData');
const { reviewSources } = require('../notifications/constants');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    additionalProperties: true,
    properties: {},
  },
  async handle(req, res, next) {
    const { id: placeId } = req.params;
    const source = reviewSources.other;
    const notification = await Review.findOne({ source, placeId });
    if(!notification) {
      throw ErrorFactory('notification not found', 404);
    }
    const { accountId } = notification;
    WebhookData.saveData({ accountId, webhookId: placeId, ...req.body });
    const {
      author, photo, text, rating, guid = null,
    } = req.body;
    if(!author) {
      throw ErrorFactory('author name required');
    }
    if(!text) {
      throw ErrorFactory('review text is required');
    }
    if(!rating || !_.isInteger(rating) || rating < 1 || rating > 5) {
      throw ErrorFactory('rating must be a number');
    }
    let { id: reviewId = null } = req.body;
    if(!reviewId) {
      const ids = [author, rating];
      if(guid) {
        ids.push(guid);
      }
      reviewId = ids.join('-');
    }
    const reviewExists = await CustomReview.findOne({ accountId, reviewId });
    if(reviewExists) {
      throw ErrorFactory('review with same id already exists');
    }
    Feed.saveFeed(accountId, 'Custom Webhook Review', {
      reviewId, author, photo, text, rating, ...(guid && { guid }), source,
    });
    await CustomReview.create({
      accountId,
      reviewId,
      guid,
      placeId,
      authorName: author,
      profilePhotoUrl: photo,
      rating,
      text,
      time: Date.now(),
      source,
    });
    res.body = { message: 'review saved' };
    next();
  },
};
