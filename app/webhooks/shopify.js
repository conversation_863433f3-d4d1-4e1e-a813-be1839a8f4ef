/* eslint-disable camelcase,no-restricted-globals */
const _ = require('lodash');
const chance = require('chance').Chance();
const authTypes = require('../../middleware/authTypes');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const logger = require('../../lib/logger')('webhooks/shopify');
const maxmind = require('../../lib/maxmind');
const Shopify = require('../../lib/apis/shopify');
const Account = require('../../app/account/models/Account');
const ShopifyEvent = require('../../app/events/models/ShopifyEvent');
const ShopifyData = require('./ShopifyData');
const Feed = require('../account/models/Feed');
const notifierService = require('../common/notifier.service');
const slack = require('../../lib/apis/slackNotifier');
const { stringUtils, objectUtils } = require('../../lib/utils');
const { SHOPIFY, SHOPIFY_EVENT_TYPES } = require('../constants');

module.exports = {
  config: {
    methods: ['POST', 'GET'],
    authType: authTypes.noAuth,
    maxPayload: '1mb',
  },
  schema: {
    type: 'object',
    properties: {},
  },
  handle(req, res, next) {
    res.body = { message: 'success' };
    next();
  },

  async handlePOST(req, res, next) {
    let accountId = null;
    let shop = null;
    res.body = { message: 'success' };
    next();
    try {
      shop = req.headers['x-shopify-shop-domain'];
      // shopify webhook errors might have been caused by this lookup taking too long?
      const account = await Account.findOne({
        'shopify.myshopify_domain': shop,
      }, '_id shopify settings');

      // apparently there are hundreds of sources and Shopify appIds that can create orders:
      // ebay, amazon, tiktok, walmart, thrivecart and many more
      const eventSource = _.get(req.body, 'source_name', 'web');
      const appId = _.get(req.body, 'app_id');
      const isPOS = appId === SHOPIFY.posAppId || eventSource === 'pos';
      if(_.get(account, 'settings.ignoreShopifyPOS') && isPOS) {
        logger.info({ shop }, 'filter POS webhooks for shop');
        return;
      }

      accountId = account.id;
      const shopData = account.getShop(shop);
      if(!shopData) {
        throw ErrorFactory('shop is not connected to account', 400, { account });
      }
      await saveEvent(account.id, shopData, req.body, req);
      logger.info({ event: req.body }, 'saved shopify event');
    } catch(err) {
      notifierService.notifyError(err, 'shopify event track failed', { shop, body: req.body, accountId });
      Feed.saveFeed(accountId, 'Shopify Webhook Failed', { error: err.message });
      logger.error({ critical: true, err, accountId }, 'shopify webhook failed');
    }
  },

  saveEvent,
};

/**
 * @param {string|ObjectID} accountId
 * @param {object} shopData
 * @param {object} order
 * @param {object} [req] the webhook request if applicable
 */
async function saveEvent(accountId, shopData, order, req) {
  const orderId = order.id;

  let info = { orderId, body: order };
  if(req) {
    info = {
      orderId: req.body.id, body: req.body, headers: req.headers, query: req.query,
    };
  }
  saveShopifyData(accountId, shopData, info);

  const dbEvent = await ShopifyEvent.findOne({ accountId, orderId });
  if(!dbEvent) {
    const event = await makeEvent(accountId, shopData, order);
    Feed.saveFeed(accountId, 'Shopify Order', event.toObject());
    return event.save();
  }
  return null;
}

async function makeEvent(accountId, shopData, data) {
  const { token } = shopData;
  const domain = shopData.domain || shopData.myshopify_domain;
  const { myshopify_domain } = shopData;
  const {
    created_at, updated_at, total_price, currency, browser_ip, line_items, id, app_id, source_name, location_id,
  } = data;
  const shopify = Shopify(myshopify_domain, token);
  let date = new Date(created_at);

  // apparently there are hundreds of sources and Shopify appIds that can create orders:
  // ebay, amazon, tiktok, walmart, thrivecart and many more
  let type = SHOPIFY_EVENT_TYPES.online;
  if(app_id === SHOPIFY.posAppId || source_name === 'pos') {
    type = SHOPIFY_EVENT_TYPES.pos;
  }
  if(isNaN(date.getTime())) {
    date = new Date(updated_at);
    if(isNaN(date.getTime())) {
      date = new Date();
    }
  }
  const total = total_price;
  const ip = browser_ip;
  const address = data.billing_address || data.shipping_address || {};
  const {
    country, province_code, province,
  } = address;
  let { country_code } = address;
  if(country === 'SGP') {
    country_code = 'SG';
  }
  const first_name = objectUtils.findFirst(data, ['first_name'], { ignoreCase: true });
  const last_name = objectUtils.findFirst(data, ['last_name'], { ignoreCase: true });
  let { city } = address;
  if(stringUtils.hasNumbers(city)) city = null;
  let location = {
    country, countryCode: country_code, state: province, stateCode: province_code, city,
  };
  if(ip && (!country || !city)) {
    location = await maxmind.geoIP(ip);
  }
  if(location_id && type === SHOPIFY_EVENT_TYPES.pos) {
    const shopLocation = await shopify.getLocation(location_id).catch(() => {});
    if(shopLocation) {
      location.country = shopLocation.country_name || location.country;
      location.countryCode = shopLocation.country_code || location.countryCode;
      location.city = shopLocation.city || location.city;
      location.state = shopLocation.province || location.state;
      location.stateCode = shopLocation.province_code || location.stateCode;
    }
  }

  let { email } = data;
  if(!email || !email.length) {
    let name = '';
    if(first_name && first_name.length) name += first_name;
    if(last_name && last_name.length) name += ` ${last_name}`;
    if(!name.length) {
      name = chance.name();
    }
    email = (`${name}@from-shopify.com`).replace(/\s/g, '.');
  }

  const products = [];
  const productIds = [];
  for(let i = 0; i < line_items.length; i += 1) {
    const lineItem = line_items[i];
    let pId = Math.trunc(lineItem.product_id);
    if(isNaN(pId)) pId = null;

    if(pId && !productIds.includes(pId)) {
      productIds.push(pId);
    }

    products.push({
      id: pId || 'N/A',
      name: lineItem.title,
      variant: lineItem.variant_id,
      variantName: lineItem.variant_title,
      quantity: lineItem.quantity,
      price: lineItem.price,
    });
  }

  if(productIds.length) {
    const [sProducts, sVariants] = await Promise.all([
      shopify.getProducts(productIds).catch(() => ([])),
      shopify.getProductVariants(products.map(p => p.variant).filter(v => v)).catch(() => ([])),
    ]);

    for(let i = 0; i < sProducts.length; i += 1) {
      const sProduct = sProducts[i];
      const product = products.find(p => sProduct.id.includes(p.id));
      const variant = product.variant && sVariants.find(v => v && v.id && v.id.includes(product.variant));
      if(product) {
        product.link = sProduct.onlineStoreUrl || `https://${domain}/products/${sProduct.handle}`;
        if(_.get(variant, 'image.url')) {
          product.image = variant.image.url;
        }
        if(!product.image) {
          product.image = _.get(sProduct, 'image.src')
            || _.get(sProduct, 'featuredImage.url')
            || _.get(sProduct, 'featuredMedia.image.originalSrc');
        }
        if(product.image) {
          if(product.image.indexOf('?') > -1) {
            product.image += '&width=100';
          } else {
            product.image += '?width=100';
          }
        }
      }
    }
  }

  return new ShopifyEvent({
    accountId,
    email,
    date,
    orderId: Math.trunc(id),
    type,
    shop: myshopify_domain,
    domain,
    firstName: first_name,
    lastName: last_name,
    ip,
    location,
    total,
    currency,
    products,
  });
}

/**
 *
 * @param accountId
 * @param shop
 * @param {object} options
 * @param {number} options.orderId
 * @param {object} options.body
 * @param {object} options.headers
 * @param {object} options.query
 */
function saveShopifyData(accountId, shop, options) {
  const opts = options || {};
  const orderId = Math.trunc(opts.orderId);
  const { body, headers, query } = opts;
  (new ShopifyData({
    accountId, orderId, shop, body, headers, query,
  })).save().catch((err) => {
    const data = { accountId, opts };
    slack.notifyError(err, 'failed to save ShopifyData', { data });
  });
}
