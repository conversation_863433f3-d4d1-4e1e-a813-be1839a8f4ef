const authTypes = require('../../middleware/authTypes');
const config = require('../../config');
const slack = require('../../lib/apis/slackNotifier');
const notifier = require('../common/notifier.service');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
    maxPayload: '1mb',
  },
  schema: {
    type: 'object',
    properties: {
      message: { type: 'string', required: true },
      err: { type: 'object' },
      data: { type: 'object' },
    },
  },
  async handle(req, res, next) {
    const { accountId } = req.jwtData || {};
    const { message, data, err } = req.body;
    notifier.notifyError(new Error(`WP Error: ${message}`), 'WP Error', {
      ...(accountId && { accountId }),
      err,
      payload: data,
      headers: req.headers,
    });
    next();
  },
};
