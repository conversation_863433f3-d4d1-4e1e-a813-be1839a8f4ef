/* eslint-disable global-require */
module.exports = {
  '/generate': require('./generate'),
  '/track/:id(\\w{20,})/funnel_webhooks/test': require('./clickfunnelsTest'),
  '/track/:id(\\w{20,})': require('./track'),
  '/track/woocommerce': require('./woocommerce'),
  '/track/shopify': require('./shopify'),
  '/track/magento2': require('./magento2'),
  '/track/magento1': require('./magento1'),
  '/track/wordpress': require('./wordpress'),
  '/wp-error': require('./wp-error'),
  '/plugin-error': require('./plugin-error'),
  '/test': require('./webhookTest'),
};
