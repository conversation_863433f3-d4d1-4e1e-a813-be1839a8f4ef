const url = require('url');
const _ = require('lodash');
const authTypes = require('../../middleware/authTypes');
const logger = require('../../lib/logger')('wordpress');
const maxmind = require('../../lib/maxmind');
const slack = require('../../lib/apis/slackNotifier');
const { cryptoUtils } = require('../../lib/utils');

const WPEvent = require('../events/models/WPEvent');
const Feed = require('../account/models/Feed');
const WebhookData = require('./WebhookData');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.api,
  },
  schema: {
    type: 'object',
    additionalProperties: false,
    properties: {
      email: { type: 'string', required: true },
      siteUrl: { type: 'string', required: true },
      firstName: { type: 'string' },
      lastName: { type: 'string' },
      ip: { type: 'string' },
      ips: {
        type: 'array',
        items: { type: 'string' },
      },
    },
  },
  async handle(req, res, next) {
    const { accountId } = req.jwtData;

    const webhookData = {
      accountId,
      source: 'wordpress',
      reqId: req.id,
      ...(_.pick(req, ['body', 'headers', 'query'])),
    };
    WebhookData.saveData(webhookData);

    try {
      let { email } = req.body;
      if(!email) {
        email = `${cryptoUtils.randomString(10)}@from-wordpress.com`;
      }
      const data = Object.assign(req.body, {
        accountId, date: Date.now(), email, pluginVersion: req.headers['x-plugin-version'],
      }, {
        host: req.body.siteUrl,
      });
      const event = new WPEvent(data);

      if(data.ip) event.location = await maxmind.geoIP(data.ip);

      const feedData = _.pick(event, ['email', 'host', 'firstName', 'lastName', 'ip']);
      logger.info({ event }, 'saving wordpress event');
      await Promise.all([
        event.save(),
        Feed.saveFeed(accountId, 'Wordpress Signup', feedData)
          .catch(err => logger.error({ err }, 'failed to save wordpress feed item')),
      ]);

      next();
    } catch(err) {
      slack.notifyError(err, `Wordpress webhook error (account ${accountId}`, { data: req.body });
      logger.error({ err }, 'failed to save wordpress event');
      next(err);
    }
  },
};
