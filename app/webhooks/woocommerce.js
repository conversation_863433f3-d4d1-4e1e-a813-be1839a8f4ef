const _ = require('lodash');
const authTypes = require('../../middleware/authTypes');
const logger = require('../../lib/logger')('woocommerce');
const dateUtils = require('../../lib/utils/dateUtils');
const slack = require('../../lib/apis/slackNotifier');
const cryptoUtils = require('../../lib/utils/cryptoUtils');

const countryCodes = require('../../lib/geo/countryCodes');
const stateCodes = require('../../lib/geo/stateCodes');
const maxmind = require('../../lib/maxmind');
const WooEvent = require('../events/models/WooEvent').model;
const Feed = require('../account/models/Feed');
const AnalyticsEvent = require('../notifications/models/AnalyticsEvent');
const WebhookData = require('./WebhookData');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.api,
    maxPayload: '1mb',
  },
  schema: {
    type: 'object',
    properties: {
      orderId: { type: ['string', 'number'] },
      siteUrl: { type: 'string' },
      firstName: { type: 'string' },
      lastName: { type: 'string' },
      email: { type: 'string' },
      date: { type: ['string', 'number'] },
      // ip: { type: 'string' },
      // ips: {
      //   type: 'array',
      //   items: { type: 'string' },
      // },
      total: { type: 'number' },
      currency: { type: 'string' },
      products: {
        type: 'array',
        minItems: 1,
        items: {
          type: 'object',
          properties: {
            id: { type: 'number' },
            quantity: { type: 'number' },
            price: { type: 'number' },
            name: { type: 'string', required: true },
            link: { type: 'string' },
            image: { type: ['string', 'null'] },
          },
        },
      },
    },
  },
  async handle(req, res, next) {
    const { accountId } = req.jwtData;
    const { ip, products, firstName } = req.body;

    const webhookData = {
      accountId,
      source: 'woocommerce',
      reqId: req.id,
      ...(_.pick(req, ['body', 'headers', 'query'])),
    };
    WebhookData.saveData(webhookData);

    const event = await makeEvent({
      accountId,
      ...req.body,
      pluginVersion: req.headers['x-plugin-version'],
    });

    // const eventExists = await WooEvent.findOne({
    //   accountId, host: event.host, orderId: event.orderId,
    // }).select('_id').lean();
    // if(eventExists) {
    //   logger.info({ event }, 'wooEvent already exists');
    //   return next();
    // }

    logger.info({ event }, 'saving woocommerce webhook');
    const feedData = {
      email: event.email,
      date: event.date,
      name: firstName,
      store: event.host,
      products,
      location: event.location,
      ip,
    };

    try {
      await event.save();
      await Promise.all([
        incAnalytics(event).catch(err => logger.error({ err }, 'failed to track woocommerce in analytics')),
        Feed.saveFeed(accountId, 'WooCommerce Order', feedData).catch(err => logger.error({ err }, 'failed to save woocommerce feed item')),
      ]);
      res.body = { message: 'success' };
      next();
    } catch(err) {
      logger.error({ err }, 'woocommerce error');
      const msg = `woocommerce webhook error (account: ${accountId})`;
      Feed.saveFeed(accountId, 'WooCommerce Track Failed', feedData);
      slack.notifyError(err, msg, { data: req.body });
      next(err);
    }
  },
  makeEvent,
};

async function makeEvent({
  accountId, siteUrl, orderId,
  email, date, ip, ips, location, products, currency, total, firstName, lastName,
  pluginVersion,
}) {
  let eventLocation;
  if(location && location.countryCode && location.city) {
    eventLocation = location;
    eventLocation.country = countryCodes.fromCode(eventLocation.countryCode);
    eventLocation.state = stateCodes.fromCode(eventLocation.stateCode);
  } else {
    eventLocation = await maxmind.geoIP(ip);
  }

  const filteredProducts = products;
  // if(filteredProducts) {
  //   filteredProducts = filteredProducts.filter(p => p.name);
  // }
  if(filteredProducts && filteredProducts.length) {
    for(let i = 0; i < filteredProducts.length; i += 1) {
      const { image } = filteredProducts[i];
      if(image && image.includes('httpss')) {
        filteredProducts[i].image = image.replace('httpss', 'https');
      }
    }
  }
  let orderDate = Date.now();
  if(date && date < orderDate) {
    orderDate = date;
  }
  return new WooEvent({
    date: orderDate,
    accountId,
    orderId,
    email: email || `${cryptoUtils.randomString(10)}@from-woocommerce.com`,
    ip,
    ips,
    firstName,
    lastName,
    location: eventLocation,
    host: siteUrl,
    products: filteredProducts,
    currency,
    total,
    pluginVersion,
  });
}

async function incAnalytics(event) {
  const findQ = { accountId: event.accountId, notificationId: null };
  const minuteOfDay = dateUtils.minuteOfTheDay();
  const updateQ = { $inc: { 'total.woocommerceOrders': 1, [`woocommerceOrders.${minuteOfDay}`]: 1 } };
  await AnalyticsEvent.update(findQ, updateQ, { upsert: true }).catch((err) => {
    logger.error({ err, event }, 'failed to store woocommerce analytics');
  });
}
