const config = require('../../config');
const cryptoUtils = require('../../lib/utils/cryptoUtils');
const authTypes = require('../../middleware/authTypes');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {},
  handle(req, res, next) {
    res.body = module.exports.generate();
    next();
  },
  /**
   * * @return {{webhook: string, webhookId: *}}
   * */
  generate() {
    const webhookId = cryptoUtils.encrypt(cryptoUtils.randomString(8), config.cryptoKeys.webhook);
    const webhook = `${config.apiUrl}/webhooks/track/${webhookId}`;
    return { webhook, webhookId };
  },
};
