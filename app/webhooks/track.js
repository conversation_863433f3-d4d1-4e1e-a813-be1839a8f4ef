/* eslint-disable no-use-before-define,no-param-reassign,no-restricted-globals,no-underscore-dangle,max-len */
const _ = require('lodash');
const logger = require('../../lib/logger')('webhooksTrack');
const ErrorFactory = require('../../lib/errors/ErrorFactory');
const authTypes = require('../../middleware/authTypes');
const config = require('../../config');
// eslint-disable-next-line no-underscore-dangle
const _utils = require('../../lib/utils/lodashUtils');

const validateWebhook = require('./validate');
const {
  dateUtils, objectUtils, stringUtils, emailUtils,
} = require('../../lib/utils');
const maxmind = require('../../lib/maxmind');
const countryCodes = require('../../lib/geo/countryCodes');
const stateCodes = require('../../lib/geo/stateCodes');
const slack = require('../../lib/apis/slackNotifier');
const stripeHelper = require('./stripeHelper');
const notifierService = require('../common/notifier.service');

const CONSTANTS = require('./constants');
const notifConstants = require('../notifications/constants');
const eventConstants = require('../events/constants');
const Notification = require('../notifications/models/Notification');
const WebhookEvent = require('../events/models/WebhookEvent');
const WebhookStreamEvent = require('../events/models/WebhookStreamEvent');
const Account = require('../account/models/Account');
const Feed = require('../account/models/Feed');
const AnalyticsEvent = require('../notifications/models/AnalyticsEvent');
const WebhookData = require('./WebhookData');
const WebhookError = require('./WebhookError');
const cacheService = require('../common/cache.service');

module.exports = {
  config: {
    methods: ['POST', 'GET', 'HEAD'],
    authType: authTypes.noAuth,
    keepRawBody: true,
    maxPayload: '1mb',
  },
  schema: {
    POST: {
      type: 'object',
      additionalProperties: true,
      properties: {
        guid: { type: ['string', 'integer'] },
        email: { type: 'string' },
        timestamp: { type: ['number', 'string'] },
        ip: { type: ['string', 'null'] },
        firstName: { type: ['string', 'null'] },
        lastName: { type: ['string', 'null'] },
        city: { type: ['string', 'null'], maxLength: 100 },
        country: { type: ['string', 'null'], maxLength: 100 },
        countryCode: { type: ['string', 'null'], maxLength: 100 },
        state: { type: ['string', 'null'], maxLength: 100 },
        stateCode: { type: ['string', 'null'], maxLength: 100 },
        total: { type: ['number', 'string', 'null'] },
        currency: { type: 'string' },
        productId: { type: ['string', 'null'] },
        productName: { type: ['string', 'null'] },
        productLink: { type: ['string', 'null'] },
        productImage: { type: ['string', 'null'] },
        products: {
          type: ['array', 'string'],
          minItems: 1,
          items: {
            type: 'object',
            properties: {
              id: { type: ['integer', 'string'] },
              quantity: { type: ['number', 'string'] },
              price: { type: ['number', 'string'] },
              name: { type: ['string', 'null'] },
              link: { type: ['string', 'null'] },
              image: { type: ['string', 'null'] },
            },
          },
        },
      },
    },
  },
  handleHEAD(req, res, next) {
    return module.exports.handleGET(req, res, next);
  },
  handleGET(req, res, next) {
    const webhookId = req.params.id;
    const isValid = validateWebhook(webhookId);
    if(!isValid) return next(ErrorFactory('unable to track this webhook', 400));

    res.body = { message: 'Webhook is active, to track conversions/webhook use **POST** request' };
    return next();
  },
  handlePOST(req, res, next) {
    const webhookId = req.params.id;
    const source = getSourceDetails(req.headers['user-agent'], req.rawBody);
    return new Promise((resolve, reject) => {
      const isValid = validateWebhook(webhookId);
      if(!isValid) {
        saveWebhookError(req, webhookId, null, 'invalid webhook');
        return reject(ErrorFactory('unable to track this webhook', 400));
      }

      // if(req.body.timestamp && isNaN(req.body.timestamp)) {
      //   return reject(ErrorFactory('timestamp must be numeric'));
      // }

      return resolve();
    }).then(() => Notification.findOne({ webhookId }, {
      type: 1, accountId: 1, name: 1, message: 1, image: 1,
    }))
      .then(async (notification) => {
        if(!notification) {
          const msg = 'no notification with webhook (trying completing / saving the notification)';
          saveWebhookError(req, webhookId, null, msg);
          throw ErrorFactory(msg, 400);
        }

        const { accountId, message, image } = notification;
        let errMsg = null;
        const data = await pickData(req, accountId, message, image).catch((err) => {
          errMsg = `Webhook failed: ${err.message}`;
        });
        data.accountId = accountId;
        data.notification = notification.name;

        saveWebhookData(req, webhookId, accountId);
        const { sources, stripeEvents } = CONSTANTS;
        if(data.source === sources.stripe && !stripeEvents.includes(req.body.type)) {
          errMsg = `Stripe webhook event not supported. Supported events: ${CONSTANTS.stripeEvents.join(', ')}`;
        }
        if(notification.type === notifConstants.notificationTypes.stream && !data.email) {
          errMsg = `${getSourceTitle(data)} Failed (missing email parameter)`;
        }
        if(errMsg) {
          saveFeed(accountId, errMsg, { webhookId, notification: notification.name });
          saveWebhookError(req, webhookId, accountId, errMsg);
          if(req.body && req.body.test === 'test') {
            return next();
          }
          throw ErrorFactory(errMsg);
        }
        logger.info({ webhookId, params: data }, 'tracking webhook');
        data.webhookId = webhookId;
        data.notification = notification.name;
        const shouldSave = await shouldProcess(data, req.body, source);
        if(!shouldSave) {
          return next();
        }

        // _.pick so there is no keys with null (overwritten in trackWebhook)
        const location = await maxmind.geoIP(data.ip).catch(() => {});
        data.location = Object.assign(
          {},
          location,
          _(data).pick(['city', 'country', 'countryCode', 'state', 'stateCode']).pickBy(_.identity).value(),
        );
        return Promise.all([
          incAnalytics(notification),
          trackWebhook(webhookId, data.accountId, data.guid),
          trackStreamWebhook(webhookId, data, notification),
          data,
        ]).catch((err) => {
          saveWebhookError(req, webhookId, accountId, err.message);
          slack.notifyError(err, `webhook track failed ${webhookId}`, { data: req.body });
          throw err;
        });
      }).then((results) => {
        if(results && results.length >= 3) {
          const event = results[2];
          const data = results[3];
          const accountId = (event && event.accountId) || data.accountId;
          const feedData = (event && event.toJSON({ versionKey: false })) || data;
          feedData.notification = data.notification;
          saveFeed(accountId, null, _.omit(feedData, ['__t', '_id']));
        }
        res.body = { message: 'success' };
        next();
      })
      .catch((err) => {
        logger.error({ err, webhookId, trackBody: req.body }, 'webhook track failed');
        if(source && source.ignoreErrors) {
          next();
        } else {
          next(err);
        }
      });
  },
};

async function pickData(req, accountId, message, image) {
  const keys = [
    'guid', 'timestamp', 'ip',
    'countryCode', 'stateCode',
    'total', 'currency', 'productImage', 'products',
  ];

  let data = req.body;
  if(_.isEmpty(data)) {
    data = req.query;
  }
  const retval = _.pick(data, keys);
  retval.source = req.headers['user-agent'];
  const source = getSource(req.headers['user-agent']);

  // const templateVars = handlebars.parseWithoutProcessing(message).body.map((param) => _.get(param, 'path.original').filter(param => param)
  const messageBrackets = (message && message.match(/{{([^{}]+)}}(?![{}])/g)) || [];
  const imageBrackets = (image && image.match(/{{([^{}]+)}}(?![{}])/g)) || [];
  const doubleBrackets = [...imageBrackets, ...messageBrackets];
  if(doubleBrackets.length > 0) {
    const payload = {};
    doubleBrackets.forEach((item) => {
      const key = item.replace(/{{|}}/g, '').trim();
      const dataValue = _.get(data, key);
      if(dataValue) {
        let payloadError = null;
        if(_.isString(dataValue) || _.isBoolean(dataValue) || _.isNumber(dataValue)) {
          const valueString = dataValue.toString();
          if(valueString.length > 500) {
            payloadError = new Error(`Variable "${key}" should not exceed 500 characters`);
          }
          _.set(payload, key, dataValue);
        } else {
          payloadError = new Error(`Variable "${key}" must be of type number, boolean, or string`);
        }
        if(payloadError) {
          notifierService.notifyError(payloadError, payloadError.message, data);
          throw payloadError;
        }
      }
    });
    const payloadString = JSON.stringify(payload);
    if(payloadString.length > 10 * 1000) {
      throw new Error('Variables payload is too large');
    }
    retval.payload = payload;
  }

  // Add stripe customer to the data (for lookup)
  if(source === CONSTANTS.sources.stripe) {
    const customerId = _.get(data, 'data.object.customer');
    try {
      const stripeCustomer = await stripeHelper.getCustomerData(accountId, customerId);
      if(stripeCustomer) Object.assign(data, stripeCustomer);
    } catch(err) { /** do nothing */ }
  }
  retval.ip = getIP(data);
  retval.email = getEmail(data, req.rawBody, req.headers['content-type']);
  if(!retval.email && source === CONSTANTS.sources.stripe) {
    retval.email = `${stringUtils.randomString(20)}@random-stripe.com`;
  }

  retval.firstName = getFirstName(data);
  retval.lastName = getLastName(data);

  retval.countryCode = getCountryCode(data);
  retval.country = getCountry(data);
  if(retval.country && retval.country.length <= 3) {
    retval.countryCode = retval.country;
    retval.country = countryCodes.fromCode(retval.countryCode);
  }
  if(retval.countryCode === 'UK') {
    retval.countryCode = 'GB';
  }
  retval.state = getState(data);
  if(retval.state && retval.state.length <= 3) {
    retval.stateCode = retval.state;
    retval.state = stateCodes.fromCode(retval.state);
  }
  retval.city = getCity(data);

  const products = getProducts(data);
  retval.products = products;

  if(products && products.length && products.find(p => p.name === retval.firstName)) {
    retval.firstName = null;
  }

  let timestamp = parseInt(retval.timestamp, 10);
  if(dateUtils.isSecondsTimestamp(timestamp)) timestamp = retval.timestamp * 1000;

  const ignoreThreshold = req.headers && req.headers['x-ps-debug'] === 'true123';
  retval.timestamp = acceptableTimestamp(timestamp, ignoreThreshold);
  retval.date = retval.timestamp;

  if(retval.country && !retval.countryCode) {
    retval.countryCode = countryCodes.toCode(retval.country);
  } else if(retval.countryCode && !retval.country) {
    retval.country = countryCodes.fromCode(retval.countryCode);
  }

  if(retval.state && !retval.stateCode) {
    retval.stateCode = stateCodes.toCode(retval.state);
  } else if(retval.stateCode) {
    retval.state = stateCodes.fromCode(retval.stateCode);
  }

  return retval;
}

function trackWebhook(webhookId, accountId, guid = null) {
  const day = dateUtils.todayNormalized12am();
  const date = day;
  const minuteOfDay = dateUtils.minuteOfTheDay();
  const countKey = `counts.${minuteOfDay}`;

  const find = {
    accountId, webhookId, guid, day, type: eventConstants.eventTypes.conversion,
  };
  const update = {
    $inc: { total: 1, [countKey]: 1 },
    $setOnInsert: { firstMinute: minuteOfDay, date },
  };
  const options = { upsert: true, w: 0 };

  return WebhookEvent.update(find, update, options).then((result) => {
    logger.info({ guid, webhookId, result }, 'trackWebhook updated db');
  });
}

function trackStreamWebhook(webhookId, data) {
  if(!data || !data.email) return null;

  const streamEvent = new WebhookStreamEvent(data);
  const promises = [];
  if(config.getSocialProfiles) {
    promises.push(streamEvent.addGravatar());
    promises.push(streamEvent.addPicasa());
  }

  return Promise.all(promises).then(() => streamEvent.save()).then((savedEvent) => {
    logger.info({ event: savedEvent }, 'saved stream event');
    return streamEvent;
  });
}

function saveFeed(accountId, message, data) {
  if(!message) {
    message = getSourceTitle(data);
    if((data.products && data.products.length) || (data.productName)) {
      message += ' Purchase Event';
    } else {
      message += ' Event';
    }
  }

  const feedData = _.omit(data, ['accountId']);
  if(data.date) {
    feedData.date = new Date(data.date).toUTCString();
  } else {
    feedData.date = new Date().toUTCString();
  }

  Feed.saveFeed(accountId, message, feedData);
}

function saveWebhookError(req, webhookId, accountId, error) {
  const data = {
    accountId,
    webhookId,
    reqId: req.id,
    error,
    body: req.body,
    query: req.query,
    headers: req.headers,
  };
  (new WebhookError(data)).save().catch((err) => {
    slack.notifyError(err, 'failed to save webhookData', data);
  });
}

function saveWebhookData(req, webhookId, accountId) {
  const data = {
    accountId, webhookId, reqId: req.id, body: req.body, query: req.query, headers: req.headers,
  };
  (new WebhookData(data)).save().catch((err) => {
    slack.notifyError(err, 'failed to save webhookData', data);
  });
}

function incAnalytics(notification) {
  const minuteOfDay = dateUtils.minuteOfTheDay();
  const { accountId } = notification;
  const updateQ = { $inc: { 'total.webhooks': 1, [`webhooks.${minuteOfDay}`]: 1 } };
  const opts = { upsert: true };
  return Promise.all([
    AnalyticsEvent.update({ accountId, notificationId: null }, updateQ, opts),
    AnalyticsEvent.update({ accountId, notificationId: notification._id }, updateQ, opts),
  ]);
}

function acceptableTimestamp(timestamp, ignoreThreshold) {
  let retval = timestamp;
  const now = Date.now();
  if(isNaN(retval)) return now;

  if(retval > now) retval = now;

  if(!ignoreThreshold) {
    const threshold = now - dateUtils.MILLISECONDS_IN_DAY * 5; // 5 days ago
    if(retval < threshold) {
      retval = now;
    }
  }
  return retval;
}

async function shouldProcess(data, rawData, source = null) {
  if(source && source.source === CONSTANTS.sources.eduzz) {
    return rawData && rawData.trans_status === '3'; // transaction status = paid, https://github.com/deveduzz/webhook-eduzz
  }
  if(source && source.source === CONSTANTS.sources.thrivecart) {
    return rawData && rawData.event === 'order.success';
  }
  return true;
}

function getSourceTitle(data) {
  const defaultValue = 'Webhook';
  if(!data || !data.source || !data.source.length) return defaultValue;

  const sourceDetails = getSourceDetails(data.source);
  return (sourceDetails && sourceDetails.title) || defaultValue;
}

function getSourceDetails(userAgent, rawData) {
  if(!userAgent || !userAgent.toLowerCase) return null;

  const uaLowercase = userAgent.toLowerCase();
  let source = CONSTANTS.sourceDetails.find(i => uaLowercase.includes(i.source));
  if(!source) {
    if(rawData) {
      if(rawData.includes('teachable')) {
        source = CONSTANTS.sourceDetails.find(i => i.source === CONSTANTS.sources.teachable);
      }
      if(rawData.includes('eduzz_value')) {
        source = CONSTANTS.sourceDetails.find(i => i.source === CONSTANTS.sources.eduzz);
      }
    }
  }
  return source;
}

function getSource(userAgent, rawData) {
  if(!userAgent || !userAgent.toLowerCase) return null;

  const source = getSourceDetails(userAgent, rawData);
  return source && source.source;
}

function getEmail(data, rawData, contentType) {
  if(!data) return null;

  let email = findValueWithPaths(data, CONSTANTS.emailKeys, true);
  if(!_.isString(email)) {
    email = objectUtils.findFirst(data, CONSTANTS.emailKeys, { ignoreCase: true });
  }
  if(!_.isString(email) && rawData && rawData.toString) {
    let matches = null;
    if(contentType.includes('encoded')) {
      matches = emailUtils.getMatchesInEncodedData(rawData);
    }
    if(!matches || !matches.length) {
      matches = emailUtils.getMatchesInJson(data);
    }
    if(matches && matches.length) {
      email = matches[0];
    }
  }

  if(_.isString(email)) {
    email = email.replace(/\s/g, '');
  }
  if(!emailUtils.isEmail(email)) {
    email = null;
  }

  return email || null;
}

function getFirstName(data) {
  if(!data) return null;

  // hash lookup before iteration (optimize for most common case)
  let firstName = findValueWithPaths(data, CONSTANTS.firstNameKeys, true);
  if(stringUtils.isEmptyString(firstName)) {
    firstName = objectUtils.findFirst(data, CONSTANTS.firstNameKeys, { ignoreCase: true });
  }
  if(stringUtils.isEmptyString(firstName)) firstName = null;

  return firstName;
}

function getLastName(data) {
  if(!data) return null;

  // hash lookup before iteration (optimize for most common case)
  let lastName = findValueWithPaths(data, CONSTANTS.lastNameKeys, true);
  if(stringUtils.isEmptyString(lastName)) {
    lastName = objectUtils.findFirst(data, CONSTANTS.lastNameKeys, { ignoreCase: true });
  }
  if(stringUtils.isEmptyString(lastName)) lastName = null;

  return lastName;
}

function getProducts(data) {
  const products = [];
  if(_.isObject(data.product)) {
    products.push(...getCleanProducts([data.product]));
  } else {
    let productsData = objectUtils.findFirst(data, 'products');
    if(_.isString(data.products)) {
      const parsedProducts = stringUtils.safeJsonParse(productsData);
      if(parsedProducts) {
        productsData = parsedProducts;
      }
    }
    products.push(...getCleanProducts(productsData));
  }
  if(!products.length) {
    const productsData = objectUtils.findFirst(data, 'line_items', { ignoreCase: true });
    products.push(...getCleanProducts(productsData));
  }

  if(!products.length) {
    const product = {
      id: getProductId(data),
      name: getProductName(data),
      link: getProductLink(data),
      image: data.productImage,
      price: data.productPrice,
    };
    if(product.name) {
      products.push(product);
    }
  }
  return products;
}

function getCleanProducts(products) {
  const cleanProducts = [];
  const length = products && products.length;
  const maxProducts = 10;
  if(_.isArray(products)) {
    for(let i = 0; i < length; i += 1) {
      const product = { ...products[i] };
      if(!product.name) {
        if(product.articleName) {
          product.name = product.articleName;
        } else if(product.title) {
          product.name = product.title;
        }
      }
      if(product.name && !_.isString(product.name)) {
        product.name = null;
      }

      if(!product.link) {
        if(product.url) {
          product.link = product.url;
        } else if(product.href) {
          product.link = product.href;
        } else if(product.productURL) {
          product.link = product.productURL;
        }
      }
      if(product.link && !_.isString(product.link)) {
        product.link = null;
      }

      if(!product.price) {
        if(product.price_full_inc_vat) {
          product.price = product.price_full_inc_vat;
        } else if(product.amount) {
          product.price = product.amount;
          if(product.amount.cents) {
            product.price = product.amount.cents / 100;
          }
        }
      }
      if(product.price && !_.isNumber(product.price)) {
        product.price = null;
      }

      if(!product.image) {
        if(product.image_url) {
          product.image = product.image_url;
        } else if(product.productImage) {
          product.image = product.productImage;
        }
      } else if(product.image.src) {
        product.image = product.image.src;
      } else if(product.image.href) {
        product.image = product.image.href;
      }
      if(product.image && !_.isString(product.image)) {
        product.image = null;
      }

      if(product.name && product.name.length) {
        cleanProducts.push(product);
      }
      if(cleanProducts.length >= maxProducts) {
        break;
      }
    }
  } else if(_.isString(products)) {
    const names = products.split(',');
    for(let i = 0; i < names.length; i += 1) {
      cleanProducts.push({ name: names[i] });
      if(cleanProducts.length >= maxProducts) {
        break;
      }
    }
  }
  return cleanProducts;
}

function getProductId(data) {
  if(!data) return null;

  let productId = data.productId || data.prod
    || _.get(data, 'object.course.id') // Teachable
    || _.get(data, 'data.product.id'); // Hotmart

  if(!productId || (_.isString(productId) && stringUtils.isEmptyString(productId))) {
    productId = undefined;
  }

  return productId;
}

function getProductName(data) {
  if(!data) return null;

  let name = data.productName || data.prod_name;
  if(stringUtils.isEmptyString(name)) {
    name = findValueWithPaths(data, CONSTANTS.productNameKeys, true);
  }
  if(stringUtils.isEmptyString(name)) {
    name = objectUtils.findFirst(data, CONSTANTS.productNameKeys, { ignoreCase: true });
  }
  if(stringUtils.isEmptyString(name)) {
    name = null;
  }

  return name;
}

function getProductLink(data) {
  if(!data) return null;

  let link = data.productLink;
  if(stringUtils.isEmptyString(link)) {
    link = findValueWithPaths(data, CONSTANTS.productLinkKeys);
  }
  if(stringUtils.isEmptyString(link)) {
    link = objectUtils.findFirst(data, CONSTANTS.productLinkKeys, { ignoreCase: true });
  }
  if(stringUtils.isEmptyString(link)) link = null;

  return link;
}

function getCity(data) {
  if(!data) return null;

  let city = findValueWithPaths(data, CONSTANTS.cityKeys, true);
  if(stringUtils.isEmptyString(city)) {
    city = objectUtils.findFirst(data, CONSTANTS.cityKeys, { ignoreCase: true });
  }
  if(stringUtils.isEmptyString(city)) city = null;

  return city;
}

function getCountryCode(data) {
  if(!data) {
    return null;
  }

  let countryCode = findValueWithPaths(data, CONSTANTS.countryCodeKeys, true);
  if(stringUtils.isEmptyString(countryCode)) {
    countryCode = objectUtils.findFirst(data, CONSTANTS.countryCodeKeys, { ignoreCase: true });
  }
  if(stringUtils.isEmptyString(countryCode)) countryCode = null;

  return countryCode;
}

function getCountry(data) {
  if(!data) return null;

  let country = findValueWithPaths(data, CONSTANTS.countryKeys, true);
  if(stringUtils.isEmptyString(country)) {
    country = objectUtils.findFirst(data, CONSTANTS.countryKeys, { ignoreCase: true });
  }
  if(stringUtils.isEmptyString(country)) country = null;

  return country;
}

function getState(data) {
  if(!data) return null;

  let state = findValueWithPaths(data, CONSTANTS.stateKeys, true);
  if(stringUtils.isEmptyString(state)) {
    state = objectUtils.findFirst(data, CONSTANTS.stateKeys);
  }
  if(stringUtils.isEmptyString(state)) state = null;

  return state;
}

function getIP(data) {
  if(!data) return null;

  let ip = findValueWithPaths(data, CONSTANTS.ipPaths, true);
  if(stringUtils.isEmptyString(ip)) {
    ip = objectUtils.findFirst(data, CONSTANTS.ipPaths, { ignoreCase: true });
  }
  if(stringUtils.isEmptyString(ip)) {
    ip = null;
  }

  return ip;
}

function findValueWithPaths(data, paths, searchContainers = false) {
  if(!data || !paths || !paths.length) return null;

  let retval = null;
  for(let i = 0; i < paths.length; i += 1) {
    retval = _utils.geti(data, paths[i], null);
    if(retval && _.isObject(retval)) retval = null;
    if(retval) break;
  }
  if(!retval && searchContainers) {
    retval = findValueWithKeys(data, paths);
  }
  return retval;
}

function findValueWithKeys(data, keysArray) {
  if(!data) return null;
  let retval = findByKeys(data, keysArray);
  if(retval) return retval;

  for(let i = 0; i < CONSTANTS.containerKeys.length; i += 1) {
    const key = CONSTANTS.containerKeys[i];
    const container = _utils.geti(data, key);
    retval = findByKeys(container, keysArray);
    if(retval) return retval;
  }

  return null;
}

function findByKeys(obj, keysArray) {
  if(!keysArray || !obj) return null;

  return _.find(obj, (v, k) => {
    if(k && k.toLowerCase) {
      const lower = k.toLowerCase();
      return !_.isObject(v) && !_.isNull(v) && !_.isUndefined(v) && keysArray.includes(lower);
    }
    return false;
  });
}
