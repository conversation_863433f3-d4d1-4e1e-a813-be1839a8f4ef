const mongoose = require('mongoose');

const ShopifyData = new mongoose.Schema({
  accountId: { type: mongoose.SchemaTypes.ObjectId, required: true, index: true },
  shop: Object,
  orderId: { type: Number },
  body: Object,
  query: Object,
  headers: Object,
}, { timestamps: true, collection: 'shopifyData' });

ShopifyData.index({ createdAt: -1 }, { expires: '30d' });

/**
 * @memberOf ShopifyData
 * @param accountId
 * @param shop
 * @param {object} options
 * @param {number} options.orderId
 * @param {object} options.body
 * @param {object} options.headers
 * @param {object} options.query
 */
ShopifyData.statics.saveData = function saveData(accountId, shop, {
  orderId = 0, body, headers, query,
}) {
  const doc = new this({
    accountId, orderId: Math.trunc(orderId), shop, body, headers, query,
  });
  return doc.save();
};

module.exports = mongoose.model('ShopifyData', ShopifyData);
