const request = require('superagent');
const { RateLimiterRedis } = require('rate-limiter-flexible');
const isUrl = require('is-url');
const authTypes = require('../../middleware/authTypes');
const redisService = require('../common/redis.service');
const ErrorFactory = require('../../lib/errors/ErrorFactory');

const rateLimiter = new RateLimiterRedis({
  storeClient: redisService.getClient(),
  blockDuration: 60,
  duration: 60,
  points: 5,
  keyPrefix: 'webhook-test',
});

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.console,
  },
  schema: {
    type: 'object',
    properties: {
      webhookUrl: { type: 'string', required: true },
    },
  },
  async handlePOST(req, res, next) {
    const { webhookUrl } = req.body;
    const { accountId } = req.session;
    await rateLimiter.consume(accountId, 1).catch(() => {
      throw ErrorFactory('Too many webhook test attempts, try again in a few minutes', 429);
    });
    if(!webhookUrl || !isUrl(webhookUrl)) {
      throw ErrorFactory('Webhook is not a valid URL');
    }
    if(!webhookUrl.includes('https://')) {
      throw ErrorFactory('Webhook must use HTTPS');
    }
    await request.post(webhookUrl)
      .set('User-Agent', 'ProveSource Webhook 1.0')
      .send({
        event: 'form.submission',
        timestamp: Date.now(),
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Smith',
        ip: '************',
        url: 'https://domain.com/contact-form',
        country: 'United States',
        countryCode: 'USA',
        city: 'New York',
      }).catch((err) => {
        throw ErrorFactory(`Failed to send webhook: ${err.message}`, 500, { accountId, webhookUrl });
      });
    res.body = { success: true };
    next();
  },
};
