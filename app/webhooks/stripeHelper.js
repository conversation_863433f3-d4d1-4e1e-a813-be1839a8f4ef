const Account = require('../account/models/Account');

const getCustomerData = async function (accountId, customerId) {
  if(!accountId || !customerId) return null;

  const account = await Account.findOne({ _id: accountId });
  const { apiKey } = account.integrations.stripe;
  if(!apiKey) {
    return null;
  }

  // eslint-disable-next-line global-require
  const stripe = require('stripe')(apiKey);
  return stripe.customers.retrieve(customerId);
};

module.exports = {
  getCustomerData,
};
