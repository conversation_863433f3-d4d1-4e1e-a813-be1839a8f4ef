const authTypes = require('../../middleware/authTypes');
const notificationService = require('../notifications/notification.service');

module.exports = {
  config: {
    methods: ['POST', 'GET'],
    authType: authTypes.noAuth,
  },
  schema: {},
  async handle(req, res, next) {
    // const { id: webhook } = req.params;
    // const notification = await notificationService
    //   .findWithWebhook(webhook, { _id: 1 })
    //   .catch(() => {});
    // if(!notification) {
    //   res.status(404);
    //   throw new Error('Webhook not found. Did you save the notification?');
    // }
    next();
  },
};
