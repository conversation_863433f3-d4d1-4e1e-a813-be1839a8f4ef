const authTypes = require('../../middleware/authTypes');
const notifier = require('../common/notifier.service');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.noAuth,
  },
  schema: {
    type: 'object',
    properties: {
      plugin: { type: 'string' },
      message: { type: 'string', required: true },
      err: { type: 'object' },
      data: { type: 'object' },
    },
  },
  async handle(req, res, next) {
    const { accountId } = req.jwtData || {};
    const {
      plugin = 'unknown', message, data, err,
    } = req.body;
    notifier.notifyError(new Error(`${plugin} plugin error: ${message}`), `${plugin} plugin error`, {
      ...(accountId && { accountId }),
      err,
      payload: data,
      headers: req.headers,
    });
    next();
  },
};
