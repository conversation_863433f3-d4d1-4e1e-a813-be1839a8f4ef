module.exports = {
  containerKeys: ['customer', 'contact', 'client', 'user', 'address',
    'billing_address', 'shipping_address', 'billing_details', 'invitee', 'payer', 'data', 'payload',
    'object.user', 'payload.user', 'order', 'data.buyer',
  ],
  emailKeys: ['email', 'e-mail', 'clientemail', 'client_email', 'customeremail', 'customer_email', 'custemail', 'cust_email', 'cus_email', 'useremail', 'user_email', 'email_address'],
  firstNameKeys: ['firstname', 'first_name', 'first name', 'fname', 'f_name', 'name_f', 'name', 'fullname', 'full_name', 'full name', 'cus_name', 'customer_name', 'prénom'],
  lastNameKeys: ['lastname', 'last_name', 'last name', 'lname', 'l_name', 'name_l', 'surname'],
  productNameKeys: ['productname', 'product_name', 'product', 'service', 'item_name', 'resource_name'],
  productLinkKeys: ['page_checkout_url'],
  countryKeys: ['country', 'address_country', 'billing_country'],
  countryCodeKeys: ['countryCode', 'country_code', 'cus_address_country'],
  stateKeys: ['state', 'address_state', 'billing_state', 'data.object.shipping.address.state'],
  cityKeys: ['city', 'address_city', 'billing_city', 'cus_address_city'],
  ipPaths: ['ip', 'ip4', 'data.data.ip_signup', 'object.user.current_sign_in_ip'],
  stripeEvents: [
    'charge.succeeded',
    'customer.created',
    'customer.subscription.created',
    'invoice.created',
    'order.created',
    'payment_intent.succeeded',
  ],
};

const sources = {
  stripe: 'stripe',
  zapier: 'zapier',
  integromat: 'integromat',
  mailchimp: 'mailchimp',
  sendowl: 'sendowl',
  shoprenter: 'shoprenter',
  infusionsoft: 'infusionsoft',
  postman: 'postman',
  zoho: 'zoho',
  memberpress: 'memberpress',
  flexie: 'flexie',
  checkfront: 'checkfront',
  getsitecontrol: 'getsitecontrol',
  samcart: 'samcart',
  paypal: 'paypal',
  paperform: 'paperform',
  thrivecart: 'thrivecart',
  teachable: 'teachable',
  eduzz: 'eduzz',
};
module.exports.sources = sources;

module.exports.sourceDetails = [
  { source: sources.stripe, title: 'Stripe Webhook' },
  { source: sources.zapier, title: 'Zapier Webhook' },
  { source: sources.integromat, title: 'Integromat Webhook' },
  { source: sources.mailchimp, title: 'MailChimp Webhook' },
  { source: sources.sendowl, title: 'SendOwl Webhook' },
  { source: sources.shoprenter, title: 'ShopRenter Webhook' },
  { source: sources.infusionsoft, title: 'InfusionSoft Webhook' },
  { source: sources.postman, title: 'Postman Webhook' },
  { source: sources.zoho, title: 'ZOHO Webhook' },
  { source: sources.memberpress, title: 'MemberPressWebhook' },
  { source: sources.flexie, title: 'Flexie CRM Webhook' },
  { source: sources.checkfront, title: 'CheckFront Webhook' },
  { source: sources.getsitecontrol, title: 'GetSiteControl Webhook' },
  { source: sources.samcart, title: 'SamCart Webhook' },
  { source: sources.paypal, title: 'PayPal IPN' },
  { source: sources.paperform, title: 'PaperForm Webhook' },
  { source: sources.thrivecart, title: 'ThriveCart Webhook' },
  { source: sources.teachable, title: 'Teachable Webhook', ignoreErrors: true },
  { source: sources.eduzz, title: 'Eduzz Webhook' },
];

/** clickfunnels */
module.exports.containerKeys.push('purchase', 'purchase.contact', 'purchase.contact.contact_profile');

// clickfunnels 2.0
module.exports.containerKeys.push('data.contact', 'data.order.contact');
module.exports.productNameKeys.push('data.line_items.0.original_product.name', 'data.line_items.0.products_variant.name');

/** paypal */
module.exports.emailKeys.push('payer_email');
module.exports.productNameKeys.push('item_name1');

/** kajabi */
module.exports.emailKeys.push('member_email');
module.exports.firstNameKeys.push('member_first_name');
module.exports.lastNameKeys.push('member_last_name');
module.exports.productNameKeys.push('offer_title');
module.exports.countryCodeKeys.push('contact_address_country');
module.exports.countryKeys.push('contact_address_country_name');
module.exports.cityKeys.push('contact_address_city');

/** italian */
module.exports.lastNameKeys.push('cognome');
module.exports.firstNameKeys.push('nome');

/** hebrew */
module.exports.firstNameKeys.push('שם');
module.exports.emailKeys.push('מייל');
module.exports.emailKeys.push('דואר אלקטרוני');
module.exports.emailKeys.push('דוא״ל');

/** kartra */
module.exports.firstNameKeys.push('buyer_first_name');
module.exports.lastNameKeys.push('buyer_last_name');
module.exports.emailKeys.push('buyer_email');
module.exports.countryKeys.push('buyer_billing_country');
module.exports.stateKeys.push('buyer_billing_state');
module.exports.cityKeys.push('buyer_billing_city');

/** stripe */
module.exports.emailKeys.push('data.object.email');
module.exports.firstNameKeys.push('data.object.name', 'data.object.shipping.name', 'data.object.metadata.Name');
module.exports.countryKeys.push('data.object.shipping.address.country', 'data.object.billing_details.address.country');
module.exports.stateKeys.push('data.object.billing_details.address.state');
module.exports.cityKeys.push('data.object.shipping.address.city', 'data.object.billing_details.address.city');
module.exports.containerKeys.push('data.customer', 'object', 'data.object.metadata', 'data.object',
  'data.object.source', 'data.object.sources.data[0]', 'sources.data[0]', 'data.object.billing_details');
module.exports.productNameKeys.push('data.object.plan.nickname');

/** whop */
module.exports.emailKeys.push('data.user.email');
module.exports.firstNameKeys.push('data.user.name');

/** teachable */
module.exports.firstNameKeys.push('object.user.name');
module.exports.productNameKeys.push('object.sale.course.name', 'object.course.name');
module.exports.productLinkKeys.push('object.sale.course.url', 'object.course.url');

/** hotmart */
module.exports.productNameKeys.push('data.product.name');

/** mailchimp */
module.exports.emailKeys.push('data.email');
module.exports.emailKeys.push('data.merges.EMAIL');
module.exports.firstNameKeys.push('data.merges.FNAME');
module.exports.lastNameKeys.push('data.merges.LNAME');
module.exports.ipPaths.push('data.ip_opt');

/** Cognito Forms */
module.exports.firstNameKeys.push('name.first');
module.exports.lastNameKeys.push('name.last');
module.exports.ipPaths.push('entry.origin.ipaddress');

/** SendOwl */
module.exports.ipPaths.push('buyer_ip_address');
module.exports.productNameKeys.push('order.cart.cart_items[0].product.name');

/** ThriveCart */
module.exports.containerKeys.push('customer');
module.exports.ipPaths.push('ip_address');
module.exports.productNameKeys.push('base_product_name');

/** Systeme.io */
module.exports.ipPaths.push('data.customer.client_ip');
module.exports.containerKeys.push('data.contact.fields');
module.exports.firstNameKeys.push('data.contact.fields.first_name');
module.exports.countryCodeKeys.push('data.contact.fields.country');

/** CardCom */
module.exports.firstNameKeys.push('CardOwnerName');
module.exports.ipPaths.push('UserIP');

/** Drip */
module.exports.containerKeys.push('subscriber');
module.exports.containerKeys.push('subscriber.custom_fields');

/** Bluepark */
module.exports.productNameKeys.push('title');
module.exports.productLinkKeys.push('href');

/** FareHarbor */
module.exports.emailKeys.push('booking.contact.email');
module.exports.firstNameKeys.push('booking.contact.name');

/** calendly */
module.exports.containerKeys.push('payload.invitee');
