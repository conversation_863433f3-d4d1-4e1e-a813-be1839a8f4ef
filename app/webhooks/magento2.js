const _ = require('lodash');
const url = require('url');
const authTypes = require('../../middleware/authTypes');
const logger = require('../../lib/logger')('magento2');
const slack = require('../../lib/apis/slackNotifier');

const maxmind = require('../../lib/maxmind');
const MagentoEvent = require('../events/models/Magento2Event');
const Feed = require('../account/models/Feed');
const WebhookData = require('./WebhookData');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.api,
  },
  schema: {
    type: 'object',
    properties: {
      firstName: { type: 'string', required: true },
      lastName: { type: 'string' },
      email: { type: 'string', required: true },
      total: { type: 'number' },
      currency: { type: 'string' },
      ip: { type: 'string' },
      ips: {
        type: 'array',
        items: {
          type: ['string', 'null'],
        },
      },
      store: {
        type: 'object',
        additionalProperties: false,
        properties: {
          id: { type: 'string' },
          code: { type: 'string' },
          url: { type: 'string' },
          name: { type: 'string' },
          websiteId: { type: 'string' },
        },
      },
      siteUrl: { type: 'string', required: true },
      products: {
        type: 'array',
        required: true,
        minItems: 1,
        items: {
          type: 'object',
          additionalProperties: false,
          properties: {
            id: { type: 'string', required: true },
            quantity: { type: 'number', required: true },
            price: { type: 'number' },
            name: { type: 'string', required: true },
            link: { type: 'string', required: true },
            image: { type: ['string', 'null'] },
          },
        },
      },
    },
  },
  async handle(req, res, next) {
    const { accountId } = req.jwtData;
    const webhookData = {
      accountId,
      source: 'magento2',
      reqId: req.id,
      ...(_.pick(req, ['body', 'headers', 'query'])),
    };
    WebhookData.saveData(webhookData);
    try {
      const {
        email, ip, ips, currency, total, firstName, lastName, products, siteUrl, store,
      } = req.body;
      const { host } = url.parse(siteUrl);
      const location = await maxmind.geoIP(ip);
      const event = new MagentoEvent({
        accountId,
        date: Date.now(),
        firstName,
        lastName,
        email,
        host,
        url: siteUrl,
        ip,
        ips,
        store,
        location,
        total: parseInt(total),
        currency,
        products,
      });

      logger.info({ event }, 'saving magento2 event');

      await Promise.all([
        event.save(),
        Feed.saveFeed(accountId, 'Magento 2 Order',
          {
            email, name: firstName, store: host, products, total, currency,
          }),
      ]);
      res.body = { message: 'success' };
      next();
    } catch(err) {
      slack.notifyError(err, `Magento2 webhook error (account: ${accountId})`, { data: req.body });
      logger.error({ err }, 'Magento2 error');
      next(err);
    }
  },
};
