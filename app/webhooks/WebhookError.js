
const mongoose = require('mongoose');

const WebhookError = new mongoose.Schema({
  accountId: { type: mongoose.SchemaTypes.ObjectId, index: true },
  webhookId: { type: String, required: true, index: true },
  error: String,
  reqId: String,
  body: Object,
  query: Object,
  headers: Object,
}, { timestamps: true, collection: 'webhookErrors' });

WebhookError.index({ createdAt: -1 }, { expires: '30d' });

module.exports = mongoose.model('WebhookError', WebhookError);
