const _ = require('lodash');
const url = require('url');
const authTypes = require('../../middleware/authTypes');
const logger = require('../../lib/logger')('magento1');
const slack = require('../../lib/apis/slackNotifier');

const maxmind = require('../../lib/maxmind');
const MagentoEvent = require('../events/models/Magento1Event');
const Feed = require('../account/models/Feed');
const WebhookData = require('./WebhookData');

module.exports = {
  config: {
    methods: ['POST'],
    authType: authTypes.api,
  },
  schema: {
    type: 'object',
    additionalProperties: true,
    properties: {
      firstName: { type: 'string', required: true },
      lastName: { type: 'string' },
      email: { type: 'string', required: true },
      total: { type: 'number' },
      currency: { type: 'string' },
      ip: { type: 'string' },
      siteUrl: { type: 'string' },
      frontUrl: { type: 'string' },
      products: {
        type: 'array',
        required: true,
        minItems: 1,
        items: {
          type: 'object',
          additionalProperties: false,
          properties: {
            id: { type: ['string', 'number'] },
            quantity: { type: 'number' },
            price: { type: 'number' },
            name: { type: 'string', required: true },
            link: { type: 'string', required: true },
            image: { type: ['string', 'null'] },
          },
        },
      },
    },
  },
  async handle(req, res, next) {
    const { accountId } = req.jwtData;
    const webhookData = {
      accountId,
      source: 'magento1',
      reqId: req.id,
      ...(_.pick(req, ['body', 'headers', 'query'])),
    };
    WebhookData.saveData(webhookData);
    try {
      const {
        email, ip, currency, total, firstName, lastName, products, siteUrl, frontUrl,
      } = req.body;
      const { host } = url.parse(siteUrl);
      const location = await maxmind.geoIP(ip);
      const event = new MagentoEvent({
        accountId,
        date: Date.now(),
        firstName,
        lastName,
        email,
        host,
        frontUrl,
        url: siteUrl,
        ip,
        location,
        ...(total && { total }),
        currency,
        products,
      });

      logger.info({ event }, 'saving magento1 event');

      await Promise.all([
        event.save(),
        Feed.saveFeed(accountId, 'Magento 1 Order', {
          email, name: firstName, store: host, products, total, currency,
        }),
      ]);
      res.body = { message: 'success' };
      next();
    } catch(err) {
      slack.notifyError(err, `Magento1 webhook error (account: ${accountId})`, { data: req.body });
      logger.error({ err }, 'Magento1 error');
      next(err);
    }
  },
};
