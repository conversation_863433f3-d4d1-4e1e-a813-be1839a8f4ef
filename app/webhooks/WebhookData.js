const mongoose = require('mongoose');

/**
* @class WebhookData
*/
const WebhookData = new mongoose.Schema({
  accountId: { type: mongoose.SchemaTypes.ObjectId, index: true },
  webhookId: { type: String, index: true },
  source: { type: String, default: 'webhook' },
  reqId: String,
  body: Object,
  query: Object,
  headers: Object,
}, { timestamps: true, collection: 'webhookData' });

WebhookData.index({ createdAt: -1 }, { expires: '30d' });

/**
* @memberOf WebhookData#
*/
WebhookData.statics.saveData = function saveData(data) {
  return (new this(data)).save().catch(() => {});
};

/**
* @type {WebhookData}
*/
module.exports = mongoose.model('WebhookData', WebhookData);
