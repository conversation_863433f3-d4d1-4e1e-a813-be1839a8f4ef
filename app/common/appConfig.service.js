const _ = require('lodash');
const AppConfig = require('./AppConfig');
const cacheService = require('../common/cache.service');

module.exports = {
  getValue,
};

async function getValue({ key, defaultValue, cache = 60 }) {
  if(!AppConfig.schema.path(key)) {
    return defaultValue;
  }
  if(cacheService.memhas(key)) {
    return cacheService.memget(key);
  }
  const appConfig = await AppConfig.findOne({});
  const retval = _.get(appConfig, key, defaultValue);
  cacheService.memset(key, retval, { seconds: cache });
  return retval;
}
