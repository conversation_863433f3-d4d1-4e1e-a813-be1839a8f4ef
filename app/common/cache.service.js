const _ = require('lodash');
const LRUCache = require('lru-cache');

const memCache = new LRUCache({
  max: 1000,
  ttl: 60 * 1000,
  allowStale: false,
});

module.exports = {
  memset,
  memget,
  memhas,
  memDelete,
};

function memset(key, val, { seconds = 60 }) {
  let k = key;
  if(_.isObject(k)) {
    k = JSON.stringify(k);
  }
  return memCache.set(k, val, { ttl: seconds * 1000 });
}

function memget(key) {
  let k = key;
  if(_.isObject(k)) {
    k = JSON.stringify(k);
  }
  return memCache.get(k);
}

function memhas(key) {
  return memCache.has(key);
}

function memDelete(key) {
  let k = key;
  if(_.isObject(k)) {
    k = JSON.stringify(k);
  }
  return memCache.delete(k);
}
