const _ = require('lodash');
const slack = require('../../lib/apis/slackNotifier');
const config = require('../../config');
const redis = require('./redis.service').getClient();

module.exports = {
  notify,
  notifyError,

  notifyWpUninstall,
  notifyWpInstall,
  notifyShopify,
  notifyWix,
  notifyBigCommerce,
  stripeChannel,
  stripeEvent,
  ipnChannel,
  notifyPayPalIPN,
  paypalWebhook,
  paypal,
  serverMessage,
  thinkific,
  chargeFailed,
};

function notifyError(err, message, data) {
  return redis.getAsync(`${message}:slack`).then((sent) => {
    if(sent) {
      return;
    }
    slack.notifyError(err, message, data);
    redis.setexAsync(`${message}:slack`, 60, 1);
  });
}

function notify(message, data, webhook) {
  slack.notify(message, data, { webhook });
}

function notifyWpUninstall(site, email) {
  let message = `wordpress plugin uninstalled ${site}`;
  if(email) {
    message += ` (${email})`;
  }
  return notify(message, null, config.slack.wp);
}

function notifyWpInstall(site, woocommerce, email) {
  let message = `wordpress plugin installed in ${site} by ${email}`;
  if(woocommerce) message += ' (woocommerce)';
  return notify(message, null, config.slack.wp);
}

function stripeChannel(message, data) {
  notify(message, data, config.slack.stripe);
}

function stripeEvent({ account, event }) {
  const { type, data: { object: eventData } } = event;
  const comps = [account && account.email, type];
  let plan = _.get(eventData, 'price.nickname') || _.get(eventData, 'plan.nickname');
  if(!plan) {
    const lineItem = _.get(eventData, 'lines.data', _.get(eventData, 'items.data', [])).find(line => line.amount > 0);
    plan = _.get(lineItem, 'price.nickname') || _.get(lineItem, 'plan.nickname');
  }
  if(plan) {
    comps.push(plan);
  }
  const cents = _.get(eventData, 'amount_paid')
    || _.get(eventData, 'amount_captured')
    || _.get(eventData, 'plan.amount');
  if(cents) {
    comps.push(cents / 100);
    const currency = _.get(eventData, 'currency') || _.get(eventData, 'plan.currency');
    if(currency) {
      comps.push(currency.toUpperCase());
    }
  }
  notify(comps.join(' '), eventData, config.slack.stripe);
}

function ipnChannel(message, data) {
  notify(message, data, config.slack.ipns);
}

function paypal(msg, data) {
  notify(msg, data, config.slack.paypal);
}

function notifyPayPalIPN(data, { txn }) {
  paypal(`Got Paypal IPN ${txn}`, data);
}

function paypalWebhook(data) {
  const { event_type: event } = data;
  paypal(`Got PayPal webhook ${event}`, data);
}

function serverMessage(msg, data) {
  notify(msg, data, config.slack.serverMessages);
}

function notifyWix(message, data) {
  notify(message, data, config.slack.wix);
}

function notifyShopify(message, data) {
  notify(message, data, config.slack.shopify);
}

function notifyBigCommerce(message, data) {
  notify(message, data, config.slack.bigcommerce);
}

function thinkific(message, data) {
  notify(message, data, config.slack.thinkific);
}

function chargeFailed({ account, ipn, source }) {
  const { email = 'N/A' } = account || {};
  const {
    email: ipnEmail, transactionType, contractName, failureReason,
  } = ipn;
  let identifier = email || ipnEmail;
  if(account && account.kind === 'SubAccount') {
    identifier += ` (SubAccount - ${account.name})`;
  }
  let transaction = transactionType;
  if(ipn.originalTransactionType) {
    transaction += ` was ${ipn.originalTransactionType}`;
  }
  const message = `${source} - ${identifier}: ${contractName} (${transaction})`;
  notify(message, { failureReason }, config.slack.chargeFailed);
}
