const config = require('../../config');
const sendgrid = require('../../lib/apis/sendgrid');

const dateUtils = require('../../lib/utils/dateUtils');
const ErrorFactory = require('../../lib/errors/ErrorFactory');

module.exports = {
  sendAdminEmail,
  sendTemplateEmail,
  sendReviewsCronSummary,
  sendPayPalIPNNotice,
  sendWhatWeDidWrong,
  sendReviewRequest,
  sendUninstall,
  sendWixInstallEmail,
};

const FROM_INFO = {
  email: '<EMAIL>',
  name: 'Team ProveSource',
};
const FROM_HELP = {
  email: '<EMAIL>',
  name: 'ProveSource Support',
};
const BCC = '<EMAIL>';
const FOUNDERS = [
  { email: '<EMAIL>', name: '<PERSON><PERSON>', shortName: '<PERSON><PERSON>' },
  { email: '<EMAIL>', name: '<PERSON><PERSON>', shortName: '<PERSON><PERSON>' },
];
const JOSH = { email: '<EMAIL>', name: '<PERSON>' };

function sendAdminEmail({
  to, subject, html, category = 'admin',
}) {
  return sendgrid.send(config.sendgrid.apiKey, {
    to, from: FROM_INFO, replyTo: FROM_INFO, subject, html, category,
  });
}

function sendTemplateEmail({
  to, from = null, subject = null, preheader = null, templateId, data = {}, category, bcc = true,
  extraOpts = {},
}) {
  return sendgrid.send(config.sendgrid.apiKey, {
    to,
    from: from || FROM_INFO,
    ...(bcc && { bcc: BCC }),
    replyTo: from || FROM_INFO,
    templateId,
    category,
    dynamic_template_data: {
      ...(subject && { subject }),
      ...(preheader && { preheader }),
      ...data,
    },
    ...extraOpts,
  }).catch((err) => {
    // eslint-disable-next-line prefer-rest-params
    throw ErrorFactory('failed sending sendgrid template email', 500, { arguments, err });
  });
}

function sendReviewsCronSummary({ results, platform }) {
  let subject = `Reviews Cron Summary ${dateUtils.getISODate(new Date())}`;
  if(platform) {
    subject = `${platform} ${subject}`;
  }
  return sendAdminEmail({
    to: '<EMAIL>',
    subject,
    html: `Cron results:<br><pre>${JSON.stringify(results, null, 2)}</pre>`,
    category: 'admin',
  });
}

function sendPayPalIPNNotice(data, { txn }) {
  return sendAdminEmail({
    to: '<EMAIL>',
    subject: `PayPal IPN Handled ${txn}`,
    html: `PayPal IPN data:<br><pre>${JSON.stringify(data, null, 2)}</pre>`,
    category: 'admin',
  });
}

function sendWhatWeDidWrong({ to, delaySeconds = 0, accountName = null }) {
  const from = FOUNDERS[Math.floor(Math.random() * FOUNDERS.length)];
  return sendTemplateEmail({
    to,
    from,
    templateId: 'd-cfde90ea81fe435989c9082a7196b19c',
    category: 'whats-wrong',
    extraOpts: {
      send_at: Math.floor(Date.now() / 1000) + delaySeconds,
    },
    data: {
      accountName,
      shortName: from.shortName,
    },
  });
}

function sendReviewRequest({ to, platform, reviewLink }) {
  return sendTemplateEmail({
    to,
    from: JOSH,
    templateId: 'd-4bce4c9114714b0c8909599c7203caa7',
    category: 'review-request',
    data: {
      platform,
      reviewLink,
    },
  });
}

function sendUninstall({
  to, platform, domain, category,
}) {
  return sendTemplateEmail({
    to,
    from: JOSH,
    templateId: 'd-1d5b477cef664fbab7d0378d1d8412c3',
    data: {
      platform,
      domain,
    },
    category,
  });
}

function sendWixInstallEmail({
  to, link, siteName, siteUrl,
}) {
  return sendTemplateEmail({
    to,
    bcc: BCC,
    from: FROM_HELP,
    templateId: 'd-92182343aa3248dbbc26f497da54c14d',
    data: {
      link,
      siteName,
      siteUrl,
    },
    category: 'wix-install-email',
  });
}
