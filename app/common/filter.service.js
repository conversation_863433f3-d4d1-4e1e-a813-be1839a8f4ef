const _ = require('lodash');

module.exports = {
  shouldIgnoreEvent,
};

function shouldIgnoreEvent({
  configuration, email, name, text,
}) {
  const namesToFilter = _.get(configuration, 'filters.names', []).map(e => e.toLowerCase());
  const emailsToFilter = _.get(configuration, 'filters.emails', []).map(e => e.toLowerCase());
  const phrasesToFilter = _.get(configuration, 'filters.phrases', []).map(e => e.toLowerCase());

  const emailLower = email ? email.toLowerCase() : null;
  const nameLower = name ? name.toLowerCase() : null;
  const textLower = text ? text.toLowerCase() : null;

  if(emailLower && _.some(emailsToFilter, e => emailLower.includes(e))) {
    return true;
  }
  if(nameLower && _.some(namesToFilter, n => nameLower.includes(n))) {
    return true;
  }
  if(text && _.some(phrasesToFilter, n => textLower.includes(n))) {
    return true;
  }
  return false;
}
