const _ = require('lodash');
const mailerlite = require('../../lib/apis/mailerlite');
const { isEmail } = require('../../lib/utils/emailUtils');
const config = require('../../config/index');
const notifierService = require('./notifier.service');

module.exports = {
  createSubscription,
  updateSubscription,
  unsubscribe,
  batchUpdate,
};

function createSubscription(email, {
  group = config.mailerlite.groups.customers,
  fields = null,
} = {}) {
  if(!isEmail(email)) return null;

  return mailerlite.subscribe(email, group, fields).catch((err) => {
    const text = _.get(err, 'response.text', '');
    if(text.includes('unsubscr') || text.includes('bounce')) {
      return;
    }
    notifierService.notifyError(err, 'mailerlite: failed to create subscription', { email, group, fields });
  });
}

function updateSubscription(email, fields, type) {
  if(!isEmail(email) || !fields) return null;

  return mailerlite.updateSubscriber(email, fields, type).catch((err) => {
    const text = _.get(err, 'response.text', '');
    if(err.status === 404 || text.includes('unsub') || text.includes('bounce')) {
      return;
    }
    notifierService.notifyError(err, 'mailerlite: failed to update subscription', { email, fields });
  });
}

function unsubscribe(email) {
  return updateSubscription(email, {}, 'unsubscribed');
}

/**
 * @param {array} updates - array of objects { email, fields }, max 50
 */
function batchUpdate(updates) {
  const requests = [];
  const length = Math.min(updates.length, 50);
  for(let i = 0; i < length; i += 1) {
    const update = updates[i];
    if(update.email && update.fields) {
      requests.push({
        method: 'PUT',
        path: `/api/v2/subscribers/${update.email}`,
        body: { fields: update.fields },
      });
    }
  }
  return mailerlite.batch(requests);
}
