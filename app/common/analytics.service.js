const Mixpanel = require('mixpanel');
const config = require('../../config');


const mixpanelClient = (config.isProduction() && config.mixpanel.token)
  ? Mixpanel.init(config.mixpanel.token)
  : null;

module.exports = {
  trackPurchasedEvent,
  trackSignupEvent,
  trackInstalledCode,
  trackOnboardingComplete,
  trackNotificationCreated,
  trackPlanLimitReached,
  trackInstalledApp,
};

function track(event, id, data) {
  if(!mixpanelClient) {
    return null;
  }
  if(!id) {
    throw new Error('Id must be provided');
  }
  return mixpanelClient.track(event, { distinct_id: id, ...data });
}


function trackPurchasedEvent(id, data = {}) {
  return track('Subscription Purchased (s2s)', id, data);
}

function trackSignupEvent(id, data = {}) {
  // add timeout to allow enough time for client side event to be captured
  // sending this before the client does "identify" post login does not merge users
  setTimeout(() => {
    track('Signup (s2s)', id, data);
  }, 10 * 1000);
}

function trackInstalledCode(id, data = {}) {
  return track('Installed Code (s2s)', id, data);
}

function trackOnboardingComplete(id, data = {}) {
  return track('Onboarding Complete (s2s)', id, data);
}

function trackNotificationCreated(id, data = {}) {
  return track('Notification Created (s2s)', id, data);
}

function trackPlanLimitReached(id, data = {}) {
  return track('Plan Limit Reached (s2s)', id, data);
}

function trackInstalledApp(id, { source, ...data }) {
  return track(`${source} Installed App (s2s)`, id, data);
}
