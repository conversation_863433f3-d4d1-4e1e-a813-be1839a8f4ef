const mongoose = require('mongoose');

const FormUrlSuggestions = new mongoose.Schema({
  accountId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'Account',
  },
  url: String,
  hits: {
    type: Number,
    default: 0,
  },
  lastHit: {
    type: Date,
    default: Date.now(),
  },
}, { timestamps: true, collection: 'formUrlSuggestions' });

FormUrlSuggestions.index({ accountId: 1, lastHit: -1 });
FormUrlSuggestions.index({
  accountId: 1, url: 1, lastHit: -1, hits: -1,
});
FormUrlSuggestions.index({ accountId: 1, hits: -1 });

module.exports = mongoose.model('FormUrlSuggestion', FormUrlSuggestions);
