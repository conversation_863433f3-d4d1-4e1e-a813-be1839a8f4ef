const mongoose = require('mongoose');

const UrlSuggestions = new mongoose.Schema({
  accountId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'Account',
  },
  url: String,
  hits: {
    type: Number,
    default: 0,
  },
  lastHit: {
    type: Date,
    default: Date.now(),
  },
}, { timestamps: true, collection: 'urlSuggestions' });

UrlSuggestions.index({ accountId: 1, lastHit: -1 });
UrlSuggestions.index({
  accountId: 1, url: 1, lastHit: -1, hits: -1,
});
UrlSuggestions.index({ accountId: 1, hits: -1 });

module.exports = mongoose.model('UrlSuggestion', UrlSuggestions);
