const _ = require('lodash');
const authTypes = require('../../../middleware/authTypes');
const { TYPES, TYPES_ARRAY } = require('./constants');
const UrlSuggestion = require('./models/UrlSuggestion');
const FormUrlSuggestion = require('./models/FormUrlSuggestion');
const { dateUtils } = require('../../../lib/utils');
const notifierService = require('../notifier.service');

module.exports = {
  config: {
    methods: ['GET'],
    authType: authTypes.console,
  },
  schema: {
    GET: {
      type: 'object',
      properties: {
        type: { type: 'string', required: true, enum: TYPES_ARRAY },
        search: { type: 'string', required: true },
      },
    },
  },
  async handle(req, res, next) {
    const { accountId } = req.locals;
    const { type, search } = req.query;

    let model = UrlSuggestion;
    if(type === TYPES.FORM) {
      model = FormUrlSuggestion;
    }

    const mongoQuery = {
      accountId,
      lastHit: { $gte: Date.now() - dateUtils.MILLISECONDS_IN_DAY * 30 },
    };
    if(search && search !== '') {
      mongoQuery.url = { $regex: _.escapeRegExp(search), $options: 'i' };
    }
    const suggestions = await model
      .find(mongoQuery)
      .select('-_id url hits lastHit')
      .sort({ lastHit: -1, hits: -1 })
      .limit(10)
      .maxTime(30 * 1000)
      .lean()
      .catch((err) => {
        notifierService.notifyError(err, 'failed to get url suggestions', { accountId, type, search });
      });

    res.body = { suggestions };
    return next();
  },
};
