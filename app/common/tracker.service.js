const config = require('../../config');
const facebook = require('../../lib/apis/facebook');
const notifier = require('./notifier.service');

module.exports = {
  signup,
  purchase,
};

const FB_PIXEL = '314801075616378';

async function signup({
  id, email, ua, ip,
}) {
  const params = {
    pixelId: FB_PIXEL,
    token: config.facebook.accessToken,
    sourceUrl: config.consoleUrl,
    id,
    email,
    ip,
    ua,
  };
  return Promise.all([
    facebook.sendEvent({ ...params, event: facebook.EVENTS.lead }),
    facebook.sendEvent({ ...params, event: facebook.EVENTS.registration }),
  ]).catch((err) => {
    notifier.notifyError(err, 'failed to track signup on fb', { email, ip, ua });
  });
}

async function purchase({
  id, email, ua, ip, value,
}) {
  const params = {
    pixelId: FB_PIXEL,
    token: config.facebook.accessToken,
    id,
    sourceUrl: config.consoleUrl,
    email,
    ip,
    ua,
    event: facebook.EVENTS.purchase,
    customData: { value, currency: 'USD' },
  };
  return facebook.sendEvent(params).catch((err) => {
    notifier.notifyError(err, 'failed to track purchase on fb', { email, ip, ua });
  });
}
