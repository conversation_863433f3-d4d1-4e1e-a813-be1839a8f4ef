/* eslint-disable no-undef */
// mongo shell

db.ipns.find().forEach((doc) => {
  const mId = doc.data.merchantTransactionId;
  if(mId && !doc.accountId && mId.length === 24) {
    doc.accountId = ObjectId(mId);
  }
  doc.transactionType = doc.data.transactionType;
  doc.type = doc.transactionType;
  doc.transactionId = doc.data.referenceNumber;
  doc.hash = `${doc.transactionType}-${doc.transactionId}-${mId || null}`;
  doc.email = doc.data.email;
  if(doc.data.invoiceAmount) {
    doc.amount = parseFloat(doc.data.invoiceAmount.replace(/[^0-9.-]+/g, ''));
  } else {
    doc.amount = 0;
  }
  doc.aff = null;
  const { accountId } = doc;
  if(accountId) {
    const account = db.accounts.findOne({ _id: accountId }, { affiliate: 1 });
    if(account && account.affiliate && account.affiliate.referrer) {
      doc.aff = account.affiliate.referrer;
    }
  }
  db.ipns.save(doc);
});
