// const ShopifyEvent = require('../../app/events/models/ShopifyEvent');
// const Stream = require('../../app/notifications/models/Notification');
// const { SHOPIFY_EVENT_TYPES } = require('../../app/constants');

/**
 * Run a .js file directly on the mongo server for max performance
 * mongo < feed2-migrate.js
 */

// (async () => {
//   await require('../../lib/mongooseLoader').load('mongodb://localhost:8080/proofsrc-prod');
//   console.log(await ShopifyEvent.updateMany({ email: /@from-shopify\.com$/i }, { $set: { type: SHOPIFY_EVENT_TYPES.POS } }));
//   console.log(await ShopifyEvent.updateMany({ email: { $not: /@from-shopify\.com$/i } }, { $set: { type: SHOPIFY_EVENT_TYPES.ONLINE } }));
//   console.log(await Stream.updateMany({ 'settings.platform': 'shopify' }, { $set: { 'settings.eventType': 'all' } }));
//   process.exit(0);
// })();

// set type=pos
db.getCollection('shopifyEvents').updateMany({ email: /@from-shopify\.com/i }, { $set: { type: 'pos' } });

// set type=online
const batchSize = 5000;
let updatedDocs = 0;
let lastProcessedId = db.shopifyEvents.find().sort({ _id: -1 }).limit(1).toArray()[0]._id;
while(true) {
  print(new Date().toISOString(), 'getting docs...');
  const docsToUpdate = db.shopifyEvents.find({ _id: { $lte: lastProcessedId }, type: { $exists: false } })
    .sort({ _id: -1 })
    .limit(batchSize)
    .toArray();
  if(docsToUpdate.length === 0) {
    print(new Date().toISOString(), 'done');
    quit(0);
  }
  const res = db.shopifyEvents.updateMany({ _id: { $in: docsToUpdate.map(doc => doc._id) } }, {
    $set: { type: 'online' },
  });
  print(new Date().toISOString(), 'update result', JSON.stringify(res));
  updatedDocs += docsToUpdate.length;
  lastProcessedId = docsToUpdate[docsToUpdate.length - 1]._id;
  print(new Date().toISOString(), `updated ${docsToUpdate.length} docs event type, total ${updatedDocs}`);
  print('last processed id:', lastProcessedId);
  sleep(100);
}

// update notifications eventType=all
db.getCollection('notifications').updateMany({ 'settings.platform': 'shopify' }, { $set: { 'settings.eventType': 'all' } });
