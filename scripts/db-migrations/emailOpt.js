const config = require('../../config');
const mongooseLoader = require('../../lib/mongooseLoader');
const Account = require('../../app/account/models/Account');

if(require.main === module) {
  migrate();
} else {
  module.exports = migrate;
}

async function migrate() {
  await mongooseLoader.load(config.mongodb.url);
  const accounts = await Account.find({}, 'email _id subscription configuration');
  const summaryActive = [];
  const summaryInactive = [];
  // eslint-disable-next-line no-await-in-loop
  for(let i = 0; i < accounts.length; i += 1) {
    const account = accounts[i];
    const active = account.isSubscriptionActive();
    if(!active) {
      summaryActive.push(account.id);
    } else {
      summaryInactive.push(account.id);
    }
    console.log(`(${i + 1}/${accounts.length}) updating ${account.email}, subscription: ${active}`);
  }
  const [summaryOn, summaryOff] = await Promise.all([
    Account.updateMany({ _id: { $in: summaryActive } }, {
      'configuration.emailOpt.summary': true,
      'configuration.emailOpt.errors': true,
    }),
    Account.updateMany({ _id: { $in: summaryInactive } }, {
      'configuration.emailOpt.summary': false,
      'configuration.emailOpt.errors': true,
    }),
  ]);
  console.log('results', summaryOn, summaryOff);
  console.log('done');
  process.exit(0);
}
