/**
 * Run a .js file directly on the mongo server for max performance
 * mongo < feed2-migrate.js
 */
const batchSize = 5000;
let insertedDocs = 0;
const lastFeed2 = db.feed2.find({})
  .sort({ _id: -1 })
  .limit(1)
  .toArray();
let lastProcessedId = lastFeed2[0]._id;
print(`continue from ${lastProcessedId}`);

let docsToUpdate = null;
let bulkOpsFeed = null;
const processStart = Date.now();
while(true) {
  let start = Date.now();
  docsToUpdate = db.feed.find({ _id: { $gt: lastProcessedId } })
    .limit(batchSize)
    .sort({ _id: 1 })
    .toArray();
  let end = Date.now();
  print(`took ${((end - start) / 1000).toFixed(3)} seconds to find feed items`);
  if(docsToUpdate.length === 0) {
    print('No feed events found');
    quit(0);
  }
  start = Date.now();
  bulkOpsFeed = docsToUpdate.map((doc) => {
    let searchHash = doc.message;
    if(doc.data) {
      if(doc.data.email) {
        searchHash += `|${doc.data.email}`;
      }
      const country = doc.data.location ? doc.data.location.country : doc.data.country;
      if(country) {
        searchHash += `|${country}`;
      }
      const city = doc.data.location ? doc.data.location.city : doc.data.city;
      if(city) {
        searchHash += `|${city}`;
      }
    }
    const newDoc = Object.assign({ searchHash }, doc);
    return {
      insertOne: {
        document: newDoc,
      },
    };
  });
  end = Date.now();
  print(`took ${((end - start) / 1000).toFixed(3)} seconds to generate bulk ops`);

  if(bulkOpsFeed.length > 0) {
    start = Date.now();
    db.feed2.bulkWrite(bulkOpsFeed);
    end = Date.now();
    print(`took ${((end - start) / 1000).toFixed(3)} seconds to bulk insert feed items`);

    insertedDocs += bulkOpsFeed.length;
    print(`Inserted ${insertedDocs} documents into Feed2, total ${((end - processStart) / 1000).toFixed(3)} seconds`);
    lastProcessedId = docsToUpdate[docsToUpdate.length - 1]._id;
    bulkOpsFeed = null;
    docsToUpdate = null;
    sleep(100);
  }
}

/**
 * This is too slow with the network overhead
 */

// const _ = require('lodash');
// const mongoose = require('mongoose');
// const config = require('../../config');
// const mongooseLoader = require('../../lib/mongooseLoader');
// const Feed = require('../../app/account/models/Feed');
// const Feed2 = require('../../app/account/models/Feed2');
//
// (async () => {
//   await mongooseLoader.load('mongodb://localhost:8080/proofsrc-prod');
//   await createSearchHash();
//   console.log('done');
//   process.exit(0);
// })();
//
// async function getLastProcessedId() {
//   const lastDoc = await Feed2.findOne({}).sort({ _id: -1 }).select('_id').lean();
//   return lastDoc ? lastDoc._id : null;
// }
//
// async function createSearchHash() {
//   const batchSize = 50000;
//   let insertedDocs = 0;
//
//   try {
//     let lastProcessedId = await getLastProcessedId();
//     while(true) {
//       const query = lastProcessedId ? { _id: { $gt: lastProcessedId } } : {};
//       const docsToUpdate = await Feed.find(query).limit(batchSize).sort({ _id: 1 });
//
//       if(docsToUpdate.length === 0) {
//         console.log('No feed events found');
//         break; // No more documents to update
//       }
//
//       const bulkOpsFeed = docsToUpdate
//         .filter(doc => !doc.searchHash)
//         .map((doc) => {
//           let searchHash = doc.message;
//           if(doc.data) {
//             if(doc.data.email) {
//               searchHash += `|${doc.data.email}`;
//             }
//             const country = _.get(doc.data, 'location.country', doc.data.country);
//             if(country) {
//               searchHash += `|${country}`;
//             }
//             const city = _.get(doc.data, 'location.city', doc.data.city);
//             if(city) {
//               searchHash += `|${city}`;
//             }
//           }
//           return {
//             insertOne: {
//               document: {
//                 ...doc.toObject(),
//                 searchHash,
//               },
//             },
//           };
//         });
//
//       if(bulkOpsFeed.length > 0) {
//         await Feed2.bulkWrite(bulkOpsFeed);
//         insertedDocs += bulkOpsFeed.length;
//         console.log(`Inserted ${insertedDocs} documents into Feed2`);
//         lastProcessedId = docsToUpdate[docsToUpdate.length - 1]._id;
//       }
//     }
//
//     console.log('Finished updating and inserting documents.');
//   } catch(err) {
//     console.error('Error updating and inserting documents:', err);
//   }
// };
