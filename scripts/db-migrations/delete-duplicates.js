// 1. replace "wooEvents" with collection of choice
// 2. replace _id with criteria for finding duplicates
const collection = 'wooEvents';
const _id = {
  accountId: '$accountId',
  host: '$host',
  orderId: '$orderId',
};
let bulk = db[collection].initializeOrderedBulkOp();
let count = 0;
db[collection].aggregate([{
  $group: {
    _id,
    ids: { $push: '$_id' },
    count: { $sum: 1 },
  },
}, {
  $match: {
    count: { $gt: 1 },
  },
}], { allowDiskUse: true }).forEach((doc) => {
  doc.ids.shift(); // remove first match
  bulk.find({ _id: { $in: doc.ids } }).remove(); // removes all $in list
  count++;

  // Execute 1 in 1000 and re-init
  if(count % 1000 == 0) {
    bulk.execute();
    bulk = db[collection].initializeOrderedBulkOp();
  }
});

if(count % 1000 != 0) bulk.execute();
