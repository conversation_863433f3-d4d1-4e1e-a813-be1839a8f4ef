const config = require('../../config');
const mongooseLoader = require('../../lib/mongooseLoader');
const Account = require('../../app/account/models/Account');


(async () => {
  await mongooseLoader.load('mongodb://localhost:8080/proofsrc-prod');
  await updateInvoiceSent();
  console.log('done');
  process.exit(0);
})();

async function updateInvoiceSent() {
  return Account.updateMany(
    { 'configuration.sendInvoices': true },
    { $set: { 'stats.sentEmails.invoice': new Date() } },
  );
}
