/* eslint-disable no-await-in-loop,no-console */
const _ = require('lodash');
const config = require('../../config/prodConfig');
const dbLoader = require('../../lib/mongooseLoader');
const wix = require('../../lib/apis/wix');
const wixService = require('../../app/wix/wix.service');
const Account = require('../../app/account/models/Account');
const IPN = require('../../app/billing/bluesnap/IPNModel');
const { WixSite } = require('../../app/wix/models');
const logger = require('../../lib/logger').getLogger('wixOps');

(async function main() {
  // eslint-disable-next-line global-require
  wix.setAuth(config.wix.appId, config.wix.secret, config.wix.publicKey);
  await dbLoader.load('mongodb://localhost:8080/proofsrc-prod');

  // df465b63-0fb2-4e24-b587-1bdddcc734ca (empty wix site), got some Java errors from wix
  // a7239445-9fd6-43c4-b42a-eaad36709bd0 (no wix site)
  const instanceId = '39cac34d-6307-49b1-ae43-75c006836246';
  const scripts = await getScripts(instanceId);
  // const accessToken = await getAccessToken(instanceId);
  // await getSiteData(instanceId);
  // console.log(await importLastEvents({ instanceId, accessToken }));

  // const accessToken = await wix.createAccessToken(instanceId);
  // await checkDuplicates();
  // await updateBillingExpiration(instanceId);
  // await triggerPlanPurchased(instanceId, {
  //   operationTimeStamp: '2024-08-23T21:13:27.100Z',
  //   vendorProductId: 'starter',
  //   cycle: 'MONTHLY',
  //   expiresOn: '2024-09-23T21:13:27Z',
  //   invoiceId: '**********',
  // });
  console.log('done');
  process.exit(0);
}());

async function getScripts(instanceId) {
  const { accessToken } = await wixService.refreshAccessToken(null, instanceId);
  const scripts = await wix.getScripts(accessToken).then(res => res.body);
  const apiKey = _.get(scripts, 'properties.parameters.apiKey').split('.')[1];
  const scriptAccountId = Buffer.from(apiKey, 'base64').toString('utf8');
  console.log('scripts', scripts, scriptAccountId);
  return scripts;
}

async function getSiteData(instanceId) {
  const { accessToken } = await wixService.refreshAccessToken(null, instanceId);
  // const account = await Account.findOne({ 'wix.instanceId': instanceId });
  // const site = account.getWixSite(instanceId);
  // let { accessToken } = site;
  // const { refreshToken } = site;
  // const tokens = await wix.refreshAccessToken(refreshToken, accessToken);
  // accessToken = tokens.access_token;
  const [appInstance, siteProperties, purchaseHistory] = await Promise.all([
    wix.getAppInstance(accessToken),
    wix.getSiteProperties(accessToken),
    wix.getPurchaseHistory(accessToken),
  ]);
  console.log('App Instance', appInstance);
  console.log('*'.repeat(20));
  console.log('Site Properties', siteProperties);
  console.log('*'.repeat(20));
  console.log('Purchase History', purchaseHistory);
}

async function getSubscriptionOrders(instanceId) {
  const account = await Account.findOne({ 'wix.instanceId': instanceId });
  const site = account.getWixSite(instanceId);
  const tokens = await wixService.refreshAccessToken(site, instanceId);
  const accessToken = tokens.access_token;
  const pricingPlanOrders = await wix.getPricingPlanOrders(accessToken);
  const contacts = await Promise.all(
    pricingPlanOrders.map(order => wix.getContact(accessToken, order.buyer.contactId)),
  );
  return { orders: pricingPlanOrders, contacts };
}

async function updateBillingExpiration(instanceId) {
  const account = await Account.findOne({ 'wix.instanceId': instanceId });
  const { updated, site } = await wixService.updateBillingExpirationDate(account);
  console.log('updated billing expiration', updated, site);
}

async function checkDuplicates() {
  const results = { wix: 0, bluesnap: 0, both: 0 };
  const accounts = await Account.find({ 'wix.0': { $exists: true }, subscription: { $ne: null } });
  await Promise.all(accounts.map(async (account) => {
    try {
      const site = account.wix[0];
      const { accessToken } = await wixService.refreshAccessToken(site, site.instanceId);
      const [ipn, wixBilling] = await Promise.all([
        IPN.find({ accountId: account.id }).sort({ _id: -1 }).limit(1).then(res => res[0]),
        getWixBilling(accessToken),
      ]);
      if(ipn && wixBilling) {
        results.both += 1;
        console.log(`${account.email} has IPN and wix billing`);
        console.log(_.omit(ipn.toObject(), ['data']));
        console.log(wixBilling);
        console.log('-'.repeat(20));
      } else if(ipn) {
        results.bluesnap += 1;
        console.log(`${account.email} paying via bluesnap`);
      } else if(wixBilling) {
        results.wix += 1;
        console.log(`${account.email} paying via wix`);
      }
    } catch(err) {
      console.error(`operation failed ${account.email}: ${err.message}`);
    }
  }));
  console.log(results);
}

async function getWixBilling(accessToken) {
  const appInstance = await wix.getAppInstance(accessToken);
  return appInstance && appInstance.instance && appInstance.instance.billing;
}

async function triggerPlanPurchased(instanceId, data) {
  await wixService.handleWebhook({ instanceId, data, eventType: 'PaidPlanPurchased' });
}

async function importLastEvents({ instanceId, accessToken }) {
  let account = await Account.findOne({ 'wix.instanceId': instanceId });
  const accountId = account.id;
  account = await wixService.handleSiteUpdate({ instanceId }, account);
  const site = account.getWixSite(instanceId);
  const apps = site.apps || [];
  const hasForms = apps.some(app => app.includes('form'));
  const hasPlans = apps.some(app => app.includes('plan'));
  const hasEcommerce = apps.some(app => /(store|order|booking|reservation|restaurant)/i.test(app));
  const [orders, subscriptions, forms] = await Promise.all([
    ...(hasEcommerce ? [wixService.importOrders({ accountId, accessToken, instanceId })] : []),
    ...(hasPlans ? [wixService.importPricingPlanOrders({ accountId, accessToken, instanceId })] : []),
    ...(hasForms ? [wixService.importFormSubmissions({ accountId, accessToken, instanceId })] : []),
  ]);
  return { orders, subscriptions, forms };
}

async function connectSite(account, instanceId) {
  const site = await WixSite.findOne({ instanceId });
  if(!site) {
    throw new Error('site not found');
  }
  const integrateRes = await wixService.integrateSite(account, site, { state: 'wix-ops' });
  console.log('integrated site', integrateRes);
}

// region migrations

async function fixInstalledDate() {
  const results = { accounts: 0, updated: 0, skipped: 0 };
  const accounts = await Account.find({
    wix: {
      $elemMatch: { installedDate: null },
    },
  }).select('wix installed');
  results.accounts = accounts.length;
  logger.info({ accounts: accounts.length }, 'updating installed date for accounts');
  await Promise.all(accounts.map(async (account) => {
    account.wix.forEach((site) => {
      if(!site.installedDate) {
        site.installedDate = account.installed;
        if(site.uninstalledDate) {
          site.uninstalledDate = null;
        }
        account.markModified('wix');
      }
    });
    if(account.isModified('wix')) {
      await account.save();
      results.updated += 1;
    } else {
      results.skipped += 1;
    }
  }));
  logger.info({ results }, 'finished updating installed date');
}

async function addWixSiteId() {
  const results = { migrated: 0, skipped: 0, error: 0 };
  const accounts = await Account.find({
    'wix.instanceId': 'b336e04d-e1e8-4f42-8e4b-fe03b5adb0ef',
    // wix: {
    //   $elemMatch: {
    //     uninstalled: null,
    //     siteId: null,
    //   },
    // },
  }).select('wix email');
  logger.info({ accounts: accounts.length }, 'found accounts to process');
  let promises = [];
  for(let i = 0; i < accounts.length; i += 1) {
    const account = accounts[i];
    const { email } = account;
    account.wix.forEach((site) => {
      const { instanceId, siteId } = site;
      if(siteId) {
        results.skipped += 1;
        return;
      }
      promises.push(wixService.handleSiteUpdate({ instanceId }, account).then((res) => {
        results.migrated += 1;
      }).catch((err) => {
        logger.error({ email, instanceId, err }, 'error updating site');
        results.error += 1;
      }));
    });
    if(promises.length && (promises.length % 20 === 0 || i === accounts.length - 1)) {
      // eslint-disable-next-line no-await-in-loop
      await Promise.all(promises);
      promises = [];
      logger.info({ i, results }, 'finished batch');
    }
  }
  logger.info({ results }, 'wix.siteId migration completed');
  return results;
}

// endregion
