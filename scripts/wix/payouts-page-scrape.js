// run this in chorme dev console in wix payout page after clicking "load more"
const results = {
  amount: 0,
  ids: [],
};
const total = 0;
const rows = [...document.querySelectorAll('tr')].slice(1);
rows.forEach((row) => {
  const tds = [...row.querySelectorAll('td')];
  const tdStrs = tds.map(td => td.innerText);
  const amountStr = tdStrs.find(str => str.startsWith('$'));
  const amount = parseFloat(amountStr.slice(1));
  const instanceId = tdStrs.find(str => str.match(/\w+-/));
  results.ids.push({ instanceId, amount });
  results.amount += amount;
});
console.log(results);

// get list of paying wix stores that are not in the payouts list from above:
db.accounts.find({
  'subscription.created': { $lte: ISODate('2024-10-31') },
  'subscription.untilDate': { $gte: ISODate('2024-10-11') },
  'subscription.source': 'wix',
  'wix.instanceId': {
    $nin: [INSTANCE_IDS],
  },
}, { email: 1, subscription: 1, wix: 1 });
