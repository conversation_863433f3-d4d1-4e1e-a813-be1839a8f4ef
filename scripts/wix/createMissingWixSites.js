/* eslint-disable no-await-in-loop */
const wixService = require('../../app/wix/wix.service');
const wix = require('../../lib/apis/wix');
const Account = require('../../app/account/models/Account');
const { WixSite } = require('../../app/wix/models');
const dbLoader = require('../../lib/mongooseLoader');
const prodConfig = require('../../config/prodConfig');

const INSTANCE_IDS = [
  // fill instanceIds from slack errors search
  'a7239445-9fd6-43c4-b42a-eaad36709bd0',
  '180f6f0f-6fa9-49a3-acf0-a7acf1b801a3',
  '85da3d0e-f2a1-40b4-ab3d-c3860e72c0dc',
  '28e15ae3-b592-4f93-8cff-31e252feb5a1',
  '3f5a1e5a-b1f6-47d5-8209-7d5593bc5ecf',
  'e4f36161-7978-44db-94cc-6dc367205256',
  'c02e7ba1-98b1-41cd-a88b-fe930ef5ee90',
  '819eeaf3-b775-48f9-8dcb-e41236e25770',
  'df465b63-0fb2-4e24-b587-1bdddcc734ca',
  'b4b8709f-ae16-4275-9d33-b85d58b4fd9f',
  '602a6002-f8b1-44f8-a669-38abed091f00',
  '846d8a26-b5e9-4e87-b90c-cabd28a575fd',
  '21cd5771-46f8-4b7f-a44f-99f1dd7120e2',
  '1fc8e78c-c45d-4ecb-bc57-781b35d7205b',
  '963ddf3f-c8c1-4538-b79c-3c6f39d80604',
  '77d6ed37-bd32-44c8-b8c2-9d13772788a5',
  'f8a206c2-5f11-4f9e-8947-c66dfddd4fc6',
  'efab8960-e4da-4bd8-a5c0-b91210d72536',
  '326839cb-5533-4b38-b30f-019ecc1cbcc8',
  'ad0b3f54-02fe-4f13-a543-6bb39e50ba50',
  'a60f9e48-2d34-43d0-8e23-ab139f833e72',
  'b801c68e-2483-42f8-b3ce-60239f797d5c',
  '99df012a-11ed-4455-a012-64318805fb62',
  'd284146c-3c31-4807-aa07-cf939c3630c1',
  'e3ab1322-6c2e-495e-b652-fab736051ec2',
  'c21ef0a9-9458-4ffc-b314-dd9c48b4ae16',
  '6829f3c3-9ab8-4a36-83cc-64fe7d8011f7',
  'e65fa016-e833-4bc0-81b0-b3bca9522cea',
  '8cc6dcd9-fbe0-4b01-9ff8-fd375ca6fa86',
  '36f0aeb2-9b75-41bd-a839-2a5a50369e71',
  'd5b62f6a-17bf-43e8-b07d-bec6979fa341',
  '5b04a288-7855-463a-ab53-300305b6f726',
  'ffd29130-a413-4272-a7ae-af9d51fba4fd',
  '5e32273d-4cce-4b2f-a75a-add68c0ee2a4',
  '50b46d14-beb8-4cde-9f92-7265f442b6ee',
  '7ca20246-30f0-4bee-bfe7-d569a61400f3',
  '876a0463-a19b-4677-a63d-4e101861b754',
  '3cecb16d-f3c1-407f-b11d-15d94533c6d5',
  'bae1752b-6265-4d82-93b7-95c877f90e6d',
  '281163ef-d602-48e3-8300-9c8850cbecfa',
  '30bb0b90-e7b0-4bff-9163-8337b27e2a5a',
  'fc1893db-e9e1-4ee7-8b85-1d81c272311b',
  '8a857075-73b2-4bb1-9a44-419c8163cb36',
  '7ec7f7b4-6a59-4681-b595-bdb3a496867b',
  '136c309f-b643-42a1-a59d-6b4d37e07017',
  '4290e547-3eab-4461-9950-a3d1f96f1ac7',
  '0e16c692-bec9-4c85-b1e3-1011c84b3d76',
  '7f5aafc0-1683-4f48-84c4-f84d3a6bd0cb',
  'e737bb24-5eec-4816-b3d5-9937f34ef1db',
  '06f8f4b8-540d-4ca3-8176-298de8f9baa4',
  'd0bb7934-695d-4d79-b17a-8012ff0c7c4d',
  '5a2626f9-8221-4ebc-b0cf-c8c8e8b09213',
  '19e2b27e-f8cd-4a49-93d0-f6e4338928a7',
  '69b3a1fb-5c1a-4389-8172-c63146a4c9f7',
  '86d0bc06-20b4-4632-b162-b30316ab587e',
  'fb400e21-ccf4-4019-ab58-dfc50d346307',
  '1617059a-f21d-4d86-8652-32605f60a69e',
  '0fc06f47-16b5-42ff-bfab-b01896423684',
  '48437e82-b6f7-42a0-97db-874d414d5141',
  '809bb889-ad6e-4dd5-ab0e-7b1247285bd8',
  '7c628fce-d9eb-455c-a752-ea3233c5f968',
  'a2b4356d-b46e-42b9-a81d-d1450ba80213',
  '9bda1f27-eec9-43d8-a098-bc42619ce576',
  '1c41480e-18e7-4312-8612-fea2b8069656',
  'e76b9767-9f48-4c62-95f4-26adc63c6bb0',
  '9223f536-c895-4166-9892-ebd632490c13',
  'a2879682-7b8f-4797-9b86-83994ee8a976',
  'd9bb3884-a1ee-463b-9dd3-43436697d23c',
  '01b468f2-ec82-4c36-95ed-41eac4c7cf94',
  '1116e99f-19a2-4c17-bcb9-91e79097c2a5',
  'a863b9b3-554b-42fc-9a51-aded569e41de',
  '10de644c-19fc-4d66-939c-b60c695b48ed',
  '96137661-7937-43f5-9bbe-6b71e642d2c8',
  'f7349b7a-b175-4b48-81ff-d8952444c9f7',
  '683861d8-7364-4459-a035-1d0e8c9ec7a7',
  '3141d2a3-10c6-40f0-8d93-882e777a9476',
  '9c35ddb7-5cbb-4fb7-ac6f-1fd7dc60713d',
  'bb936dc7-52aa-4919-8463-9dc6e9a5c0c6',
  '52ed9c06-cc08-47fa-98a3-9855344073ae',
  '93c2b05f-851a-4597-b21f-a48d482f9d52',
  '53fa5baf-7730-48dc-894b-48c1752e4b98',
  '334bbb01-9e68-4e1f-b5f3-71969f53bb67',
  '11220a66-51e7-4d9c-bf9a-d718100f2472',
  '482224c2-dbbb-4baf-9a73-7e4856bc7fe9',
  '80f679b8-3586-421a-923e-7313a06b405e',
  '238b16a3-4969-42c3-9a99-d1a03d8de003',
  '93178109-65f4-4729-84fc-db0fa8ae1bc3',
  '0589bb6f-1282-4b46-a8f3-6d11f229377e',
  '3445f630-2af7-4334-b612-145abdfa74a5',
  '54561310-87a7-4df9-92bc-b751664a5414',
  '47ab10c9-eae2-4806-80cc-832a9fdcfe53',
  'c4dac928-077d-4758-9619-1fb79d79bc87',
  '727d98e4-7770-4dd8-a1f5-0609cecdb455',
  'ef9ee089-f370-4db8-a17d-269eb1eb2829',
  '3a463ca9-5d97-46f0-8101-27b22356a6dc',
  '7f9ed3f9-e03a-43ea-b586-bdb4826239db',
  '0073f0d0-85cf-4786-b137-60126d53a7b3',
  '79f3203e-7e54-4895-8602-023b5c5556dd',
  '24dfd61d-1b35-4416-8d83-fe363699c0ff',
  '6c2e1408-2299-406e-bc17-4659f9548bd0',
  'dc9553a5-53af-4f77-8939-5ff0171d2a4b',
  '088e93a4-7e6b-4a40-8870-44504485b5c7',
  '595c5dab-eb79-4999-b4a6-0353dec043a3',
  'a4c50d97-17f4-4bdc-a247-5fa0a6971e9e',
  '7a866922-0cbc-4158-8263-44b8fa5c5c4f',
  '6ea283a1-ecd6-4329-a660-ffb1a9b647cb',
  'fc08c162-166b-4b61-89e9-915282b12ee5',
  'ec638dd5-c523-460c-ab44-ec305285bbb5',
  '6460bb48-8847-494f-96da-6eaba333e8ff',
  'dad23247-4f24-4522-847d-ee0669bcf242',
  '87d208dc-516a-4b42-a314-a7978dcb26d7',
  'ad89a6f6-2e4c-46a5-ae76-c3bfe3851f2a',
  'e66d0201-91ec-4cc8-8314-04b38a301e5d',
  '8b71718b-41bb-4a10-a2f3-a4928cde3961',
  '8ac33f61-5a35-48aa-a61b-0184fb3f3573',
  '50780f95-df0b-4dca-9f0a-b1116edf40d5',
  'f91ecafa-e44a-4d07-8d79-f1f5f6ca8a72',
  '58b8d552-7324-4f89-95dc-4b09b418fe73',
  'c852fe9d-bbef-45d3-8aae-53a7c24447eb',
  '8b804440-315e-4703-8e05-1eac7dde67fe',
  '15e32c87-7441-4b3d-8e40-c31c8d4f880d',
  '5f399105-9e88-4d36-934e-c3cc802acd7c',
  'db90570e-0d11-426e-984e-3a57ee5e042e',
  '4dd7280d-774a-410d-8920-34d580fc657d',
  '20c294f5-f7d1-42ab-ba72-99c7f1b2b9ea',
  '703d3b0c-2a96-4308-a541-2ec13b6bf0aa',
  '77eb9f18-4c6a-42bf-ac4d-f4c686d011cf',
  'e2485f81-cb44-4166-bf89-0edd5683a9c7',
  '3a963596-4ec2-4ae4-a61f-de54e637e8f9',
  'a1170cf7-5de1-48c6-a86c-73d0f5a898da',
  '5392f8cc-b0d9-4571-b0a9-2a49da48fdab',
  '9a0e203b-7baa-4c9f-81ca-35816c70e22d',
  'bb984f23-86e6-4c62-81f2-48aa0d44f0ec',
  '338886f8-d773-492c-8156-ae78a3202229',
  '28c66316-d2f7-4965-9e2f-d7790c37d8ea',
  'bf8a3f08-5df9-43ac-afaa-093ed5d05810',
  '6a2e3956-6424-4593-ae46-ff777c62e7c2',
  '5e19aa42-1352-40e4-aeeb-a8ffd76297bb',
  '635672bd-4560-45e7-8256-c27e35ea7fb5',
  '9f60007d-62f1-4328-acf4-8620f3e4c223',
  '9378a8ee-4a20-4dc9-97ad-6a3d3bddde1d',
  '1c994998-3590-4357-b9dc-a0b1dc96bcda',
  '485ef546-499b-459b-acd3-fe961ab25b16',
  '76ee8873-61b1-48af-a99a-ef92d523d0f0',
  'aebb7506-1434-45cc-9ff6-02f4d7802240',
  'f00c424d-9b52-4615-88e1-f0a2c8826c53',
  '72ce2ee8-3b04-41e1-a0d9-ddd71b2c24fd',
];

(async () => {
  await dbLoader.load('mongodb://localhost:8080/proofsrc-prod', prodConfig.mongodb.readPref);
  console.log(await createMissingWixSites().then(results => JSON.stringify(results, null, 2)));
  process.exit();
})();

async function createMissingWixSites() {
  const results = {
    total: INSTANCE_IDS.length,
    updated: 0,
    failed: 0,
    noAccount: [],
    errors: [],
  };
  wix.setAuth(prodConfig.wix.appId, prodConfig.wix.secret, prodConfig.wix.publicKey);
  for(let i = 0; i < INSTANCE_IDS.length; i += 1) {
    const instanceId = INSTANCE_IDS[i];
    try {
      console.log(`checking ${instanceId} (${i + 1}/${results.total})`);
      const account = await Account.findOne({
        $or: [{ 'wix.instanceId': instanceId }, { 'removedWix.instanceId': instanceId }],
      });
      const accessToken = await wix.createAccessToken(instanceId);
      const [appInstance = {}, properties = {}] = await Promise.all([
        wixService.getAppInstance(accessToken),
        wixService.getSiteProperties(accessToken),
      ]);
      const info = {
        accessToken, ...appInstance, ...properties, noConnectedAccount: true,
      };
      if(!account) {
        const email = info.ownerEmail || info.email;
        info.noConnectedAccount = true;
        results.noAccount.push(`${email},${info.url},${instanceId}`);
      }
      await WixSite.findOneAndUpdate({ instanceId }, info, {
        upsert: true,
        new: true,
      });
      results.updated += 1;
    } catch(err) {
      results.failed += 1;
      results.errors.push({ instanceId, error: err.message });
      console.error(`Error processing instanceId ${instanceId}:`, err.message);
    }
  }

  return results;
}
