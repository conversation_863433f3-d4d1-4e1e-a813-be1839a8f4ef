/* eslint-disable no-await-in-loop */
const { ObjectId } = require('mongoose').Types.ObjectId;
const mongooseLoader = require('../../lib/mongooseLoader');
const config = require('../../config/prodConfig');
const wix = require('../../lib/apis/wix');
const wixService = require('../../app/wix/wix.service');
const WixSite = require('../../app/wix/models/WixSite');
const Account = require('../../app/account/models/Account');

(async () => {
  await mongooseLoader.load('mongodb://localhost:8080/proofsrc-prod');
  wix.setAuth(config.wix.appId, config.wix.secret, config.wix.publicKey);

  // the buggy version that broke getAppInstance was uploaded on 22/09/2021 the ObjectId timestamp
  // also getAppInstance has an 'isFree' field so sites that don't have it have been affected.
  const sites = await WixSite.find({
    _id: { $gte: ObjectId('614a47d00000000000000000') },
    isFree: { $exists: false },
    // instanceId: '4036d7cd-beff-42a7-9098-202705e73b03',
  });
  console.log(`processing ${sites.length} sites`);
  const results = {
    processed: 0,
    success: 0,
    uninstalled: { num: 0, instanceIds: {} },
    failed: { num: 0, errors: {}, instanceIds: {} },
  };
  // const promises = [];
  for(let i = 0; i < sites.length; i += 1) {
    results.processed += 1;
    const site = sites[i];
    const { instanceId } = site;
    const email = site.ownerEmail || site.email;
    const account = await Account.findOne({ 'wix.instanceId': instanceId });
    if(!account) {
      console.log('no account', instanceId, email);
      results.uninstalled.num += 1;
      results.uninstalled.instanceIds[instanceId] = email;
      continue;
    }
    try {
      console.log('processing', account.id, instanceId);
      const { refreshToken, accessToken } = await wixService.refreshAccessToken(site);
      const updatedSite = await wixService.updateSite(instanceId, accessToken, refreshToken).then(({ site }) => site);
      await wixService.integrateSite(account, updatedSite).catch((err) => {
        console.error(err);
      });
      results.success += 1;
      console.log('processed', instanceId, account.id, email);
    } catch(err) {
      results.failed.num += 1;
      results.failed.errors[err.message] = (results.failed.errors[err.message] + 1) || 1;
      results.failed.instanceIds[instanceId] = email;
      console.log('failed', err.message, instanceId, account.id, email);
    }
  }
  console.log(`Done: ${JSON.stringify(results, null, 2)}`);
})();
