const { WebClient } = require('@slack/web-api');
const config = require('../../config/prodConfig');

const client = new WebClient(config.slack.secret);

const channelId = 'CBCRS486Q';
const startDate = new Date('2024-09-11T12:00:00Z'); // Set timestamp for Sep 11, this will get all messages from the start date to the latest

(async () => {
  const retval = await fetchChannelMessages();
  console.log(JSON.stringify(retval, null, 2));
  process.exit();
})();

async function fetchChannelMessages() {
  const retval = {
    totalErrors: 0,
    totalUniqueErrors: 0,
    errors: {},
    instanceIds: [],
  };
  let hasMore = true;
  let cursor = null;

  const oldestTimestamp = Math.floor(startDate.getTime() / 1000);
  while(hasMore) {
    const result = await client.conversations.history({
      channel: channelId,
      cursor,
      limit: 1000,
      oldest: oldestTimestamp.toString(),
    });

    if(result.messages && result.messages.length > 0) {
      result.messages.filter(msg => msg.text && msg.text.toLowerCase().includes('wix')).forEach((msg) => {
        retval.totalErrors++;
        const dataAttachment = msg.attachments.find(att => att.title === 'data');
        const errorAttachment = msg.attachments.find(att => att.title === 'error');
        if(!dataAttachment && !errorAttachment) {
          return;
        }
        const dataStr = dataAttachment.text.replace(/```/g, '');
        const errorStr = errorAttachment.text.replace(/```/g, '');
        const error = JSON.parse(errorStr);
        if(error && error.message && !error.message.includes('not connected')) {
          return;
        }
        let instanceId;
        if(dataStr.includes('instanceId')) {
          const regex = /"instanceId"\s*:\s*"([^"]+)"/;
          const match = dataStr.match(regex);
          instanceId = match && match[1];
        }
        if(!instanceId && errorStr.includes('instanceId')) {
          instanceId = error._data.instanceId;
        }
        if(!instanceId) {
          return;
        }
        if(!retval.instanceIds.includes(instanceId)) {
          retval.instanceIds.push(instanceId);
        }
        const errorMessage = msg.text;
        if(!retval.errors[errorMessage]) {
          retval.errors[errorMessage] = [];
        }
        if(!retval.errors[errorMessage].includes(instanceId)) {
          retval.totalUniqueErrors++;
          retval.errors[errorMessage].push(instanceId);
        }
      });
    }
    hasMore = result.has_more;
    cursor = result.response_metadata.next_cursor;
    if(!cursor || !hasMore) {
      break;
    }
  }
  return retval;
}
