const { connect } = require('puppeteer-real-browser');

// Array of messages to use randomly
const messages = [
  "Hey there! I noticed you’re not using social proof notifications on your store.\n\nMegan here from ProveSource (https://provesrc.com), trusted by 40,000+ sites. You can boost sales and conversion rates by 50% — and install it in just 1 minute with our Shopify app: https://apps.shopify.com/provesource. Want me to walk you through it?",

  "Hey! Noticed your store isn’t showcasing live customer sales and reviews.\n\nI’m Megan from ProveSource (https://provesrc.com). We help businesses build trust and increase revenue fast. Stores see up to a 50% boost in sales and conversions, and you can install the app in 1 minute: https://apps.shopify.com/provesource. Want to give it a try?",

  "Hi! I saw your store and wanted to share a quick tip.\n\nMegan here from ProveSource (https://provesrc.com) — we help 40,000+ stores turn visitors into buyers with real-time social proof. You could boost sales by 50%, and it only takes 1 minute to install: https://apps.shopify.com/provesource. Want me to show you how?",

  "Hey! Quick question — are you using sales popups to boost trust?\n\nI’m <PERSON> from ProveSource (https://provesrc.com). We help Shopify stores increase revenue with simple notifications. Stores often see a 50% boost in conversions, and setup takes just 1 minute: https://apps.shopify.com/provesource. Should I guide you through it?",

  "Hey there! I was checking out your store and saw an easy way to improve conversions.\n\nI’m Megan from ProveSource (https://provesrc.com). We help online stores show off recent sales and build trust fast. You could boost sales by 50% — and get started in just 1 minute: https://apps.shopify.com/provesource. Want me to help you set it up?"
];


const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Function to dismiss popups by clicking in corners
async function dismissPopups(page) {
  const viewport = await page.viewport();

  // Define corner coordinates (10px from edges)
  const corners = [
    { x: 10, y: 10 },                              // Top-left
    { x: viewport.width - 10, y: 10 },              // Top-right
    { x: 10, y: viewport.height - 10 },             // Bottom-left
    { x: viewport.width - 10, y: viewport.height - 10 } // Bottom-right
  ];

  // Click each corner
  for (const corner of corners) {
    try {
      await page.mouse.click(corner.x, corner.y);
      await wait(250);
    } catch (error) {
      console.log(`Failed to click at coordinates (${corner.x}, ${corner.y}):`, error.message);
    }
  }

  // Try to click common close buttons
  const closeSelectors = [
    '[aria-label="Close"]',
    '[class*="close"]',
    '[class*="dismiss"]',
    '[id*="close"]',
    '[title*="Close"]',
    'button:has(svg)',  // Many close buttons are SVG icons
    '.modal button',
    '.popup button',
    '[class*="popup"] button',
    '[class*="modal"] button'
  ];

  for (const selector of closeSelectors) {
    try {
      await page.evaluate((sel) => {
        document.querySelectorAll(sel).forEach(el => {
          if (el.textContent.toLowerCase().includes('close') ||
              el.textContent.toLowerCase().includes('dismiss') ||
              el.textContent.trim() === '×' ||
              el.textContent.trim() === 'x') {
            el.click();
          }
        });
      }, selector);
      await wait(500);
    } catch {}
  }

  // Press Escape key to dismiss popups
  try {
    await page.keyboard.press('Escape');
    await wait(500);
    await page.keyboard.press('Escape');
  } catch {}
}

async function checkSubmissionSuccess(page, previousUrl, previousContent) {
  try {
    // Check if URL changed to a thank you page
    await wait(2000);

    const currentUrl = page.url();
    if(currentUrl !== previousUrl) {
      const urlIndicatesSuccess = [
        'thank',
        'thanks',
        'success',
        'confirmation',
        'contact_posted',
        '#ContactForm',
        'confirmed',
        'submitted'
      ].some(term => currentUrl.toLowerCase()
        .includes(term));

      if(urlIndicatesSuccess) {
        console.log('Detected successful submission - Redirected to thank you page');
        return true;
      }
    }

    // Get current page content
    const currentContent = await page.content();

    // If content changed, check for success messages
    if(currentContent !== previousContent) {
      // Check for success message elements
      const successSelectors = [
        // Common success message selectors
        '.success-message',
        '.thank-you-message',
        '.form-success',
        '.submission-success',
        '.alert-success',
        // Text content selectors
        'div:contains("Thank you")',
        'p:contains("Thank you")',
        'div:contains("Success")',
        'div:contains("Message sent")',
        'div:contains("Form submitted")',
        // Alert/modal selectors
        '.modal:visible',
        '.popup:visible',
        '.alert:visible'
      ];

      for(const selector of successSelectors) {
        try {
          const element = await page.$(selector);
          if(element) {
            const text = await page.evaluate(el => el.textContent, element);
            const successIndicators = [
              'thank you',
              'for contacting us',
              'soon as possible',
              'thanks',
              'success',
              'submitted',
              'received',
              'sent',
              'confirmation',
              'form-status form-status-list form__message'
            ];

            if(successIndicators.some(indicator =>
              text.toLowerCase()
                .includes(indicator))) {
              console.log(`Detected success message: "${text.trim()}"`);
              return true;
            }
          }
        } catch(error) {
          continue;
        }
      }

      // Check if the form is no longer visible (might indicate success)
      const formStillVisible = await page.$('form');
      if(!formStillVisible) {
        console.log('Form is no longer visible - likely successful submission');
        return true;
      }
    }

    return false;
  } catch(error) {
    console.error('Error checking submission success:', error);
    return false;
  }
}

async function fillContactForm(page) {
  console.log('Looking for contact form fields...');

  // Common selectors for form fields, prioritizing Shopify-specific selectors
  const formSelectors = {
    name: [
      // Shopify-specific selectors
      '#contact-form-name',
      '#ContactForm-name',
      'input[name="contact[Name]"]',
      'input[name="contact[name]"]',
      // Generic selectors
      'input[name*="name" i]',
      'input[id*="name" i]',
      'input[placeholder*="name" i]',
      'input.field__input[type="text"]',
      'input[type="text"]'
    ],
    email: [
      // Shopify-specific selectors
      '#contact-form-email',
      '#ContactForm-email',
      'input[name="contact[email]"]',
      'input[name="contact[Email]"]',
      // Generic selectors
      'input[name*="email" i]',
      'input[id*="email" i]',
      'input[placeholder*="email" i]',
      'input[type="email"]'
    ],
    phone: [
      // Shopify-specific selectors
      '#ContactFormPhone',
      '#contact-form-phone',
      'input[name="contact[Phone]"]',
      'input[name="contact[phone]"]',
      'input[name="contact[Phone Number]"]',
      // Generic selectors
      'input[name*="phone" i]',
      'input[id*="phone" i]',
      'input[placeholder*="phone" i]',
      'input[type="tel"]'
    ],
    message: [
      // Shopify-specific selectors
      '#ContactForm-body',
      'textarea[name="contact[Comment]"]',
      'textarea[name="contact[comment]"]',
      'textarea[name="contact[Message]"]',
      'textarea[name="contact[message]"]',
      // Generic selectors
      'textarea[name*="message" i]',
      'textarea[id*="message" i]',
      'textarea[placeholder*="message" i]',
      'textarea.text-area',
      'textarea.field__input',
      'textarea'
    ]
  };

  // Form data with random message
  const formData = {
    name: 'Megan Cooper',
    email: '<EMAIL>',
    phone: '111',
    message: messages[Math.floor(Math.random() * messages.length)]
  };

  // First, find the form that has all required fields
  const forms = await page.$$('form');
  let targetForm = null;
  let formFields = {};

  for (const form of forms) {
    const fields = {
      name: null,
      email: null,
      phone: null,
      message: null
    };

    // Check each field type within this form
    for (const [fieldType, selectors] of Object.entries(formSelectors)) {
      for (const selector of selectors) {
        try {
          // Use evaluateHandle to check within the specific form context
          const element = await page.evaluateHandle((form, selector) => {
            return form.querySelector(selector);
          }, form, selector);

          const elementHandle = element.asElement();
          if (elementHandle) {
            fields[fieldType] = { element: elementHandle, selector };
            break;
          }
        } catch (error) {
          continue;
        }
      }
    }

    // Check if this form has all required fields
    if (fields.name && fields.email && fields.message) {
      // Check if phone field exists and is required
      if (fields.phone) {
        const isPhoneRequired = await page.evaluate(element => {
          return element.required || element.getAttribute('aria-required') === 'true' ||
                 element.closest('div')?.textContent.includes('*');
        }, fields.phone.element);

        if (isPhoneRequired) {
          console.log('Found required phone field');
        } else {
          // If phone is not required, remove it from fields
          fields.phone = null;
        }
      }

      targetForm = form;
      formFields = fields;
      break;
    }
  }

  if (!targetForm) {
    throw new Error('Could not find a complete contact form with all required fields (name, email, message)');
  }

  console.log('✓ Found complete contact form. Proceeding to fill the fields...');

  // Fill the fields in the target form
  for (const [field, data] of Object.entries(formFields)) {
    if (data && data.element) {
      try {
        // Try different methods to fill the field
        const methods = [
          // Method 1: Direct value assignment (fastest)
          async () => {
            await page.evaluate((el, value) => {
              el.value = value;
              el.dispatchEvent(new Event('input', { bubbles: true }));
              el.dispatchEvent(new Event('change', { bubbles: true }));
            }, data.element, formData[field]);
          },
          // Method 2: Focus and type (more human-like)
          async () => {
            await data.element.focus();
            await data.element.type(formData[field], { delay: 100 });
          },
          // Method 3: Click and type (most human-like)
          async () => {
            await data.element.click({ clickCount: 3 }); // Triple click to select all
            await data.element.type(formData[field], { delay: 150 });
          }
        ];

        // Try each method until one works
        for (const method of methods) {
          try {
            await method();
            // Verify the value was set
            const value = await page.evaluate(el => el.value, data.element);
            if (value === formData[field]) {
              console.log(`Filled ${field} field using selector: ${data.selector}`);
              break;
            }
          } catch (methodError) {
            continue;
          }
        }
      } catch (error) {
        console.error(`Error filling ${field} field:`, error.message);
        throw error;
      }
    }
  }

  console.log('Form filling completed!');

  // Wait before submitting
  console.log('Waiting 3 seconds before submitting...');
  await wait(3000);

  // Check for terms and conditions checkbox
  const termsSelectors = [
    // Common terms checkbox selectors
    'input[name="terms_agreement"]',
    'input[id*="terms" i]',
    'input[name*="terms" i]',
    'input[id*="agreement" i]',
    'input[name*="agreement" i]',
    'input[type="checkbox"][required]',
    // Specific selectors from example
    '#terms_agreement_contact',
    'input[data-terms-trigger]',
    // Generic required checkbox selectors
    '.form-agreement input[type="checkbox"]',
    '.terms input[type="checkbox"]',
    '.agreement input[type="checkbox"]'
  ];

  // Try to find and check the terms checkbox
  let termsChecked = false;
  for(const selector of termsSelectors) {
    try {
      const termsCheckbox = await page.$(selector);
      if(termsCheckbox) {
        // Check if the checkbox is already checked
        const isChecked = await page.evaluate(el => el.checked, termsCheckbox);
        if(!isChecked) {
          await termsCheckbox.click();
          console.log(`Checked terms and conditions checkbox using selector: ${selector}`);
        } else {
          console.log('Terms and conditions checkbox was already checked');
        }
        termsChecked = true;
        break;
      }
    } catch(error) {
      continue;
    }
  }

  try {
    // First try specific submit button selectors within the form context
    const submitButton = await page.evaluateHandle((form) => {
      // Try to find the submit button within this specific form
      const button = form.querySelector('button[type="submit"], input[type="submit"]');
      if (button) return button;

      // If not found, try more generic selectors but still within this form
      return form.querySelector(
        'button:not([type="button"]), ' +
        'input[type="button"], ' +
        'button.button[type="submit"], ' +
        'button.submit-button, ' +
        '.form-submit button, ' +
        '.contact-submit button'
      );
    }, targetForm);

    const buttonElement = submitButton.asElement();
    if (!buttonElement) {
      throw new Error('Could not find submit button within the contact form');
    }

    console.log('✓ Found submit button within the contact form');

    // Store the current URL before submission
    const currentUrl = page.url();

    // Get the current page content before submission
    const contentBeforeSubmit = await page.content();

    // If we found a terms checkbox but couldn't check it, log a warning
    if (!termsChecked) {
      console.log('Warning: Could not find or check terms and conditions checkbox. Form submission might fail.');
    }

    // Store the URL before submission
    const preSubmitUrl = page.url();

    // Set up navigation handling with longer timeout
    const navigationPromise = page.waitForNavigation({ timeout: 30000 }).catch(() => null);

    // Click the submit button multiple times with delays
    console.log('Submitting form...');
    await buttonElement.click();
    await wait(500);
    await buttonElement.click();
    await wait(500);
    await buttonElement.click();
    console.log('Form submitted!');
    await wait(3000);

    // Wait for initial navigation
    await navigationPromise;
    await wait(1000);
    await navigationPromise;

    // Function to safely evaluate page content
    const safeEvaluate = async (evaluateFn) => {
      try {
        // Wait for any navigation to settle
        await page.waitForFunction('document.readyState === "complete"', { timeout: 10000 }).catch(() => {});

        // Create a fresh execution context
        const context = await page.mainFrame().executionContext();
        if (!context) return false;

        // Evaluate in the fresh context
        return await context.evaluate(evaluateFn).catch(() => false);
      } catch (error) {
        console.log('Safe evaluate error:', error.message);
        return false;
      }
    };

    // Function to check for Cloudflare verification page
    const isOnCloudflare = async () => {
      try {
        await wait(5000);
        return await safeEvaluate(() => {
          const text = document.body?.textContent || '';
          return text.includes('needs to be verified before');
        });
      } catch (error) {
        console.log('Error checking for Cloudflare:', error.message);
        return false;
      }
    };

    // Function to check if we're back on the contact form
    const isOnContactForm = async () => {
      try {
        return await safeEvaluate(() => {
          return !!document.querySelector('form');
        });
      } catch (error) {
        console.log('Error checking for contact form:', error.message);
        return false;
      }
    };

    // Function to check for success message
    const hasSuccessMessage = async () => {
      try {
        return await safeEvaluate(() => {
          const text = (document.body?.textContent || '').toLowerCase();
          return text.includes('thank you') ||
                 text.includes('successfully') ||
                 text.includes('submitted') ||
                 text.includes('received') ||
                 text.includes('message sent') ||
                 text.includes('we\'ll be in touch');
        });
      } catch (error) {
        console.log('Error checking for success message:', error.message);
        return false;
      }
    };

    // Check for Cloudflare verification page
    if (await isOnCloudflare()) {
      console.log('Cloudflare verification required. Please complete the verification manually...');
      console.log('Waiting up to 5 minutes for verification to complete...');

      const maxWaitTime = 300000; // 5 minutes
      const startTime = Date.now();
      let verificationComplete = false;

      // Keep checking until verification is complete or timeout
      while (!verificationComplete && Date.now() - startTime < maxWaitTime) {
        // Wait between checks
        await wait(5000);

        try {
          // Wait for any navigation to complete
          try {
            await page.waitForNavigation({ timeout: 5000, waitUntil: 'networkidle0' }).catch(() => {});
          } catch {}

          // Check if we're still on Cloudflare page
          if (!(await isOnCloudflare())) {
            // Wait for the page to stabilize
            await wait(5000);

            // Create a fresh execution context
            const context = await page.mainFrame().executionContext();
            if (!context) continue;

            // Check for success indicators
            const hasForm = await isOnContactForm();
            const hasSuccess = await hasSuccessMessage();

            if (hasForm) {
              console.log('Successfully returned to contact form after verification');
              verificationComplete = true;
              return true;
            }

            if (hasSuccess) {
              console.log('Form submission successful after verification!');
              verificationComplete = true;
              return true;
            }
          }
        } catch (error) {
          console.log('Error during verification check:', error.message);
          await wait(2000);
        }
      }

      if (!verificationComplete) {
        console.log('Manual verification timed out after', Math.floor(maxWaitTime/1000), 'seconds');
        return false;
      }
    }

    try {
      // Check for Cloudflare after form submission
      if (await isOnCloudflare()) {
        console.log('Cloudflare verification required after form submission. Please complete the verification...');
        console.log('Waiting up to 5 minutes for verification to complete...');

        const maxWaitTime = 300000; // 5 minutes
        const startTime = Date.now();
        let verificationComplete = false;

        while (!verificationComplete && Date.now() - startTime < maxWaitTime) {
          await wait(3000);

          try {
            if (!(await isOnCloudflare())) {
              // Wait for any redirects
              await wait(5000);

              // Get current URL
              const currentUrl = await page.url();

              // Check for success message
              if (await hasSuccessMessage()) {
                console.log('Form submission confirmed successful after Cloudflare verification!');
                return true;
              }

              // Check if URL changed
              if (currentUrl !== preSubmitUrl) {
                console.log('Form submission confirmed successful - URL changed after verification!');
                return true;
              }

              // Check if we're back on form
              if (await isOnContactForm()) {
                // This might mean the submission failed
                console.log('Returned to contact form after verification - submission may have failed');
                return false;
              }

              // If we got past Cloudflare but don't see success or form, wait longer
              await wait(5000);

              // Final success check
              if (await hasSuccessMessage()) {
                console.log('Form submission confirmed successful on final check!');
                return true;
              }

              // If we got here, assume success since we got past Cloudflare
              console.log('Got past Cloudflare verification - assuming submission was successful');
              return true;
            }
          } catch (error) {
            // If error occurs during check (like during navigation), wait and continue
            await wait(2000);
          }
        }

        if (!verificationComplete) {
          console.log('Manual verification timed out after', Math.floor(maxWaitTime/1000), 'seconds');
          return false;
        }
      }

      // If no Cloudflare, proceed with normal success checks
      const postSubmitUrl = await page.url();

      // If URL changed, it's a success
      if (postSubmitUrl !== preSubmitUrl) {
        console.log('Form submission confirmed successful - URL changed!');
        return true;
      }

      // Check for other success indicators
      const submissionSuccess = await checkSubmissionSuccess(page, preSubmitUrl, contentBeforeSubmit);
      if (submissionSuccess) {
        console.log('Form submission confirmed successful!');
        return true;
      }

      // If we got here, we couldn't confirm success
      console.log('Could not confirm if form submission was successful');
      return false;

    } catch (error) {
      // If error occurs due to navigation, consider it a success
      if (error.message.includes('Execution context was destroyed')) {
        console.log('Form submission successful (navigation detected)');
        return true;
      }
      throw error;
    }
  } catch (error) {
    // If we get a navigation error, it's usually because the form submitted successfully
    if (error.message.includes('Execution context was destroyed')) {
      console.log('Form submission successful (navigation detected)');
      return true;
    }
    console.error('Error submitting form:', error.message);
    return false;
  }
}

async function findContactPage(page, url) {
  try {
    // Navigate to the website
    console.log(`Navigating to ${url}...`);
    await page.goto(url, { waitUntil: 'domcontentloaded' });

    // Dismiss any initial popups
    await dismissPopups(page);
    await wait(200);

    // Look for common contact page link patterns
    const contactSelectors = [
      'a[href*="contact"]',
      'a[href*="conta"]',
      'a[href*="Contact"]',
      'a[href*="CONTACT"]',
      '.contact-link',
      '#contact-link',
      // Add more specific selectors
      'header a',
      'nav a',
      'footer a',
      'a' // Check all links last
    ];

    // Try to find the contact link
    let contactHref = null;
    for(const selector of contactSelectors) {
      try {
        const elements = await page.$$(selector);
        for(const element of elements) {
          const href = await page.evaluate(el => el.href, element)
            .catch(() => null);
          const text = await page.evaluate(el => el.textContent.trim(), element)
            .catch(() => '');

          if (!href) continue;

          // Check both href and text content for various contact-related terms
          const isContactLink = [
            href.toLowerCase().includes('conta'),
            href.toLowerCase().includes('contact'),
            text.toLowerCase().includes('contact'),
            text.toLowerCase().includes('get in touch'),
            text.toLowerCase().includes('reach us')
          ].some(Boolean);

          if(isContactLink) {
            contactHref = href;
            console.log(`Found contact link: ${href} (text: ${text})`);
            break;
          }
        }
        if(contactHref) break;
      } catch(error) {
        console.log(`Error with selector ${selector}:`, error.message);
        continue;
      }
    }

    if(!contactHref) {
      throw new Error('Could not find contact page link');
    }

    // Navigate to the contact page directly using the href
    console.log('Navigating to contact page...');
    await page.goto(contactHref, { waitUntil: 'domcontentloaded' });
    await wait(1000); // Wait longer for page load
    console.log('Successfully reached contact page!');

    // After reaching contact page, fill the form
    const success = await fillContactForm(page);
    if (!success) {
      throw new Error('Form submission was not successful');
    }
    return true;

  } catch(error) {
    // If the error is due to navigation after successful submission, consider it a success
    if (error.message.includes('Execution context was destroyed')) {
      console.log(`Form submitted successfully for ${url} (detected from navigation)`);
      return true;
    }
    throw error;
  }
}

async function processUrls(urls) {
  // Validate and clean URLs
  const validUrls = urls
    .map(url => url.trim())
    .filter(url => {
      try {
        new URL(url);
        return true;
      } catch {
        console.error(`Invalid URL skipped: ${url}`);
        return false;
      }
    });

  if(validUrls.length === 0) {
    console.error('No valid URLs to process!');
    return;
  }

  console.log(`Starting to process ${validUrls.length} URLs...\n`);

  const results = {
    successful: [],
    failed: []
  };

  let browser;
  try {
    // Initialize browser once for all URLs
    const connection = await connect({
      headless: false,
      args: [],
      customConfig: {},
      turnstile: true,
      connectOption: {},
      disableXvfb: false,
      ignoreAllFlags: false,
    });

    browser = connection.browser;
    const page = connection.page;

    // Set viewport to a large desktop size
    await page.setViewport({
      width: 1500,
      height: 1300
    });

    // Process each URL using the same browser window
    for(const [index, url] of validUrls.entries()) {
      const startTime = new Date();
      console.log(`=== [${index + 1}/${validUrls.length}] Processing ${url} ===`);

      try {
        const success = await findContactPage(page, url);
        if (success) {
          results.successful.push(url);
          const processingTime = (new Date() - startTime) / 1000;
          console.log(`\n✓ Successfully processed ${url} in ${processingTime.toFixed(1)} seconds`);
          stores = updateStoreStatus(stores, url, true);
          saveStores(stores, storesPath);
        } else {
          throw new Error('Form submission was not confirmed');
        }

        // Add a delay between URLs, but not after the last one
        if(index < validUrls.length - 1) {
          const delaySeconds = 5;
          console.log(`Waiting ${delaySeconds} seconds before processing next URL...`);
          await wait(delaySeconds * 1000);
        }
      } catch(error) {
        // Don't treat navigation errors as failures
        if (error.message.includes('Execution context was destroyed')) {
          results.successful.push(url);
          const processingTime = (new Date() - startTime) / 1000;
          console.log(`\n✓ Successfully processed ${url} in ${processingTime.toFixed(1)} seconds (detected from navigation)`);
          stores = updateStoreStatus(stores, url, true);
          saveStores(stores, storesPath);
        } else {
          results.failed.push({
            url,
            error: error.message
          });
          console.error(`\n✗ Failed to process ${url}:`, error.message);
          stores = updateStoreStatus(stores, url, false, error.message);
          saveStores(stores, storesPath);
        }
        continue;
      }
    }
  } catch(error) {
    console.error('Error initializing browser:', error);
  } finally {
    // Ensure browser is closed even if there are errors
    if (browser) {
      try {
        await browser.close();
        console.log('Browser closed.');
      } catch (closeError) {
        console.error('Error closing browser:', closeError);
      }
    }
  }

  // Final save to ensure everything is in sync
  saveStores(stores, storesPath);

  return results;
}


const fs = require('fs');
const path = require('path');

// Function to save the current state to stores.json
function saveStores(stores, storesPath) {
  try {
    fs.writeFileSync(storesPath, JSON.stringify(stores, null, 2));
    console.log('Stores file updated successfully');
  } catch (error) {
    console.error('Error updating stores file:', error);
  }
}

// Function to update store status
function updateStoreStatus(stores, url, success, error = null) {
  return stores.map(store => {
    if (store.url === url) {
      return {
        ...store,
        submitted: success,
        ...(error && { error })
      };
    }
    return store;
  });
}

// Read and parse the stores file
const storesPath = path.join(__dirname, 'stores.json');
let stores = [];
try {
  stores = JSON.parse(fs.readFileSync(storesPath, 'utf8'));
} catch (error) {
  console.error('Error reading stores file:', error);
  process.exit(1);
}

// Filter out already submitted stores and stores with errors
const pendingStores = stores.filter(store => !store.submitted && (!store.error || store.error.includes('was not successful') || store.error.includes('page has been closed') || store.error.includes('Target closed')));
const targetUrls = pendingStores.map(store => store.url);

console.log(`Found ${stores.length} total stores:`);
console.log(`- ${stores.filter(store => store.submitted).length} successfully submitted`);
console.log(`- ${stores.filter(store => store.error).length} failed with errors`);
console.log(`- ${pendingStores.length} pending to process`);

// Process URLs and handle the results
processUrls(targetUrls)
  .then(results => {
    // Print summary
    console.log('\n=== Processing Summary ===');
    console.log(`Total URLs processed: ${results.successful.length + results.failed.length}`);
    console.log(`Successful: ${results.successful.length}`);
    console.log(`Failed: ${results.failed.length}`);

    if(results.failed.length > 0) {
      console.log('\nFailed URLs:');
      results.failed.forEach(({url, error}) => {
        console.log(`- ${url}: ${error}`);
      });
    }

    if(results.successful.length > 0) {
      console.log('\nSuccessfully processed URLs:', results.successful);
    }
  })
  .catch(error => {
    console.error('Error in main process:', error);
  });
