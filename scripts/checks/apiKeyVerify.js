const jwt = require('jsonwebtoken');
const config = require('../../config');
const mongooseLoader = require('../../lib/mongooseLoader');
const Account = require('../../app/account/models/Account');
const logUtils = require('../../lib/utils/logUtils');

(async () => {
  await mongooseLoader.load(config.mongodb.url);
  const cursor = Account.find().select('apiKey email createdAt').maxTime(100 * 1000).cursor();
  const total = await cursor.query.count();
  let i = 0;
  const failed = [];
  await cursor.eachAsync((account) => {
    i += 1;
    const progress = logUtils.getProgressString(i, total);
    if(i % 1000 === 0) {
      console.log(progress, 'processing...');
    }
    try {
      jwt.verify(account.apiKey, config.jwt.api);
      // console.log(progress, 'verified', account.email, account.id);
    } catch(err) {
      Account.updateOne({ _id: account.id }, {
        apiKey: jwt.sign({ accountId: account.id }, config.jwt.api),
      }).then(() => {
        console.log('updated account', account.email);
      }).catch((err) => {
        console.error('failed to update', err);
      });
      failed.push(account.email);
      console.error(progress, 'failed to verify', account.email, account.id, account.createdAt);
    }
  });
  console.log('failed', JSON.stringify(failed));
  setTimeout(() => {
    process.exit(0);
  }, 1000);
})();
