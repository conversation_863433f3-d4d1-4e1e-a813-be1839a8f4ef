/* eslint-disable no-await-in-loop */
const request = require('superagent');
const _ = require('lodash');
const prodConfig = require('../../config/prodConfig');
const { SHOPIFY } = require('../../app/constants');
const mongooseLoader = require('../../lib/mongooseLoader');
const Account = require('../../app/account/models/Account');
const Shopify = require('../../lib/apis/shopify');
const dateUtils = require('../../lib/utils/dateUtils');
const ShopifyEvent = require('../../app/events/models/ShopifyEvent');
const shopifyWebhook = require('../../app/webhooks/shopify');
const accountService = require('../../app/account/account.service');

(async () => {
  await mongooseLoader.load('mongodb://localhost:8080/proofsrc-prod');
  const account = await Account.findOne({ email: '<EMAIL>' });
  let shopifyDomain = '';
  const shop = shopifyDomain ? account.getShop(shopifyDomain) : account.getActiveShop();
  shopifyDomain = shop.myshopify_domain;
  const shopify = Shopify(shopifyDomain, shop.token);
  await getShopData(shopify);

  // await updateScript(shopify);
  // await getOrders(shopify);
  // await getShopData(shopify);
  // await getScripts(shopify);
  // await cancelPlan(shopify, shop);
  // console.log('sent discount:', await Shopify.sendCredits({
  //   partnerId: SHOPIFY.partnerId,
  //   appId: SHOPIFY.appId,
  //   partnerAccessToken: prodConfig.shopify.partnerAccessToken,
  //   shopId: shop.id,
  //   amount: 14.50,
  //   description: '50% discount for first month',
  // }).then(res => `status: ${res.status} \nresponse: ${res.body}`));
  // await getCustomPlan({
  //   account,
  //   shop,
  //   shopify,
  //   planName: 'Gorilla Monthly Plan',
  //   price: 595,
  // });
  process.exit();
})();

async function getCustomPlan({
  account, shop, shopify, planName, price,
}) {
  if(!account || !shop || !shopify || !planName || !price) {
    throw new Error('missing function parameters');
  }
  const charge = await shopify.charge(planName, price, prodConfig.shopify.confirm);
  shop.chargeId = charge.id;
  shop.price = price;
  await account.save();
  console.log('charge url:', charge.confirmation_url);
}

async function getShopData(shopify) {
  const shopData = await shopify.getShop().catch((err) => {
    throw new Error((`${err.message}: ${err.response.text}`));
  });
  console.log('shop', shopData);
  const charges = await shopify.getCharges();
  console.log('charges', charges);
}

async function getOrders(shopify) {
  return shopify.getOrders({ limit: 30, status: 'any' }).then((orders) => {
    console.log('got orders', orders[0]);
    return orders;
  }).catch((err) => {
    console.log('failed to pull latest orders', err);
  });
}

async function importOrders({ account, shop, orders }) {
  const results = [];
  for(let i = 0; i < orders.length; i += 1) {
    const res = await shopifyWebhook.saveEvent(account.id, shop, orders[i]);
    results.push(res);
  }
  return results;
}

async function getScripts(shopify) {
  console.log('got scripts', await shopify.getScripts());
}

async function updateScript(shopify) {
  const scripts = await shopify.getScripts();
  const script = scripts[0];
  console.log('update script', await shopify.updateScript(script.id, { display_scope: 'online_store' }));
}

async function getWebhooks(account, shopify) {
  const trackUrl = `${prodConfig.shopify.webhooks.track}?apiKey=${account.apiKey}`;
  const webhooks = await Promise.all([
    shopify.getWebhook(Shopify.topics.order_create, trackUrl),
    shopify.getWebhook(Shopify.topics.app_uninstall, prodConfig.shopify.webhooks.uninstall),
    shopify.getWebhook(Shopify.topics.shop_update, prodConfig.shopify.webhooks.update),
  ]);
  console.log(webhooks);
}

async function addWebhooks(account, shopify) {
  const trackUrl = `${prodConfig.shopify.webhooks.track}?apiKey=${account.apiKey}`;
  const webhooks = await Promise.all([
    shopify.addWebhook(Shopify.topics.order_create, trackUrl),
    shopify.addWebhook(Shopify.topics.app_uninstall, prodConfig.shopify.webhooks.uninstall),
    shopify.addWebhook(Shopify.topics.shop_update, prodConfig.shopify.webhooks.update),
  ]);
  console.log(webhooks);
}

async function cancelPlan(shopify, shop) {
  console.log('cancelled shopify plan:', await shopify.cancelCharge(shop.chargeId).then(res => res.status));
}

async function updateProductImages({ account }) {
  const events = await ShopifyEvent
    .find({ accountId: account.id, date: { $gte: '2021-11-01' } })
    .sort({ date: -1 })
    .limit(1000);

  console.log(`processing ${events.length} events`);
  const images = {};
  for(let i = 0; i < account.shopify.length; i += 1) {
    const shop = account.shopify[i];
    const shopify = Shopify(shop.myshopify_domain, shop.token);
    const productImages = await getImages(shopify);
    Object.assign(images, productImages);
  }
  console.log(`got ${Object.keys(images).length} images`);

  for(let i = 0; i < events.length; i += 1) {
    const event = events[i];
    const shop = account.getShop(event.shop);
    if(!shop) {
      throw new Error('shop not found');
    }
    const shopify = Shopify(shop.myshopify_domain, shop.token);
    const productIds = event.products
      .map(p => p.id)
      .filter(id => !images[id]);
    if(productIds && productIds.length) {
      const productImages = await getImages(shopify, productIds);
      Object.assign(images, productImages);
    }
    let shouldSave = false;
    event.products.forEach((p) => {
      const image = images[`${p.id}_${p.variant}`] || images[p.id];
      if((!image && p.image)
        || (image && !p.image)
        || image.split('?')[0] !== p.image.split('?')[0]
      ) {
        console.log('image changed:', event.id, p.id, p.name);
        console.log('-- old image:', p.image);
        console.log('-- new image:', image);
        p.image = image;
        shouldSave = true;
      }
    });
    if(shouldSave) {
      await event.save();
      console.log('updated event', event.id, event.email);
    }
    if((i + 1) % 50 === 0) {
      console.log(`processed ${i + 1} events (downloaded ${Object.keys(images).length} images)`);
    }
  }
  console.log('done');
}

async function getImages(shopify, productIds) {
  const images = {};
  const products = await shopify.getProducts(productIds);
  // TODO: need to handle new product/variant graphQL API and fetch variant images
  products.forEach((p) => {
    const src = p.image && p.image.src;
    if(src) {
      if(src.indexOf('?') > -1) {
        images[p.id] = `${src}&width=100`;
      } else {
        images[p.id] = `${src}?width=100`;
      }
    } else {
      console.log(`product ${p.id} ${p.name} has no image`);
    }
    const variantId = p.variant;
    const variantImage = p.images.find(img => img.variant_ids.includes(variantId));
    if(variantImage) {
      images[`${p.id}_${variantId}`] = variantImage;
    }
  });
  return images;
}

function getInstallReauthUrl(shop) {
  const apiKey = `client_id=${prodConfig.shopify.apiKey}`;
  const scope = `scope=${SHOPIFY.scopes}`;
  const redirect = `redirect_uri=${prodConfig.shopify.redirect}`;
  return `https://${shop}/admin/oauth/authorize?${apiKey}&${scope}&${redirect}`;
}

async function sendReviewRequest() {
  const results = { errors: [], sent: [] };
  // $gte + $lt search in array of objects is broken into two queries, returning wrong results
  // must use $elemMatch
  const shopifyAccounts = await Account.find({
    'stats.sentEmails.reviewRequest': null,
    'stats.notifications.lastImpression': { $gte: new Date('2025-04-13') },
    shopify: {
      $elemMatch: {
        plan_name: { $nin: ['frozen', 'cancelled', 'dormant'] },
      },
    },
  });
  await Promise.all(shopifyAccounts.map((account) => {
    const platform = 'shopify';
    return accountService.sendReviewRequest({ account, platform }).then(() => {
      results.sent.push({ email: account.email, platform });
    }).catch((err) => {
      results.errors.push({ email: account.email, platform, err });
    });
  }));
  console.log('sent emails', results);
  return results;
}

// async function sendCreditsToPastStores() {
//   const results = {
//     sent: [], errors: [], notFound: [], alreadyApplied: [],
//   };
//   const installedBefore = dateUtils.todayNormalized12am() - dateUtils.MILLISECONDS_IN_DAY * 12;
//   const accounts = await Account.find({
//     shopify: {
//       $elemMatch: {
//         installed: { $lt: installedBefore },
//       },
//     },
//     'stats.sentEmails.shopifyCredit': { $exists: false },
//     subscription: null,
//   }).limit(5);
//   for(let i = 0; i < accounts.length; i += 1) {
//     const account = accounts[i];
//     const { email } = account;
//     const shop = account.shopify.find(store => !store.paying && store.installed < installedBefore);
//     if(!shop) {
//       console.log(`shop not found ${account.email}`);
//       results.notFound.push(account.email);
//       continue;
//     }
//     const { myshopify_domain: domain } = shop;
//     const shopify = ShopifyLib(domain, shop.token);
//     const credits = await shopify.getCredits().catch((err) => {
//       console.log(`no credits found for shop ${domain}`);
//     });
//     if(credits && credits.length) {
//       console.log(`credits already applied ${email} on shop ${domain}: ${credits[0].description}`);
//       results.alreadyApplied.push(domain);
//       continue;
//     }
//     await ShopifyLib.sendCredits({
//       partnerId: SHOPIFY.partnerId,
//       appId: SHOPIFY.appId,
//       partnerAccessToken: prodConfig.shopify.partnerAccessToken,
//       shopId: shop.id,
//       amount: 14.50,
//       description: '50% discount for first month',
//     }).then(() => Account.updateOne({ _id: account._id }, {
//       $set: {
//         'stats.sentEmails.shopifyCredit': Date.now(),
//       },
//     })).then(() => {
//       console.log(`sent credits to ${email} on shop ${domain}`);
//       results.sent.push(domain);
//     }).catch((error) => {
//       console.log(`failed to send credits for ${email} on shop ${domain}`, error);
//       results.errors.push({
//         email, domain, error: error.message || 'Unknown error occurred while sending credits',
//       });
//     });
//   }
//   console.log(results);
//   return results;
// }
