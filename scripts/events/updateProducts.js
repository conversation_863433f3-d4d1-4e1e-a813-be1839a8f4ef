/* eslint-disable no-await-in-loop */
// const config = require('../../config');
const mongooseLoader = require('../../lib/mongooseLoader');
const Account = require('../../app/account/models/Account');
const WooEvent = require('../../app/events/models/WooEvent').model;

(async () => {
  await mongooseLoader.load('mongodb://localhost:8080/proofsrc-prod');
  const accountId = '5ecbdf8c5be69c3a282a8a8c';
  const events = await WooEvent.find({ accountId }).sort({ _id: -1 });
  console.log(`got ${events.length} events`);
  for(let i = 0; i < events.length; i += 1) {
    const event = events[i];
    event.products.forEach((product) => {
      if(product.link.includes('shop.faithola.com')) {
        product.link = product.link.replace('shop.', '');
        console.log('updated link', product.link);
      }
      if(product.image.includes('shop.faithola.com')) {
        product.image = product.image.replace('shop.', '');
        console.log('updated image', product.image);
      }
    });
    const modified = event.isModified();
    if(modified) {
      await event.save();
    }
    console.log(event.date, 'modified products:', modified);
  }
  console.log('done');
  process.exit(0);
})();
