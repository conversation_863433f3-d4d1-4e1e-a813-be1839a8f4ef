const config = require('../../config');
const sendgrid = require('../../lib/apis/sendgrid');
const mongooseLoader = require('../../lib/mongooseLoader');
const Account = require('../../app/account/models/Account');
const SubAccount = require('../../app/account/models/SubAccount');
const shouldFilter = require('./filterEmails');

const FROM = {
  email: '<EMAIL>',
  name: 'Team ProveSource',
};

const FOUNDERS = [
  { email: '<EMAIL>', name: '<PERSON><PERSON>', sender: '<PERSON><PERSON>' },
  { email: '<EMAIL>', name: '<PERSON><PERSON>', sender: 'Yo<PERSON>' },
];

const TEMPLATE_ID = 'd-db125022e2ff4b1783ac4130b42cc3bb';
const CATEGORY = 'newsletter-shapo';
const STAT_FIELD = 'stats.sentEmails.shapo';

(async () => {
  if(!config.sendgrid.active || !config.sendgrid.apiKey) {
    throw new Error('Sendgrid is not configured');
  }
  await mongooseLoader.load('mongodb://localhost:8080/proofsrc-prod');
  const emails = await getEmails();
  const results = {
    total: {
      num: 0,
      emails: [],
    },
    failed: {
      num: 0,
      emails: [],
    },
    filtered: {
      num: 0,
      emails: [],
    },
  };
  let emailsToUpdate = [];
  let promises = [];
  console.log('sending', emails.length, 'emails');
  for(let i = 0; i < emails.length; i += 1) {
    // const founder = FOUNDERS[Math.floor(Math.random() * FOUNDERS.length)];
    const email = emails[i];
    results.total.emails.push(email);
    results.total.num += 1;
    if(shouldFilter(email)) {
      results.filtered.num += 1;
      results.filtered.emails.push(email);
      continue;
    }
    promises.push(sendgrid.sendTemplate(config.sendgrid.apiKey, {
      to: email,
      from: FROM,
      replyTo: { name: 'Team ProveSource', email: '<EMAIL>' },
      templateId: TEMPLATE_ID,
      // data: { sender: founder.sender, email },
      category: CATEGORY,
    })
      .then(() => emailsToUpdate.push(email))
      .catch((err) => {
        results.failed.num += 1;
        results.failed.emails.push(email);
        console.log('failed sending to', email, err);
      }));
    if(i !== 0 && i % 100 === 0) {
      console.log('sending batch, total:', results.total.num);
      await Promise.all(promises);
      if(STAT_FIELD) {
        await Account.updateMany({ email: { $in: emailsToUpdate } }, { [STAT_FIELD]: Date.now() });
      }
      emailsToUpdate = [];
      promises = [];
    }
  }
  if(promises.length) {
    console.log('sending final batch', promises.length);
    await Promise.all(promises);
  }
  console.log('filtered', results.filtered.num, results.filtered.emails);
  console.log('failures', results.failed.num, results.failed.emails);
  console.log('done');
  process.exit(0);
})();

function getEmails() {
  return Account.find({
    active: true,
    deleted: { $ne: true },
    ...(STAT_FIELD && { [STAT_FIELD]: null }),
    // 'subscription.period': { $ne: 'lifetime' },
    // 'subscription.recentIPN': { $in: ['RECURRING', 'CHARGE', 'SUBSCRIPTION_CHARGE_FAILURE'] },
    // 'subscription.plan': 'starter',
  })
    .select('email parent kind')
    .then(docs => docs.map(doc => doc.email || doc.parent.email))
    .then(emails => [...new Set(emails)]);
}

function getFreeAccountEmails() {
  return Account.distinct('email', {
    $or: [
      { subscription: null },
      { 'subscription.recentIPN': /CANCEL/ },
    ],
    // createdAt: { $lte: new Date('2021-06-20') },
    active: true,
    deleted: { $ne: true },
    email: { $not: /DELETED/i },
  });
}
