// const fs = require('fs').promises;
// const list = require('./free-onboarded.json');
// const filterEmails = [...new Set([
//   ...require('./removed-bigcommerce.json'),
//   ...require('./removed-wix.json'),
//   ...require('./removed-wordpress.json'),
//   ...require('./removed-shopify.json'),
// ])];

module.exports = function shouldFilter(email) {
  return email && (
    email.includes('deleted')
    || email.includes('buckley')
    || email.includes('anchoradesign')
    || email.includes('ao.graphic.design')
    || email.includes('daveshrein')
    || email.includes('bellsouth')
    || email.includes('@wix.com')
    || email.includes('@provesrc')
    || email.includes('csmtest')
    || email.includes('afterglow.marketing')
    || email.includes('meinfotowort')
    || email.includes('dogwalktowealth')
    || email.includes('zuegrady')
    || email.includes('bigravity.co')
    || email.includes('dmedalion')
    || email.includes('chloecousins.com')
    || email.includes('franz.piszczan')
    || email.includes('doctorsinitaly')
    || email.includes('fameshop')
    || email.includes('opexshadygrove')
    || email.includes('<EMAIL>')
  );
};

// (async () => {
//   const unfiltered = [...new Set(list.map(email => email.toLowerCase()))];
//   const filtered = unfiltered.filter(email => (
//     !email.includes('deleted')
//       && !email.includes('buckley')
//       && !email.includes('anchoradesign')
//       && !email.includes('ao.graphic.design')
//       && !email.includes('daveshrein')
//       && !email.includes('bellsouth')
//       && !email.includes('@wix.com')
//       && !email.includes('@provesrc')
//       && !email.includes('csmtest')
//       && !email.includes('afterglow.marketing')
//       && !email.includes('meinfotowort')
//       && !email.includes('dogwalktowealth')
//       && !email.includes('zuegrady')
//       && !email.includes('bigravity.co')
//       && !email.includes('dmedalion')
//       && !filterEmails.includes(email)
//   ));
//   await fs.writeFile('./filtered-emails-onboarded.json', JSON.stringify(filtered, null, 2));
// })();
