const fs = require('fs-extra');
const parseDomain = require('parse-domain');
const dbLoader = require('../../lib/mongooseLoader');
const logger = require('../../lib/logger/LoggerFactory')('outreach');

const list = require('./list.json');
const Account = require('../../app/account/models/Account');

const DB_URL = 'mongodb://127.0.0.1:8080/proofsrc-prod';

/**
 * list structure example
 * {
 *   "domain.com": {
 *     "list": []
 *   },
 *  }
 */

(async function main() {
  logger.info({ url: DB_URL }, 'connecting to mongo');
  await dbLoader.load(DB_URL);

  const domains = Object.keys(list);
  logger.info({ domains: domains.length }, 'checking domains');

  const [hosts, emails] = await Promise.all([
    Account.distinct('hosts'),
    Account.distinct('email'),
  ]);

  const domainsSet = new Set();
  hosts.forEach((h) => {
    const parsed = parseDomain(h);
    if(parsed) {
      const domain = `${parsed.domain}.${parsed.tld}`;
      if(!domain.includes('.cn')) {
        domainsSet.add(domain);
      }
    }
  });
  const dbDomains = [...domainsSet];
  logger.info({ dbDomains: dbDomains.length }, 'got domains from db');

  const emailDomainsSet = new Set();
  emails.forEach(email => emailDomainsSet.add(email.split('@')[1]));
  const emailDomains = [...emailDomainsSet];
  logger.info({ emailDomains: emailDomains.length }, 'got email domains from db');

  const newEmailList = {};
  let found = 0;
  for(let i = 0; i < domains.length; i += 1) {
    const domain = domains[i];
    if(dbDomains.includes(domain)) {
      logger.info({ domain }, 'domain filtered');
      found += 1;
    } else if(emailDomains.includes(domain)) {
      logger.info({ domain }, 'domain filtered (email)');
      found += 1;
    } else {
      newEmailList[domain] = list[domain];
    }
  }
  logger.info({ found }, 'total filtered domains');
  await fs.writeJson('./new-list.json', newEmailList);
  process.exit(0);
}());
