const youtube = require('../../lib/apis/youtube');

(async () => {
  // channel ID = 'UC' + user ID

  const variations = [
    'UCnK2PqwRqvwDwiKc9erhDsQ',
    'UC4rlAVgAK0SGk-yTfe48Qpw',
    'https://www.youtube.com/channel/UCWiH2XAOx_Alu85HlimaMUA',
    'https://www.youtube.com/user/reiclub',
    'https://www.youtube.com/c/JamHouseRadio',
    'https://www.youtube.com/channel/UClRtX8MEkJrTzELu_1SFQwg',
  ];
  await Promise.all(variations.map(async (v) => {
    const subs = await youtube.getSubscribers(v).catch((err) => {
      console.error('failed to fetch subscribers for', v, err);
    });
    console.log('youtube subscribers', v, subs);
  }));
})();
