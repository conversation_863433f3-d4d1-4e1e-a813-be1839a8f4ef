const twitter = require('../../lib/apis/twitter');

(async () => {
  const profiles = [
    'https://twitter.com/AndSison',
    'https://www.twitter.com/dukawise',
    'FlintTCHIKAPA',
    'https://twitter.com/ZlatushkaC',
    'https://twitter.com/IanKhanFuturist',
    'twitter.com/marketers_gold',
    'www.twitter.com/theprophecyclo1',
  ];
  await Promise.all(profiles.map(profileId => twitter.getFollowers(profileId)
    .then(followers => console.log(profileId, followers))
    .catch(err => console.error(profileId, err))));
})();
