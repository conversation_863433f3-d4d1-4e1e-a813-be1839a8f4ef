const config = require('../../config');
const dbLoader = require('../../lib/mongooseLoader');
const instagramService = require('../../app/social/instagram.service');
const instagram = require('../../lib/apis/instagram');
const Notification = require('../../app/notifications/models/SocialCounter');
const profiles = require('../../sandbox/instagram/profiles');

(async () => {
  await getInstagramFollowers();

  setTimeout(() => {
    process.exit(0);
  }, 500);
})();

async function getInstagramFollowers() {
  for(let i = 0; i < profiles.length; i += 1) {
    const profileId = profiles[i];
    const followers = await instagram.getFollowers(profileId);
    console.log(profileId, followers);
  }
}

// async function scrapePage() {
//   let success = 0;
//   const promises = [];
//   for(let i = 0; i < profiles.length; i += 1) {
//     // const proxyIP = config.proxies[Math.floor(Math.random() * config.proxies.length)];
//     const proxy = null; // proxyIP && `http://${proxyIP}`;
//     // const json = Math.random() > 0.5;
//     const profileId = profiles[i];
//     promises.push(instagram.getFollowers(profileId)
//       .then((followers) => {
//         success += 1;
//         console.log(profileId, followers, proxy);
//         return followers;
//       })
//       .catch((err) => {
//         console.error('get followers failed', profileId, err.message, proxy);
//       }));
//   }
//   const results = await Promise.all(promises);
//   console.log('total results', results.length, 'success:', success);
// }
