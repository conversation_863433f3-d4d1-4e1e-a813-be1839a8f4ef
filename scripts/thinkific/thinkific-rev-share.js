/* eslint-disable no-console */
const dbLoader = require('../../lib/mongooseLoader');
const Account = require('../../app/account/models/Account');
const { dateUtils } = require('../../lib/utils');
const { SUBSCRIPTION_SOURCE } = require('../../app/billing/constants');
const { PERIODS } = require('../../app/account/plansEnum');

// UPDATE THIS
const monthStart = new Date('2025-01-01');
const nextMonth = new Date('2025-02-01');

(async () => {
  await dbLoader.load('mongodb://localhost:8080/proofsrc-prod');

  const columns = ['signup', 'installed', 'uninstalled', 'customer id', 'plan', 'plan period', 'cost'];
  console.log(columns.join(','));

  const results = {
    active: 0, total: 0, skipped: [], skippedYearly: [],
  };
  const accounts = await Account.find({
    createdAt: { $gte: new Date('2021-06-08') },
    'thinkific.0': { $exists: true },
  }, {
    email: 1,
    createdAt: 1,
    thinkific: 1,
    subscription: 1,
    transactions: 1,
  }).sort({ createdAt: -1 });
  accounts.forEach((account) => {
    const {
      id: accountId, email, createdAt: created, subscription, thinkific: thinkificSites,
    } = account;
    const {
      created: subCreateDate, recentIPN: ipn, source, untilDate,
    } = subscription || {};
    let { contractName: contract, period } = subscription || { contractName: 'free', period: 'N/A' };
    const thinkific = thinkificSites[0];
    let price = 0;
    let transaction = null;
    const isPaying = ['CHARGE', 'RECURRING', 'CONTRACT_CHANGE'].includes(ipn);
    const wasCustomer = subCreateDate < thinkific.installed
      || created.getTime() < thinkific.installed.getTime() - ********;
    if(untilDate < monthStart) {
      contract = 'free';
      period = 'N/A';
    }
    if(isPaying) {
      if(source !== SUBSCRIPTION_SOURCE.bluesnap) {
        results.skipped.push({ email, note: `${source} subscription` });
        return;
      }
      if(contract.toLowerCase().includes('yearly')) {
        results.skippedYearly.push({ email, note: `${contract} subscription` });
        return;
      }
      if(account.transactions) {
        transaction = account.transactions.find(t => ['RECURRING', 'CHARGE'].includes(t.ipn) && t.date > monthStart && t.date < nextMonth);
        price = (!wasCustomer && transaction && transaction.amountUSD) || 0;
      }
    } else {
      contract = 'free';
      period = 'N/A';
    }

    if(period === PERIODS.LIFETIME) {
      results.skipped.push({ email, note: 'lifetime subscription' });
      return;
    }
    if(isPaying && wasCustomer && subCreateDate < thinkific.installed) {
      results.skipped.push({ email, note: `subscription created before thinkific (subscription: ${dateUtils.getISODate(subCreateDate)}, thinkific: ${dateUtils.getISODate(thinkific.installed)})` });
      return;
    }
    console.log([
      dateUtils.getISODate(created),
      dateUtils.getISODate(thinkific.installed),
      dateUtils.getISODate(thinkific.uninstalled) || 'N/A',
      accountId,
      contract,
      period,
      price,
    ].join(','));
    if(!thinkific.uninstalled) {
      results.active += 1;
    }
    if(!wasCustomer
        && price
        && transaction
        && transaction.date > monthStart
        && transaction.date < nextMonth
    ) {
      results.total += price;
    }
  });
  console.log(`\n${'='.repeat(20)}`);
  console.log(`Active accounts: ${results.active}`);
  console.log(`Total revenue: ${results.total.toFixed(2)} USD`);
  console.log(`Fees: ${(results.total * 0.15).toFixed(2)} USD`);
  console.log('skipped:', results.skipped.map(({ email, note }) => `\n- ${email}: ${note}`).join(''));
  console.log('skippedYearly:', results.skippedYearly.map(({ email, note }) => `\n- ${email}: ${note}`).join(''));
  console.log(`\n${'='.repeat(20)}`);
  process.exit(0);
})();
