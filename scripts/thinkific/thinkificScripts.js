/* eslint-disable no-await-in-loop,no-console */
const config = require('../../config/prodConfig');
const dbLoader = require('../../lib/mongooseLoader');
const Account = require('../../app/account/models/Account');
const thinkificService = require('../../app/thinkific/thinkific.service');
const thinkificTasks = require('../../cron/jobs/thinkificTasks');
const ThinkificEvent = require('../../app/thinkific/ThinkificEvent');

(async function main() {
  await dbLoader.load('mongodb://localhost:8080/proofsrc-prod');
  const gid = 'cc5d4554-69ed-4a20-bbe3-d27aed16171c';
  console.log('webhooks', await getWebhooks(gid));
  // console.log('re-auth', await authAgain(gid));
  // await getCourse(gid, '1288202');
  // console.log(await getSiteAndToken(gid));
  // await fixBundleProductLinks(gid);
  // await fixProductImages(gid);
  console.log('done');
  process.exit(0);
}());

// doesn't work
async function authAgain(gid) {
  const account = await Account.findOne({ 'thinkific.gid': gid, 'thinkific.uninstalled': null });
  const site = account.thinkific[0];
  const newSite = await thinkificService.saveSite({ code: site.code, subdomain: site.subdomain });
  account.thinkific[0] = site;
  await thinkificService.refreshAndSaveTokens({ account, site: newSite, force: true });
  return newSite;
}

async function getSiteAndToken(gid) {
  const account = await Account.findOne({ 'thinkific.gid': gid, 'thinkific.uninstalled': null });
  const site = account.thinkific[0];
  const { subdomain } = site;
  let { accessToken } = site;
  accessToken = await thinkificService.refreshAndSaveTokens({ account, site, force: true });
  return {
    account, site, accessToken, subdomain,
  };
}

async function getWebhooks(gid) {
  const {
    account, site, subdomain, accessToken,
  } = await getSiteAndToken(gid);
  return thinkificService.getWebhooks(accessToken);
}

async function getCourse(gid, courseId) {
  const {
    account, site, subdomain, accessToken,
  } = await getSiteAndToken(gid);
  const course = await thinkificService.getCourse({
    accessToken, subdomain, courseId, api: 'courses',
  });
  console.log('got course', course);
}

async function refreshTokensCron() {
  const results = await thinkificTasks.refreshTokens();
  console.log('refresh tokens results', results);
}

async function fixBundleProductLinks(gid) {
  let updated = 0;
  const { accessToken, subdomain } = await getSiteAndToken(gid);
  const events = await ThinkificEvent.find({ gid });
  console.log(`scanning ${events.length} events`);
  for(let i = 0; i < events.length; i += 1) {
    const event = events[i];
    const { products } = event;
    let save = false;
    await Promise.all(products.map(async (p) => {
      const product = await thinkificService.getCourse({
        accessToken, subdomain, courseId: p.id, api: 'products',
      }).catch((err) => {
        console.log(`failed getting course details ${err.name}: ${err.message}`);
      });
      if(product && product.link !== p.link) {
        updated += 1;
        console.log('product link changed');
        console.log('-- old:', p.link);
        console.log('-- new:', product.link);
        p.link = product.link;
        save = true;
      }
    }));
    if(save) {
      await event.save();
    }
    if(i && i % 100 === 0) {
      console.log(`scanned ${i + 1}/${events.length}`);
    }
  }
  console.log(`updated ${updated} product links`);
}

async function fixProductImages(gid) {
  let updated = 0;
  const { accessToken, subdomain } = await getSiteAndToken(gid);
  const events = await ThinkificEvent.find({ gid, accountId: '658a338e40f1fb55999f45e2' });
  console.log(`scanning ${events.length} events`);
  for(let i = 0; i < events.length; i += 1) {
    const event = events[i];
    const { products } = event;
    let save = false;
    await Promise.all(products.map(async (p) => {
      const product = await thinkificService.getCourse({
        accessToken, subdomain, courseId: p.id, api: 'courses',
      }).catch((err) => {
        console.log(`failed getting course details ${err.name}: ${err.message}`);
      });
      const product2 = product && product.productId && await thinkificService.getCourse({
        accessToken, subdomain, courseId: product.productId, api: 'products',
      }).catch((err) => {
        console.log(`failed getting course details ${err.name}: ${err.message}`);
      });
      const image = thinkificService.getProductImage(subdomain, product2);
      if(image && image !== p.image && !image.includes('default-product')) {
        updated += 1;
        console.log('product image changed');
        console.log('-- old:', p.image);
        console.log('-- new:', image);
        p.image = image;
        save = true;
      }
    }));
    if(save) {
      await event.save();
    }
    if(i && i % 100 === 0) {
      console.log(`scanned ${i + 1}/${events.length}`);
    }
  }
  console.log(`updated ${updated} product links`);
}
