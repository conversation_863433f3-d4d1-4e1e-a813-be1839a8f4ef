/* eslint-disable no-await-in-loop,no-console */
const config = require('../../config/prodConfig');
const dbLoader = require('../../lib/mongooseLoader');

const Account = require('../../app/account/models/Account');
const profitwell = require('../../lib/apis/profitwell').getClient(config.profitWell.apiKey);

(async function main() {
  await dbLoader.load('mongodb://localhost:8080/proofsrc-prod', config.mongodb.readPref);
  const query = {
    'subscription.untilDate': { $gte: new Date('2020-07-26') },
    'subscription.period': { $ne: 'lifetime' },
    'subscription.recentIPN': { $ne: 'CANCELLATION' },
    'subscription.contractName': /(?!.*promo)/i,
    'shopify.plan_name': { $ne: 'frozen' },
  };
  const rowsCount = await Account.count(query);
  console.log(`scanning ${rowsCount} accounts`);

  const checkAccounts = [];
  let count = 0;
  let cursor = await getCursor(query);
  let account = await cursor.next();
  while(account) {
    count += 1;
    let issue = false;
    const subsRes = await profitwell.getSubscriptionsForUser(account.id).catch(() => {});
    const pwSub = (subsRes && subsRes.length && subsRes[subsRes.length - 1]);
    const activeSub = account.isSubscriptionActive()
                   && !account.subscriptionInfo().recentIPN.includes('CANCEL');

    const pwActive = pwSub && !pwSub.status.includes('churn') && pwSub.value !== 0;
    if(activeSub && pwActive) {
      const branding = pwSub.plan_id.toLowerCase().includes('brand')
                       && account.subscription.plan.includes('brand');
      if(!pwSub.plan_id.match(new RegExp(account.subscription.plan, 'i')) && !branding) {
        issue = `#${count} plan mismatch`;
      } else {
        console.log(`#${count} synced`, accountToString(account));
      }
    } else if(activeSub) {
      issue = `#${count} missing profitwell subscription`;
    } else if(pwActive) {
      issue = `#${count} has profitwell subscription - should be churned`;
    }
    if(issue) {
      checkAccounts.push(`${issue} ${accountToString(account)}`);
      printError(issue, account);
    }
    if(count % 100 === 0) {
      console.log(`scanned ${count}/${rowsCount}`);
      await cursor.close();
      cursor = await getCursor(query, count);
    }
    account = await cursor.next();
  }
  console.log('check these accounts:', JSON.stringify(checkAccounts, null, 2));
  process.exit(0);
}());

function printError(msg, account) {
  console.error(msg, accountToString(account), '[ERROR]');
}

function accountToString(account) {
  return `${account.email} (${account.id})`;
}

function getCursor(query, skip = 0) {
  return Account.find(query)
    .select('_id email subscription shopify email')
    .skip(skip)
    .maxTime(100 * 1000)
    .cursor();
}

process.on('UnhandledPromiseRejectionWarning', () => {
  process.exit(1);
});
