/* eslint-disable no-restricted-syntax,no-console,import/no-extraneous-dependencies */
process.env.NODE_ENV = 'production'; // load production profitwell
const csv = require('csv-parse');
const fs = require('fs');

const dbLoader = require('../../lib/mongooseLoader');

const Account = require('../../app/account/models/Account');

const pwController = require('../../app/billing/profitwell.controller');

(async function main() {
  await dbLoader.load('mongodb://localhost:8080/proofsrc-prod');
  const parser = fs.createReadStream('./profitwell.csv').pipe(csv());
  const issues = [];
  let count = 0;
  for await (const row of parser) {
    count += 1;
    if(count === 1) {
      continue;
    }
    if(count % 100 === 0) {
      console.log('scanned:', count);
    }
    const [email, userId, plan] = row;
    const account = await Account.findOne({
      $or: [
        { email },
        { emails: email },
      ],
    }).select('_id subscription email shopify');

    if(!account) {
      const issue = `no such account ${email}, ${userId}`;
      issues.push(issue);
      console.error(issue);
    } else if(!account.isSubscriptionActive()) {
      const issue = `account has no subscription ${account.email} (${account.id})`;
      issues.push(issue);
      console.log(issue);
      const sub = account.subscription || {};
      console.table([{
        email: account.email,
        plan: sub.plan,
        untilDate: sub.untilDate,
        source: sub.source,
        pwPlan: plan,
      }]);

      if(sub.untilDate > new Date()) {
        console.log('churning in profitwell');
        await pwController.churnSubscription(userId,
          new Date('2020-06-01'),
          pwController.CHURN_TYPES.voluntary);
      }
    }
  }
  console.log('issues:', issues.length);
  console.log('done');
  process.exit(0);
}());
