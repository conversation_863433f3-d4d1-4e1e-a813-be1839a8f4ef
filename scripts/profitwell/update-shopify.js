const csvUtils = require('../../lib/utils/csvUtils');
const profitwell = require('../../app/billing/profitwell.controller');
const sleep = require('../../lib/utils/sleep');

(async () => {
  const rows = await csvUtils.parseArray('./profitwell-list.csv');
  const shopifySubs = rows.filter(row => row.Plans.toLowerCase().includes('shopify'));
  let totalDiff = 0;
  let promises = [];
  for(let i = 0; i < shopifySubs.length; i += 1) {
    const sub = shopifySubs[i];
    const mrr = sub.MRR;
    const newMrr = mrr / 0.8;
    const diff = newMrr - mrr;
    totalDiff += diff;
    const value = Math.ceil(newMrr * 100);
    console.log('updating', sub.Email, sub.Plans, sub.MRR, 'to mrr:', newMrr, `(cents: ${value})`);
    promises.push(profitwell.updateSubscription(sub['UserRevenue User ID'], {
      email: sub.Email, plan: sub.Plans, value,
    }));
    if(promises.length && promises.length % 10 === 0) {
      await Promise.all(promises);
      await sleep(5000);
      promises = [];
    }
  }
  if(promises.length && promises % 10 === 0) {
    await Promise.all(promises);
  }
  console.log('total added MRR', totalDiff);
  console.log('done');
})();
