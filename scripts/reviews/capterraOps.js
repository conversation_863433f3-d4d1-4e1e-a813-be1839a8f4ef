const _ = require('lodash');
const config = require('../config');
const capterra = require('../lib/apis/capterra');
const capterraSourceController = require('../app/reviews/capterra.controller');
const mongooseLoader = require('../lib/mongooseLoader/testLoader');
const cronCapterraTask = require('../cron/jobs/capterraReviews');
const Scrapfly = require('../lib/apis/Scrapfly');

const scrapfly = new Scrapfly(config.scrapfly.apiKey);

(async () => {
  const testUrlId = 'https://www.capterra.co.uk/software/141155/pt-distinction\n';
  const accountId = '5e3abbcef133dc4e6c134bbe';
  const notif = {
    placeId: testUrlId,
    accountId,
  };
  const res = await capterraSourceController.handleReviews(notif, scrapfly);
  console.log('response', res);
})();
