const _ = require('lodash');
const facebook = require('../../lib/apis/facebook');

(async function main() {
  const retval = {
    total: 0,
    failed: 0,
    dupFailed: 0,
    success: 0,
    dupSuccess: 0,
    reviews: 0,
    errors: {},
  };
  const ids = getIds();
  const promises = [];
  for(let i = 0; i < ids.length; i += 1) {
    const { pageId: id, token: accessToken, duplicate } = ids[i];
    retval.total += 1;
    promises.push(facebook
      .getRatings(id, { access_token: accessToken })
      .then((results) => {
        const numReviews = (results.data && results.data.length) || null;
        retval.success += 1;
        retval.reviews += numReviews;
        if(duplicate) {
          retval.dupSuccess += 1;
        }
        console.log(id, 'success', numReviews, (duplicate && 'duplicate') || '');
      })
      .catch((err) => {
        retval.failed += 1;
        if(duplicate) {
          retval.dupFailed += 1;
        }
        const message = _.get(err, 'response.body.error.message', err.message);
        retval.errors[message] = (retval.errors[message] + 1) || 1;
        console.error(id, err.message, err.response && err.response.text, (duplicate && 'duplicate') || '');
      }));
  }
  await Promise.all(promises);
  console.log(retval);
}());

function getIds() {
  /*
   * db query (mongoose)
    var ids = [];
    var results = [];
    db.notifications.find({
    //  active: true,
        type: 'review',
        source: 'facebook',
    //     updatedAt: { $lte: ISODate('2020-06-01') },
    }).forEach((notification) => {
        var pageId = notification.placeId;
        var token = notification.pageToken
        var obj = { pageId, token };
        if(ids.includes(pageId)) {
            obj.duplicate = true;
        } else {
            ids.push(pageId);
        }
        results.push(obj);
    });
    print(results);
   */
  return [
    {
      pageId: '777122678973242',
      token: 'EAAFsOHByU6oBAHiSrI6wKURQQ8QJ2TSWE0Ocz4cZC0CCC1IPv8OCpgK4d2HlQV8M1R8ZCuKZBipcQPG4UpEv85gudqr5MI8YlCppGc2cJsAyg7UTwws0V9tLV7ZBG6X6nxYktdnUMhoexGY2A5euLmdHsGJc5lcgZBAeUbAHxlK5wxK0ZBa7weZCQPpvYqIkNAZD',
    },
  ];
}
