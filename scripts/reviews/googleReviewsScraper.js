// const path = require('path');
const _ = require('lodash');
const dbLoader = require('../../lib/mongooseLoader');
const googleReviews = require('../../lib/apis/googleReviews');
const gmb = require('../../lib/apis/googleMyBusiness');
const config = require('../../config');
const prodConfig = require('../../config/prodConfig');
const Notification = require('../../app/notifications/models/Notification');
const Review = require('../../app/notifications/models/Review');
const googleReviewsCron = require('../../cron/jobs/googleReviews');

const { gmbCallback, clientId, clientSecret } = config.googleAuth;

(async () => {
  // uncomment one of those
  await testScrape();
  // await scrapeReviewsForProd('67869405011b2ef89f05af62');
  // await getGmbReviews();
})();

async function testScrape() {
  const values = [
    'ChIJlw631wl75kcRYwlqtsONURw',
    // 'ChIJC2VOJP8ioFMR-wil0w6SdHI', // forever loading (user agent issue)
    // 'ChIJew-QdZjXuEcRizdIR4F-RZk', //german
    // 'ChIJ4bRjbTpz5kcRsy5uty6h2Gs', //france
    // 'ChIJc2Rf305JHRUR2yBQLp6Ax7U', //hebrew
    // 'https://g.co/kgs/u7a972', // g.co SERP
    // 'https://goo.gl/maps/MNJKHKcVt66PzpfSA', // ProveSource
    // 'https://g.page/ProveSource?gm', // ProveSource
    // 'ChIJOwDNuf5mAhURaox2aQIulY4',  // ProveSource
    // 'https://maps.app.goo.gl/mcn2QEck7N8aN1DG8',
    // 'https://www.google.com/maps/place/l+and+b+restaurant+bolton/@53.570666,-2.4384288,15z/data=!4m5!3m4!1s0x0:0x680e770e8430b4f1!8m2!3d53.5706553!4d-2.4384682',
    // 'ChIJ6Zeu3ZfTzRIR967LzNCylVQ',
    // 'https://g.page/agadirbr7',
    // 'https://g.page/Shufesalgavyam',
    // 'ChIJ1QFrTlxmAhUR_MISDkW2kZ8',
    // 'ChIJU8aKiTqbeUgRhkyLtlzngxM',
    // 'https://goo.gl/maps/Wm4NMDddWJa8JnKS8', // (aquarium)
    // 'ChIJqy5LJsJbzpQRUNcwe0n37dw', // (aquarium)
    // 'https://g.page/MercatoMontiRoma',
    // 'https://g.page/traininginmotion',
    // 'https://goo.gl/maps/8byZkpWDbTzkJC3c6', // stars replaced with numbers like 5/5
    // 'https://g.page/r/CUYPUuAeH_OsEAE', // new g.page selectors
    // 'https://www.google.com/maps/place/l+and+b+restaurant+bolton/@53.570666,-2.4384288,15z/data=!4m5!3m4!1s0x0:0x680e770e8430b4f1!8m2!3d53.5706553!4d-2.4384682',
    // 'https://goo.gl/maps/Ce9BrZJZmWBgXUX47', // new maps page selectors
    // 'https://goo.gl/maps/x51JL1LDr4SgDYEJ7', // new translation button (show original)
  ];
  for(let i = 0; i < values.length; i += 1) {
    const value = values[i];
    console.time(value);
    console.log('getting reviews for', value);
    await googleReviews.getScrapedReviews(value, {
      executable: config.googlePlaces.executable,
      headless: false,
      proxy: _.sample(prodConfig.proxies),
      // screenshotFolder: path.join(__dirname, './screenshots'),
      captchaApiKey: prodConfig.googlePlaces.captchaToken,
      lowestRating: 4,
      maximumReviews: 30,
    }).then((reviews) => {
      console.log(`got reviews ${reviews.length} ${value}`);
      console.log(reviews.map(r => `${'*'.repeat(20)}\n${r.authorName}: ${r.text}\n${'*'.repeat(20)}`).join('\n'));
      return reviews;
    }).catch((err) => {
      console.error('failed to get reviews', value, err);
      throw err;
    });
    console.timeEnd(value);
  }
}

async function scrapeReviewsForProd(notificationId) {
  await dbLoader.load('mongodb://localhost:8080/proofsrc-prod', config.mongodb.readPref);
  const notif = await Review.findOne({ _id: notificationId });
  const results = await googleReviewsCron.googleReviewsTask([notif]);
  console.log('got google reviews', results);
  // allow time for Feed to be saved
  setTimeout(() => {
    process.exit(0);
  }, 1000 * 5);
}

async function getGmbReviews() {
  // go to <api-url>/account/gmb-auth to get an access token
  const accessToken = '';
  const refreshToken = '';
  const tokens = await gmb.refreshAccessToken({ clientId, clientSecret, refreshToken });
  console.log('got new tokens', tokens);
  const accounts = await gmb.getAccounts({ accessToken });
  const account = accounts[0];
  console.log('accounts.0', JSON.stringify(account, null, 2));
  const locations = await gmb.getLocations({ accessToken, account: account.name });
  const location = locations[0];
  console.log('locations.0', JSON.stringify(location, null, 2));
  const reviews = await gmb.getReviews({ accessToken, location: location.name });
  console.log('reviews', JSON.stringify(reviews, null, 2));
}
