/* eslint-disable global-require */
const path = require('path');
const CustomReview = require('../../app/reviews/models/CustomReview');
const csvUtils = require('../../lib/utils/csvUtils');

(async () => {
  require('../../lib/mongooseLoader').load('mongodb://localhost:8080/proofsrc-prod');
  // await addCSVReviews(path.join(__dirname, './<EMAIL>'));
  console.log('done');
  process.exit(0);
})();

async function addCSVReviews(file) {
  const reviews = await csvUtils.parseArray(file);
  await Promise.all(reviews.map((review) => {
    const {
      review_date: dateStr,
      customer_name: author,
      'product id': productId,
      rate: ratingStr,
      review: reviewText,
    } = review;
    const date = new Date(dateStr.split(' ')[0]);
    return CustomReview.create({
      accountId: '630b515d2b46d5589e260e71',
      placeId: 'https://www.t5ear.com',
      active: true,
      time: date,
      authorName: author.trim(),
      rating: parseInt(ratingStr, 10),
      text: reviewText.trim(),
      guid: productId,
    });
  }));
}

async function addJSONReviews() {
  const reviews = require('./dovly-reviews.json');
  await Promise.all(reviews.map(r => CustomReview.create(({
    accountId: '5b8bded7434f965d7a85e116',
    placeId: 'https://www.bbb.org/us/az/phoenix/profile/credit-repair-services/dovly-1126-**********/customer-reviews',
    active: true,
    authorName: r.author,
    rating: 5,
    text: r.text,
    time: r.posted,
  }))));
}
