const querystring = require('querystring');
const facebook = require('../../lib/apis/facebook');
const urlUtils = require('../../lib/utils/urlUtils');

(async () => {
  const list = [
    'glassprismartworks',
    'Nike',
    'ProveSource',
    'https://facebook.com/Coca-Cola',
    'KaliGlassArt',
    'pishpeshuk.co.il',
  ];

  list.forEach(async (element) => {
    const likes = await facebook.getPageLikes(element).catch((err) => {
      console.error('facebook error', err);
    });
    console.log('Facebook page Likes: ', element, likes);
  });
})();
