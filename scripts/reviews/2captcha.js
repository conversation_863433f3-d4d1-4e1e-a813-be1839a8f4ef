const puppeteer = require('puppeteer-extra');
const recaptchaPlugin = require('puppeteer-extra-plugin-recaptcha');
const stealthPlugin = require('puppeteer-extra-plugin-stealth');
const { installMouseHelper } = require('../../lib/apis/google/install-mouse-helper');

const prodConfig = require('../../config/prodConfig');

(async () => {
  puppeteer.use(recaptchaPlugin({
    provider: { id: '2captcha', token: prodConfig.googlePlaces.captchaToken },
    visualFeedback: true, // colorize reCAPTCHAs (violet = detected, green = solved)
  }));
  const browser = await puppeteer.launch({ slowMo: 250, headless: false, args: ['--incognito', '--proxy-server=219.121.1.93'] });
  const pages = await browser.pages();
  const page = pages[0];
  await page.setViewport({
    width: 1024 + Math.trunc(Math.random() * 896),
    height: 768 + Math.trunc(Math.random() * 312),
  });
  await installMouseHelper(page);

  await page.goto('https://www.google.com/recaptcha/api2/demo');
  const results = await page.solveRecaptchas({ throwOnError: true });
  console.log(results);
  await Promise.all([
    page.waitForNavigation(),
    page.click('#recaptcha-demo-submit'),
  ]);
})();
