/* eslint-disable no-await-in-loop */
const fs = require('fs').promises;
const jwt = require('jsonwebtoken');
const chance = require('chance').Chance();
const mongooseLoader = require('../../lib/mongooseLoader');
const fileUtils = require('../../lib/utils/fileUtils');
const s3 = require('../../lib/apis/s3');
const prodConfig = require('../../config/prodConfig');

const accountService = require('../../app/account/account.service');
const Account = require('../../app/account/models/Account');
const notificationService = require('../../app/notifications/notification.service');

(async function main() {
  await mongooseLoader.load('mongodb://localhost:8080/proofsrc-prod');
  const files = await fileUtils.getFiles('./ava', { recursive: true });
  const email = chance.email({ domain: 'csmtest.com', length: 8 });
  const name = email.split('@')[0];
  const password = chance.string({ length: 10, alpha: true, numeric: true });
  const account = await createAccount({ email, password });
  account.apiKey = jwt.sign({ accountId: account.id }, prodConfig.jwt.api);
  await Promise.all([
    createWebhookNotification(account.id),
    createInfoNotification(account.id),
  ]);
  console.log('uploading', files);
  const promises = [];
  for(let i = 0; i < files.length; i += 1) {
    const file = files[i];
    const pathComps = file.split('/');
    // replace "/ava" with "test-pages", the folder on the bucket
    pathComps.splice(0, 1, `test-pages/${name}`);
    const bucketPath = pathComps.join('/');
    const fileBuffer = await fs.readFile(file);
    const fileString = fileBuffer.toString('utf8').replace('INJECT_API_KEY', account.apiKey);
    const fileBody = Buffer.from(fileString);
    promises.push(s3.uploadFile({
      accessKey: prodConfig.s3.accessKey,
      secret: prodConfig.s3.secret,
      bucket: 'proofsource-public-assets',
      body: fileBody,
      path: bucketPath,
    }));
  }
  const results = await Promise.all(promises);
  const urls = results.filter(res => res.url.includes('.html')).map(res => res.url);
  console.log('*'.repeat(10), 'test account ready', '*'.repeat(10), '\n');
  console.log(getInstructions({
    email,
    password,
    homepage: urls.find(url => url.includes('index.html')),
    thankyou: urls.find(url => url.includes('thank-you.html')),
  }));
  console.log('\n', '*'.repeat(50));
  process.exit(0);
}());

async function createAccount({ email, password }) {
  const account = await Account.findOne({ email });
  if(account) {
    throw new Error('account already exists');
  }
  return accountService.makeAccount({
    email: email || '<EMAIL>',
    password: password || 'Zaq12345',
    active: true,
  }).then((acc) => {
    acc.installed = Date.now();
    return acc.save();
  });
}

function createWebhookNotification(accountId) {
  const notif = notificationService.getWebhookStream({ accountId, name: 'signup' });
  notif.priority = 0;
  return notif.save();
}

function createInfoNotification(accountId) {
  const notif = notificationService.getInformational({
    accountId, name: 'info', message: 'Thanks for signing up!',
  });
  notif.urlTypes.display = 'contains';
  notif.displayURLs = ['thank-you'];
  notif.priority = 1;
  return notif.save();
}

function getInstructions({
  email, password, homepage, thankyou,
}) {
  return `
Thanks for your time the other day, it was nice talking to you.

As discussed, I’d like you to do a small practical test. Please send over your responses by Sunday evening. 
See the details and instructions below:

- Your test website is ${homepage}
- Login to the ProveSource dashboard: https://console.provesrc.com
- username: ${email}
- password: ${password}
- In the dashboard you will find two notifications, the questions below refer to these notifications.
- You can use our help center guides here: https://help.provesrc.com and videos here: https://www.youtube.com/c/ProveSource/videos

Here are 3 conversation scripts I'd like you to respond to (reply to this email) and solve their issue (if possible).
Keep your answers short, professional and to the point, do your best to help the customer.

1. Hi
I have a problem with my notification called "signup", I want it to show users who sign up on the homepage. I think I have everything setup correctly but it's not showing up.
Can you help?


2. Hi there,
I have an informational notification that's not showing up on my thank-you page ${thankyou}
What's wrong with it?

(Assume the customer has a subscription)
3. Hi!
I want to cancel my monthly subscription.`;
}
