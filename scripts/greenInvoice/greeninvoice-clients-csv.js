/* eslint-disable no-await-in-loop */
const _ = require('lodash');
const path = require('path');
const fs = require('fs').promises;
const prodConfig = require('../../config/prodConfig');
const countryCodes = require('../../lib/geo/countryCodes');

const { apiKey, secret } = prodConfig.greenInvoice;
const giV2 = require('../../lib/apis/GreenInvoice.v2').getClient(apiKey, secret);

/**
 * This generates a clients.csv file with all customer details for "Income Per Country" report
 * GreenInvoice is limited to 5k rows, those fucks.
 */
(async () => {
  const clients = [];
  console.log('fetching all clients from GreenInvoice');
  for(let i = 1; i < Infinity; i += 1) {
    const clientsRes = await giV2.searchClients({
      page: i,
      pageSize: 500,
    });
    if(!clientsRes.length) {
      break;
    }
    clients.push(...clientsRes);
    console.log(`got ${clientsRes.length} clients, total ${clients.length}`);
  }
  const lines = [[
    'שם לקוח', 'מספר עוסק', 'מספר בהנהלת חשבונות', 'מחלקה', 'תנאי תשלום', 'כתובת', 'עיר', 'מיקוד', 'קוד מדינה', 'מדינה',
  ].join(',')];
  lines.push(...clients.map(c => [
    `"${c.name}"`, '', c.accountingKey, '', '0', `"${c.address}"`, `"${c.city}"`, `"${c.zip}"`, c.country, `"${countryCodes.fromCode(c.country)}"`,
  ].join(',')));
  console.log(`writing ${clients.length} clients to clients.csv file`);
  await fs.writeFile(path.join(__dirname, 'clients.csv'), lines.join('\n'));
  process.exit(0);
})();
