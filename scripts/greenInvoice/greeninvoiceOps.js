const config = require('../../config/prodConfig');

const { apiKey, secret } = config.greenInvoice;
const gi = require('../../lib/apis/greeninvoice').getClient(api<PERSON>ey, secret);
const giV2 = require('../../lib/apis/GreenInvoice.v2').getClient(apiKey, secret, false);
// const Account = require('../app/account/models/Account');

(async () => {
  // await getInvoices();
  // await cancelInvoice('c20d2542-e3c1-48b5-977f-7904e3ba9d6e');
})();

async function cancelInvoice(docId) {
  await giV2.cancelInvoiceWithId(docId).catch((err) => {
    console.log('failed to cancel invoice', err);
  });
}

async function getInvoices() {
  const names = [
    'Zeta Media Pte Ltd',
    'VINCAR Pte Ltd',
  ];
  const docs = await gi.getDocumentsByNames(names);
  console.log(docs);
}

async function testClientSearch() {
  const clients = await giV2.searchClients({ name: 'www.amirarahim.com' });
  console.log('got clients', clients);
  // this fails because GreenInvoice are dumbasses
  const client = await giV2.addClient('www.amirarahim.com');
  console.log('added client', client);
}
