const readline = require('readline').promises;
const mongooseLoader = require('../../lib/mongooseLoader');
const Account = require('../../app/account/models/Account');
const Feed = require('../../app/account/models/Feed');
const WebsiteEvent = require('../../app/events/models/WebsiteEvent');
const CleanWebsiteEvent = require('../../app/events/models/CleanWebsiteEvent');
const FormEvent = require('../../app/events/models/FormEvent');
const CleanFormEvent = require('../../app/events/models/CleanFormEvent');
const StreamEvent = require('../../app/events/models/StreamEvent');
const CleanFormStreamEvent = require('../../app/events/models/CleanFormStreamEvent');
const WPEvent = require('../../app/events/models/WPEvent');
const WooEvent = require('../../app/events/models/WooEvent').model;
const WixEvent = require('../../app/wix/models/WixEvent');
const Magento2Event = require('../../app/events/models/Magento2Event');
const Magento1Event = require('../../app/events/models/Magento1Event');
const ShopifyEvent = require('../../app/events/models/ShopifyEvent');
const BigCommerceEvent = require('../../app/bigcommerce/models/BigcommerceEvent');
const ThinkificEvent = require('../../app/thinkific/ThinkificEvent');
const URLSuggestion = require('../../app/common/urlSuggestions/models/UrlSuggestion');
const FormURLSuggestion = require('../../app/common/urlSuggestions/models/FormUrlSuggestion');

(async () => {
  await mongooseLoader.load('mongodb://localhost:8080/proofsrc-prod');
  const accountId = '629e03f745b82f3810e2684f';
  const account = await Account.findOne({ _id: accountId });
  const rl = readline.createInterface({ input: process.stdin, output: process.stdout });
  let answer = await rl.question(`Delete form/conversion events related to  ${account.email}? (yes/no)\n`);
  if(answer && answer.trim().toLowerCase() === 'yes') {
    console.log('deleting feed and events');
    await Promise.all([
      Feed.remove({ accountId }),
      FormEvent.remove({ accountId }),
      CleanFormEvent.remove({ accountId }),
      StreamEvent.remove({ accountId }),
      CleanFormStreamEvent.remove({ accountId }),
      WPEvent.remove({ accountId }),
      WooEvent.remove({ accountId }),
      Magento1Event.remove({ accountId }),
      Magento2Event.remove({ accountId }),
      ShopifyEvent.remove({ accountId }),
      BigCommerceEvent.remove({ accountId }),
      ThinkificEvent.remove({ accountId }),
      WixEvent.remove({ accountId }),
    ]);
  }
  answer = await rl.question(`Delete counter events and url suggestions to  ${account.email}? (yes/no)\n`);
  if(answer && answer.trim().toLowerCase() === 'yes') {
    console.log('deleting counter events');
    await Promise.all([
      WebsiteEvent.remove({ accountId }),
      CleanWebsiteEvent.remove({ accountId }),
      URLSuggestion.remove({ accountId }),
      FormURLSuggestion.remove({ accountId }),
    ]);
  }
  console.log('done');
  process.exit(0);
})();
