// const fs = require('fs').promises;
// const parseDomain = require('parse-domain');

// (async () => {
//   const domains = require('./domains.json');
//   const file = './filteredDomains.json';
//   console.log('removing old file');
//   await fs.remove(file);
//   const filteredDomains = [];
//   console.log(`filtering ${domains.length} domains`);
//   for(let i = 0; i < domains.length; i += 1) {
//     const d = domains[i];
//     const { tld, domain } = parseDomain(d) || {};
//     const val = `${domain}.${tld}`;
//     if(tld
//       && domain
//       && !tld.includes('cn')
//       && !domain.includes('cloudflare')
//       && !domain.includes('preview')
//       && !domain.includes('localhost')
//       && !filteredDomains.includes(val)
//     ) {
//       filteredDomains.push(val);
//     }
//   }
//   console.log(`filtered, got ${filteredDomains.length} domains`);
//   console.log('writing file');
//   await fs.writeFile(file, JSON.stringify(filteredDomains, null, 2));
//   console.log('done');
//   process.exit(0);
// })();
//
// (async () => {
//   const txt = await fs.readFile('./sites.txt', { encoding: 'utf8' });
//   console.log('txt', txt.length);
//   const domains = txt.split('\n');
//   console.log('domains', domains.length);
//   const json = require('./filteredDomainsObjects.json');
//   for(let i = 0; i < domains.length; i += 1) {
//     json.push({ domain: domains[i], exists: false });
//   }
//   await fs.writeFile('./allDomains.json', JSON.stringify(json, null, 2));
// })();
