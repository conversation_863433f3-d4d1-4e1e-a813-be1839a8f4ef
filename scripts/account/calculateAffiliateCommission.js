/* eslint-disable no-console,no-await-in-loop */
const moment = require('moment');
const mongooseLoader = require('../../lib/mongooseLoader');
const Account = require('../../app/account/models/Account');
const IPN = require('../../app/billing/bluesnap/IPNModel');
const PaypalIPN = require('../../app/paypal/PaypalIPN');
const { stringUtils, dateUtils } = require('../../lib/utils');

const AFFECTS_BALANCE = ['CHARGE', 'RECURRING', 'REFUND', 'CANCELLATION_REFUND', 'CHARGEBACK'];

(async () => {
  const email = '<EMAIL>';
  await mongooseLoader.load('mongodb://localhost:8080/proofsrc-prod');
  await calculate(email);
  process.exit(0);
})();

async function calculate(email) {
  const account = await Account.findOne({
    $or: [{ email }, { 'affiliate.paypal': email }],
  });
  if(!account) {
    throw new Error('affiliate not found', email);
  }
  if(account.affiliate.balance < 0) {
    throw new Error('balance < 0, could be a refund after payout request');
  }
  console.log(`email: ${account.email}`);
  console.log(`created: ${moment(account.createdAt).format('YYYY-MM-DD')}`);
  console.log(`affiliate ID: ${account.affiliate.id}`);
  console.log(`balance: ${account.affiliate.balance.toFixed(2)}`);
  console.log(`paid: ${account.affiliate.paid.toFixed(2)}`);
  console.log(`payouts: ${account.affiliate.payouts}`);
  const currentPayout = account.affiliate.payouts && account.affiliate.payouts.pop();
  const previousPayout = account.affiliate.payouts && account.affiliate.payouts.pop();
  const startDate = dateUtils.normalizeTo12Am((previousPayout && previousPayout.date) || new Date('2018-01-01'));
  const endDate = (currentPayout && currentPayout.date) || new Date();
  if(currentPayout.date.getTime() - account.createdAt.getTime() < 86400 * 10 * 1000) {
    throw new Error('account asked for payout right after it was created');
  }
  const [free, paying] = await Promise.all([
    Account.find({ 'affiliate.referrer': account.affiliate.id, subscription: null }),
    Account.find({ 'affiliate.referrer': account.affiliate.id, subscription: { $ne: null } }),
  ]);
  console.log(`free: ${free.length}`);
  console.log(`paying: ${paying.length}`);
  console.log('-'.repeat(20), '\n');
  await printCommissions(account, startDate, endDate);
}

async function printCommissions(affAccount, startDate, endDate) {
  const affiliateId = affAccount.affiliate.id;
  const accounts = await Account.find({
    'affiliate.referrer': affiliateId, subscription: { $ne: null },
  }).select('email affiliate createdAt subscription source origin query hosts');
  if(!accounts || !accounts.length) {
    throw Error('account not found');
  }

  let ltv = 0;
  let total = 0;
  const transactions = [];
  for(let i = 0; i < accounts.length; i += 1) {
    const account = accounts[i];
    const subId = account.subscription.subscriptionId;
    const isShopify = account.shopify && account.shopify.length;
    const queryString = JSON.stringify(account.query);
    console.log(`scanning ${account.email}`, isShopify ? '(SHOPIFY)' : '', `(${account.id})`);
    console.log(`|- created: ${account.createdAt}`);
    console.log(`|- until date: ${moment(account.subscription.untilDate).format('YYYY-MM-DD')}`);
    console.log(`|- source: ${account.source}, origin: ${account.origin}, query: ${queryString}`);
    console.log(`|- hosts: ${account.hosts}`);
    const [bsIpns, paypalIpns] = await Promise.all([
      IPN.find({
        accountId: account.id,
        $or: [
          { type: { $in: AFFECTS_BALANCE } },
          { type: { $exists: false } },
        ],
        createdAt: { $gte: startDate, $lte: endDate },
      }),
      PaypalIPN.find({
        type: 'recurring_payment',
        createdAt: { $gte: startDate, $lte: endDate },
        'data.recurring_payment_id': subId,
      }),
    ]);
    const ipns = [].concat(bsIpns, paypalIpns);
    if(!ipns.length) {
      continue;
    }
    const commissionRate = affAccount.affiliate.commissionRate || 0.2;
    for(let j = 0; j < ipns.length; j += 1) {
      const ipnData = getIpnData(ipns[j]);
      const {
        type, reference, name, date, taxlessAmount, contractName, invoiceAmount, source,
      } = ipnData;
      const added = transactions.find(t => t.reference === reference && t.type === type);
      if(added) {
        console.log(`|- skipping ${reference}`);
      } else {
        transactions.push(ipnData);
        let commission = 0;
        if(!Number.isNaN(taxlessAmount)) {
          commission = taxlessAmount * commissionRate;
          total += commission;
          ltv += taxlessAmount;
        }
        console.log(`|- name: ${name}`);
        console.log(`|- transaction ${type} (#${reference}, ${source})`);
        console.log(`\t|- date: ${date.toISOString().split('T')[0]}`);
        console.log(`\t|- contract: ${contractName}`);
        // console.log(`\t|- invoice amount: ${invoiceAmount}`);
        console.log(`\t|- amount: ${taxlessAmount} (commission: ${commission.toFixed(2)}, rate: ${commissionRate}, total: ${total.toFixed(2)})`);
      }
    }
    console.log('*********************************\n');
  }
  console.log(`total ltv: ${ltv.toFixed(2)}`);
  console.log(`total: ${total.toFixed(2)}`);
  console.log(`Paypal: ${affAccount.affiliate.paypal}`);
  console.log(`ProveSource Affiliate Commission ${affAccount.id}`);
}

function getIpnData(ipn) {
  let type;
  let reference;
  let name;
  let date;
  let taxlessAmount;
  let contractName;
  let invoiceAmount;
  let source;
  if(ipn.data.referenceNumber) {
    ({ transactionType: type, invoiceAmount, contractName } = ipn.data);
    source = 'bluesnap';
    reference = ipn.data.referenceNumber;
    date = new Date(ipn.data.transactionDate);
    taxlessAmount = parseFloat(stringUtils.getNumber(invoiceAmount));
    if(contractName && contractName.toLowerCase().includes('vat')) {
      taxlessAmount /= 1.18;
    }
    const { firstName, lastName } = ipn.data;
    name = `${firstName} ${lastName}`;
  } else if(ipn.data.txn_id) {
    ({ type } = ipn);
    source = 'paypal';
    date = ipn.createdAt;
    reference = ipn.data.txn_id;
    contractName = ipn.data.transaction_subject;
    invoiceAmount = parseFloat(ipn.data.mc_gross);
    taxlessAmount = invoiceAmount;
    const { first_name: firstName, last_name: lastName } = ipn.data;
    name = `${firstName} ${lastName}`;
  } else {
    return null;
  }
  return {
    type, reference, name, date, invoiceAmount, taxlessAmount, contractName, source,
  };
}
