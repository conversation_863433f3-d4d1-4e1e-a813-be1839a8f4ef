const fs = require('fs-extra');
const json = require('./churned_2020-01-2020-11.json');
const phoneCodes = require('../../lib/geo/phoneCodes');

(async () => {
  for(let i = 0; i < json.length; i += 1) {
    const contact = json[i];
    const { email, countryCode } = contact;
    let phone = `${contact.phone}`;
    const phoneCode = phoneCodes[countryCode];
    contact.phoneCode = phoneCode;
    if(!phoneCode) {
      console.error('not found for', countryCode);
    }
    if(!phone.startsWith(phoneCode.substring(1))) {
      phone = phoneCode + phone;
    } else if(!phone.startsWith('+')) {
      phone = `+${phone}`;
    }
    contact.phoneCode = phoneCode;
    contact.fullPhone = phone;
  }
  await fs.writeFile('./customers.json', JSON.stringify(json, null, 2));
})();
