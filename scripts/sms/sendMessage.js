const fs = require('fs-extra');
const twilio = require('../../app/common/twilio.service');
const json = require('./customers.json');

(async () => {
  const results = [];
  for(let i = 0; i < json.length; i += 1) {
    const contact = json[i];
    const { name, fullPhone } = contact;
    const body = `Hi ${name},\nThis is ProveSource. You used to have a plan with us but have left since.\nWe wanted to offer you a BFCM deal:\nbit.ly/2UFki4M`;
    await twilio.sendMessage({ to: fullPhone, body })
      .then((res) => {
        results.push({ name, phone: fullPhone, res });
        console.log('sent', name, fullPhone, res);
      })
      .catch((err) => {
        results.push({ name, phone: fullPhone, err });
        console.log('failed', name, fullPhone, err);
      });
  }
  await fs.writeFile('./results.json', JSON.stringify(results, null, 2));
})();
