const config = require('../../config');
const mongooseLoader = require('../../lib/mongooseLoader');
const Account = require('../../app/account/models/Account');
const bigComService = require('../../app/bigcommerce/bigcommerce.service');

(async () => {
  await mongooseLoader.load('mongodb://localhost:8080/proofsrc-prod');
  const accounts = await Account
    .find({ 'bigcommerce.0': { $exists: true } })
    .select('bigcommerce email');

  const promises = [];
  for(let i = 0; i < accounts.length; i += 1) {
    const account = accounts[i];
    const bcStore = account.bigcommerce[0];
    const { storeHash, accessToken } = bcStore;
    promises.push(
      bigComService
        .getStoreInfo(storeHash, accessToken)
        .then((info) => {
          Object.assign(bcStore, info);
          return Promise.all([
            bigComService.saveSite(storeHash, info),
            account.save(),
          ]);
        })
        .catch((err) => {
          console.error(`failed updating bigcommerce store: ${storeHash}`, err);
        }),
    );
  }
  await Promise.all(promises);
  console.log('done');
  process.exit(0);
})();
