const mongooseLoader = require('../../lib/mongooseLoader');
const Account = require('../../app/account/models/Account');
const BigCommerce = require('../../lib/apis/bigcommerce');

(async () => {
  await mongooseLoader.load('mongodb://localhost:8080/proofsrc-prod');
  const hash = '6bmy1u2gws';
  const [account, removedAccount] = await Promise.all([
    Account.findOne({ 'bigcommerce.storeHash': hash }),
    Account.findOne({ 'removedBigcommerce.storeHash': hash }),
  ]);
  if(!account && !removedAccount) {
    throw new Error('account with store not found');
  } else if(!account && removedAccount) {
    console.log('app removed from store');
  }
  const store = (account && account.getBigcommerceStore(hash)) || removedAccount.getBigcommerceStore(hash, true);
  console.log('store', store.toObject());
  const bc = BigCommerce(hash, store.accessToken);
  const webhooks = await bc.getAllWebhooks();
  console.log('webhooks', webhooks);
  const webhook = webhooks.find(hook => hook.destination.includes('bigcommerce/order'));
  const activateRes = await bc.activateWebhook(webhook.id);
  console.log('activated', activateRes);
  process.exit(0);
})();
