/* eslint-disable no-console */
const mongooseLoader = require('../../lib/mongooseLoader');
const Account = require('../../app/account/models/Account');
const BigcommerceEvent = require('../../app/bigcommerce/models/BigcommerceEvent');
const bigcommerceService = require('../../app/bigcommerce/bigcommerce.service');
const { importOrders } = require('../../app/bigcommerce/auth');

// Static constant for accountId
const ACCOUNT_ID = '6814f2175fa5baae186a8478'; // Replace with your desired accountId
const STORE_HASH = 'ugru7t1'; // Replace with your desired storeHash

(async () => {
  await mongooseLoader.load('mongodb://localhost:8080/proofsrc-prod');
  const account = await Account.findById(ACCOUNT_ID);
  if(!account) {
    console.error(`Account not found: ${ACCOUNT_ID}`);
    process.exit(1);
  }
  console.log(`processing account ${account.email}`);

  // Find the specific store to process
  if(!account.bigcommerce || !account.bigcommerce.length) {
    console.error('No BigCommerce stores found for this account.');
    process.exit(1);
  }

  const store = account.getBigcommerceStore(STORE_HASH);
  if(!store) {
    console.error(`No store found with storeHash: ${STORE_HASH}`);
    process.exit(1);
  }

  const { storeHash, accessToken, domain } = store;
  if(!storeHash || !accessToken) {
    console.error(`Store has missing storeHash or accessToken: ${JSON.stringify(store)}`);
    process.exit(1);
  }
  // Fetch latest orders
  console.log(`Refetching orders for storeHash: ${storeHash}`);
  const orders = await bigcommerceService.getLastOrders(storeHash, accessToken);
  if(!orders || !orders.length) {
    console.log(`No orders found for storeHash: ${storeHash}`);
    process.exit(0);
  }

  // Delete all BigcommerceEvent documents for this account
  const deleteResult = await BigcommerceEvent.deleteMany({ accountId: ACCOUNT_ID, store: storeHash });
  console.log(`Deleted ${deleteResult.n} BigcommerceEvent(s) for account ${ACCOUNT_ID}`);

  // Import orders using the same logic as in auth.js
  const events = await importOrders({
    accountId: ACCOUNT_ID,
    accessToken,
    storeHash,
    domain,
    orders,
  });

  console.log(`Re-imported ${events.length} orders for storeHash: ${storeHash}`);

  console.log('Done.');
  process.exit(0);
})();
