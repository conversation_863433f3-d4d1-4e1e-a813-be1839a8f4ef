// const config = require('../../config');
const mongooseLoader = require('../../lib/mongooseLoader');
const Account = require('../../app/account/models/Account');
const BigCommerceEvent = require('../../app/bigcommerce/models/BigcommerceEvent');
const bcService = require('../../app/bigcommerce/bigcommerce.service');

(async () => {
  await mongooseLoader.load('mongodb://localhost:8080/proofsrc-prod');
  const accountId = '60f5a2368853cb59ed8db8da';
  const account = await Account.findOne({ _id: accountId });
  const events = await BigCommerceEvent.find({ accountId, date: { $lt: new Date('2021-08-11') } }).sort({ date: -1 }).limit(50);

  // const promises = [];
  for(let i = 0; i < events.length; i += 1) {
    const event = events[i];
    const bcStore = account.getBigcommerceStore(event.store);
    const { domain, storeHash, accessToken } = bcStore;
    const products = await Promise.all(
      event.products.map(p => bcService.getProductInfo({
        domain, accessToken, storeHash, productId: p.id,
      })),
    );
    event.products.forEach((product) => {
      const apiProduct = products.find(p => p.id === product.id);
      if(product.image !== apiProduct.image) {
        console.log('image changed', product.image, apiProduct.image);
        console.log(`${product.name}: ${product.image}`);
        console.log(`${apiProduct.name}: ${apiProduct.image}`);
        product.image = apiProduct.image;
      }
    });
    const modified = event.isModified();
    if(modified) {
      await event.save();
    }
    console.log(event.date, 'modified products:', modified);
  }
  // await Promise.all(promises);
  console.log('done');
  process.exit(0);
})();
