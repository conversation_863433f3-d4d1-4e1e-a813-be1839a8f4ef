/* eslint-disable no-console */
const dbLoader = require('../../lib/mongooseLoader');
const Account = require('../../app/account/models/Account');
const IPN = require('../../app/billing/bluesnap/IPNModel');
const StripeEvent = require('../../app/billing/stripe/StripeEvent');
const PaypalIPN = require('../../app/paypal/PaypalIPN');

(async () => {
  try {
    await dbLoader.load('mongodb://localhost:8080/proofsrc-prod');
    const startDate = new Date('2022-01-01');
    const endDate = new Date('2025-05-31');
    const activeOnly = true; // include only currently active subscriptions
    const loginTypeOnly = true; // include only bigcommerce login accounts
    const maxAmount = 110; // include yearly transactions? set to Infinity
    await calculateRevShare({
      startDate, endDate, activeOnly, loginTypeOnly, maxAmount,
    });
    process.exit(0);
  } catch(error) {
    console.error('Error:', error);
    process.exit(1);
  }
})();

async function calculateRevShare({
  startDate, endDate, activeOnly = true, loginTypeOnly = true, maxAmount = 0,
}) {
  // Get all BigCommerce accounts
  const accounts = await Account.find({
    ...(activeOnly
      ? { 'subscription.recentIPN': { $in: ['CHARGE', 'RECURRING'] } }
      : { subscription: { $ne: null } }
    ),
    ...(loginTypeOnly && { loginType: 'bigcommerce' }),
    $or: [
      { 'bigcommerce.0': { $exists: true } },
      { 'removedBigcommerce.0': { $exists: true } },
    ],
  }).select('email subscription bigcommerce loginType');

  const monthRanges = generateMonthRanges(startDate, endDate);
  const monthlyResults = [];

  // Process months sequentially
  /* eslint-disable no-await-in-loop */
  for(let i = 0; i < monthRanges.length; i += 1) {
    const { start, end, monthName } = monthRanges[i];
    const monthResults = await processMonth({
      start, end, accounts, maxAmount,
    });
    // Print monthly summary
    console.log([
      `\n${'='.repeat(20)}`,
      `Summary for ${monthName}`,
      `total: ${monthResults.total}`,
      `amounts: ${monthResults.amounts.join(' + ')}`,
      `fees: ${monthResults.total * 0.2}`,
      `subscriptions: ${monthResults.subscriptions}`,
      `transactions: ${monthResults.transactions}`,
      `refunds: ${monthResults.refundCount}`,
      `chargebacks: ${monthResults.chargebacks}`,
      `bluesnap: ${monthResults.bluesnap}`,
      `stripe: ${monthResults.stripe}`,
      `paypal: ${monthResults.paypal}`,
      `${'='.repeat(20)}`,
    ].join('\n'));

    monthlyResults.push({
      month: monthName,
      ...monthResults,
    });
  }
  /* eslint-enable no-await-in-loop */

  // Print overall summary
  const totals = monthlyResults.reduce((acc, curr) => ({
    total: acc.total + curr.total,
    transactions: acc.transactions + curr.transactions,
    subscriptions: acc.subscriptions + curr.subscriptions,
    chargebacks: acc.chargebacks + curr.chargebacks,
    refundCount: acc.refundCount + curr.refundCount,
    bluesnap: acc.bluesnap + curr.bluesnap,
    stripe: acc.stripe + curr.stripe,
    paypal: acc.paypal + curr.paypal,
  }), {
    total: 0, transactions: 0, subscriptions: 0, chargebacks: 0, refundCount: 0, bluesnap: 0, stripe: 0, paypal: 0,
  });

  console.log([
    `\n${'='.repeat(40)}`,
    'OVERALL SUMMARY',
    `${'='.repeat(40)}`,
    `Total Revenue: ${totals.total}`,
    `Total Fees: ${totals.total * 0.2}`,
    `Total Transactions: ${totals.transactions}`,
    `Total Refunds: ${totals.refundCount}`,
    `Total Chargebacks: ${totals.chargebacks}`,
    `BlueSnap Revenue: ${totals.bluesnap}`,
    `Stripe Revenue: ${totals.stripe}`,
    `PayPal Revenue: ${totals.paypal}`,
    `Net Revenue (after chargebacks): ${totals.total - totals.chargebacks}`,
    `Net Fees (after chargebacks): ${(totals.total - totals.chargebacks) * 0.2}`,
    `Average Monthly Revenue: ${(totals.total / monthlyResults.length).toFixed(2)}`,
    `Average Monthly Transactions: ${(totals.transactions / monthlyResults.length).toFixed(2)}`,
    `${'='.repeat(40)}`,
  ].join('\n'));

  // Print monthly totals
  monthlyResults.forEach(({ month, total }) => {
    console.log(`${month}: ${total}`);
  });

  return totals;
}

async function processMonth({
  start, end, accounts, maxAmount,
}) {
  const results = {
    total: 0,
    subscriptions: 0,
    transactions: 0,
    amounts: [],
    skipped: [],
    chargebacks: 0,
    refundCount: 0,
    bluesnap: 0,
    stripe: 0,
    paypal: 0,
  };

  // Get IPN transactions for the period
  const ipnTransactions = await IPN.find({
    createdAt: {
      $gte: start,
      $lt: end,
    },
    type: { $in: ['CHARGE', 'RECURRING', 'REFUND', 'CHARGEBACK', 'CANCELLATION_REFUND'] },
  }).select('accountId amount createdAt email type');

  // Get Stripe events for the period
  const stripeEvents = await StripeEvent.find({
    createdAt: {
      $gte: start,
      $lt: end,
    },
    type: { $in: ['invoice.payment_succeeded', 'charge.succeeded', 'charge.refunded', 'charge.dispute.created'] },
  }).select('event createdAt');

  // Get PayPal IPN transactions for the period
  const paypalTransactions = await PaypalIPN.find({
    createdAt: {
      $gte: start,
      $lt: end,
    },
    type: { $in: ['recurring_payment'] },
  }).select('data createdAt');

  accounts.forEach(({ _id, email, subscription }) => {
    let amount = 0;
    let transactionDate = null;
    let source = 'none';

    // Check IPN transactions
    const ipnTransaction = ipnTransactions.find(t => t.accountId && t.accountId.toString() === _id.toString());
    if(ipnTransaction) {
      ({ amount, createdAt: transactionDate, type } = ipnTransaction);
      source = 'bluesnap';
      if(type === 'REFUND' || type === 'CANCELLATION_REFUND') {
        amount = -Math.abs(amount);
        results.refundCount += 1;
      } else if(type === 'CHARGEBACK') {
        results.chargebacks += Math.abs(amount);
        amount = -Math.abs(amount);
      }
    }

    // Check Stripe transactions
    const stripeTransaction = stripeEvents.find((e) => {
      const { event } = e;
      return event && event.data && event.data.object
             && event.data.object.customer_email
             && event.data.object.customer_email.toLowerCase() === email.toLowerCase();
    });
    if(stripeTransaction && !amount) { // Only use Stripe if no IPN found
      const { event } = stripeTransaction;
      if(event.type === 'charge.refunded') {
        amount = -(event.data.object.amount_refunded / 100); // Make refunds negative
        results.refundCount += 1;
      } else if(event.type === 'charge.dispute.created') {
        results.chargebacks += Math.abs(event.data.object.amount / 100);
        amount = -Math.abs(event.data.object.amount / 100);
      } else {
        amount = event.data.object.amount_paid / 100; // Convert from cents
      }
      transactionDate = stripeTransaction.createdAt;
      source = 'stripe';

      if(amount !== 0) {
        results.stripe += amount;
      }
    }

    // Check PayPal transactions
    const paypalTransaction = paypalTransactions.find((t) => {
      const { data } = t;
      return data && data.custom && data.custom.toLowerCase() === email.toLowerCase();
    });
    if(paypalTransaction && !amount) { // Only use PayPal if no other transaction found
      const { data } = paypalTransaction;
      if(data.txn_type === 'refund') {
        amount = -Math.abs(parseFloat(data.mc_gross || 0)); // Make refunds negative
        results.refundCount += 1;
      } else {
        amount = parseFloat(data.mc_gross || 0);
      }
      transactionDate = paypalTransaction.createdAt;
      source = 'paypal';
    }

    // has amount and not yearly
    if(amount !== 0 && amount < maxAmount) {
      results.transactions += 1;
      results.amounts.push(amount);
      results.total += amount;
      results[source] += amount;
    } else {
      results.skipped.push({ email, note: 'no transaction found' });
    }
    results.subscriptions += subscription.untilDate >= start;
    // console.log([
    //   email,
    //   contractName: ,
    //   getDate(transactionDate) || 'N/A',
    //   amount || 0,
    //   source,
    //   amount > 0,
    // ].join(','));
  });

  return results;
}

function generateMonthRanges(startDate, endDate) {
  const ranges = [];
  const currentDate = new Date(startDate);
  currentDate.setDate(1); // Ensure we start from the first day of the month
  while(currentDate < endDate) {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const start = new Date(year, month, 1);
    const end = new Date(year, month + 1, 1);
    ranges.push({
      start,
      end,
      monthName: start.toLocaleString('default', { month: 'long', year: 'numeric' }),
    });
    currentDate.setMonth(currentDate.getMonth() + 1);
  }
  return ranges;
}
